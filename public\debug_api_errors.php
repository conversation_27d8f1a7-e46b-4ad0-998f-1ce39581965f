<?php
/**
 * 调试API错误
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>调试API错误</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;} .log{background:#f8f9fa;padding:10px;border-radius:4px;margin:10px 0;font-family:monospace;font-size:12px;max-height:300px;overflow-y:auto;}</style>";
echo "</head><body>";

echo "<h1>🔍 调试API错误</h1>";

echo "<h2>🧪 测试API端点</h2>";
echo "<button class='test-btn' onclick=\"testAPI('/strongadmin/customer-service/session/1/messages')\">测试获取消息API</button>";
echo "<button class='test-btn' onclick=\"testAPI('/strongadmin/customer-service/message-stream')\">测试SSE流</button>";
echo "<button class='test-btn' onclick=\"testAPI('/strongadmin/customer-service/system/status')\">测试系统状态</button>";
echo "<button class='test-btn' onclick=\"testAPI('/api/customer-service/status')\">测试前台状态</button>";

echo "<div id='test-results' class='log'></div>";

echo "<h2>📋 Laravel日志</h2>";
echo "<p>检查 storage/logs/laravel.log 文件中的错误信息</p>";

$logFile = dirname(__DIR__) . '/storage/logs/laravel.log';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    $recentLines = array_slice($lines, -50); // 最近50行
    
    echo "<div class='log'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'ERROR') !== false || strpos($line, 'Exception') !== false) {
            echo "<div class='error'>" . htmlspecialchars($line) . "</div>";
        } else {
            echo "<div>" . htmlspecialchars($line) . "</div>";
        }
    }
    echo "</div>";
} else {
    echo "<p class='info'>日志文件不存在</p>";
}

echo "<h2>🔗 测试链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/strongadmin/customer-service/system-config' target='_blank'>系统配置</a></li>";
echo "<li><a href='/debug_admin_reply.php' target='_blank'>调试后台回复</a></li>";
echo "</ul>";

echo "<script>
function testAPI(url) {
    const results = document.getElementById('test-results');
    results.innerHTML += '<p class=\"info\">🔄 测试: ' + url + '</p>';
    
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        results.innerHTML += '<p class=\"info\">📊 状态码: ' + response.status + '</p>';
        
        if (response.ok) {
            return response.text();
        } else {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }
    })
    .then(data => {
        results.innerHTML += '<p class=\"success\">✅ 响应成功</p>';
        results.innerHTML += '<pre>' + data.substring(0, 500) + (data.length > 500 ? '...' : '') + '</pre>';
    })
    .catch(error => {
        results.innerHTML += '<p class=\"error\">❌ 错误: ' + error.message + '</p>';
    });
}
</script>";

echo "</body></html>";
?>
