<?php
/**
 * 调试会话数据
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>调试会话数据</title>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;} .json{background:#f8f8f8;padding:10px;border-radius:4px;overflow:auto;}</style>";
echo "</head><body>";

echo "<h1>🔍 调试会话数据</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p style='color:green;'>✅ 数据库连接成功</p>";
    
    // 获取会话数据
    $sessions = $pdo->query("
        SELECT * FROM st_customer_service_sessions 
        ORDER BY created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    echo "<h2>📋 会话列表</h2>";
    if (empty($sessions)) {
        echo "<p style='color:orange;'>⚠️ 没有找到会话数据</p>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>会话ID</th><th>访客名称</th><th>状态</th><th>未读数量</th><th>创建时间</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>" . ($session['visitor_name'] ?: '访客-' . strtoupper(substr(md5($session['session_id']), 0, 6))) . "</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>" . ($session['unread_count'] ?? 0) . "</td>";
            echo "<td>{$session['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 显示JSON格式
        echo "<h3>📄 JSON格式数据</h3>";
        echo "<div class='json'>";
        echo "<pre>" . json_encode($sessions, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        echo "</div>";
    }
    
    // 获取消息数据
    echo "<h2>💬 最新消息</h2>";
    $messages = $pdo->query("
        SELECT m.*, s.session_id 
        FROM st_customer_service_messages m
        LEFT JOIN st_customer_service_sessions s ON m.session_id = s.id
        ORDER BY m.created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    if (empty($messages)) {
        echo "<p style='color:orange;'>⚠️ 没有找到消息数据</p>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>会话</th><th>发送者</th><th>消息</th><th>已读</th><th>时间</th></tr>";
        foreach ($messages as $msg) {
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['session_id']}</td>";
            echo "<td>{$msg['sender_type']}</td>";
            echo "<td>" . htmlspecialchars(substr($msg['message'], 0, 50)) . "</td>";
            echo "<td>" . ($msg['is_read'] ? '是' : '否') . "</td>";
            echo "<td>{$msg['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>🔗 测试链接</h2>";
    echo "<ul>";
    echo "<li><a href='test_cs.php' target='_blank'>发送测试消息</a></li>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
    echo "<li><a href='javascript:location.reload()'>刷新此页面</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color:red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
