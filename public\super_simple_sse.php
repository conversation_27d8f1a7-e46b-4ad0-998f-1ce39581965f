<?php
// 超级简单的SSE - 只检查消息
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');

$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 数据库连接
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=mostxx_com;charset=utf8mb4', 'mostxx_com', 'fHnrmH9w5nw1pd53');
    
    // 获取会话ID
    $stmt = $pdo->prepare("SELECT id FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        // 创建会话
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, status, created_at, updated_at) VALUES (?, 'active', NOW(), NOW())");
        $stmt->execute([$sessionId]);
        $sessionDbId = $pdo->lastInsertId();
    } else {
        $sessionDbId = $session['id'];
    }
    
    echo "data: 连接成功,会话ID:$sessionDbId\n\n";
    flush();
    
    // 简单循环检查
    for ($i = 0; $i < 300; $i++) {
        // 检查新消息
        $stmt = $pdo->prepare("SELECT * FROM st_customer_service_messages WHERE session_id = ? AND id > ? AND sender_type = 'admin' ORDER BY id ASC");
        $stmt->execute([$sessionDbId, $lastMessageId]);
        $messages = $stmt->fetchAll();
        
        if ($messages) {
            foreach ($messages as $msg) {
                echo "data: " . json_encode([
                    'type' => 'new_reply',
                    'message' => $msg['message'],
                    'id' => $msg['id']
                ]) . "\n\n";
                $lastMessageId = $msg['id'];
            }
            flush();
        }
        
        sleep(1);
    }
    
} catch (Exception $e) {
    echo "data: 错误:" . $e->getMessage() . "\n\n";
}
?>
