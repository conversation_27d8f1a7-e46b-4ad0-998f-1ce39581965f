<?php
/**
 * 测试删除会话路由
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>测试删除路由</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 测试删除会话路由</h1>";

// 获取CSRF Token
$token = '';
try {
    // 启动session来获取CSRF token
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // 简单的token生成
    if (!isset($_SESSION['_token'])) {
        $_SESSION['_token'] = bin2hex(random_bytes(32));
    }
    $token = $_SESSION['_token'];
    
    echo "<p class='info'>📋 CSRF Token: " . substr($token, 0, 20) . "...</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ 无法获取CSRF Token: " . $e->getMessage() . "</p>";
}

echo "<h2>🧪 测试删除会话</h2>";
echo "<form method='POST' style='margin:20px 0;'>";
echo "<input type='hidden' name='_token' value='$token'>";
echo "<label>会话ID: <input type='number' name='session_id' value='1' required></label><br><br>";
echo "<button type='submit' name='test_delete' class='test-btn'>测试删除会话</button>";
echo "</form>";

if (isset($_POST['test_delete'])) {
    $sessionId = $_POST['session_id'] ?? 1;
    $csrfToken = $_POST['_token'] ?? '';
    
    echo "<h3>🔄 正在测试删除会话 $sessionId...</h3>";
    
    // 测试DELETE请求
    $url = "http://www.strongshop.local/strongadmin/customer-service/session/$sessionId";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'X-CSRF-TOKEN: ' . $csrfToken,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p class='info'>📊 HTTP状态码: $httpCode</p>";
    
    if ($error) {
        echo "<p class='error'>❌ CURL错误: $error</p>";
    } else {
        echo "<p class='info'>📄 响应内容:</p>";
        echo "<pre style='background:#f5f5f5;padding:10px;border-radius:4px;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        // 尝试解析JSON
        $json = json_decode($response, true);
        if ($json) {
            echo "<p class='success'>✅ JSON解析成功:</p>";
            echo "<pre style='background:#e8f5e8;padding:10px;border-radius:4px;'>";
            print_r($json);
            echo "</pre>";
        } else {
            echo "<p class='error'>❌ 响应不是有效的JSON</p>";
        }
    }
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/strongadmin' target='_blank'>后台首页</a></li>";
echo "</ul>";

echo "</body></html>";
?>
