<?php

namespace App\Http\Controllers\Strongadmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class CustomerServiceSettingsController extends Controller
{
    /**
     * 显示设置页面
     */
    public function index()
    {
        $settings = $this->getSettingsByCategory();
        return view('strongadmin.customer-service.settings', compact('settings'));
    }

    /**
     * 保存设置
     */
    public function store(Request $request)
    {
        try {
            $settings = $request->input('settings', []);

            // 调试日志
            \Log::info('客服设置保存请求', [
                'settings' => $settings,
                'all_input' => $request->all()
            ]);

            foreach ($settings as $key => $value) {
                // 处理文件上传
                if ($request->hasFile("settings.{$key}")) {
                    $value = $this->handleFileUpload($request->file("settings.{$key}"), $key);
                }

                // 处理布尔值 - 支持数组形式的值
                if (is_array($value)) {
                    // 如果是数组，取最后一个值（checkbox会产生数组）
                    $value = end($value);
                }

                if (is_string($value) && in_array($value, ['0', '1'])) {
                    $value = $value === '1' ? '1' : '0';
                }
                
                // 更新设置
                DB::table('customer_service_settings')
                  ->where('setting_key', $key)
                  ->update([
                      'setting_value' => $value,
                      'updated_at' => now()
                  ]);
            }
            
            return response()->json([
                'success' => true,
                'message' => '设置保存成功'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('保存客服设置失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个设置值
     */
    public function getSetting($key)
    {
        $setting = DB::table('customer_service_settings')
                    ->where('setting_key', $key)
                    ->where('is_active', 1)
                    ->first();
        
        if (!$setting) {
            return response()->json([
                'success' => false,
                'message' => '设置不存在'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'key' => $setting->setting_key,
                'value' => $setting->setting_value,
                'type' => $setting->setting_type
            ]
        ]);
    }

    /**
     * 获取所有设置（API）
     */
    public function getSettings()
    {
        $settings = DB::table('customer_service_settings')
                     ->where('is_active', 1)
                     ->get()
                     ->keyBy('setting_key')
                     ->map(function ($item) {
                         return $item->setting_value;
                     });
        
        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * 上传音效文件
     */
    public function uploadSound(Request $request)
    {
        $request->validate([
            'sound_file' => 'required|file|mimes:mp3,wav,ogg|max:2048' // 最大2MB
        ]);

        try {
            $file = $request->file('sound_file');
            $filename = 'customer_service_sound_' . time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('customer-service/sounds', $filename, 'public');
            
            // 更新设置
            DB::table('customer_service_settings')
              ->where('setting_key', 'sound_file')
              ->update([
                  'setting_value' => $path,
                  'updated_at' => now()
              ]);
            
            return response()->json([
                'success' => true,
                'message' => '音效文件上传成功',
                'file_path' => $path,
                'file_url' => Storage::url($path)
            ]);
            
        } catch (\Exception $e) {
            \Log::error('上传音效文件失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '上传失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 按分类获取设置
     */
    private function getSettingsByCategory()
    {
        $settings = DB::table('customer_service_settings')
                     ->where('is_active', 1)
                     ->orderBy('category')
                     ->orderBy('sort_order')
                     ->get()
                     ->groupBy('category');
        
        return $settings;
    }

    /**
     * 离线留言列表
     */
    public function offlineMessages()
    {
        $messages = DB::table('customer_service_offline_messages')
                     ->orderBy('created_at', 'desc')
                     ->paginate(20);

        return view('strongadmin.customer-service.offline-messages', compact('messages'));
    }

    /**
     * 回复离线留言
     */
    public function replyOfflineMessage(Request $request, $id)
    {
        $request->validate([
            'reply' => 'required|string|max:2000'
        ]);

        try {
            $message = DB::table('customer_service_offline_messages')
                        ->where('id', $id)
                        ->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => '留言不存在'
                ], 404);
            }

            // 更新留言状态
            DB::table('customer_service_offline_messages')
              ->where('id', $id)
              ->update([
                  'admin_reply' => $request->input('reply'),
                  'status' => 'replied',
                  'replied_at' => now(),
                  'replied_by' => auth()->id(),
                  'updated_at' => now()
              ]);

            // 这里可以添加邮件通知逻辑
            if ($message->visitor_email) {
                $this->sendEmailReply($message, $request->input('reply'));
            }

            return response()->json([
                'success' => true,
                'message' => '回复成功'
            ]);

        } catch (\Exception $e) {
            \Log::error('回复离线留言失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '回复失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取设置值（用于前台）
     */
    public function getPublicSettings()
    {
        $settings = DB::table('customer_service_settings')
                     ->where('is_active', 1)
                     ->whereIn('category', ['basic', 'appearance', 'sound', 'offline'])
                     ->get()
                     ->keyBy('setting_key')
                     ->map(function ($item) {
                         // 处理不同类型的值
                         switch ($item->setting_type) {
                             case 'boolean':
                                 return $item->setting_value === '1';
                             case 'number':
                                 return (float) $item->setting_value;
                             case 'json':
                                 return json_decode($item->setting_value, true);
                             case 'file':
                                 return $item->setting_value ? Storage::url($item->setting_value) : '';
                             default:
                                 return $item->setting_value;
                         }
                     });

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * 处理文件上传
     */
    private function handleFileUpload($file, $settingKey)
    {
        $allowedTypes = [
            'sound_file' => ['mp3', 'wav', 'ogg'],
            'admin_avatar' => ['jpg', 'jpeg', 'png', 'gif']
        ];

        $extension = $file->getClientOriginalExtension();

        if (isset($allowedTypes[$settingKey]) && !in_array($extension, $allowedTypes[$settingKey])) {
            throw new \Exception("不支持的文件类型: {$extension}");
        }

        $subdir = strpos($settingKey, 'sound') !== false ? 'sounds' : 'avatars';
        $filename = $settingKey . '_' . time() . '.' . $extension;
        $path = $file->storeAs("customer-service/{$subdir}", $filename, 'public');

        return $path;
    }

    /**
     * 发送邮件回复（示例）
     */
    private function sendEmailReply($message, $reply)
    {
        // 这里可以实现邮件发送逻辑
        // 暂时只记录日志
        \Log::info("发送邮件回复给 {$message->visitor_email}: {$reply}");
    }
}
