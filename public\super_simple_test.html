<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>超级简单测试</title></head>
<body>
<h1>超级简单测试</h1>
<div id="messages" style="border:1px solid #ccc; height:300px; overflow-y:auto; padding:10px;"></div>
<button onclick="startSSE()">开始接收消息</button>
<button onclick="openReplyTool()">打开回复工具</button>

<script>
let eventSource = null;

function log(msg) {
    document.getElementById('messages').innerHTML += new Date().toLocaleTimeString() + ': ' + msg + '<br>';
}

function startSSE() {
    if (eventSource) eventSource.close();
    
    log('开始连接SSE...');
    eventSource = new EventSource('/super_simple_sse.php?session_id=super_simple_test&last_message_id=0');
    
    eventSource.onopen = function() {
        log('SSE连接成功！');
    };
    
    eventSource.onmessage = function(event) {
        log('收到: ' + event.data);
        
        try {
            const data = JSON.parse(event.data);
            if (data.type === 'new_reply') {
                log('🎉 新回复: ' + data.message);
            }
        } catch (e) {
            // 忽略解析错误
        }
    };
    
    eventSource.onerror = function() {
        log('SSE连接错误');
    };
}

function openReplyTool() {
    window.open('/super_simple_reply.php', '_blank');
}
</script>
</body>
</html>
