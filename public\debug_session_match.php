<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>调试SessionID匹配</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .info { background: #e6f7ff; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #fff2f0; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .success { background: #f6ffed; padding: 15px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔍 调试SessionID匹配问题</h1>
    
    <div class="info">
        <h3>📋 当前数据库中的会话</h3>
        <?php
        try {
            $sessions = DB::select("
                SELECT id, session_id, visitor_name, visitor_ip, status, created_at
                FROM st_customer_service_sessions 
                ORDER BY created_at DESC 
                LIMIT 10
            ");
            
            if (empty($sessions)) {
                echo "<p>没有找到会话记录</p>";
            } else {
                echo "<table>";
                echo "<tr><th>ID</th><th>Session ID</th><th>访客</th><th>IP</th><th>状态</th><th>创建时间</th></tr>";
                
                foreach ($sessions as $session) {
                    echo "<tr>";
                    echo "<td>{$session->id}</td>";
                    echo "<td style='font-family:monospace;'>{$session->session_id}</td>";
                    echo "<td>{$session->visitor_name}</td>";
                    echo "<td>{$session->visitor_ip}</td>";
                    echo "<td>{$session->status}</td>";
                    echo "<td>{$session->created_at}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p style='color:red;'>查询失败: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>
    
    <div class="info">
        <h3>🧪 前台SessionID测试</h3>
        <button onclick="testFrontendSessionId()">获取前台SessionID</button>
        <button onclick="testAPI()">测试API调用</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="frontend-session-info">点击按钮获取前台SessionID</div>
    </div>
    
    <div class="info">
        <h3>📝 调试日志</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logDiv.innerHTML += `<div style="color: ${color};">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 复制前台的sessionId生成逻辑
        function generateDeviceFingerprint() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint', 2, 2);
            
            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                new Date().getTimezoneOffset(),
                canvas.toDataURL()
            ].join('|');
            
            // 简单hash
            let hash = 0;
            for (let i = 0; i < fingerprint.length; i++) {
                const char = fingerprint.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(16);
        }

        function getOrCreatePersistentSessionId() {
            let sessionId = localStorage.getItem('customer_service_session_id');
            
            if (!sessionId) {
                const deviceFingerprint = generateDeviceFingerprint();
                sessionId = 'session_' + deviceFingerprint + '_' + Date.now();
                localStorage.setItem('customer_service_session_id', sessionId);
                localStorage.setItem('device_fingerprint', deviceFingerprint);
                localStorage.setItem('session_created_at', new Date().toISOString());
                log(`🆕 生成新的SessionID: ${sessionId}`, 'success');
            } else {
                log(`📋 使用现有SessionID: ${sessionId}`, 'info');
            }
            
            return sessionId;
        }

        function testFrontendSessionId() {
            log('🔍 测试前台SessionID生成...');
            
            const sessionId = getOrCreatePersistentSessionId();
            const deviceFingerprint = localStorage.getItem('device_fingerprint');
            const createdAt = localStorage.getItem('session_created_at');
            
            log(`📋 SessionID: ${sessionId}`);
            log(`🔑 设备指纹: ${deviceFingerprint}`);
            log(`⏰ 创建时间: ${createdAt}`);
            
            document.getElementById('frontend-session-info').innerHTML = `
                <div style="background: #f0f0f0; padding: 10px; border-radius: 4px; margin-top: 10px;">
                    <strong>SessionID:</strong> <code>${sessionId}</code><br>
                    <strong>设备指纹:</strong> <code>${deviceFingerprint}</code><br>
                    <strong>创建时间:</strong> <code>${createdAt}</code>
                </div>
            `;
            
            // 检查数据库中是否存在这个sessionId
            checkSessionInDatabase(sessionId);
        }

        async function checkSessionInDatabase(sessionId) {
            try {
                log(`🔍 检查数据库中是否存在SessionID: ${sessionId}`);
                
                const response = await fetch(`/test_get_session_id.php?session_id=${sessionId}`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ 数据库中找到匹配的会话: ID=${data.id}`, 'success');
                } else {
                    log(`❌ 数据库中未找到匹配的会话`, 'error');
                    log(`💡 这可能是问题所在！前台sessionId和数据库不匹配`, 'error');
                }
            } catch (error) {
                log(`💥 检查数据库失败: ${error.message}`, 'error');
            }
        }

        async function testAPI() {
            const sessionId = getOrCreatePersistentSessionId();
            
            try {
                log(`🧪 测试API调用: /api/customer-service/messages/${sessionId}`);
                
                const response = await fetch(`/api/customer-service/messages/${sessionId}`);
                const data = await response.json();
                
                log(`📡 API响应状态: ${response.status}`);
                log(`📨 API响应数据: ${JSON.stringify(data)}`);
                
                if (data.success) {
                    log(`✅ API调用成功，返回 ${data.messages.length} 条消息`, 'success');
                } else {
                    log(`❌ API调用失败`, 'error');
                }
            } catch (error) {
                log(`💥 API调用异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            setTimeout(testFrontendSessionId, 1000);
        });
    </script>
</body>
</html>
