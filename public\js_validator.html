<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript语法验证器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
        .code-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .line-number {
            color: #666;
            margin-right: 10px;
            user-select: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JavaScript语法验证器</h1>
        <p>系统性检查和修复JavaScript语法错误</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="validateMainPage()">验证主页面</button>
            <button class="test-btn" onclick="extractAndValidateJS()">提取并验证JS</button>
            <button class="test-btn" onclick="findSyntaxErrors()">查找语法错误</button>
            <button class="test-btn danger" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>📋 JavaScript语法验证器已就绪</h3>
                <p>这个工具将帮助您系统性地检查和修复JavaScript语法错误</p>
            </div>
        </div>
        
        <div class="code-section">
            <h3>🎯 错误定位策略</h3>
            <div>
                <strong>1. 错误信息分析:</strong><br>
                - "missing ) after argument list" 通常表示函数调用缺少右括号<br>
                - 错误行号2677，但文件只有2262行，说明可能是动态生成的代码<br><br>
                
                <strong>2. 可能的原因:</strong><br>
                - 模板字符串中的表达式未正确闭合<br>
                - 函数调用参数列表有语法错误<br>
                - PHP变量插值导致的JavaScript语法错误<br>
                - 三元运算符嵌套问题<br><br>
                
                <strong>3. 检查重点:</strong><br>
                - console.log/error语句<br>
                - fetch函数调用<br>
                - addEventListener回调<br>
                - 模板字符串表达式
            </div>
        </div>
    </div>

    <script>
        let errorLog = [];
        
        // 重写console.error来捕获错误
        const originalConsoleError = console.error;
        console.error = function(...args) {
            errorLog.push({
                type: 'console_error',
                timestamp: new Date().toISOString(),
                message: args.join(' ')
            });
            originalConsoleError.apply(console, args);
        };
        
        // 捕获全局JavaScript错误
        window.addEventListener('error', function(event) {
            errorLog.push({
                type: 'syntax_error',
                timestamp: new Date().toISOString(),
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error ? event.error.toString() : 'Unknown error'
            });
        });

        function updateDisplay(content, isError = false) {
            const display = document.getElementById('result-display');
            const className = isError ? 'error-box' : 'success-box';
            display.innerHTML = `<div class="${className}">${content}</div>`;
        }

        function validateMainPage() {
            updateDisplay('<h3>🔍 正在验证主页面...</h3><p>即将打开主页面进行语法检查</p>');
            
            // 清空错误日志
            errorLog = [];
            
            // 打开主页面
            const mainWindow = window.open('/', '_blank');
            
            setTimeout(() => {
                updateDisplay(`
                    <h3>📖 主页面已打开</h3>
                    <p>请在新标签页中:</p>
                    <ol>
                        <li>打开开发者工具 (F12)</li>
                        <li>查看Console标签页</li>
                        <li>记录任何红色的语法错误信息</li>
                        <li>特别注意 "missing ) after argument list" 错误</li>
                        <li>记录错误的具体行号和内容</li>
                    </ol>
                    <p><strong>提示</strong>: 如果看到语法错误，请复制完整的错误信息。</p>
                `);
            }, 1000);
        }

        function extractAndValidateJS() {
            updateDisplay('<h3>🔍 提取并验证JavaScript代码...</h3>');
            
            // 模拟一些可能有问题的JavaScript模式
            const problematicPatterns = [
                // 1. 模板字符串中的console语句
                {
                    code: 'console.error(`错误: ${error}`, error);',
                    description: 'Console语句带额外参数'
                },
                
                // 2. 复杂的三元运算符
                {
                    code: `const result = field === 'name' ? 'visitor_name' :
                                   field === 'email' ? 'visitor_email' :
                                   field === 'phone' ? 'visitor_phone' : field;`,
                    description: '三元运算符嵌套'
                },
                
                // 3. 模板字符串中的复杂表达式
                {
                    code: '`结果: ${obj && obj.prop ? obj.prop.value : "默认"}`',
                    description: '模板字符串中的条件表达式'
                },
                
                // 4. 函数调用中的模板字符串
                {
                    code: 'alert(`请填写${fieldName}`);',
                    description: 'Alert函数中的模板字符串'
                },
                
                // 5. Fetch调用
                {
                    code: `fetch('/api/test', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    })`,
                    description: 'Fetch API调用'
                }
            ];
            
            let results = '<h3>🔧 JavaScript代码验证结果</h3>';
            let hasErrors = false;
            
            problematicPatterns.forEach((pattern, index) => {
                try {
                    // 使用Function构造函数测试语法
                    new Function('error', 'field', 'obj', 'fieldName', 'data', 'console', 'alert', 'fetch', 'JSON', pattern.code);
                    results += `<div style="color: green; margin: 10px 0;">✅ 模式 ${index + 1}: ${pattern.description} - 语法正确</div>`;
                } catch (e) {
                    hasErrors = true;
                    results += `<div style="color: red; margin: 10px 0;">❌ 模式 ${index + 1}: ${pattern.description} - 语法错误</div>`;
                    results += `<div style="color: red; margin-left: 20px; font-size: 11px;">错误: ${e.message}</div>`;
                    results += `<div class="code-section" style="margin-left: 20px;">${pattern.code}</div>`;
                }
            });
            
            if (!hasErrors) {
                results += `
                    <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <h4 style="color: #155724; margin: 0 0 10px 0;">🎉 所有测试模式语法正确！</h4>
                        <p style="margin: 0; color: #155724;">
                            常见的JavaScript语法模式都通过了验证。
                            如果主页面仍有语法错误，可能是由于其他原因。
                        </p>
                    </div>
                `;
            }
            
            updateDisplay(results, hasErrors);
        }

        function findSyntaxErrors() {
            updateDisplay('<h3>🔍 查找语法错误...</h3>');
            
            setTimeout(() => {
                let errorReport = '<h3>🔧 语法错误分析报告</h3>';
                
                if (errorLog.length > 0) {
                    errorReport += '<h4>📊 捕获到的错误:</h4>';
                    
                    errorLog.forEach((error, index) => {
                        errorReport += `
                            <div style="border: 1px solid #dc3545; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 4px;">
                                <strong>错误 ${index + 1}:</strong><br>
                                <strong>类型:</strong> ${error.type}<br>
                                <strong>时间:</strong> ${error.timestamp}<br>
                                <strong>消息:</strong> ${error.message}<br>
                                ${error.filename ? `<strong>文件:</strong> ${error.filename}<br>` : ''}
                                ${error.lineno ? `<strong>行号:</strong> ${error.lineno}<br>` : ''}
                                ${error.colno ? `<strong>列号:</strong> ${error.colno}<br>` : ''}
                            </div>
                        `;
                    });
                } else {
                    errorReport += '<div style="color: green;">✅ 当前会话中未捕获到JavaScript错误</div>';
                }
                
                errorReport += `
                    <h4>🎯 针对 "missing ) after argument list" 错误的建议:</h4>
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px;">
                        <p><strong>这个错误通常由以下原因导致:</strong></p>
                        <ol>
                            <li><strong>函数调用缺少右括号</strong> - 检查所有函数调用是否有匹配的括号</li>
                            <li><strong>模板字符串表达式未闭合</strong> - 检查 \${} 表达式是否完整</li>
                            <li><strong>三元运算符嵌套错误</strong> - 已修复，但可能还有其他地方</li>
                            <li><strong>字符串中的特殊字符</strong> - 检查是否有未转义的引号或反引号</li>
                        </ol>
                        
                        <p><strong>建议的检查步骤:</strong></p>
                        <ol>
                            <li>在浏览器中按F12打开开发者工具</li>
                            <li>查看Console标签页中的具体错误信息</li>
                            <li>记录错误的确切行号和内容</li>
                            <li>检查该行及其前后几行的代码</li>
                        </ol>
                    </div>
                `;
                
                updateDisplay(errorReport, errorLog.length > 0);
            }, 1000);
        }

        function clearResults() {
            errorLog = [];
            updateDisplay(`
                <div class="success-box">
                    <h3>🧹 结果已清空</h3>
                    <p>错误日志已清空，可以重新开始检查。</p>
                </div>
            `);
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay(`
                <h3>🚀 JavaScript语法验证器已启动</h3>
                <p>准备检查和修复JavaScript语法错误。</p>
                <p><strong>当前问题</strong>: "missing ) after argument list" 在第2677行</p>
                <p><strong>建议</strong>: 点击"验证主页面"开始系统性检查</p>
            `);
        });
    </script>
</body>
</html>
