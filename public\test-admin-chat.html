<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台客服聊天功能测试</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 20px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container { 
            background: white; 
            border-radius: 12px; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1); 
            overflow: hidden;
        }
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        .message.admin {
            justify-content: flex-end;
        }
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
        }
        .message.admin .message-content {
            background: #4caf50;
            color: white;
        }
        .message.customer .message-content {
            background: #e3f2fd;
            color: #1976d2;
        }
        .emoji-picker {
            display: none;
            background: white;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-height: 200px;
            overflow-y: auto;
        }
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 8px;
        }
        .emoji-item {
            cursor: pointer;
            font-size: 20px;
            text-align: center;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s;
        }
        .emoji-item:hover {
            background: #f0f0f0;
        }
        .toolbar {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            align-items: center;
            padding: 0 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.emoji {
            background: #28a745;
        }
        .btn.image {
            background: #ffc107;
            color: #212529;
        }
        .input-area {
            padding: 20px;
            border-top: 1px solid #e6e6e6;
            background: white;
        }
        .input-group {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 24px;
            outline: none;
            font-size: 14px;
            resize: none;
            min-height: 20px;
            max-height: 120px;
        }
        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 24px;
            cursor: pointer;
            font-size: 14px;
        }
        .result {
            margin: 10px 20px;
            padding: 10px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-header">
            <h2>🎯 后台客服聊天功能测试</h2>
            <p>测试表情包和图片发送功能</p>
        </div>
        
        <div class="chat-messages" id="chat-messages">
            <div class="message customer">
                <div class="message-content">
                    您好，我想咨询一下产品信息
                </div>
            </div>
            <div class="message admin">
                <div class="message-content">
                    您好！我是客服，很高兴为您服务 😊
                </div>
            </div>
        </div>
        
        <div class="result" id="result"></div>
        
        <div class="input-area">
            <!-- 表情包选择器 -->
            <div id="emoji-picker" class="emoji-picker">
                <div class="emoji-grid">
                    <span class="emoji-item" onclick="insertEmoji('😀')">😀</span>
                    <span class="emoji-item" onclick="insertEmoji('😃')">😃</span>
                    <span class="emoji-item" onclick="insertEmoji('😄')">😄</span>
                    <span class="emoji-item" onclick="insertEmoji('😁')">😁</span>
                    <span class="emoji-item" onclick="insertEmoji('😊')">😊</span>
                    <span class="emoji-item" onclick="insertEmoji('😍')">😍</span>
                    <span class="emoji-item" onclick="insertEmoji('🥰')">🥰</span>
                    <span class="emoji-item" onclick="insertEmoji('😘')">😘</span>
                    <span class="emoji-item" onclick="insertEmoji('🤗')">🤗</span>
                    <span class="emoji-item" onclick="insertEmoji('🤔')">🤔</span>
                    <span class="emoji-item" onclick="insertEmoji('👍')">👍</span>
                    <span class="emoji-item" onclick="insertEmoji('👎')">👎</span>
                    <span class="emoji-item" onclick="insertEmoji('👌')">👌</span>
                    <span class="emoji-item" onclick="insertEmoji('✌️')">✌️</span>
                    <span class="emoji-item" onclick="insertEmoji('👏')">👏</span>
                    <span class="emoji-item" onclick="insertEmoji('🙌')">🙌</span>
                    <span class="emoji-item" onclick="insertEmoji('🙏')">🙏</span>
                    <span class="emoji-item" onclick="insertEmoji('❤️')">❤️</span>
                    <span class="emoji-item" onclick="insertEmoji('💛')">💛</span>
                    <span class="emoji-item" onclick="insertEmoji('💚')">💚</span>
                    <span class="emoji-item" onclick="insertEmoji('💙')">💙</span>
                    <span class="emoji-item" onclick="insertEmoji('💜')">💜</span>
                    <span class="emoji-item" onclick="insertEmoji('💯')">💯</span>
                    <span class="emoji-item" onclick="insertEmoji('🔥')">🔥</span>
                    <span class="emoji-item" onclick="insertEmoji('⭐')">⭐</span>
                    <span class="emoji-item" onclick="insertEmoji('🌟')">🌟</span>
                    <span class="emoji-item" onclick="insertEmoji('✨')">✨</span>
                    <span class="emoji-item" onclick="insertEmoji('⚡')">⚡</span>
                </div>
            </div>
            
            <!-- 工具栏 -->
            <div class="toolbar">
                <button class="btn emoji" onclick="toggleEmojiPicker()">😀 表情</button>
                <button class="btn image" onclick="selectImage()">📷 图片</button>
                <input type="file" id="image-input" accept="image/*" style="display: none;" onchange="handleImageSelect(event)">
                <div id="upload-progress" style="display: none;">
                    <span style="font-size: 12px; color: #666;">上传中...</span>
                </div>
            </div>
            
            <div class="input-group">
                <textarea class="message-input" id="message-input" placeholder="输入消息..." rows="1"></textarea>
                <button class="send-btn" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 切换表情包选择器
        function toggleEmojiPicker() {
            const emojiPicker = document.getElementById('emoji-picker');
            if (emojiPicker.style.display === 'none' || emojiPicker.style.display === '') {
                emojiPicker.style.display = 'block';
            } else {
                emojiPicker.style.display = 'none';
            }
        }

        // 插入表情包
        function insertEmoji(emoji) {
            const messageInput = document.getElementById('message-input');
            const currentValue = messageInput.value;
            const cursorPosition = messageInput.selectionStart || currentValue.length;
            
            const newValue = currentValue.slice(0, cursorPosition) + emoji + currentValue.slice(cursorPosition);
            messageInput.value = newValue;
            
            // 设置光标位置到表情后面
            const newCursorPosition = cursorPosition + emoji.length;
            messageInput.setSelectionRange(newCursorPosition, newCursorPosition);
            messageInput.focus();
            
            // 隐藏表情选择器
            document.getElementById('emoji-picker').style.display = 'none';
            
            // 自动调整textarea高度
            autoResizeTextarea(messageInput);
        }

        // 选择图片
        function selectImage() {
            document.getElementById('image-input').click();
        }

        // 处理图片选择
        function handleImageSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showResult('error', '请选择图片文件！');
                return;
            }

            // 检查文件大小 (限制为5MB)
            if (file.size > 5 * 1024 * 1024) {
                showResult('error', '图片文件不能超过5MB！');
                return;
            }

            // 显示上传进度
            const progressDiv = document.getElementById('upload-progress');
            const messageInput = document.getElementById('message-input');
            const originalPlaceholder = messageInput.placeholder;
            
            progressDiv.style.display = 'block';
            messageInput.placeholder = '正在上传图片...';
            messageInput.disabled = true;

            // 创建FormData上传图片
            const formData = new FormData();
            formData.append('image', file);
            formData.append('session_id', 'test-session-123');

            fetch('/api/customer-service/upload-image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示图片消息
                    addImageMessage(data.data.image_url, file.name);
                    showResult('success', '图片上传成功！');
                } else {
                    showResult('error', '图片上传失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('图片上传错误:', error);
                showResult('error', '图片上传失败，请稍后重试');
            })
            .finally(() => {
                progressDiv.style.display = 'none';
                messageInput.placeholder = originalPlaceholder;
                messageInput.disabled = false;
                // 清空文件输入
                event.target.value = '';
            });
        }

        // 发送消息
        function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (!message) return;
            
            // 添加消息到聊天区域
            addTextMessage(message);
            
            // 清空输入框
            messageInput.value = '';
            autoResizeTextarea(messageInput);
            
            showResult('success', '消息发送成功！');
        }

        // 添加文本消息
        function addTextMessage(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message admin';
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${escapeHtml(message)}
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 添加图片消息
        function addImageMessage(imageUrl, fileName) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message admin';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-image">
                        <img src="${imageUrl}" 
                             alt="图片消息" 
                             style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" 
                             onclick="window.open('${imageUrl}', '_blank')">
                        <div style="font-size: 12px; color: rgba(255,255,255,0.8); margin-top: 4px;">[图片] ${fileName}</div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 自动调整textarea高度
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 显示结果
        function showResult(type, message) {
            const result = document.getElementById('result');
            result.className = `result ${type}`;
            result.textContent = message;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('message-input');
            
            // 自动调整高度
            messageInput.addEventListener('input', function() {
                autoResizeTextarea(this);
            });
            
            // Enter键发送消息
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>
