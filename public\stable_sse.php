<?php
/**
 * 稳定的SSE实现
 */

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 禁用输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '稳定SSE连接成功',
    'session_id' => $sessionId,
    'last_message_id' => $lastMessageId,
    'timestamp' => time(),
    'php_version' => PHP_VERSION
]) . "\n\n";
flush();

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

$pdo = null;
$sessionDbId = null;

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 5
    ]);
    
    echo "data: " . json_encode([
        'type' => 'database_connected',
        'message' => '数据库连接成功',
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    // 获取或创建会话
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        // 创建新会话
        try {
            $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
            $stmt->execute([
                $sessionId, 
                $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', 
                $_SERVER['HTTP_USER_AGENT'] ?? 'SSE Client'
            ]);
            $sessionDbId = $pdo->lastInsertId();
            
            echo "data: " . json_encode([
                'type' => 'session_created',
                'message' => '新会话已创建',
                'session_db_id' => $sessionDbId,
                'timestamp' => time()
            ]) . "\n\n";
            flush();
            
        } catch (Exception $e) {
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => '创建会话失败: ' . $e->getMessage(),
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
    } else {
        $sessionDbId = $session['id'];
        
        echo "data: " . json_encode([
            'type' => 'session_found',
            'message' => '找到现有会话',
            'session_db_id' => $sessionDbId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'database_error',
        'message' => '数据库连接失败: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
}

// 主循环 - 无论数据库是否连接都要保持
$checkInterval = 2; // 2秒检查一次
$maxDuration = 300; // 最大连接时间5分钟
$startTime = time();
$heartbeatCounter = 0;

echo "data: " . json_encode([
    'type' => 'loop_start',
    'message' => '开始消息检查循环',
    'session_db_id' => $sessionDbId,
    'max_duration' => $maxDuration,
    'timestamp' => time()
]) . "\n\n";
flush();

while (time() - $startTime < $maxDuration) {
    try {
        $heartbeatCounter++;
        
        // 如果有数据库连接和会话，检查新消息
        if ($pdo && $sessionDbId) {
            try {
                $stmt = $pdo->prepare("
                    SELECT * FROM st_customer_service_messages 
                    WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') 
                    ORDER BY id ASC
                ");
                $stmt->execute([$sessionDbId, $lastMessageId]);
                $newMessages = $stmt->fetchAll();
                
                if (!empty($newMessages)) {
                    echo "data: " . json_encode([
                        'type' => 'debug',
                        'message' => '🎉 检测到 ' . count($newMessages) . ' 条新消息！',
                        'count' => count($newMessages),
                        'timestamp' => time()
                    ]) . "\n\n";
                    flush();
                    
                    foreach ($newMessages as $message) {
                        echo "data: " . json_encode([
                            'type' => 'new_reply',
                            'message_id' => $message['id'],
                            'sender_type' => $message['sender_type'],
                            'message' => $message['message'],
                            'created_at' => $message['created_at'],
                            'timestamp' => time()
                        ]) . "\n\n";
                        
                        $lastMessageId = max($lastMessageId, $message['id']);
                    }
                    flush();
                }
                
            } catch (Exception $e) {
                echo "data: " . json_encode([
                    'type' => 'query_error',
                    'message' => '查询消息失败: ' . $e->getMessage(),
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
            }
        }
        
        // 每10秒发送心跳包
        if ($heartbeatCounter % 5 == 0) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'message' => '💓 心跳包 #' . ($heartbeatCounter / 5),
                'session_db_id' => $sessionDbId,
                'last_message_id' => $lastMessageId,
                'connection_time' => time() - $startTime,
                'memory_usage' => memory_get_usage(true),
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 每30秒发送详细状态
        if ($heartbeatCounter % 15 == 0 && $pdo && $sessionDbId) {
            try {
                $totalMessages = $pdo->prepare("SELECT COUNT(*) FROM st_customer_service_messages WHERE session_id = ?");
                $totalMessages->execute([$sessionDbId]);
                $messageCount = $totalMessages->fetchColumn();
                
                echo "data: " . json_encode([
                    'type' => 'status',
                    'message' => '📊 连接状态正常',
                    'session_db_id' => $sessionDbId,
                    'total_messages' => $messageCount,
                    'last_message_id' => $lastMessageId,
                    'connection_time' => time() - $startTime,
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
                
            } catch (Exception $e) {
                echo "data: " . json_encode([
                    'type' => 'status_error',
                    'message' => '获取状态失败: ' . $e->getMessage(),
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
            }
        }
        
        // 检查连接是否还活着
        if (connection_aborted()) {
            echo "data: " . json_encode([
                'type' => 'connection_aborted',
                'message' => '客户端连接已断开',
                'timestamp' => time()
            ]) . "\n\n";
            flush();
            break;
        }
        
        sleep($checkInterval);
        
    } catch (Exception $e) {
        echo "data: " . json_encode([
            'type' => 'loop_error',
            'message' => '循环错误: ' . $e->getMessage(),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        
        // 出错后等待一下再继续
        sleep($checkInterval);
    }
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '稳定SSE连接正常结束',
    'connection_time' => time() - $startTime,
    'timestamp' => time()
]) . "\n\n";
flush();
?>
