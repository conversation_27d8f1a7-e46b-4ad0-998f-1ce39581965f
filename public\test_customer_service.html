<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统语法测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服系统语法测试</h1>
        <p>测试客服系统相关的JavaScript代码</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="testCustomerServiceInit()">测试客服初始化</button>
            <button class="test-btn" onclick="testMessageHandling()">测试消息处理</button>
            <button class="test-btn" onclick="testFormValidation()">测试表单验证</button>
        </div>
        
        <div id="result-display">
            <div class="result-box">
                <h3>📋 客服系统语法测试已就绪</h3>
                <p>点击上面的按钮开始测试客服系统的各个组件</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟客服系统的全局变量
        let sessionId = 'test_session_123';
        let csSettings = {
            system_enabled: true,
            welcome_message: '您好！欢迎咨询',
            offline_form_required: ['message']
        };
        let isOpen = false;
        let heartbeatInterval = null;

        function updateDisplay(content) {
            const display = document.getElementById('result-display');
            display.innerHTML = `<div class="result-box">${content}</div>`;
        }

        function testCustomerServiceInit() {
            updateDisplay('<h3>🔍 测试客服系统初始化...</h3>');
            
            let results = '<h3>✅ 客服初始化测试结果</h3>';
            let hasErrors = false;
            
            // 测试1: 会话ID生成
            try {
                function getOrCreatePersistentSessionId() {
                    let sessionId = localStorage.getItem('customer_service_session_id');
                    if (!sessionId) {
                        sessionId = 'session_' + Date.now();
                        localStorage.setItem('customer_service_session_id', sessionId);
                    }
                    return sessionId;
                }
                
                const testSessionId = getOrCreatePersistentSessionId();
                results += `<div style="color: green;">✅ 会话ID生成: 成功 (${testSessionId})</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ 会话ID生成: 失败 - ${e.message}</div>`;
            }
            
            // 测试2: 设置加载
            try {
                async function loadCustomerServiceSettings() {
                    const response = await fetch('/api/test-settings.php');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    const data = await response.json();
                    if (data.success) {
                        return data.data;
                    } else {
                        throw new Error(data.error || data.message);
                    }
                }
                
                // 只测试语法，不实际执行
                results += `<div style="color: green;">✅ 设置加载函数: 语法正确</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ 设置加载函数: 失败 - ${e.message}</div>`;
            }
            
            // 测试3: UI应用
            try {
                function applySettingsToUI() {
                    const chatWidget = document.getElementById('customer-service-widget');
                    const chatWindow = document.getElementById('cs-chat-window');
                    const chatButton = document.getElementById('cs-chat-button');
                    
                    if (!chatWidget) {
                        console.warn('⚠️ 找不到客服组件');
                        return;
                    }
                    
                    if (csSettings.chat_button_color && chatButton) {
                        chatButton.style.background = csSettings.chat_button_color;
                    }
                }
                
                applySettingsToUI();
                results += `<div style="color: green;">✅ UI设置应用: 成功</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ UI设置应用: 失败 - ${e.message}</div>`;
            }
            
            updateDisplay(results);
        }

        function testMessageHandling() {
            updateDisplay('<h3>🔍 测试消息处理...</h3>');
            
            let results = '<h3>✅ 消息处理测试结果</h3>';
            let hasErrors = false;
            
            // 测试1: 消息添加
            try {
                function addMessage(sender, message) {
                    const messageDiv = document.createElement('div');
                    messageDiv.style.marginBottom = '15px';
                    
                    const time = new Date().toLocaleTimeString('zh-CN', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                    });
                    
                    if (sender === 'customer') {
                        messageDiv.innerHTML = `
                            <div style="text-align: right;">
                                <div style="background: #007bff; color: white; padding: 10px; border-radius: 10px; display: inline-block;">
                                    ${message}
                                </div>
                                <div style="font-size: 11px; color: #666; margin-top: 4px;">${time}</div>
                            </div>
                        `;
                    } else {
                        messageDiv.innerHTML = `
                            <div style="text-align: left;">
                                <div style="background: #f1f1f1; color: #333; padding: 10px; border-radius: 10px; display: inline-block;">
                                    ${message}
                                </div>
                                <div style="font-size: 11px; color: #666; margin-top: 4px;">${time}</div>
                            </div>
                        `;
                    }
                    
                    return messageDiv;
                }
                
                const testMessage = addMessage('admin', '这是一条测试消息');
                results += `<div style="color: green;">✅ 消息添加: 成功</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ 消息添加: 失败 - ${e.message}</div>`;
            }
            
            // 测试2: 轮询消息接收
            try {
                function startRealtimeMessageReceiver() {
                    if (!sessionId) {
                        console.error('❌ sessionId为空，无法启动轮询');
                        return;
                    }
                    
                    if (window.customerServicePolling) {
                        console.log('⚠️ 轮询已在运行，跳过重复启动');
                        return;
                    }
                    
                    window.customerServicePolling = true;
                    let lastMessageId = 0;
                    
                    const pollInterval = setInterval(() => {
                        const apiUrl = `/api/customer-service/messages/${sessionId}?last_id=${lastMessageId}`;
                        
                        fetch(apiUrl)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success && data.messages && data.messages.length > 0) {
                                    console.log(`📨 收到 ${data.messages.length} 条新消息`);
                                    
                                    data.messages.forEach(message => {
                                        if (message.sender_type === 'admin' && message.id > lastMessageId) {
                                            console.log('💬 处理管理员消息:', message.message);
                                            lastMessageId = message.id;
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('轮询失败:', error);
                            });
                    }, 3000);
                    
                    window.customerServicePollingInterval = pollInterval;
                }
                
                // 只测试语法，不实际执行
                results += `<div style="color: green;">✅ 轮询消息接收: 语法正确</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ 轮询消息接收: 失败 - ${e.message}</div>`;
            }
            
            updateDisplay(results);
        }

        function testFormValidation() {
            updateDisplay('<h3>🔍 测试表单验证...</h3>');
            
            let results = '<h3>✅ 表单验证测试结果</h3>';
            let hasErrors = false;
            
            // 测试1: 字段映射（这是之前有问题的地方）
            try {
                const required = ['name', 'email', 'message'];
                
                for (const field of required) {
                    const fieldKey = field === 'name' ? 'visitor_name' :
                                   field === 'email' ? 'visitor_email' :
                                   field === 'phone' ? 'visitor_phone' :
                                   field === 'whatsapp' ? 'visitor_whatsapp' : field;
                    
                    const fieldName = field === 'name' ? '姓名' :
                                    field === 'email' ? '邮箱' :
                                    field === 'phone' ? '电话' :
                                    field === 'whatsapp' ? 'WhatsApp' : '留言内容';
                    
                    // 模拟验证逻辑
                    const testData = { visitor_name: '测试', visitor_email: '<EMAIL>', message: '测试消息' };
                    
                    if (!testData[fieldKey]) {
                        const alertMessage = `请填写${fieldName}`;
                        console.log('验证失败:', alertMessage);
                    }
                }
                
                results += `<div style="color: green;">✅ 字段映射和验证: 成功</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ 字段映射和验证: 失败 - ${e.message}</div>`;
            }
            
            // 测试2: 表单提交
            try {
                async function submitOfflineMessage(data) {
                    const response = await fetch('/api/customer-service/submit-offline-message.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        console.log('提交成功:', result.auto_reply || '留言提交成功');
                    } else {
                        console.error('提交失败:', result.message || '提交失败');
                    }
                }
                
                // 只测试语法，不实际执行
                results += `<div style="color: green;">✅ 表单提交: 语法正确</div>`;
            } catch (e) {
                hasErrors = true;
                results += `<div style="color: red;">❌ 表单提交: 失败 - ${e.message}</div>`;
            }
            
            if (!hasErrors) {
                results += `
                    <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin-top: 15px; color: #155724;">
                        <h4>🎉 所有客服系统测试通过！</h4>
                        <p>客服系统的核心JavaScript代码语法都是正确的。</p>
                        <p>如果主页面仍有语法错误，可能需要检查:</p>
                        <ul>
                            <li>其他脚本块的代码</li>
                            <li>动态生成的内容</li>
                            <li>浏览器缓存问题</li>
                        </ul>
                    </div>
                `;
            }
            
            updateDisplay(results);
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay(`
                <h3>🚀 客服系统语法测试已启动</h3>
                <p>这个页面将测试客服系统相关的JavaScript代码。</p>
                <p><strong>重点</strong>: 测试之前修复的三元运算符和其他可能有问题的代码</p>
                <p><strong>目标</strong>: 确认客服系统代码本身没有语法错误</p>
            `);
        });
    </script>
</body>
</html>
