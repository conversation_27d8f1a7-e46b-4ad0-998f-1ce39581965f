<?php
/**
 * 管理员回复测试工具
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>管理员回复测试</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;} input,select{padding:8px;margin:5px;border:1px solid #ddd;border-radius:4px;} .panel{background:#f8f9fa;padding:15px;margin:10px 0;border-radius:8px;border:1px solid #ddd;}</style>";
echo "</head><body>";

echo "<h1>👨‍💼 管理员回复测试工具</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 处理回复操作
    if ($_POST['action'] ?? '' === 'send_reply') {
        $sessionDbId = $_POST['session_db_id'] ?? '';
        $replyMessage = $_POST['reply_message'] ?? '';
        
        if ($sessionDbId && $replyMessage) {
            try {
                $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, sender_id, message, is_read, created_at, updated_at) VALUES (?, 'admin', 1, ?, 0, NOW(), NOW())");
                $stmt->execute([$sessionDbId, $replyMessage]);
                $messageId = $pdo->lastInsertId();
                
                echo "<div class='panel'>";
                echo "<p class='success'>✅ 管理员回复发送成功！</p>";
                echo "<p class='info'>消息ID: $messageId</p>";
                echo "<p class='info'>会话ID: $sessionDbId</p>";
                echo "<p class='info'>回复内容: " . htmlspecialchars($replyMessage) . "</p>";
                echo "<p class='info'>发送时间: " . date('Y-m-d H:i:s') . "</p>";
                echo "</div>";
                
                // 更新会话活动时间
                $stmt = $pdo->prepare("UPDATE st_customer_service_sessions SET last_activity = NOW(), updated_at = NOW() WHERE id = ?");
                $stmt->execute([$sessionDbId]);
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ 发送失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ 请填写完整信息</p>";
        }
    }
    
    // 获取活跃会话
    $sessions = $pdo->query("
        SELECT s.*, 
               (SELECT COUNT(*) FROM st_customer_service_messages m WHERE m.session_id = s.id) as message_count,
               (SELECT m.message FROM st_customer_service_messages m WHERE m.session_id = s.id ORDER BY m.created_at DESC LIMIT 1) as latest_message
        FROM st_customer_service_sessions s 
        WHERE s.status = 'active' 
        ORDER BY s.last_activity DESC 
        LIMIT 10
    ")->fetchAll();
    
    echo "<h2>📋 活跃会话列表</h2>";
    if (empty($sessions)) {
        echo "<p class='info'>暂无活跃会话</p>";
    } else {
        echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
        echo "<tr><th>会话ID</th><th>会话标识</th><th>消息数</th><th>最新消息</th><th>最后活动</th><th>操作</th></tr>";
        foreach ($sessions as $session) {
            $latestMessage = $session['latest_message'] ? substr($session['latest_message'], 0, 30) . '...' : '无消息';
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>" . substr($session['session_id'], 0, 20) . "...</td>";
            echo "<td>{$session['message_count']}</td>";
            echo "<td>" . htmlspecialchars($latestMessage) . "</td>";
            echo "<td>{$session['last_activity']}</td>";
            echo "<td><button class='test-btn' onclick=\"selectSession({$session['id']}, '{$session['session_id']}')\">选择</button></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<h2>💬 发送管理员回复</h2>";
echo "<div class='panel'>";
echo "<form method='POST'>";
echo "<input type='hidden' name='action' value='send_reply'>";
echo "<div>";
echo "<label>会话数据库ID: <input type='number' name='session_db_id' id='session_db_id' required></label>";
echo "</div>";
echo "<div>";
echo "<label>回复消息: <input type='text' name='reply_message' placeholder='输入管理员回复...' style='width:400px;' required></label>";
echo "</div>";
echo "<div>";
echo "<button type='submit' class='test-btn'>发送回复</button>";
echo "<button type='button' class='test-btn' onclick=\"sendQuickReply()\">快速回复</button>";
echo "</div>";
echo "</form>";
echo "</div>";

echo "<h2>🔗 测试链接</h2>";
echo "<ul>";
echo "<li><a href='/working_chat.html' target='_blank'>工作的客服系统</a></li>";
echo "<li><a href='/' target='_blank'>前台首页</a></li>";
echo "<li><a href='/test_sse.php?session_id=test123&last_message_id=0' target='_blank'>测试SSE</a></li>";
echo "</ul>";

echo "<script>
function selectSession(sessionDbId, sessionId) {
    document.getElementById('session_db_id').value = sessionDbId;
    alert('已选择会话: ' + sessionId + ' (数据库ID: ' + sessionDbId + ')');
}

function sendQuickReply() {
    const sessionDbId = document.getElementById('session_db_id').value;
    if (!sessionDbId) {
        alert('请先选择一个会话');
        return;
    }
    
    const quickReplies = [
        '您好，我是客服，有什么可以帮助您的吗？',
        '感谢您的咨询，我正在为您查询相关信息...',
        '好的，我明白了您的问题，让我为您处理一下。',
        '请稍等，我正在联系相关部门为您解决。',
        '您的问题我已经记录，会有专人跟进处理。'
    ];
    
    const randomReply = quickReplies[Math.floor(Math.random() * quickReplies.length)];
    document.querySelector('input[name=\"reply_message\"]').value = randomReply + ' [' + new Date().toLocaleTimeString() + ']';
}
</script>";

echo "</body></html>";
?>
