<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确定位第2677行</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .line-highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .code-line {
            margin: 2px 0;
            padding: 3px 8px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }
        .target-line {
            background: #ffcdd2;
            border-left: 4px solid #f44336;
        }
        .context-line {
            background: #f5f5f5;
            border-left: 4px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 精确定位第2677行</h1>
        <p>专门用于找到导致语法错误的第2677行内容</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="findExactLine()">精确定位第2677行</button>
            <button class="test-btn" onclick="analyzeScriptBlocks()">分析脚本块</button>
            <button class="test-btn" onclick="testSyntaxPatterns()">测试语法模式</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>📋 第2677行定位工具已就绪</h3>
                <p>点击"精确定位第2677行"开始查找错误位置</p>
            </div>
        </div>
    </div>

    <script>
        function updateDisplay(content, type = 'success') {
            const display = document.getElementById('result-display');
            const className = type === 'error' ? 'error-box' : 
                            type === 'warning' ? 'warning-box' : 'success-box';
            display.innerHTML = `<div class="${className}">${content}</div>`;
        }

        async function findExactLine() {
            updateDisplay('<h3>🔍 正在精确定位第2677行...</h3>', 'warning');
            
            try {
                const response = await fetch('/');
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                
                const pageContent = await response.text();
                const allLines = pageContent.split('\n');
                
                let result = '<h3>🎯 第2677行精确定位结果</h3>';
                result += '<p><strong>页面总行数:</strong> ' + allLines.length + '</p>';
                
                if (allLines.length >= 2677) {
                    const targetLineIndex = 2676; // 数组索引从0开始
                    const targetLine = allLines[targetLineIndex];
                    
                    result += '<h4>🔍 第2677行内容:</h4>';
                    result += '<div class="code-line target-line">';
                    result += '<strong>行 2677:</strong> ' + escapeHtml(targetLine);
                    result += '</div>';
                    
                    // 显示周围的上下文
                    result += '<h4>📋 上下文 (第2667-2687行):</h4>';
                    
                    for (let i = Math.max(0, targetLineIndex - 10); i <= Math.min(allLines.length - 1, targetLineIndex + 10); i++) {
                        const lineNum = i + 1;
                        const line = allLines[i] || '';
                        const isTarget = lineNum === 2677;
                        
                        result += '<div class="code-line ' + (isTarget ? 'target-line' : 'context-line') + '">';
                        result += '<strong>行 ' + lineNum + ':</strong> ' + escapeHtml(line.substring(0, 150));
                        if (line.length > 150) result += '...';
                        result += '</div>';
                    }
                    
                    // 分析第2677行的内容
                    result += '<h4>🔬 第2677行详细分析:</h4>';
                    result += '<div style="background: #e3f2fd; padding: 15px; border-radius: 4px;">';
                    result += '<p><strong>完整内容:</strong></p>';
                    result += '<pre style="background: white; padding: 10px; border-radius: 3px; overflow-x: auto;">' + escapeHtml(targetLine) + '</pre>';
                    result += '<p><strong>长度:</strong> ' + targetLine.length + ' 字符</p>';
                    result += '<p><strong>包含左括号:</strong> ' + (targetLine.match(/\(/g) || []).length + ' 个</p>';
                    result += '<p><strong>包含右括号:</strong> ' + (targetLine.match(/\)/g) || []).length + ' 个</p>';
                    result += '<p><strong>包含script标签:</strong> ' + (targetLine.includes('<script') || targetLine.includes('</script>') ? '是' : '否') + '</p>';
                    
                    // 检查常见的语法错误模式
                    const errorPatterns = [
                        { pattern: /console\.(log|error|warn)\([^)]*$/, name: '未闭合的console调用' },
                        { pattern: /alert\([^)]*$/, name: '未闭合的alert调用' },
                        { pattern: /fetch\([^)]*$/, name: '未闭合的fetch调用' },
                        { pattern: /document\.(querySelector|getElementById)\([^)]*$/, name: '未闭合的DOM查询' },
                        { pattern: /JSON\.(stringify|parse)\([^)]*$/, name: '未闭合的JSON调用' },
                        { pattern: /`[^`]*\$\{[^}]*$/, name: '未闭合的模板字符串' },
                        { pattern: /@json\([^)]*$/, name: '未闭合的@json调用' }
                    ];
                    
                    let foundPatterns = [];
                    errorPatterns.forEach(ep => {
                        if (ep.pattern.test(targetLine)) {
                            foundPatterns.push(ep.name);
                        }
                    });
                    
                    if (foundPatterns.length > 0) {
                        result += '<p><strong style="color: #d32f2f;">检测到的问题模式:</strong></p>';
                        result += '<ul>';
                        foundPatterns.forEach(pattern => {
                            result += '<li style="color: #d32f2f;">' + pattern + '</li>';
                        });
                        result += '</ul>';
                    } else {
                        result += '<p><strong style="color: #388e3c;">未检测到明显的语法错误模式</strong></p>';
                    }
                    
                    result += '</div>';
                    
                } else {
                    result += '<div style="background: #ffebee; padding: 15px; border-radius: 4px;">';
                    result += '<h4>❌ 第2677行不存在</h4>';
                    result += '<p>页面只有 ' + allLines.length + ' 行，但错误报告在第2677行。</p>';
                    result += '<p>这表明错误可能来自:</p>';
                    result += '<ul>';
                    result += '<li>动态生成的JavaScript代码</li>';
                    result += '<li>外部脚本文件</li>';
                    result += '<li>浏览器内部处理过程中的行号计算</li>';
                    result += '</ul>';
                    result += '</div>';
                }
                
                updateDisplay(result, allLines.length >= 2677 ? 'warning' : 'error');
                
            } catch (error) {
                updateDisplay('<h3>❌ 定位过程出错</h3><p><strong>错误:</strong> ' + error.message + '</p>', 'error');
            }
        }

        async function analyzeScriptBlocks() {
            updateDisplay('<h3>🔍 分析脚本块...</h3>', 'warning');
            
            try {
                const response = await fetch('/');
                const pageContent = await response.text();
                
                // 提取所有script标签
                const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/gi;
                const scripts = [];
                let match;
                let totalLines = 0;
                
                while ((match = scriptRegex.exec(pageContent)) !== null) {
                    const scriptContent = match[1].trim();
                    if (scriptContent) {
                        const lines = scriptContent.split('\n');
                        scripts.push({
                            index: scripts.length + 1,
                            content: scriptContent,
                            lines: lines,
                            startLine: totalLines + 1,
                            endLine: totalLines + lines.length
                        });
                        totalLines += lines.length;
                    }
                }
                
                let result = '<h3>📊 脚本块分析结果</h3>';
                result += '<p><strong>脚本块数量:</strong> ' + scripts.length + '</p>';
                result += '<p><strong>JavaScript总行数:</strong> ' + totalLines + '</p>';
                result += '<p><strong>错误行号:</strong> 2677</p>';
                
                // 找到包含第2677行的脚本块
                let targetScript = null;
                let targetLineInScript = 0;
                
                for (const script of scripts) {
                    if (2677 >= script.startLine && 2677 <= script.endLine) {
                        targetScript = script;
                        targetLineInScript = 2677 - script.startLine + 1;
                        break;
                    }
                }
                
                if (targetScript) {
                    result += '<div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0;">';
                    result += '<h4>🎯 找到目标脚本块</h4>';
                    result += '<p><strong>脚本块编号:</strong> ' + targetScript.index + '</p>';
                    result += '<p><strong>脚本内行号:</strong> ' + targetLineInScript + '</p>';
                    result += '<p><strong>全局行号范围:</strong> ' + targetScript.startLine + ' - ' + targetScript.endLine + '</p>';
                    result += '</div>';
                    
                    // 显示目标行及其周围的代码
                    const startCheck = Math.max(0, targetLineInScript - 5);
                    const endCheck = Math.min(targetScript.lines.length, targetLineInScript + 5);
                    
                    result += '<h4>🔍 目标行周围的代码:</h4>';
                    
                    for (let i = startCheck; i < endCheck; i++) {
                        const line = targetScript.lines[i];
                        const lineNum = i + 1;
                        const globalLineNum = targetScript.startLine + i;
                        const isTarget = globalLineNum === 2677;
                        
                        result += '<div class="code-line ' + (isTarget ? 'target-line' : 'context-line') + '">';
                        result += '<strong>脚本行 ' + lineNum + ' (全局 ' + globalLineNum + '):</strong> ';
                        result += escapeHtml(line.substring(0, 100));
                        if (line.length > 100) result += '...';
                        result += '</div>';
                    }
                } else {
                    result += '<div style="background: #ffebee; padding: 15px; border-radius: 4px;">';
                    result += '<h4>❌ 未在脚本块中找到第2677行</h4>';
                    result += '<p>JavaScript总行数: ' + totalLines + '</p>';
                    result += '<p>这可能表示错误来自页面的HTML部分或外部脚本。</p>';
                    result += '</div>';
                }
                
                updateDisplay(result, targetScript ? 'warning' : 'error');
                
            } catch (error) {
                updateDisplay('<h3>❌ 分析过程出错</h3><p><strong>错误:</strong> ' + error.message + '</p>', 'error');
            }
        }

        function testSyntaxPatterns() {
            updateDisplay('<h3>🧪 测试常见语法错误模式...</h3>', 'warning');
            
            const problematicCodes = [
                'console.log("test"',
                'alert("message"',
                'fetch("/api/test"',
                'document.getElementById("id"',
                'JSON.stringify(data',
                'new Date(',
                'setTimeout(function() {',
                'addEventListener("click"',
                'const data = @json(',
                'console.error(`错误: ${error}`, error',
                'alert(`请填写${fieldName}`',
                'fetch(`/api/test/${id}`'
            ];
            
            let result = '<h3>🧪 语法错误模式测试</h3>';
            result += '<p>测试常见的导致 "missing ) after argument list" 错误的代码:</p>';
            
            problematicCodes.forEach((code, index) => {
                try {
                    new Function(code);
                    result += '<div style="color: orange; margin: 5px 0;">⚠️ 测试 ' + (index + 1) + ': ' + code + ' - 意外通过</div>';
                } catch (error) {
                    const isMissingParen = error.message.includes('missing') && error.message.includes('after argument list');
                    result += '<div style="color: ' + (isMissingParen ? 'red' : 'blue') + '; margin: 5px 0;">';
                    result += (isMissingParen ? '❌' : '🔵') + ' 测试 ' + (index + 1) + ': ' + code + ' - ' + error.message;
                    result += '</div>';
                }
            });
            
            updateDisplay(result);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay('<h3>🚀 第2677行定位工具已启动</h3><p>准备精确定位导致语法错误的第2677行内容。</p>');
        });
    </script>
</body>
</html>
