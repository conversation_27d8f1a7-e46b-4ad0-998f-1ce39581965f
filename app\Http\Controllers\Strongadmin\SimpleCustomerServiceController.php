<?php

namespace App\Http\Controllers\Strongadmin;

use App\Http\Controllers\Strongadmin\BaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SimpleCustomerServiceController extends BaseController
{
    // 会话管理
    public function sessions(Request $request)
    {
        try {
            // 获取会话列表 - 使用分页
            $query = DB::table('customer_service_sessions')
                      ->orderBy('last_activity', 'desc');

            // 手动创建分页
            $page = $request->get('page', 1);
            $perPage = 20;
            $total = $query->count();
            $sessions = $query->offset(($page - 1) * $perPage)->limit($perPage)->get();

            // 为每个会话添加额外信息
            foreach ($sessions as $session) {
                // 改进访客命名：如果没有提供名字，使用"访客-随机数字"
                if (empty($session->visitor_name)) {
                    $randomNum = substr(md5($session->session_id), 0, 6);
                    $session->display_name = '访客-' . strtoupper($randomNum);
                } else {
                    $session->display_name = $session->visitor_name;
                }

                $session->contact_info = $session->visitor_email ?: $session->visitor_ip;

                // 获取最新消息
                $latestMessage = DB::table('customer_service_messages')
                                  ->where('session_id', $session->id)
                                  ->orderBy('created_at', 'desc')
                                  ->first();
                $session->latest_message = $latestMessage ? $latestMessage->message : '暂无消息';
                $session->latest_message_time = $latestMessage ? $latestMessage->created_at : null;
            }

            // 创建分页对象
            $sessions = new \Illuminate\Pagination\LengthAwarePaginator(
                $sessions,
                $total,
                $perPage,
                $page,
                ['path' => $request->url(), 'pageName' => 'page']
            );

            // 统计数据
            $stats = [
                'total' => DB::table('customer_service_sessions')->count(),
                'active' => DB::table('customer_service_sessions')->where('status', 'active')->count(),
                'waiting' => DB::table('customer_service_sessions')->where('status', 'waiting')->count(),
                'closed' => DB::table('customer_service_sessions')->where('status', 'closed')->count(),
            ];

            return $this->view('customer-service.sessions', compact('sessions', 'stats'));
        } catch (\Exception $e) {
            // 创建空的分页对象
            $sessions = new \Illuminate\Pagination\LengthAwarePaginator(
                collect([]),
                0,
                20,
                1,
                ['path' => $request->url(), 'pageName' => 'page']
            );

            return $this->view('customer-service.sessions', [
                'sessions' => $sessions,
                'stats' => ['total' => 0, 'active' => 0, 'waiting' => 0, 'closed' => 0]
            ]);
        }
    }

    // AI规则管理
    public function aiRules(Request $request)
    {
        try {
            $rules = DB::table('ai_auto_reply_rules')
                      ->orderBy('priority', 'desc')
                      ->get();

            // 解析keywords JSON
            foreach ($rules as $rule) {
                $rule->keywords = json_decode($rule->keywords, true) ?: [];
            }

            return $this->view('customer-service.ai-rules', compact('rules'));
        } catch (\Exception $e) {
            return $this->view('customer-service.ai-rules', ['rules' => collect([])]);
        }
    }

    // 创建AI规则
    public function createAiRule(Request $request)
    {
        if ($request->isMethod('POST')) {
            try {
                $keywords = array_map('trim', explode(',', $request->keywords));
                
                DB::table('ai_auto_reply_rules')->insert([
                    'name' => $request->name,
                    'keywords' => json_encode($keywords),
                    'reply_message' => $request->reply_message,
                    'priority' => $request->priority ?: 5,
                    'is_active' => $request->is_active ?: 1,
                    'usage_count' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                return redirect()->back()->with('success', 'AI规则创建成功');
            } catch (\Exception $e) {
                return redirect()->back()->with('error', '创建失败：' . $e->getMessage());
            }
        }

        return $this->view('customer-service.ai-rule-form');
    }

    // 编辑AI规则
    public function editAiRule($id, Request $request)
    {
        try {
            $rule = DB::table('ai_auto_reply_rules')->where('id', $id)->first();
            
            if (!$rule) {
                return redirect()->back()->with('error', '规则不存在');
            }

            if ($request->isMethod('POST')) {
                $keywords = array_map('trim', explode(',', $request->keywords));
                
                DB::table('ai_auto_reply_rules')->where('id', $id)->update([
                    'name' => $request->name,
                    'keywords' => json_encode($keywords),
                    'reply_message' => $request->reply_message,
                    'priority' => $request->priority ?: 5,
                    'is_active' => $request->is_active ?: 1,
                    'updated_at' => now(),
                ]);

                return redirect()->back()->with('success', 'AI规则更新成功');
            }

            $rule->keywords = json_decode($rule->keywords, true) ?: [];
            return $this->view('customer-service.ai-rule-form', compact('rule'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', '操作失败：' . $e->getMessage());
        }
    }

    // 删除AI规则
    public function deleteAiRule($id)
    {
        try {
            DB::table('ai_auto_reply_rules')->where('id', $id)->delete();
            return redirect()->back()->with('success', 'AI规则删除成功');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', '删除失败：' . $e->getMessage());
        }
    }

    // 统计报表
    public function statistics(Request $request)
    {
        try {
            // 基础统计
            $stats = [
                'total_sessions' => DB::table('customer_service_sessions')->count(),
                'active_sessions' => DB::table('customer_service_sessions')->where('status', 'active')->count(),
                'total_messages' => DB::table('customer_service_messages')->count(),
                'ai_messages' => DB::table('customer_service_messages')->where('sender_type', 'ai')->count(),
                'customer_messages' => DB::table('customer_service_messages')->where('sender_type', 'customer')->count(),
                'admin_messages' => DB::table('customer_service_messages')->where('sender_type', 'admin')->count(),
            ];

            // 最近会话
            $recentSessions = DB::table('customer_service_sessions')
                               ->orderBy('last_activity', 'desc')
                               ->limit(10)
                               ->get();

            // 热门AI规则
            $popularRules = DB::table('ai_auto_reply_rules')
                             ->where('is_active', 1)
                             ->orderBy('usage_count', 'desc')
                             ->limit(5)
                             ->get();

            foreach ($popularRules as $rule) {
                $rule->keywords = json_decode($rule->keywords, true) ?: [];
            }

            return $this->view('customer-service.statistics', compact('stats', 'recentSessions', 'popularRules'));
        } catch (\Exception $e) {
            return $this->view('customer-service.statistics', [
                'stats' => [
                    'total_sessions' => 0,
                    'active_sessions' => 0,
                    'total_messages' => 0,
                    'ai_messages' => 0,
                    'customer_messages' => 0,
                    'admin_messages' => 0,
                ],
                'recentSessions' => collect([]),
                'popularRules' => collect([])
            ]);
        }
    }

    // 查看会话详情
    public function showSession($id, Request $request)
    {
        try {
            $session = DB::table('customer_service_sessions')->where('id', $id)->first();
            
            if (!$session) {
                return redirect()->back()->with('error', '会话不存在');
            }

            // 获取消息
            $messages = DB::table('customer_service_messages')
                         ->where('session_id', $id)
                         ->orderBy('created_at', 'asc')
                         ->get();

            if ($request->isMethod('POST')) {
                // 管理员回复
                DB::table('customer_service_messages')->insert([
                    'session_id' => $id,
                    'sender_type' => 'admin',
                    'sender_id' => 1, // 假设管理员ID为1
                    'message' => $request->message,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                return redirect()->back()->with('success', '回复发送成功');
            }

            $session->messages = $messages;
            $session->display_name = $session->visitor_name ?: '访客';
            $session->contact_info = $session->visitor_email ?: $session->visitor_ip;

            return $this->view('customer-service.session-detail', compact('session'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', '操作失败：' . $e->getMessage());
        }
    }

    // 回复消息
    public function replyMessage(Request $request, $id)
    {
        try {
            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                return redirect()->back()->with('error', '会话不存在');
            }

            $message = $request->input('message');
            if (!$message) {
                return redirect()->back()->with('error', '消息内容不能为空');
            }

            // 插入管理员回复消息
            DB::table('customer_service_messages')->insert([
                'session_id' => $id,
                'sender_type' => 'admin',
                'sender_id' => auth()->id(), // 当前登录的管理员ID
                'message' => $message,
                'is_read' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // 更新会话最后活动时间
            DB::table('customer_service_sessions')
              ->where('id', $id)
              ->update([
                  'last_activity' => now(),
                  'updated_at' => now()
              ]);

            return redirect()->back()->with('success', '回复成功');

        } catch (\Exception $e) {
            \Log::error('Reply message error: ' . $e->getMessage());
            return redirect()->back()->with('error', '回复失败：' . $e->getMessage());
        }
    }

    // 获取会话消息（API）
    public function getSessionMessages($id)
    {
        try {
            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                return response()->json(['success' => false, 'error' => '会话不存在'], 404);
            }

            // 获取消息记录
            $messages = DB::table('customer_service_messages')
                         ->where('session_id', $id)
                         ->orderBy('created_at', 'asc')
                         ->get();

            return response()->json(['success' => true, 'messages' => $messages]);

        } catch (\Exception $e) {
            \Log::error('Get session messages error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '获取消息失败'], 500);
        }
    }

    // 回复消息（API）
    public function replyMessageApi(Request $request, $id)
    {
        try {
            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                return response()->json(['success' => false, 'error' => '会话不存在'], 404);
            }

            $message = $request->input('message');
            if (!$message) {
                return response()->json(['success' => false, 'error' => '消息内容不能为空'], 400);
            }

            // 插入管理员回复消息
            DB::table('customer_service_messages')->insert([
                'session_id' => $id,
                'sender_type' => 'admin',
                'sender_id' => auth()->id(), // 当前登录的管理员ID
                'message' => $message,
                'is_read' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // 更新会话最后活动时间
            DB::table('customer_service_sessions')
              ->where('id', $id)
              ->update([
                  'last_activity' => now(),
                  'updated_at' => now()
              ]);

            return response()->json(['success' => true, 'message' => '回复成功']);

        } catch (\Exception $e) {
            \Log::error('Reply message API error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '回复失败'], 500);
        }
    }
}
