<?php

/*
  |--------------------------------------------------------------------------
  | Admin Routes
  |--------------------------------------------------------------------------
  |
 */

Route::get('index/panel', 'Strongadmin\IndexController@panel'); //面板

//在线客服管理
Route::prefix('customer-service')->group(function () {
    Route::any('sessions', 'Strongadmin\CustomerServiceController@sessions')->name('admin.customer-service.sessions');

    // 新增的批量操作路由（放在前面避免冲突）
    Route::post('session/{id}/delete', 'Strongadmin\CustomerServiceController@deleteSession');
    Route::post('session/{id}/mark-read', 'Strongadmin\CustomerServiceController@markSessionAsRead');
    Route::get('sessions/unread-counts', 'Strongadmin\CustomerServiceController@getUnreadCounts');
    Route::post('sessions/clear-all', 'Strongadmin\CustomerServiceController@clearAllSessions');
    Route::post('sessions/mark-all-read', 'Strongadmin\CustomerServiceController@markAllAsRead');

    // 原有路由
    Route::get('session/{id}/messages', 'Strongadmin\CustomerServiceController@getSessionMessages');
    Route::post('session/{id}/send', 'Strongadmin\CustomerServiceController@sendAdminMessage');
    Route::post('session/{id}/reply', 'Strongadmin\CustomerServiceController@replyMessageApi');
    Route::post('session/{id}/notes', 'Strongadmin\CustomerServiceController@saveSessionNotes');
    Route::post('session/{id}/status', 'Strongadmin\CustomerServiceController@updateSessionStatus');
    Route::get('session/{id}/info', 'Strongadmin\CustomerServiceController@getSessionInfo');
    Route::get('session/{id}/stats', 'Strongadmin\CustomerServiceController@getSessionStats');
    Route::any('session/{id}', 'Strongadmin\CustomerServiceController@showSession')->name('admin.customer-service.session');
    Route::any('system-config', 'Strongadmin\CustomerServiceController@systemConfig');
    Route::post('system/toggle', 'Strongadmin\CustomerServiceController@toggleSystem');
    Route::get('system/status', 'Strongadmin\CustomerServiceController@getSystemStatus');
    Route::get('message-stream', 'Strongadmin\CustomerServiceController@messageStream');
    Route::get('statistics', 'Strongadmin\CustomerServiceController@getStatistics');
    Route::any('ai-rules', 'Strongadmin\CustomerServiceController@aiRules')->name('admin.customer-service.ai-rules');
    Route::any('ai-rule/create', 'Strongadmin\CustomerServiceController@createAiRule')->name('admin.customer-service.ai-rule.create');
    Route::any('ai-rule/{id}/edit', 'Strongadmin\CustomerServiceController@editAiRule')->name('admin.customer-service.ai-rule.edit');
    Route::post('ai-rule/store', 'Strongadmin\CustomerServiceController@storeAiRule')->name('admin.customer-service.ai-rule.store');
    Route::post('ai-rule/{id}/update', 'Strongadmin\CustomerServiceController@updateAiRule')->name('admin.customer-service.ai-rule.update');
    Route::post('ai-rule/{id}/delete', 'Strongadmin\CustomerServiceController@deleteAiRule')->name('admin.customer-service.ai-rule.delete');
    Route::post('ai-rule/{id}/toggle', 'Strongadmin\CustomerServiceController@toggleAiRule');
    Route::any('statistics', 'Strongadmin\CustomerServiceController@statistics')->name('admin.customer-service.statistics');

    // 设置中心
    Route::get('settings', 'Strongadmin\CustomerServiceSettingsController@index')->name('admin.customer-service.settings');
    Route::post('settings', 'Strongadmin\CustomerServiceSettingsController@store')->name('admin.customer-service.settings.store');
    Route::get('settings/{key}', 'Strongadmin\CustomerServiceSettingsController@getSetting')->name('admin.customer-service.settings.get');
    Route::post('settings/upload-sound', 'Strongadmin\CustomerServiceSettingsController@uploadSound')->name('admin.customer-service.settings.upload-sound');

    // 离线留言
    Route::get('offline-messages', 'Strongadmin\CustomerServiceController@offlineMessages')->name('admin.customer-service.offline-messages');
    Route::post('offline-messages/{id}/reply', 'Strongadmin\CustomerServiceController@replyOfflineMessage')->name('admin.customer-service.offline-messages.reply');
    Route::delete('offline-messages/{id}', 'Strongadmin\CustomerServiceController@deleteOfflineMessage')->name('admin.customer-service.offline-messages.delete');
});

//上传
Route::post('upload/image', 'Common\UploadController@image'); //上传图片
Route::post('upload/image/wangeditor', 'Common\UploadController@imageWangEditor'); //富文本图片上传【wangEditor】
//产品管理
Route::any('product/index', 'Strongadmin\Product\ProductController@index');
Route::any('product/show', 'Strongadmin\Product\ProductController@show');
Route::any('product/create', 'Strongadmin\Product\ProductController@create')->name('product.create');
Route::any('product/update', 'Strongadmin\Product\ProductController@update');
Route::any('product/destroy', 'Strongadmin\Product\ProductController@destroy');
Route::any('product/copy', 'Strongadmin\Product\ProductController@copy');
Route::any('product/spec/htmlString', 'Strongadmin\Product\ProductController@specHtmlString');
//产品分类
Route::any('product/category/index', 'Strongadmin\Product\CategoryController@index');
Route::any('product/category/show', 'Strongadmin\Product\CategoryController@show');
Route::any('product/category/create', 'Strongadmin\Product\CategoryController@create');
Route::any('product/category/update', 'Strongadmin\Product\CategoryController@update');
Route::any('product/category/destroy', 'Strongadmin\Product\CategoryController@destroy');
//产品规格组
Route::any('product/specGroup/index', 'Strongadmin\Product\SpecGroupController@index');
Route::any('product/specGroup/show', 'Strongadmin\Product\SpecGroupController@show');
Route::any('product/specGroup/create', 'Strongadmin\Product\SpecGroupController@create');
Route::any('product/specGroup/update', 'Strongadmin\Product\SpecGroupController@update');
Route::any('product/specGroup/destroy', 'Strongadmin\Product\SpecGroupController@destroy');
//产品规格
Route::any('product/spec/index', 'Strongadmin\Product\SpecController@index');
Route::any('product/spec/show', 'Strongadmin\Product\SpecController@show');
Route::any('product/spec/create', 'Strongadmin\Product\SpecController@create');
Route::any('product/spec/update', 'Strongadmin\Product\SpecController@update');
Route::any('product/spec/destroy', 'Strongadmin\Product\SpecController@destroy');
//产品品牌
Route::any('product/brand/index', 'Strongadmin\Product\BrandController@index');
Route::any('product/brand/show', 'Strongadmin\Product\BrandController@show');
Route::any('product/brand/create', 'Strongadmin\Product\BrandController@create');
Route::any('product/brand/update', 'Strongadmin\Product\BrandController@update');
Route::any('product/brand/destroy', 'Strongadmin\Product\BrandController@destroy');
//订单管理
Route::any('order/index', 'Strongadmin\OrderController@index');
Route::any('order/show', 'Strongadmin\OrderController@show');
Route::any('order/create', 'Strongadmin\OrderController@create');
Route::any('order/update', 'Strongadmin\OrderController@update');
Route::any('order/destroy', 'Strongadmin\OrderController@destroy');
Route::any('order/confirm/close', 'Strongadmin\OrderController@close'); //确认关闭
Route::any('order/confirm/paid', 'Strongadmin\OrderController@confirmPaid'); //确认已付款
Route::any('order/confirm/shipped', 'Strongadmin\OrderController@confirmShipped'); //确认发货
Route::any('order/confirm/returned', 'Strongadmin\OrderController@confirmReturned'); //确认已退货
Route::any('order/confirm/done', 'Strongadmin\OrderController@confirmDone'); //确认已完成
Route::any('order/products', 'Strongadmin\OrderController@products'); //订单产品
Route::any('order/print', 'Strongadmin\OrderController@print'); //打印
//会员管理
Route::any('user/index', 'Strongadmin\User\UserController@index');
Route::any('user/show', 'Strongadmin\User\UserController@show');
Route::any('user/create', 'Strongadmin\User\UserController@create');
Route::any('user/update', 'Strongadmin\User\UserController@update');
Route::any('user/destroy', 'Strongadmin\User\UserController@destroy');
//文章管理
Route::any('article/article/index', 'Strongadmin\Article\ArticleController@index');
Route::any('article/article/show', 'Strongadmin\Article\ArticleController@show');
Route::any('article/article/create', 'Strongadmin\Article\ArticleController@create');
Route::any('article/article/update', 'Strongadmin\Article\ArticleController@update');
Route::any('article/article/destroy', 'Strongadmin\Article\ArticleController@destroy');
//文章分类
Route::any('article/category/index', 'Strongadmin\Article\CategoryController@index');
Route::any('article/category/show', 'Strongadmin\Article\CategoryController@show');
Route::any('article/category/create', 'Strongadmin\Article\CategoryController@create');
Route::any('article/category/update', 'Strongadmin\Article\CategoryController@update');
Route::any('article/category/destroy', 'Strongadmin\Article\CategoryController@destroy');
//意见反馈
Route::any('user/feedback/index', 'Strongadmin\User\FeedbackController@index');
Route::any('user/feedback/show', 'Strongadmin\User\FeedbackController@show');
Route::any('user/feedback/update', 'Strongadmin\User\FeedbackController@update');
Route::any('user/feedback/destroy', 'Strongadmin\User\FeedbackController@destroy');
//会员等级
Route::any('user/userRank/index', 'Strongadmin\User\UserRankController@index');
Route::any('user/userRank/show', 'Strongadmin\User\UserRankController@show');
Route::any('user/userRank/create', 'Strongadmin\User\UserRankController@create');
Route::any('user/userRank/update', 'Strongadmin\User\UserRankController@update');
Route::any('user/userRank/destroy', 'Strongadmin\User\UserRankController@destroy');
//配送方式管理
Route::any('shippingOption/index', 'Strongadmin\ShippingOptionController@index');
Route::any('shippingOption/show', 'Strongadmin\ShippingOptionController@show');
Route::any('shippingOption/create', 'Strongadmin\ShippingOptionController@create');
Route::any('shippingOption/update', 'Strongadmin\ShippingOptionController@update');
//Route::any('shippingOption/destroy', 'Strongadmin\ShippingOptionController@destroy');
//配送方式管理-配送区域
Route::any('shippingOptionConfig/index', 'Strongadmin\ShippingOptionConfigController@index');
Route::any('shippingOptionConfig/show', 'Strongadmin\ShippingOptionConfigController@show');
Route::any('shippingOptionConfig/create', 'Strongadmin\ShippingOptionConfigController@create');
Route::any('shippingOptionConfig/update', 'Strongadmin\ShippingOptionConfigController@update');
Route::any('shippingOptionConfig/destroy', 'Strongadmin\ShippingOptionConfigController@destroy');
//支付方式管理
Route::any('paymentOption/index', 'Strongadmin\PaymentOptionController@index');
Route::any('paymentOption/show', 'Strongadmin\PaymentOptionController@show');
Route::any('paymentOption/create', 'Strongadmin\PaymentOptionController@create');
Route::any('paymentOption/update', 'Strongadmin\PaymentOptionController@update');
Route::any('paymentOption/destroy', 'Strongadmin\PaymentOptionController@destroy');
//地区-国家管理
Route::any('region/regionCountry/index', 'Strongadmin\Region\RegionCountryController@index');
Route::any('region/regionCountry/show', 'Strongadmin\Region\RegionCountryController@show');
Route::any('region/regionCountry/create', 'Strongadmin\Region\RegionCountryController@create');
Route::any('region/regionCountry/update', 'Strongadmin\Region\RegionCountryController@update');
Route::any('region/regionCountry/destroy', 'Strongadmin\Region\RegionCountryController@destroy');
//地区-大陆管理
Route::any('region/regionContinent/index', 'Strongadmin\Region\RegionContinentController@index');
Route::any('region/regionContinent/show', 'Strongadmin\Region\RegionContinentController@show');
Route::any('region/regionContinent/create', 'Strongadmin\Region\RegionContinentController@create');
Route::any('region/regionContinent/update', 'Strongadmin\Region\RegionContinentController@update');
Route::any('region/regionContinent/destroy', 'Strongadmin\Region\RegionContinentController@destroy');
//网站配置
Route::any('webconfig/show', 'Strongadmin\WebconfigController@showForm');//配置详情
Route::any('webconfig/save/config', 'Strongadmin\WebconfigController@saveConfig');//保存配置
Route::any('webconfig/clearcache', 'Strongadmin\WebconfigController@clearcache');//清楚缓存
Route::any('webconfig/sendReceiveMailTest', 'Strongadmin\WebconfigController@sendReceiveMailTest');//清楚缓存

// 客服系统后台路由
Route::any('customer-service/sessions', 'Strongadmin\SimpleCustomerServiceController@sessions')->name('admin.customer-service.sessions');
Route::any('customer-service/session/{id}', 'Strongadmin\SimpleCustomerServiceController@showSession')->name('admin.customer-service.session');
Route::any('customer-service/ai-rules', 'Strongadmin\SimpleCustomerServiceController@aiRules')->name('admin.customer-service.ai-rules');
Route::any('customer-service/ai-rule/create', 'Strongadmin\SimpleCustomerServiceController@createAiRule')->name('admin.customer-service.ai-rule.create');
Route::any('customer-service/ai-rule/{id}/edit', 'Strongadmin\SimpleCustomerServiceController@editAiRule')->name('admin.customer-service.ai-rule.edit');
Route::any('customer-service/ai-rule/{id}/delete', 'Strongadmin\SimpleCustomerServiceController@deleteAiRule')->name('admin.customer-service.ai-rule.delete');
Route::any('customer-service/statistics', 'Strongadmin\SimpleCustomerServiceController@statistics')->name('admin.customer-service.statistics');
//产品评论
Route::any('product/comment/index', 'Strongadmin\Product\CommentController@index');
Route::any('product/comment/show', 'Strongadmin\Product\CommentController@show');
Route::any('product/comment/create', 'Strongadmin\Product\CommentController@create');
Route::any('product/comment/update', 'Strongadmin\Product\CommentController@update');
Route::any('product/comment/destroy', 'Strongadmin\Product\CommentController@destroy');
Route::any('product/comment/approve', 'Strongadmin\Product\CommentController@approve');
Route::any('product/comment/refused ', 'Strongadmin\Product\CommentController@refused');