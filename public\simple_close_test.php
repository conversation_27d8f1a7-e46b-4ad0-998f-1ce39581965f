<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单关闭测试</title>
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        #status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .online { background: #d4edda; color: #155724; }
        .offline { background: #f8d7da; color: #721c24; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 简单关闭检测测试</h1>
        
        <div id="status">状态: 未开始</div>
        
        <div>
            <button onclick="startTest()">开始测试</button>
            <button onclick="sendOffline()">发送离线</button>
            <button onclick="clearLog()">清空日志</button>
            <button class="danger" onclick="testClose()">测试关闭</button>
        </div>
        
        <h3>📊 日志</h3>
        <div id="log"></div>
        
        <h3>📋 说明</h3>
        <ul>
            <li>点击"开始测试"初始化会话</li>
            <li>点击"测试关闭"模拟关闭浏览器</li>
            <li>在另一个标签页打开 <a href="/realtime_monitor.php" target="_blank">实时监控</a> 查看状态变化</li>
        </ul>
    </div>

    <script>
        let sessionId = null;
        let heartbeatInterval = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += '[' + time + '] ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log('[' + time + '] ' + message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(text, isOnline) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = '状态: ' + text;
            statusDiv.className = isOnline ? 'online' : 'offline';
        }

        async function startTest() {
            log('🚀 开始测试...');
            updateStatus('初始化中...', false);
            
            try {
                const response = await fetch('/customer-service/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        visitor_name: '测试用户',
                        visitor_email: '<EMAIL>'
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    sessionId = data.session_id;
                    log('✅ 会话创建成功: ' + sessionId);
                    updateStatus('在线', true);
                    
                    // 开始心跳
                    heartbeatInterval = setInterval(sendHeartbeat, 8000); // 每8秒
                    sendHeartbeat(); // 立即发送一次
                    
                    // 设置关闭检测
                    setupCloseDetection();
                    
                } else {
                    log('❌ 会话创建失败');
                    updateStatus('失败', false);
                }
            } catch (error) {
                log('💥 异常: ' + error.message);
                updateStatus('异常', false);
            }
        }

        async function sendHeartbeat() {
            if (!sessionId) return;
            
            try {
                const response = await fetch('/customer-service/heartbeat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        status: 'online'
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    log('💓 心跳发送成功');
                } else {
                    log('❌ 心跳失败');
                }
            } catch (error) {
                log('💥 心跳异常: ' + error.message);
            }
        }

        function sendOffline() {
            if (!sessionId) {
                log('⚠️ 没有会话ID');
                return;
            }
            
            log('📤 发送离线状态...');
            updateStatus('离线', false);
            
            const data = JSON.stringify({
                session_id: sessionId,
                status: 'offline'
            });
            
            // 尝试sendBeacon
            if (navigator.sendBeacon) {
                const blob = new Blob([data], { type: 'application/json' });
                const success = navigator.sendBeacon('/customer-service/heartbeat', blob);
                log('📡 sendBeacon: ' + (success ? '成功' : '失败'));
            } else {
                log('❌ 不支持sendBeacon');
            }
            
            // 尝试同步XHR
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/customer-service/heartbeat', false);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                xhr.send(data);
                log('📡 同步XHR: 状态' + xhr.status);
            } catch (e) {
                log('❌ 同步XHR失败: ' + e.message);
            }
            
            // 停止心跳
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }
        }

        function setupCloseDetection() {
            log('🔧 设置关闭检测');
            
            window.addEventListener('beforeunload', function(event) {
                log('🚪 beforeunload触发');
                sendOfflineImmediate();
            });
            
            window.addEventListener('pagehide', function(event) {
                log('📱 pagehide触发');
                sendOfflineImmediate();
            });
            
            window.addEventListener('unload', function(event) {
                log('🗑️ unload触发');
                sendOfflineImmediate();
            });
        }

        function sendOfflineImmediate() {
            if (!sessionId) return;
            
            const data = JSON.stringify({
                session_id: sessionId,
                status: 'offline'
            });
            
            // 多种方式同时发送
            if (navigator.sendBeacon) {
                const blob = new Blob([data], { type: 'application/json' });
                navigator.sendBeacon('/customer-service/heartbeat', blob);
            }
            
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/customer-service/heartbeat', false);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                xhr.send(data);
            } catch (e) {}
            
            try {
                const img = new Image();
                img.src = '/customer-service/heartbeat-img?session_id=' + sessionId + '&status=offline';
            } catch (e) {}
        }

        function testClose() {
            if (confirm('这将关闭当前窗口来测试关闭检测，确定吗？')) {
                log('🚪 即将关闭窗口...');
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        }
    </script>
</body>
</html>
