<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单测试自动打开</title>
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    <style>
        body { font-family: Arial; margin: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 4px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔔 简单测试自动打开聊天窗口</h1>
    
    <div class="step">
        <h3>步骤1: 检查当前会话</h3>
        <button onclick="checkSessions()">检查会话</button>
        <div id="sessions-info">点击检查会话</div>
    </div>
    
    <div class="step">
        <h3>步骤2: 发送测试消息</h3>
        <button onclick="sendTestMessage()">发送客服消息</button>
        <div id="message-info">等待发送</div>
    </div>
    
    <div class="step">
        <h3>步骤3: 检查API</h3>
        <button onclick="testAPI()">测试消息API</button>
        <div id="api-info">等待测试</div>
    </div>
    
    <div class="step">
        <h3>操作日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logDiv.innerHTML += `<div style="color: ${color};">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function checkSessions() {
            try {
                log('🔍 检查当前会话...');
                
                const response = await fetch('/strongadmin/customer-service/sessions');
                const text = await response.text();
                
                // 简单解析HTML获取会话信息
                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'text/html');
                const sessionRows = doc.querySelectorAll('[data-session-id]');
                
                if (sessionRows.length > 0) {
                    log(`✅ 找到 ${sessionRows.length} 个会话`, 'success');
                    
                    sessionRows.forEach((row, index) => {
                        const sessionId = row.getAttribute('data-session-id');
                        const visitorName = row.querySelector('td:nth-child(2)')?.textContent?.trim();
                        log(`  会话${index + 1}: ID=${sessionId}, 访客=${visitorName}`);
                    });
                    
                    // 使用第一个会话进行测试
                    window.testSessionId = sessionRows[0].getAttribute('data-session-id');
                    document.getElementById('sessions-info').innerHTML = `<span style="color: green;">找到 ${sessionRows.length} 个会话，将使用会话ID: ${window.testSessionId}</span>`;
                } else {
                    log('❌ 没有找到会话', 'error');
                    document.getElementById('sessions-info').innerHTML = '<span style="color: red;">没有找到会话，请先在首页打开客服</span>';
                }
                
            } catch (error) {
                log(`💥 检查会话失败: ${error.message}`, 'error');
                document.getElementById('sessions-info').innerHTML = '<span style="color: red;">检查失败</span>';
            }
        }

        async function sendTestMessage() {
            if (!window.testSessionId) {
                log('❌ 请先检查会话', 'error');
                return;
            }

            try {
                log('📤 发送测试消息...');
                
                const response = await fetch(`/strongadmin/customer-service/session/${window.testSessionId}/reply`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: '🔔 这是一条测试消息，用于测试自动打开聊天窗口功能！时间：' + new Date().toLocaleTimeString()
                    })
                });

                const data = await response.json();
                if (data.success) {
                    log('✅ 测试消息发送成功', 'success');
                    document.getElementById('message-info').innerHTML = '<span style="color: green;">消息发送成功，请观察首页聊天窗口</span>';
                } else {
                    log(`❌ 发送消息失败: ${data.message || '未知错误'}`, 'error');
                    document.getElementById('message-info').innerHTML = '<span style="color: red;">发送失败</span>';
                }
            } catch (error) {
                log(`💥 发送消息异常: ${error.message}`, 'error');
                document.getElementById('message-info').innerHTML = '<span style="color: red;">发送异常</span>';
            }
        }

        async function testAPI() {
            if (!window.testSessionId) {
                log('❌ 请先检查会话', 'error');
                return;
            }

            try {
                log('🧪 测试消息API...');

                // 使用PHP后端直接查询session_id
                const response = await fetch(`/test_get_session_id.php?id=${window.testSessionId}`);
                const data = await response.json();

                if (!data.success) {
                    log(`❌ 获取session_id失败: ${data.error}`, 'error');
                    return;
                }

                const sessionId = data.session_id;
                log(`📋 找到session_id: ${sessionId}`);
                
                // 测试消息API
                const response = await fetch(`/api/customer-service/messages/${sessionId}`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ API测试成功，返回 ${data.messages.length} 条消息`, 'success');
                    
                    if (data.messages.length > 0) {
                        const lastMessage = data.messages[data.messages.length - 1];
                        log(`📨 最新消息: ${lastMessage.message} (发送者: ${lastMessage.sender_type})`);
                    }
                    
                    document.getElementById('api-info').innerHTML = `<span style="color: green;">API正常，返回 ${data.messages.length} 条消息</span>`;
                } else {
                    log(`❌ API测试失败: ${data.message || '未知错误'}`, 'error');
                    document.getElementById('api-info').innerHTML = '<span style="color: red;">API测试失败</span>';
                }
                
            } catch (error) {
                log(`💥 API测试异常: ${error.message}`, 'error');
                document.getElementById('api-info').innerHTML = '<span style="color: red;">API测试异常</span>';
            }
        }

        // 页面加载时自动检查会话
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            setTimeout(checkSessions, 1000);
        });
    </script>
</body>
</html>
