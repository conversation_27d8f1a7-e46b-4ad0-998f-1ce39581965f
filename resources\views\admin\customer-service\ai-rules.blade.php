@extends('admin.layouts.app')

@section('title', 'AI自动回复规则')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-robot"></i> AI自动回复规则
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.customer-service.ai-rule.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加规则
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 搜索 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="form-inline">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="搜索规则..." value="{{ request('search') }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 规则列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="5%">ID</th>
                                    <th width="15%">规则名称</th>
                                    <th width="20%">关键词</th>
                                    <th width="30%">回复内容</th>
                                    <th width="8%">优先级</th>
                                    <th width="8%">使用次数</th>
                                    <th width="8%">状态</th>
                                    <th width="6%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($rules as $rule)
                                <tr>
                                    <td>{{ $rule->id }}</td>
                                    <td>
                                        <strong>{{ $rule->name }}</strong>
                                    </td>
                                    <td>
                                        @foreach($rule->keywords as $keyword)
                                            <span class="badge badge-secondary">{{ $keyword }}</span>
                                        @endforeach
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 300px;" title="{{ $rule->reply_message }}">
                                            {{ $rule->reply_message }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $rule->priority }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">{{ $rule->usage_count }}</span>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input toggle-rule" 
                                                   id="rule-{{ $rule->id }}" 
                                                   data-id="{{ $rule->id }}"
                                                   {{ $rule->is_active ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="rule-{{ $rule->id }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.customer-service.ai-rule.edit', $rule->id) }}" 
                                               class="btn btn-sm btn-warning" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" action="{{ route('admin.customer-service.ai-rule.delete', $rule->id) }}" 
                                                  style="display: inline;" onsubmit="return confirm('确定要删除这个规则吗？')">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-danger" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-robot fa-3x mb-3"></i>
                                        <p>暂无AI规则</p>
                                        <a href="{{ route('admin.customer-service.ai-rule.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> 添加第一个规则
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="d-flex justify-content-center">
                        {{ $rules->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge {
    margin: 2px;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-switch {
    padding-left: 2.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 切换规则状态
    document.querySelectorAll('.toggle-rule').forEach(function(toggle) {
        toggle.addEventListener('change', function() {
            const ruleId = this.dataset.id;
            const isActive = this.checked;
            
            fetch(`/admin/customer-service/ai-rule/${ruleId}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示成功提示
                    const status = data.is_active ? '启用' : '禁用';
                    // 这里可以添加toast提示
                    console.log(`规则已${status}`);
                } else {
                    // 恢复开关状态
                    this.checked = !isActive;
                    alert('操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // 恢复开关状态
                this.checked = !isActive;
                alert('网络错误，请重试');
            });
        });
    });
});
</script>
@endsection
