<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>重置会话</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .info { background: #e6f7ff; padding: 15px; border-radius: 4px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .success { background: #f6ffed; padding: 15px; border-radius: 4px; margin: 10px 0; color: #52c41a; }
    </style>
</head>
<body>
    <h1>🔄 重置客服会话</h1>
    
    <div class="info">
        <p>这个页面会清除浏览器中保存的客服会话信息，强制创建新的会话。</p>
        <p>如果遇到sessionId不匹配的问题，可以使用这个功能重置。</p>
    </div>
    
    <button onclick="resetSession()">重置会话</button>
    <button onclick="checkCurrentSession()">检查当前会话</button>
    
    <div id="result"></div>

    <script>
        function resetSession() {
            // 清除localStorage中的会话信息
            localStorage.removeItem('customer_service_session_id');
            localStorage.removeItem('device_fingerprint');
            localStorage.removeItem('session_created_at');
            
            document.getElementById('result').innerHTML = `
                <div class="success">
                    ✅ 会话信息已清除！<br>
                    现在请：<br>
                    1. 访问首页<br>
                    2. 打开客服聊天窗口<br>
                    3. 这会创建新的会话记录
                </div>
            `;
            
            console.log('✅ 会话信息已重置');
        }
        
        function checkCurrentSession() {
            const sessionId = localStorage.getItem('customer_service_session_id');
            const deviceFingerprint = localStorage.getItem('device_fingerprint');
            const createdAt = localStorage.getItem('session_created_at');
            
            if (sessionId) {
                document.getElementById('result').innerHTML = `
                    <div class="info">
                        <strong>当前会话信息：</strong><br>
                        SessionID: <code>${sessionId}</code><br>
                        设备指纹: <code>${deviceFingerprint}</code><br>
                        创建时间: <code>${createdAt}</code>
                    </div>
                `;
            } else {
                document.getElementById('result').innerHTML = `
                    <div class="info">
                        <strong>当前没有保存的会话信息</strong>
                    </div>
                `;
            }
        }
        
        // 页面加载时检查当前会话
        window.addEventListener('load', checkCurrentSession);
    </script>
</body>
</html>
