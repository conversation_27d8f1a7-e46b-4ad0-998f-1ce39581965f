<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>项目客服系统调试</title>
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 4px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .debug-section { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 项目客服系统调试</h1>
        
        <div class="info">📋 这个页面用来调试项目中真实的客服系统问题</div>
        
        <div class="debug-section">
            <h3>📊 当前会话状态</h3>
            <?php
            try {
                $sessions = DB::select("
                    SELECT 
                        id, session_id, visitor_name, visitor_ip, status, 
                        last_seen, created_at,
                        TIMESTAMPDIFF(SECOND, last_seen, NOW()) as seconds_ago
                    FROM st_customer_service_sessions 
                    WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                    ORDER BY created_at DESC 
                    LIMIT 10
                ");
                
                if (empty($sessions)) {
                    echo "<div class='warning'>⚠️ 最近1小时内没有会话记录</div>";
                } else {
                    echo "<table border='1' style='width:100%;border-collapse:collapse;'>";
                    echo "<tr style='background:#f8f9fa;'>";
                    echo "<th style='padding:8px;'>ID</th>";
                    echo "<th style='padding:8px;'>会话ID</th>";
                    echo "<th style='padding:8px;'>访客</th>";
                    echo "<th style='padding:8px;'>IP</th>";
                    echo "<th style='padding:8px;'>状态</th>";
                    echo "<th style='padding:8px;'>最后心跳</th>";
                    echo "<th style='padding:8px;'>多久前</th>";
                    echo "</tr>";
                    
                    foreach ($sessions as $session) {
                        $statusIcon = $session->status === 'online' ? '🟢' : '⚫';
                        $timeAgo = $session->seconds_ago ? $session->seconds_ago . '秒前' : '无记录';
                        
                        echo "<tr>";
                        echo "<td style='padding:8px;'>{$session->id}</td>";
                        echo "<td style='padding:8px;'>" . substr($session->session_id, 0, 15) . "...</td>";
                        echo "<td style='padding:8px;'>{$session->visitor_name}</td>";
                        echo "<td style='padding:8px;'>{$session->visitor_ip}</td>";
                        echo "<td style='padding:8px;'>{$statusIcon} {$session->status}</td>";
                        echo "<td style='padding:8px;'>{$session->last_seen}</td>";
                        echo "<td style='padding:8px;'>{$timeAgo}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ 查询失败: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h3>🧪 客服系统测试</h3>
            <div>
                <button onclick="testProjectCS()">测试项目客服系统</button>
                <button onclick="checkCSStatus()">检查客服状态</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            <div id="log"></div>
        </div>
        
        <div class="debug-section">
            <h3>📋 调试说明</h3>
            <ul>
                <li><strong>测试步骤:</strong></li>
                <li>1. 点击"测试项目客服系统"</li>
                <li>2. 在另一个标签页打开网站首页，点击客服按钮</li>
                <li>3. 观察这里的日志和会话状态变化</li>
                <li>4. 关闭客服窗口，看是否能检测到离线</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += '[' + time + '] ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log('[' + time + '] ' + message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testProjectCS() {
            log('🚀 开始测试项目客服系统...');

            // 项目中使用的是内联脚本，不是CustomerService类
            log('💡 项目使用内联客服脚本，不是独立的CustomerService类');

            // 检查客服系统状态API
            fetch('/api/customer-service/status')
                .then(response => response.json())
                .then(data => {
                    log('📊 API状态: ' + JSON.stringify(data));
                    if (data.success && data.enabled) {
                        log('✅ 客服系统API可用');
                    } else {
                        log('❌ 客服系统API不可用');
                    }
                })
                .catch(error => {
                    log('❌ API调用失败: ' + error.message);
                });
            
            // 检查DOM元素
            const chatButton = document.getElementById('cs-chat-button');
            if (chatButton) {
                log('✅ 客服按钮元素存在');
            } else {
                log('❌ 客服按钮元素不存在');
            }

            const chatWindow = document.getElementById('cs-chat-window');
            if (chatWindow) {
                log('✅ 客服窗口元素存在');
            } else {
                log('❌ 客服窗口元素不存在');
            }

            const customerServiceWidget = document.getElementById('customer-service-widget');
            if (customerServiceWidget) {
                log('✅ 客服组件存在，显示状态: ' + customerServiceWidget.style.display);
            } else {
                log('❌ 客服组件不存在');
            }

            // 检查全局变量
            if (typeof sessionId !== 'undefined') {
                log('✅ sessionId 存在: ' + sessionId);
            } else {
                log('❌ sessionId 不存在');
            }

            if (typeof isOpen !== 'undefined') {
                log('✅ isOpen 存在: ' + isOpen);
            } else {
                log('❌ isOpen 不存在');
            }
        }

        async function checkCSStatus() {
            log('🔍 检查客服系统状态...');
            
            try {
                const response = await fetch('/customer-service/status');
                const data = await response.json();
                log('📊 客服系统状态: ' + JSON.stringify(data));
            } catch (error) {
                log('❌ 获取状态失败: ' + error.message);
            }
            
            // 刷新页面数据
            setTimeout(() => {
                location.reload();
            }, 2000);
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            setTimeout(testProjectCS, 1000);
        });
    </script>
</body>
</html>
