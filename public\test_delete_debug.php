<?php
/**
 * 专门测试删除功能的调试页面
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>删除功能调试</title>";
echo "<style>
body{font-family:Arial;margin:20px;} 
.success{color:green;} 
.error{color:red;} 
.info{color:blue;} 
.test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}
.debug-box{background:#f5f5f5;padding:15px;border-radius:4px;margin:10px 0;border-left:4px solid #007bff;}
pre{background:white;padding:10px;border:1px solid #ddd;border-radius:4px;overflow:auto;max-height:300px;}
</style>";
echo "</head><body>";

echo "<h1>🔧 删除功能专项调试</h1>";

echo "<div class='debug-box'>";
echo "<h3>📋 测试信息</h3>";
echo "<p><strong>测试URL:</strong> <code>DELETE /strongadmin/customer-service/session/{id}</code></p>";
echo "<p><strong>当前时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>服务器:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "</div>";

echo "<div class='debug-box'>";
echo "<h3>🧪 测试删除功能</h3>";
echo "<form id='delete-test-form'>";
echo "<label>会话ID: <input type='number' id='session-id' value='1' min='1' required></label><br><br>";
echo "<button type='button' onclick='testDelete()' class='test-btn'>🗑️ 测试删除</button>";
echo "<button type='button' onclick='testDeleteWithFormData()' class='test-btn'>📝 用FormData测试</button>";
echo "<button type='button' onclick='testDeleteWithoutCSRF()' class='test-btn'>🚫 无CSRF测试</button>";
echo "</form>";
echo "</div>";

echo "<div class='debug-box'>";
echo "<h3>📊 测试结果</h3>";
echo "<div id='test-results'>";
echo "<p>点击上面的按钮开始测试...</p>";
echo "</div>";
echo "</div>";

echo "<script>
let testCount = 0;

function addResult(title, content, type = 'info') {
    testCount++;
    const results = document.getElementById('test-results');
    const div = document.createElement('div');
    div.innerHTML = `
        <h4 class='${type}'>测试 #${testCount}: ${title}</h4>
        <pre>${content}</pre>
        <hr>
    `;
    results.appendChild(div);
    results.scrollTop = results.scrollHeight;
}

function testDelete() {
    const sessionId = document.getElementById('session-id').value;
    addResult('标准DELETE请求', '开始测试...');
    
    fetch(`/strongadmin/customer-service/session/${sessionId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': 'test-token-' + Date.now(),
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        addResult('响应状态', `状态码: ${response.status}\\n状态文本: ${response.statusText}\\nURL: ${response.url}`);
        return response.text();
    })
    .then(text => {
        addResult('响应内容', text);
        try {
            const json = JSON.parse(text);
            addResult('JSON解析', JSON.stringify(json, null, 2), 'success');
        } catch (e) {
            addResult('JSON解析失败', e.message, 'error');
        }
    })
    .catch(error => {
        addResult('请求失败', error.message, 'error');
    });
}

function testDeleteWithFormData() {
    const sessionId = document.getElementById('session-id').value;
    addResult('FormData DELETE请求', '使用FormData格式...');
    
    const formData = new FormData();
    formData.append('_method', 'DELETE');
    formData.append('_token', 'test-token-' + Date.now());
    
    fetch(`/strongadmin/customer-service/session/${sessionId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => {
        addResult('FormData响应状态', `状态码: ${response.status}\\n状态文本: ${response.statusText}`);
        return response.text();
    })
    .then(text => {
        addResult('FormData响应内容', text);
        try {
            const json = JSON.parse(text);
            addResult('FormData JSON解析', JSON.stringify(json, null, 2), 'success');
        } catch (e) {
            addResult('FormData JSON解析失败', e.message, 'error');
        }
    })
    .catch(error => {
        addResult('FormData请求失败', error.message, 'error');
    });
}

function testDeleteWithoutCSRF() {
    const sessionId = document.getElementById('session-id').value;
    addResult('无CSRF DELETE请求', '测试CSRF验证...');
    
    fetch(`/strongadmin/customer-service/session/${sessionId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        addResult('无CSRF响应状态', `状态码: ${response.status}\\n状态文本: ${response.statusText}`);
        return response.text();
    })
    .then(text => {
        addResult('无CSRF响应内容', text);
    })
    .catch(error => {
        addResult('无CSRF请求失败', error.message, 'error');
    });
}
</script>";

echo "<div class='debug-box'>";
echo "<h3>🔗 相关链接</h3>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/test_customer_service_routes.php' target='_blank'>路由测试页面</a></li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
