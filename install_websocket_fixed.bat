@echo off
chcp 65001 >nul
echo Installing WebSocket packages...
echo.

echo 1. Installing Laravel WebSockets...
composer require beyondcode/laravel-websockets

echo.
echo 2. Installing Pusher PHP Server...
composer require pusher/pusher-php-server

echo.
echo 3. Publishing WebSocket config...
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="config"

echo.
echo 4. Publishing WebSocket migrations...
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="migrations"

echo.
echo 5. Running migrations...
php artisan migrate

echo.
echo WebSocket installation completed!
echo.
pause
