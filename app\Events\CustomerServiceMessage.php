<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CustomerServiceMessage implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $sessionId;
    public $messageId;
    public $senderType;
    public $message;
    public $createdAt;

    /**
     * Create a new event instance.
     */
    public function __construct($sessionId, $messageId, $senderType, $message, $createdAt = null)
    {
        $this->sessionId = $sessionId;
        $this->messageId = $messageId;
        $this->senderType = $senderType;
        $this->message = $message;
        $this->createdAt = $createdAt ?: now();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('customer-service.' . $this->sessionId),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'new.message';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message_id' => $this->messageId,
            'sender_type' => $this->senderType,
            'message' => $this->message,
            'created_at' => $this->createdAt,
            'timestamp' => time()
        ];
    }
}
