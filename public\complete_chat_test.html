<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整聊天测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; text-align: center; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .messages { height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #fafafa; margin: 10px 0; }
        .message { margin: 5px 0; padding: 8px; border-radius: 8px; }
        .message.customer { background: #e3f2fd; text-align: right; }
        .message.admin { background: #f3e5f5; text-align: left; }
        .message.new { background: #fff3cd; border: 2px solid #ffc107; animation: highlight 2s ease-out; }
        @keyframes highlight { from { background: #ffeb3b; } to { background: #fff3cd; } }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; border: 1px solid #ddd; }
        input[type="text"] { width: 70%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .info { background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💬 完整聊天测试系统</h1>
        
        <div class="info">
            <strong>测试目标：</strong>验证前台能实时收到后台的回复消息
        </div>
        
        <div class="grid">
            <!-- 左侧：前台客户 -->
            <div class="panel">
                <h2>👤 前台客户端</h2>
                <div class="info">
                    <div>会话ID: <span id="session-id">未生成</span></div>
                    <div>SSE状态: <span id="sse-status" class="status offline">未连接</span></div>
                    <div>收到消息数: <span id="received-count">0</span></div>
                </div>
                
                <div id="customer-messages" class="messages"></div>
                
                <div>
                    <input type="text" id="customer-input" placeholder="输入客户消息..." onkeypress="handleCustomerKeyPress(event)">
                    <button class="btn" onclick="sendCustomerMessage()">发送</button>
                </div>
                
                <div>
                    <button class="btn" onclick="startCustomerSSE()">启动SSE</button>
                    <button class="btn danger" onclick="stopCustomerSSE()">停止SSE</button>
                </div>
                
                <div id="customer-log" class="log"></div>
            </div>
            
            <!-- 右侧：后台客服 -->
            <div class="panel">
                <h2>👨‍💼 后台客服端</h2>
                <div class="info">
                    <div>会话数据库ID: <span id="session-db-id">未知</span></div>
                    <div>发送消息数: <span id="sent-count">0</span></div>
                </div>
                
                <div id="admin-messages" class="messages"></div>
                
                <div>
                    <input type="text" id="admin-input" placeholder="输入客服回复..." onkeypress="handleAdminKeyPress(event)">
                    <button class="btn" onclick="sendAdminReply()">发送回复</button>
                </div>
                
                <div>
                    <button class="btn success" onclick="sendQuickReply()">快速回复</button>
                    <button class="btn" onclick="refreshMessages()">刷新消息</button>
                </div>
                
                <div id="admin-log" class="log"></div>
            </div>
        </div>
        
        <div class="panel">
            <h2>🧪 测试步骤</h2>
            <ol>
                <li><button class="btn" onclick="step1()">步骤1: 初始化会话</button> - 生成会话ID并启动SSE</li>
                <li><button class="btn" onclick="step2()">步骤2: 发送客户消息</button> - 模拟客户发送消息</li>
                <li><button class="btn" onclick="step3()">步骤3: 发送客服回复</button> - 模拟客服回复</li>
                <li><strong>观察结果</strong> - 左侧应该立即收到右侧的回复</li>
            </ol>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script>
    let sessionId = null;
    let sessionDbId = null;
    let eventSource = null;
    let receivedCount = 0;
    let sentCount = 0;
    
    // 步骤1: 初始化会话
    function step1() {
        sessionId = 'complete_test_' + Date.now();
        document.getElementById('session-id').textContent = sessionId;
        logTest('🚀 步骤1: 会话初始化 - ' + sessionId);
        
        startCustomerSSE();
    }
    
    // 步骤2: 发送客户消息
    function step2() {
        if (!sessionId) {
            alert('请先执行步骤1');
            return;
        }
        
        const message = '测试客户消息 ' + new Date().toLocaleTimeString();
        document.getElementById('customer-input').value = message;
        sendCustomerMessage();
        logTest('📤 步骤2: 发送客户消息');
    }
    
    // 步骤3: 发送客服回复
    function step3() {
        if (!sessionDbId) {
            alert('请先执行步骤1和步骤2，等待获取会话数据库ID');
            return;
        }
        
        const reply = '测试客服回复 ' + new Date().toLocaleTimeString();
        document.getElementById('admin-input').value = reply;
        sendAdminReply();
        logTest('💬 步骤3: 发送客服回复');
    }
    
    // 启动客户端SSE
    function startCustomerSSE() {
        if (!sessionId) {
            alert('请先生成会话ID');
            return;
        }
        
        if (eventSource) {
            eventSource.close();
        }
        
        // 使用数据库版本的SSE
        const url = `/stable_sse.php?session_id=${sessionId}&last_message_id=0`;
        logCustomer('🔄 启动SSE: ' + url);
        updateSSEStatus('连接中...', 'offline');
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            logCustomer('✅ SSE连接成功');
            updateSSEStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                logCustomer('📨 ' + data.type + ': ' + (data.message || ''));
                
                if (data.type === 'session_created' || data.type === 'session_found') {
                    sessionDbId = data.session_db_id;
                    document.getElementById('session-db-id').textContent = sessionDbId;
                    logAdmin('📍 获取会话数据库ID: ' + sessionDbId);
                }
                
                if (data.type === 'new_reply') {
                    addCustomerMessage(data.sender_type, data.message, true);
                    receivedCount++;
                    document.getElementById('received-count').textContent = receivedCount;
                    logCustomer('🎉 收到新回复！');
                    logTest('✅ 前台成功收到后台回复！');
                    playNotificationSound();
                }
                
            } catch (e) {
                logCustomer('❌ 解析消息失败: ' + e.message);
            }
        };
        
        eventSource.onerror = function() {
            logCustomer('❌ SSE连接错误');
            updateSSEStatus('连接错误', 'offline');
        };
    }
    
    // 停止客户端SSE
    function stopCustomerSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            logCustomer('🛑 SSE连接已停止');
            updateSSEStatus('已断开', 'offline');
        }
    }
    
    // 发送客户消息
    function sendCustomerMessage() {
        const input = document.getElementById('customer-input');
        const message = input.value.trim();
        
        if (!message || !sessionId) return;
        
        addCustomerMessage('customer', message, false);
        input.value = '';
        
        // 发送到服务器（这里可以添加实际的发送逻辑）
        logCustomer('📤 客户消息已发送');
    }
    
    // 发送管理员回复
    function sendAdminReply() {
        const input = document.getElementById('admin-input');
        const message = input.value.trim();
        
        if (!message || !sessionDbId) {
            alert('请确保有消息内容和会话数据库ID');
            return;
        }
        
        logAdmin('📤 发送管理员回复...');
        
        // 使用数据库版本发送
        fetch('/admin_reply_test.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=send_reply&session_db_id=${sessionDbId}&reply_message=${encodeURIComponent(message)}`
        })
        .then(response => response.text())
        .then(data => {
            if (data.includes('发送成功')) {
                logAdmin('✅ 管理员回复发送成功');
                addAdminMessage('admin', message);
                input.value = '';
                sentCount++;
                document.getElementById('sent-count').textContent = sentCount;
                logTest('📤 后台回复已发送，等待前台接收...');
            } else {
                logAdmin('❌ 发送失败');
                console.log('服务器响应:', data);
            }
        })
        .catch(error => {
            logAdmin('❌ 网络错误: ' + error.message);
        });
    }
    
    // 快速回复
    function sendQuickReply() {
        const quickReplies = [
            '您好，我是客服，有什么可以帮助您的吗？',
            '感谢您的咨询，我正在为您查询相关信息...',
            '好的，我明白了您的问题，让我为您处理一下。'
        ];
        
        const randomReply = quickReplies[Math.floor(Math.random() * quickReplies.length)];
        document.getElementById('admin-input').value = randomReply + ' [' + new Date().toLocaleTimeString() + ']';
    }
    
    // 添加客户消息
    function addCustomerMessage(sender, message, isNew) {
        const messagesDiv = document.getElementById('customer-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender + (isNew ? ' new' : '');
        
        const time = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${time})</small>`;
        
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
        
        if (isNew) {
            setTimeout(() => messageDiv.classList.remove('new'), 3000);
        }
    }
    
    // 添加管理员消息
    function addAdminMessage(sender, message) {
        const messagesDiv = document.getElementById('admin-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender;
        
        const time = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${time})</small>`;
        
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
    
    // 更新SSE状态
    function updateSSEStatus(text, className) {
        const statusElement = document.getElementById('sse-status');
        statusElement.textContent = text;
        statusElement.className = 'status ' + className;
    }
    
    // 播放提示音
    function playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (e) {
            console.log('无法播放提示音');
        }
    }
    
    // 处理回车键
    function handleCustomerKeyPress(event) {
        if (event.key === 'Enter') {
            sendCustomerMessage();
        }
    }
    
    function handleAdminKeyPress(event) {
        if (event.key === 'Enter') {
            sendAdminReply();
        }
    }
    
    // 日志函数
    function logCustomer(message) {
        const logDiv = document.getElementById('customer-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function logAdmin(message) {
        const logDiv = document.getElementById('admin-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function logTest(message) {
        const logDiv = document.getElementById('test-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    </script>
</body>
</html>
