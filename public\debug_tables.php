<?php
/**
 * 调试数据库表结构
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>调试数据库表结构</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;}</style>";
echo "</head><body>";

echo "<h1>🔍 调试数据库表结构</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 检查客服相关表
    echo "<h2>📋 客服系统相关表</h2>";
    $tables = $pdo->query("SHOW TABLES LIKE '%customer_service%'")->fetchAll();
    
    if (empty($tables)) {
        echo "<p class='error'>❌ 没有找到客服系统相关表</p>";
    } else {
        echo "<table><tr><th>表名</th><th>记录数</th><th>操作</th></tr>";
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM `$tableName`")->fetchColumn();
                echo "<tr><td>$tableName</td><td>$count</td><td><a href='#$tableName'>查看结构</a></td></tr>";
            } catch (Exception $e) {
                echo "<tr><td>$tableName</td><td class='error'>错误: {$e->getMessage()}</td><td>-</td></tr>";
            }
        }
        echo "</table>";
        
        // 显示每个表的结构
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            echo "<h3 id='$tableName'>📊 表结构: $tableName</h3>";
            
            try {
                $columns = $pdo->query("DESCRIBE `$tableName`")->fetchAll();
                echo "<table><tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>{$column['Field']}</td>";
                    echo "<td>{$column['Type']}</td>";
                    echo "<td>{$column['Null']}</td>";
                    echo "<td>{$column['Key']}</td>";
                    echo "<td>{$column['Default']}</td>";
                    echo "<td>{$column['Extra']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // 显示前几条记录
                if (strpos($tableName, 'sessions') !== false) {
                    echo "<h4>📝 前5条记录:</h4>";
                    $records = $pdo->query("SELECT * FROM `$tableName` ORDER BY created_at DESC LIMIT 5")->fetchAll();
                    if (!empty($records)) {
                        echo "<table>";
                        // 表头
                        echo "<tr>";
                        foreach (array_keys($records[0]) as $key) {
                            echo "<th>$key</th>";
                        }
                        echo "</tr>";
                        // 数据
                        foreach ($records as $record) {
                            echo "<tr>";
                            foreach ($record as $value) {
                                $displayValue = is_null($value) ? 'NULL' : (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
                                echo "<td>" . htmlspecialchars($displayValue) . "</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                    } else {
                        echo "<p class='info'>表中暂无数据</p>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ 获取表结构失败: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // 测试具体的查询
    echo "<h2>🧪 测试具体查询</h2>";
    
    // 测试会话查询
    try {
        $sessionCount = $pdo->query("SELECT COUNT(*) FROM st_customer_service_sessions")->fetchColumn();
        echo "<p class='success'>✅ st_customer_service_sessions 表存在，记录数: $sessionCount</p>";
        
        if ($sessionCount > 0) {
            $session = $pdo->query("SELECT * FROM st_customer_service_sessions ORDER BY created_at DESC LIMIT 1")->fetch();
            echo "<p class='info'>最新会话ID: {$session['id']}</p>";
            
            // 测试消息查询
            $messageCount = $pdo->query("SELECT COUNT(*) FROM st_customer_service_messages WHERE session_id = {$session['id']}")->fetchColumn();
            echo "<p class='info'>该会话消息数: $messageCount</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 查询测试失败: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/update_cs_system.php' target='_blank'>更新客服系统</a></li>";
echo "<li><a href='/test_api_direct.php' target='_blank'>测试API</a></li>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "</ul>";

echo "</body></html>";
?>
