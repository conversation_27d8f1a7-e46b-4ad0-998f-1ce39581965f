<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 客服系统测试工具</h1>
        <p>这个页面帮助您测试客服系统的各个组件</p>
        
        <div class="test-section">
            <h2>🧪 测试项目</h2>
            <button onclick="testJSLoad()">测试JS文件加载</button>
            <button onclick="testCSRFToken()">测试CSRF Token</button>
            <button onclick="testAPIEndpoints()">测试API接口</button>
            <button onclick="testChatWidget()">测试聊天组件</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="test-section">
            <h2>📝 测试日志</h2>
            <div id="log"></div>
        </div>
        
        <div class="test-section">
            <h2>💬 模拟聊天测试</h2>
            <div>
                <input type="text" id="testMessage" placeholder="输入测试消息..." style="width: 300px; padding: 8px;">
                <button onclick="sendTestMessage()">发送测试消息</button>
            </div>
        </div>
    </div>

    <script>
        let log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : (type === 'warning' ? 'warning' : ''));
            log.innerHTML += `[${timestamp}] <span class="${className}">${message}</span>\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        function testJSLoad() {
            addLog('测试JavaScript文件加载...');
            
            // 检查customer-service.js是否加载
            const script = document.createElement('script');
            script.src = '/js/customer-service.js';
            script.onload = function() {
                addLog('✅ customer-service.js 加载成功', 'success');
            };
            script.onerror = function() {
                addLog('❌ customer-service.js 加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        function testCSRFToken() {
            addLog('测试CSRF Token...');
            
            const token = document.querySelector('meta[name="csrf-token"]');
            if (token) {
                addLog('✅ CSRF Token 存在: ' + token.getAttribute('content'), 'success');
            } else {
                addLog('❌ CSRF Token 不存在', 'error');
            }
        }
        
        async function testAPIEndpoints() {
            addLog('测试API接口...');
            
            const endpoints = [
                '/customer-service/init',
                '/customer-service/status'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    addLog(`测试接口: ${endpoint}`);
                    const response = await fetch(endpoint, {
                        method: endpoint.includes('init') ? 'POST' : 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 'test'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        addLog(`✅ ${endpoint} 响应正常: ${JSON.stringify(data)}`, 'success');
                    } else {
                        addLog(`⚠️ ${endpoint} 响应状态: ${response.status}`, 'warning');
                    }
                } catch (error) {
                    addLog(`❌ ${endpoint} 请求失败: ${error.message}`, 'error');
                }
            }
        }
        
        function testChatWidget() {
            addLog('测试聊天组件...');
            
            // 检查是否有客服按钮
            const chatButton = document.getElementById('cs-chat-button');
            if (chatButton) {
                addLog('✅ 客服按钮存在', 'success');
            } else {
                addLog('❌ 客服按钮不存在', 'error');
                addLog('尝试手动创建客服按钮...', 'warning');
                createTestChatButton();
            }
        }
        
        function createTestChatButton() {
            // 创建测试用的客服按钮
            const button = document.createElement('div');
            button.id = 'cs-chat-button';
            button.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 60px;
                height: 60px;
                background: #007bff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: white;
                font-size: 24px;
                z-index: 9999;
            `;
            button.innerHTML = '💬';
            button.onclick = function() {
                addLog('客服按钮被点击', 'success');
            };
            document.body.appendChild(button);
            addLog('✅ 测试客服按钮已创建', 'success');
        }
        
        async function sendTestMessage() {
            const message = document.getElementById('testMessage').value.trim();
            if (!message) {
                addLog('❌ 请输入测试消息', 'error');
                return;
            }
            
            addLog(`发送测试消息: ${message}`);
            
            try {
                const response = await fetch('/customer-service/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 'test'
                    },
                    body: JSON.stringify({
                        session_id: 'test_session_' + Date.now(),
                        message: message
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog(`✅ 消息发送成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    addLog(`⚠️ 消息发送失败，状态码: ${response.status}`, 'warning');
                    const text = await response.text();
                    addLog(`响应内容: ${text}`, 'warning');
                }
            } catch (error) {
                addLog(`❌ 发送消息时出错: ${error.message}`, 'error');
            }
            
            document.getElementById('testMessage').value = '';
        }
        
        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testCSRFToken();
                testChatWidget();
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', function(e) {
            addLog(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });
    </script>
</body>
</html>
