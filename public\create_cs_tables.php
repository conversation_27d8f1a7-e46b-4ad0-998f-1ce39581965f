<?php
/**
 * 创建客服系统数据表
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>创建客服数据表</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;} .btn{padding:8px 15px;margin:5px;background:#007bff;color:white;text-decoration:none;border-radius:3px;} details{margin:10px 0;} table{border-collapse:collapse;} th,td{border:1px solid #ddd;padding:5px;}</style>";
echo "</head><body>";

echo "<h1>🛠️ 创建客服系统数据表</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    echo "<p class='info'>📊 数据库: $database</p>";

    // 读取SQL文件
    $sqlFile = dirname(__DIR__) . '/create_cs_tables_simple.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }

    $sql = file_get_contents($sqlFile);
    echo "<p class='info'>📄 读取SQL文件成功</p>";

    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    echo "<h2>📝 执行SQL语句：</h2>";
    
    foreach ($statements as $index => $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            
            // 判断语句类型
            if (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`([^`]+)`/', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "<p class='success'>✅ 创建表: $tableName</p>";
            } elseif (stripos($statement, 'INSERT') !== false) {
                echo "<p class='success'>✅ 插入数据成功</p>";
            } elseif (stripos($statement, 'SET') !== false) {
                echo "<p class='info'>ℹ️ 设置变量</p>";
            } else {
                echo "<p class='success'>✅ 执行成功</p>";
            }

        } catch (Exception $e) {
            echo "<p class='error'>❌ 执行失败: " . $e->getMessage() . "</p>";
        }
    }

    echo "<h2>📊 验证表创建结果：</h2>";

    // 检查表是否创建成功
    $tables = [
        'st_customer_service_sessions' => '客服会话表',
        'st_customer_service_messages' => '客服消息表',
        'st_ai_auto_reply_rules' => 'AI自动回复规则表'
    ];

    foreach ($tables as $table => $description) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                echo "<p class='success'>✅ $description ($table) 存在，记录数: $count</p>";

                // 显示表结构
                echo "<details style='margin-left: 20px;'>";
                echo "<summary>查看表结构和字段注释</summary>";
                echo "<table style='margin:10px 0;'>";
                echo "<tr><th>字段名</th><th>类型</th><th>注释</th></tr>";

                $columns = $pdo->query("SHOW FULL COLUMNS FROM $table")->fetchAll();
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>{$column['Field']}</td>";
                    echo "<td>{$column['Type']}</td>";
                    echo "<td>{$column['Comment']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "</details>";

            } else {
                echo "<p class='error'>❌ $description ($table) 不存在</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 检查表 $table 失败: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🎉 数据表创建完成！</h2>";

    echo "<div style='background:#fff3cd;border:1px solid #ffeaa7;padding:15px;border-radius:5px;margin:15px 0;'>";
    echo "<h3>⚠️ 重要提醒：需要手动添加后台菜单</h3>";
    echo "<p>请在后台管理系统中手动添加以下菜单：</p>";
    echo "<ol>";
    echo "<li><strong>在线客服</strong>（父菜单）</li>";
    echo "<li><strong>会话管理</strong> - 路由：customer-service/sessions</li>";
    echo "<li><strong>AI规则管理</strong> - 路由：customer-service/ai-rules</li>";
    echo "<li><strong>统计报表</strong> - 路由：customer-service/statistics</li>";
    echo "</ol>";
    echo "</div>";

    echo "<p>现在可以测试客服系统了：</p>";
    echo "<div>";
    echo "<a href='test_cs.php' target='_blank' class='btn'>测试前台发送消息</a>";
    echo "<a href='check_cs_data.php' target='_blank' class='btn'>检查数据库数据</a>";
    echo "<a href='check_menu_table.php' target='_blank' class='btn'>检查菜单表结构</a>";
    echo "<a href='/strongadmin/customer-service/sessions' target='_blank' class='btn'>查看后台会话管理</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
