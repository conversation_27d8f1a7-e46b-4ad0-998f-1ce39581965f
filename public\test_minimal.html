<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试页面</title>
    <meta name="csrf-token" content="test-token">
</head>
<body>
    <h1>最小化测试页面</h1>
    <p>这个页面用于逐步测试JavaScript代码，找出语法错误的源头。</p>
    
    <div id="test-results"></div>
    
    <!-- 第一步：基础JavaScript -->
    <script>
        console.log('步骤1: 基础JavaScript测试');
        
        // 测试基础变量
        let testVar = 'hello';
        const testConst = 'world';
        
        // 测试基础函数
        function testFunction() {
            return 'test';
        }
        
        // 测试DOM操作
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            if (resultsDiv) {
                resultsDiv.innerHTML = '<p style="color: green;">✅ 步骤1: 基础JavaScript正常</p>';
            }
        });
        
        console.log('步骤1完成');
    </script>
    
    <!-- 第二步：字符串拼接 -->
    <script>
        console.log('步骤2: 字符串拼接测试');
        
        // 测试字符串拼接（替代模板字符串）
        const name = 'test';
        const message = '测试消息: ' + name;
        
        // 测试复杂的字符串拼接
        const now = new Date().toLocaleTimeString();
        const logMessage = '时间: ' + now + ', 状态: 正常';
        
        console.log('步骤2: ' + logMessage);
        console.log('步骤2完成');
    </script>
    
    <!-- 第三步：条件语句和三元运算符 -->
    <script>
        console.log('步骤3: 条件语句测试');
        
        // 测试三元运算符（之前修复的）
        const field = 'name';
        const fieldKey = field === 'name' ? 'visitor_name' :
                       field === 'email' ? 'visitor_email' :
                       field === 'phone' ? 'visitor_phone' : field;
        
        const fieldName = field === 'name' ? '姓名' :
                        field === 'email' ? '邮箱' :
                        field === 'phone' ? '电话' : '其他';
        
        console.log('字段映射: ' + fieldKey + ' -> ' + fieldName);
        console.log('步骤3完成');
    </script>
    
    <!-- 第四步：DOM查询和事件 -->
    <script>
        console.log('步骤4: DOM查询测试');
        
        // 测试DOM查询
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            const tokenValue = metaToken.getAttribute('content');
            console.log('CSRF Token: ' + tokenValue);
        }
        
        // 测试getElementById
        const resultsDiv = document.getElementById('test-results');
        if (resultsDiv) {
            console.log('找到结果容器');
        }
        
        console.log('步骤4完成');
    </script>
    
    <!-- 第五步：异步操作（不实际执行） -->
    <script>
        console.log('步骤5: 异步操作语法测试');
        
        // 测试fetch语法（不实际执行）
        function testFetchSyntax() {
            const sessionId = 'test123';
            const apiUrl = '/api/test/' + sessionId + '?param=value';
            
            // 这里只测试语法，不实际执行
            const fetchCode = 'fetch(apiUrl, { method: "POST", headers: { "Content-Type": "application/json" } })';
            console.log('Fetch语法测试: ' + fetchCode);
        }
        
        testFetchSyntax();
        console.log('步骤5完成');
    </script>
    
    <!-- 第六步：复杂逻辑 -->
    <script>
        console.log('步骤6: 复杂逻辑测试');
        
        // 测试对象操作
        const testData = {
            session_id: 'test123',
            status: 'online',
            timestamp: new Date().getTime()
        };
        
        // 测试JSON操作
        const jsonString = JSON.stringify(testData);
        console.log('JSON测试: ' + jsonString.substring(0, 50) + '...');
        
        // 测试数组操作
        const testArray = [1, 2, 3, 4, 5];
        testArray.forEach(function(item) {
            console.log('数组项: ' + item);
        });
        
        console.log('步骤6完成');
    </script>
    
    <!-- 第七步：错误处理 -->
    <script>
        console.log('步骤7: 错误处理测试');
        
        try {
            // 测试可能出错的代码
            const testObj = { prop: 'value' };
            const result = testObj.prop || 'default';
            console.log('错误处理测试结果: ' + result);
            
            // 测试条件检查
            if (typeof window !== 'undefined') {
                console.log('Window对象存在');
            }
            
            if (typeof document !== 'undefined') {
                console.log('Document对象存在');
            }
            
        } catch (error) {
            console.error('步骤7出错: ' + error.message);
        }
        
        console.log('步骤7完成');
    </script>
    
    <!-- 最终测试 -->
    <script>
        console.log('最终测试: 所有步骤完成');
        
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            if (resultsDiv) {
                resultsDiv.innerHTML += '<p style="color: blue;">✅ 所有JavaScript测试步骤完成，未发现语法错误</p>';
                resultsDiv.innerHTML += '<p style="color: orange;">如果主页面仍有错误，问题可能在于:</p>';
                resultsDiv.innerHTML += '<ul>';
                resultsDiv.innerHTML += '<li>特定的PHP变量值导致的JavaScript语法错误</li>';
                resultsDiv.innerHTML += '<li>外部脚本文件的问题</li>';
                resultsDiv.innerHTML += '<li>浏览器缓存的旧代码</li>';
                resultsDiv.innerHTML += '<li>动态生成的内容有问题</li>';
                resultsDiv.innerHTML += '</ul>';
            }
        });
    </script>
</body>
</html>
