<?php
/**
 * 最终SSE实现 - 使用正确的表名
 */

// 设置执行时间和内存限制
set_time_limit(0);
ini_set('memory_limit', '128M');

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');

// 禁用所有输出缓冲
while (ob_get_level()) {
    ob_end_clean();
}

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '最终SSE连接成功！',
    'session_id' => $sessionId,
    'timestamp' => time()
]) . "\n\n";
flush();

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

$pdo = null;
$sessionDbId = null;

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "data: " . json_encode([
        'type' => 'database_connected',
        'message' => '数据库连接成功',
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    // 获取或创建会话 - 使用正确的表名 st_customer_service_sessions
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        // 创建新会话
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
        $stmt->execute([
            $sessionId, 
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', 
            $_SERVER['HTTP_USER_AGENT'] ?? 'SSE Client'
        ]);
        $sessionDbId = $pdo->lastInsertId();
        
        echo "data: " . json_encode([
            'type' => 'session_created',
            'message' => '新会话已创建',
            'session_db_id' => $sessionDbId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    } else {
        $sessionDbId = $session['id'];
        
        echo "data: " . json_encode([
            'type' => 'session_found',
            'message' => '找到现有会话',
            'session_db_id' => $sessionDbId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'database_error',
        'message' => '数据库错误: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
}

// 主循环
$startTime = time();
$maxDuration = 300; // 5分钟
$checkInterval = 0.5; // 0.5秒检查一次，更快响应
$heartbeatCounter = 0;

echo "data: " . json_encode([
    'type' => 'loop_start',
    'message' => '开始实时消息检查',
    'session_db_id' => $sessionDbId,
    'timestamp' => time()
]) . "\n\n";
flush();

while (time() - $startTime < $maxDuration) {
    $heartbeatCounter++;
    
    try {
        // 如果有数据库连接和会话，检查新消息
        if ($pdo && $sessionDbId) {
            // 使用正确的表名 st_customer_service_messages
            $stmt = $pdo->prepare("
                SELECT * FROM st_customer_service_messages 
                WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') 
                ORDER BY id ASC
            ");
            $stmt->execute([$sessionDbId, $lastMessageId]);
            $newMessages = $stmt->fetchAll();
            
            if (!empty($newMessages)) {
                echo "data: " . json_encode([
                    'type' => 'debug',
                    'message' => '🎉 检测到 ' . count($newMessages) . ' 条新消息！',
                    'count' => count($newMessages),
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
                
                foreach ($newMessages as $message) {
                    echo "data: " . json_encode([
                        'type' => 'new_reply',
                        'message_id' => $message['id'],
                        'sender_type' => $message['sender_type'],
                        'message' => $message['message'],
                        'created_at' => $message['created_at'],
                        'timestamp' => time()
                    ]) . "\n\n";
                    
                    $lastMessageId = max($lastMessageId, $message['id']);
                }
                flush();
            }
        }
        
        // 每20次循环（10秒）发送心跳包
        if ($heartbeatCounter % 20 == 0) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'message' => '💓 心跳包 #' . ($heartbeatCounter / 20),
                'session_db_id' => $sessionDbId,
                'last_message_id' => $lastMessageId,
                'connection_time' => time() - $startTime,
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 检查客户端连接
        if (connection_aborted()) {
            break;
        }
        
        sleep($checkInterval);
        
    } catch (Exception $e) {
        echo "data: " . json_encode([
            'type' => 'error',
            'message' => '循环错误: ' . $e->getMessage(),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        sleep($checkInterval);
    }
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '最终SSE连接正常结束',
    'connection_time' => time() - $startTime,
    'timestamp' => time()
]) . "\n\n";
flush();
?>
