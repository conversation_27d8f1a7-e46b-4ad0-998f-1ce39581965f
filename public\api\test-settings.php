<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 直接数据库连接，不使用Laravel
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'mostxx_com';
    $username = 'mostxx_com';
    $password = 'fHnrmH9w5nw1pd53';

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 查询设置
    $stmt = $pdo->prepare("
        SELECT setting_key, setting_value, setting_type 
        FROM customer_service_settings 
        WHERE is_active = 1 
        AND category IN ('basic', 'appearance', 'sound', 'offline')
        ORDER BY setting_key
    ");
    $stmt->execute();
    $rawSettings = $stmt->fetchAll();
    
    $settings = [];
    $debug = [];
    
    foreach ($rawSettings as $setting) {
        $key = $setting['setting_key'];
        $value = $setting['setting_value'];
        $type = $setting['setting_type'];
        
        // 记录调试信息
        $debug[$key] = [
            'raw_value' => $value,
            'type' => $type
        ];
        
        // 类型转换
        switch ($type) {
            case 'boolean':
                $converted = $value === '1';
                $debug[$key]['converted'] = $converted;
                $settings[$key] = $converted;
                break;
            case 'number':
                $converted = (float) $value;
                $debug[$key]['converted'] = $converted;
                $settings[$key] = $converted;
                break;
            case 'json':
                $converted = json_decode($value, true) ?: [];
                $debug[$key]['converted'] = $converted;
                $settings[$key] = $converted;
                break;
            case 'color':
                $converted = $value ?: '#667eea';
                $debug[$key]['converted'] = $converted;
                $settings[$key] = $converted;
                break;
            default:
                $debug[$key]['converted'] = $value;
                $settings[$key] = $value;
        }
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $settings,
        'debug' => $debug,
        'query_count' => count($rawSettings)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
