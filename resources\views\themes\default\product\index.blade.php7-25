@extends('layouts.app')

@section('content')
@include('layouts.includes.breadcrumb')
<div class="st-main">
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="row">
            <div class="col-sm-3">
                <!--主体左侧-->
                <div class="st-main-left hidden-xs">
                    <!--现代化产品分类-->
                    <div class="st-main-left-category">
                        <div class="filter-header">
                            <h5>@lang('Categories')</h5>
                        </div>
                        <div style="padding: 10px 0;">
                            @foreach($categories as $category)
                            <div class="category-group">
                                <!-- 一级分类 -->
                                <div class="category-level-1 @if(request('catid')==$category->id) active @endif">
                                    <a href="{{route('product.list.rewrite', ['catid'=>$category->id])}}">
                                        {{$category->name}}
                                    </a>
                                </div>

                                @if($category->children->count() > 0)
                                <div class="category-children">
                                    @foreach($category->children as $child)
                                    <div class="category-level-2">
                                        <div class="@if(request('catid')==$child['id']) active @endif">
                                            <a href="{{route('product.list.rewrite', ['catid'=>$child['id']])}}">
                                                {{$child['name']}}
                                            </a>
                                        </div>

                                        @if(count($child['children']) > 0)
                                        <div class="category-level-3">
                                            @foreach($child['children'] as $childChild)
                                            <div class="@if(request('catid')==$childChild['id']) active @endif">
                                                <a href="{{route('product.list.rewrite', ['catid'=>$childChild['id']])}}">
                                                    • {{$childChild['name']}}
                                                </a>
                                            </div>
                                            @endforeach
                                        </div>
                                        @endif
                                    </div>
                                    @endforeach
                                </div>
                                @endif
                            </div>
                            @endforeach
                        </div>
                    </div>
                    <!--现代化价格筛选-->
                    <div class="st-main-left-price">
                        <div class="filter-header">
                            <h5>@lang('Price')</h5>
                        </div>
                        <div class="filter-content">
                            @foreach($filterPrices as $filterPrice)
                            @if($loop->last)
                            <div class="price-option @if(request('price.min')==$filterPrice['max'] && request('price.max')==null) active @endif">
                                <a href="{{request()->fullUrlWithQuery(['price' => ['min' => (request('price.min')!==null?null:$filterPrice['max']), 'max' => null]])}}">
                                    <span class="custom-checkbox @if(request('price.min')==$filterPrice['max'] && request('price.max')==null) checked @endif">
                                        @if(request('price.min')==$filterPrice['max'] && request('price.max')==null)✓@endif
                                    </span>
                                    &ge; {{$filterPrice['maxLabel']}}
                                </a>
                            </div>
                            @else
                            <div class="price-option @if(request('price.min')==$filterPrice['min'] && request('price.max')==$filterPrice['max']) active @endif">
                                <a href="{{request()->fullUrlWithQuery(['price' => ['min' => (request('price.min')!==null?null:$filterPrice['min']), 'max' => (request('price.max')!==null?null:$filterPrice['max'])]])}}">
                                    <span class="custom-checkbox @if(request('price.min')==$filterPrice['min'] && request('price.max')==$filterPrice['max']) checked @endif">
                                        @if(request('price.min')==$filterPrice['min'] && request('price.max')==$filterPrice['max'])✓@endif
                                    </span>
                                    {{$filterPrice['minLabel']}} - {{$filterPrice['maxLabel']}}
                                </a>
                            </div>
                            @endif
                            @endforeach
                        </div>
                    </div>
                    <!--现代化库存状态筛选-->
                    <div class="st-main-left-stock">
                        <div class="filter-header">
                            <h5>@lang('Stock Status')</h5>
                        </div>
                        <div class="filter-content">
                            <div class="stock-option @if(request('stock')==1) active @endif">
                                <a href="{{request()->fullUrlWithQuery(['stock' => request('stock') ? null : 1])}}">
                                    <span class="custom-checkbox @if(request('stock')==1) checked @endif">
                                        @if(request('stock')==1)✓@endif
                                    </span>
                                    @lang('In Stock')
                                </a>
                            </div>
                            <div class="stock-option @if(request('stock')==2) active @endif">
                                <a href="{{request()->fullUrlWithQuery(['stock' => request('stock') ? null : 2])}}">
                                    <span class="custom-checkbox @if(request('stock')==2) checked @endif">
                                        @if(request('stock')==2)✓@endif
                                    </span>
                                    @lang('Stockout')
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-9">
                <!--主体右侧-->
                <div class="st-main-right st-main-product">
                    <div class="st-main-product-header" style="background: #fff; padding: 20px; margin-bottom: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                        <!--排序-->
                        <div class="st-main-product-header-sortby pull-left" style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 14px; color: #666; font-family: 'Times New Roman', Times, serif;">@lang('Sort By'):</span>
                            <select style="border: 1px solid #ddd; padding: 8px 12px; border-radius: 4px; font-size: 14px; color: #333; background: #fff; font-family: 'Times New Roman', Times, serif;">
                                <option>@lang('default')</option>
                                <option value="1" @if(request('sortBy')==1)selected @endif>@lang('Lowest Price')</option>
                                <option value="2" @if(request('sortBy')==2)selected @endif>@lang('Highest Price')</option>
                                <option value="3" @if(request('sortBy')==3)selected @endif>@lang('Best Views')</option>
                                <option value="4" @if(request('sortBy')==4)selected @endif>@lang('Best Selling')</option>
                            </select>
                        </div>
                        <!--移动端*隐藏-->
                        <div class="st-main-product-header-pager pull-right hidden-xs">
                            <!--分页-->
                            <nav aria-label="Page navigation" style="margin: 0;">
                                {{$rows->links()}}
                            </nav>
                        </div>
                        <!--移动端*显示-->
                        <div class="st-main-product-header-filter pull-right visible-xs-block">
                            <span>@lang('Filter')</span>
                            <div id="FILTER">
                                <span class="bi-x-square st-close"></span>
                                <!--筛选-价格-->
                                <div class="st-main-product-header-filter-price">
                                    <h5>@lang('Price')</h5>
                                    @foreach($filterPrices as $filterPrice)
                                    @if($loop->last)
                                    <div class="checkbox">
                                        <a href="{{request()->fullUrlWithQuery(['price' => ['min' => (request('price.min')!==null?null:$filterPrice['max']), 'max' => null]])}}">
                                            <i class="glyphicon @if(request('price.min')==$filterPrice['max'] && request('price.max')==null) glyphicon-check  @else glyphicon-unchecked @endif"></i>
                                            &ge; {{$filterPrice['maxLabel']}}
                                        </a>
                                    </div>
                                    @else
                                    <div class="checkbox">
                                        <a href="{{request()->fullUrlWithQuery(['price' => ['min' => (request('price.min')!==null?null:$filterPrice['min']), 'max' => (request('price.max')!==null?null:$filterPrice['max'])]])}}">
                                            <i class="glyphicon @if(request('price.min')==$filterPrice['min'] && request('price.max')==$filterPrice['max']) glyphicon-check  @else glyphicon-unchecked @endif"></i>
                                            {{$filterPrice['minLabel']}} - {{$filterPrice['maxLabel']}}
                                        </a>
                                    </div>
                                    @endif
                                    @endforeach
                                </div>
                                <!--筛选-库存状态-->
                                <div class="st-main-product-header-filter-stock">
                                    <h5>@lang('Stock Status')</h5>
                                    <div class="checkbox">
                                        <a href="{{request()->fullUrlWithQuery(['stock' => request('stock') ? null : 1])}}">
                                            <i class="glyphicon @if(request('stock')==1) glyphicon-check  @else glyphicon-unchecked @endif"></i> In Stock
                                        </a>
                                    </div>
                                    <div class="checkbox">
                                        <a href="{{request()->fullUrlWithQuery(['stock' => request('stock') ? null : 2])}}">
                                            <i class="glyphicon @if(request('stock')==2) glyphicon-check  @else glyphicon-unchecked @endif"></i> Oversold
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <!--灵活的产品列表 - 一行3个-->
                    <div style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: flex-start;">
                        @foreach($rows as $row)
                        <div style="width: calc(33.333% - 14px); max-width: 320px; margin-bottom: 30px;">
                            <div class="product-card" style="border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; padding: 0; margin: 0; background: #fff; transition: all 0.3s ease; overflow: hidden;"
                                 onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 4px 20px rgba(0,0,0,0.15)';"
                                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)';">
                                <a href="{{route('product.show.rewrite', ['id'=>$row->id])}}" style="display: block; overflow: hidden; border-radius: 8px 8px 0 0;">
                                    <div style="width: 100%; height: 240px; overflow: hidden; position: relative; background: #f8f8f8; display: flex; align-items: center; justify-content: center;">
                                        <img alt="{{$row->title}}" title="{{$row->title}}" src="{{$row->img_cover}}"
                                             style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; object-position: center; transition: transform 0.3s ease;"
                                             onmouseover="this.style.transform='scale(1.05)';"
                                             onmouseout="this.style.transform='scale(1)';" />
                                    </div>
                                </a>
                                <div style="padding: 12px; text-align: center; background: #fff;">
                                    <h5 title="{{$row->title}}" style="margin: 0 0 5px 0; font-size: 14px; line-height: 1.3; height: 35px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; font-family: 'Times New Roman', Times, serif;">
                                        <a href="{{route('product.show.rewrite', ['id'=>$row->id])}}" style="color: #333; text-decoration: none; font-weight: 400;">{{$row->title}}</a>
                                    </h5>
                                    <p class="st-home-product-price" style="font-size: 16px; font-weight: 600; color: #6e504d; margin: 0 0 8px 0; font-family: 'Times New Roman', Times, serif;">{{$_current_currency_name}} {{$row->sale_price}}</p>
                                    <div>
                                        <button class="btn btn-primary" onclick="Util.addtocart({{$row->id}}, 1)" style="background: #333; border-color: #333; font-family: 'Times New Roman', Times, serif; font-size: 12px; padding: 8px 16px; width: 100%; border-radius: 4px;">@lang('ADD TO CART')</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <!--分页-->
                    <nav aria-label="Page navigation">
                        {{$rows->links()}}
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@push('scripts_bottom')
<script>
    // jQuery功能 - 安全检查
    $(document).ready(function() {
        try {
            // 排序功能 - jQuery版本（降级支持）
            const sortSelect = $(".st-main-product-header-sortby select");
            if (sortSelect.length > 0) {
                sortSelect.off('change').on('change', function () {
                    var val = $(this).val();
                    if (typeof Util !== 'undefined' && Util.setUrlParam) {
                        window.location.href = Util.setUrlParam(window.location.href, 'sortBy', val);
                    } else {
                        // 简单的URL参数设置
                        const url = new URL(window.location);
                        if (val && val !== '@lang('default')') {
                            url.searchParams.set('sortBy', val);
                        } else {
                            url.searchParams.delete('sortBy');
                        }
                        window.location.href = url.toString();
                    }
                });
            }

            // 移动端筛选功能
            const filterBtn = $(".st-main-product-header-filter");
            if (filterBtn.length > 0) {
                filterBtn.off('click').on('click', function () {
                    const filter = $("#FILTER");
                    if (filter.length > 0) {
                        filter.slideDown('fast');
                    }
                });
            }

            // 移动端关闭筛选
            const closeBtn = $("#FILTER .st-close");
            if (closeBtn.length > 0) {
                closeBtn.off('click').on('click', function (event) {
                    const filter = $("#FILTER");
                    if (filter.length > 0) {
                        filter.slideUp('fast');
                    }
                    event.stopPropagation();
                });
            }
        } catch (error) {
            console.warn('jQuery功能初始化错误:', error);
        }
    });

    // 现代化产品列表功能
    document.addEventListener("DOMContentLoaded", function(){
        try {
            // 价格格式化
            const priceElements = document.querySelectorAll('.st-home-product-price');
            if (priceElements && priceElements.length > 0) {
                priceElements.forEach(e => {
                    if (e && e.textContent) {
                        e.textContent = e.textContent.replace(/(\d+\.\d{3})/, m => (+m).toFixed(2));
                    }
                });
            }
        } catch (error) {
            console.warn('价格格式化错误:', error);
        }
    });
</script>
@endpush