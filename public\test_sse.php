<?php
/**
 * 测试专用SSE - 使用测试数据
 */

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '测试SSE连接成功',
    'session_id' => $sessionId,
    'last_message_id' => $lastMessageId,
    'timestamp' => time()
]) . "\n\n";
flush();

// 检查是否有测试数据
$testDataFile = 'test_session_data.txt';
if (file_exists($testDataFile)) {
    $testData = json_decode(file_get_contents($testDataFile), true);
    
    echo "data: " . json_encode([
        'type' => 'test_data_found',
        'message' => '找到测试数据',
        'data' => $testData,
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    // 如果是测试会话，发送测试回复
    if ($sessionId === $testData['session_id']) {
        sleep(3); // 等待3秒
        
        echo "data: " . json_encode([
            'type' => 'new_reply',
            'message_id' => $testData['admin_message_id'],
            'sender_type' => 'admin',
            'message' => '这是来自测试数据的管理员回复！',
            'created_at' => date('Y-m-d H:i:s'),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
}

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "data: " . json_encode([
        'type' => 'database_connected',
        'message' => '数据库连接成功',
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    // 获取会话
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if ($session) {
        $sessionDbId = $session['id'];
        
        echo "data: " . json_encode([
            'type' => 'session_found',
            'message' => '找到会话',
            'session_db_id' => $sessionDbId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        
        // 实时消息检查循环
        $checkInterval = 1; // 1秒检查一次，更频繁
        $maxDuration = 300; // 最大连接时间5分钟
        $startTime = time();
        $heartbeatCounter = 0;

        while (time() - $startTime < $maxDuration) {
            try {
                // 检查新的回复消息
                $stmt = $pdo->prepare("
                    SELECT * FROM st_customer_service_messages
                    WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin')
                    ORDER BY id ASC
                ");
                $stmt->execute([$sessionDbId, $lastMessageId]);
                $newMessages = $stmt->fetchAll();

                if (!empty($newMessages)) {
                    echo "data: " . json_encode([
                        'type' => 'debug',
                        'message' => '🎉 实时检测到 ' . count($newMessages) . ' 条新消息！',
                        'count' => count($newMessages),
                        'session_db_id' => $sessionDbId,
                        'last_message_id' => $lastMessageId,
                        'timestamp' => time()
                    ]) . "\n\n";
                    flush();

                    foreach ($newMessages as $message) {
                        echo "data: " . json_encode([
                            'type' => 'new_reply',
                            'message_id' => $message['id'],
                            'sender_type' => $message['sender_type'],
                            'message' => $message['message'],
                            'created_at' => $message['created_at'],
                            'timestamp' => time()
                        ]) . "\n\n";

                        $lastMessageId = max($lastMessageId, $message['id']);
                    }
                    flush();
                }

                // 每10秒发送心跳包和调试信息
                $heartbeatCounter++;
                if ($heartbeatCounter % 10 == 0) {
                    echo "data: " . json_encode([
                        'type' => 'heartbeat',
                        'message' => '💓 心跳包 #' . ($heartbeatCounter / 10),
                        'session_db_id' => $sessionDbId,
                        'last_message_id' => $lastMessageId,
                        'checking_for_messages' => true,
                        'timestamp' => time()
                    ]) . "\n\n";
                    flush();
                }

                // 每30秒发送详细状态
                if ($heartbeatCounter % 30 == 0) {
                    $totalMessages = $pdo->prepare("SELECT COUNT(*) FROM st_customer_service_messages WHERE session_id = ?");
                    $totalMessages->execute([$sessionDbId]);
                    $messageCount = $totalMessages->fetchColumn();

                    echo "data: " . json_encode([
                        'type' => 'status',
                        'message' => '📊 连接状态正常',
                        'session_db_id' => $sessionDbId,
                        'total_messages' => $messageCount,
                        'last_message_id' => $lastMessageId,
                        'connection_time' => time() - $startTime,
                        'timestamp' => time()
                    ]) . "\n\n";
                    flush();
                }

                // 检查连接
                if (connection_aborted()) {
                    break;
                }

                sleep($checkInterval);

            } catch (Exception $e) {
                echo "data: " . json_encode([
                    'type' => 'error',
                    'message' => 'Loop error: ' . $e->getMessage(),
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
                break;
            }
        }
        
    } else {
        echo "data: " . json_encode([
            'type' => 'session_not_found',
            'message' => '会话不存在，会话ID: ' . $sessionId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'database_error',
        'message' => 'Database error: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '测试SSE连接结束',
    'timestamp' => time()
]) . "\n\n";
flush();
?>
