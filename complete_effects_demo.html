<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ 完整效果演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #0a0a0a;
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(20,20,20,0.9) 100%);
            border-radius: 10px;
            border: 1px solid #333;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 300;
            letter-spacing: 3px;
        }
        
        /* 酷炫高端标题样式 */
        .elite-section-header {
            position: relative;
            margin: 40px 0 30px 0;
            padding: 0;
            background: transparent;
        }

        .elite-title-container {
            position: relative;
            display: flex;
            align-items: center;
            padding: 20px 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(20,20,20,0.95) 100%);
            border-radius: 0;
            border-left: 4px solid #00d4ff;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .elite-section-header.new-products .elite-title-container {
            border-left-color: #00ff88;
            background: linear-gradient(135deg, rgba(0,20,0,0.95) 0%, rgba(0,40,20,0.95) 100%);
        }

        .elite-section-header.hot-products .elite-title-container {
            border-left-color: #ff3366;
            background: linear-gradient(135deg, rgba(20,0,0,0.95) 0%, rgba(40,0,20,0.95) 100%);
        }

        .elite-icon-bar {
            width: 6px;
            height: 60px;
            background: linear-gradient(180deg, #00d4ff 0%, #0099cc 100%);
            margin-right: 25px;
            margin-left: 20px;
            border-radius: 3px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            animation: elitePulse 2s ease-in-out infinite;
        }

        .elite-section-header.new-products .elite-icon-bar {
            background: linear-gradient(180deg, #00ff88 0%, #00cc66 100%);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .elite-section-header.hot-products .elite-icon-bar {
            background: linear-gradient(180deg, #ff3366 0%, #cc1144 100%);
            box-shadow: 0 0 20px rgba(255, 51, 102, 0.5);
        }

        .elite-title {
            color: #ffffff;
            font-size: 24px;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-decoration: none;
            margin: 0;
            position: relative;
            transition: all 0.3s ease;
            font-family: 'Arial', sans-serif;
        }

        .elite-title:hover {
            color: #00d4ff;
            text-decoration: none;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        .elite-section-header.new-products .elite-title:hover {
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .elite-section-header.hot-products .elite-title:hover {
            color: #ff3366;
            text-shadow: 0 0 10px rgba(255, 51, 102, 0.8);
        }

        .elite-glow-line {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            width: 0%;
            background: linear-gradient(90deg, #00d4ff 0%, #0099cc 100%);
            transition: width 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        .elite-section-header.new-products .elite-glow-line {
            background: linear-gradient(90deg, #00ff88 0%, #00cc66 100%);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .elite-section-header.hot-products .elite-glow-line {
            background: linear-gradient(90deg, #ff3366 0%, #cc1144 100%);
            box-shadow: 0 0 10px rgba(255, 51, 102, 0.8);
        }

        .elite-title-container:hover .elite-glow-line {
            width: 100%;
        }

        .elite-title-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            transition: left 0.8s ease;
        }

        .elite-title-container:hover::before {
            left: 100%;
        }

        .elite-title-container:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
        }

        /* 产品网格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .product-card {
            background: rgba(20,20,20,0.8);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 1px solid #333;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.5);
            border-color: #00d4ff;
        }

        .product-hover-effect {
            position: relative;
            overflow: hidden;
            display: block;
            height: 200px;
        }

        .product-main-img, .product-hover-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.4s ease, transform 0.4s ease;
        }

        .product-hover-img {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            z-index: 2;
        }

        .product-hover-effect:hover .product-main-img {
            transform: scale(1.05);
        }

        .product-hover-effect:hover .product-hover-img {
            opacity: 1;
            transform: scale(1.05);
        }

        .product-info {
            padding: 20px;
            text-align: center;
        }

        .product-title {
            color: #fff;
            font-size: 16px;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }

        .product-card:hover .product-title {
            color: #00d4ff;
        }

        .product-price {
            color: #ff6b6b;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .product-card:hover .product-price {
            transform: scale(1.05);
            color: #ff5252;
        }

        /* 动画效果 */
        @keyframes elitePulse {
            0%, 100% { 
                transform: scaleY(1);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            }
            50% { 
                transform: scaleY(1.1);
                box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
            }
        }

        .demo-section {
            background: rgba(10,10,10,0.8);
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #333;
        }

        .demo-title {
            color: #00d4ff;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .feature-list {
            list-style: none;
            margin: 20px 0;
        }

        .feature-list li {
            padding: 8px 0;
            color: #ccc;
            display: flex;
            align-items: center;
        }

        .feature-list li:before {
            content: "⚡";
            margin-right: 10px;
            color: #00d4ff;
        }

        @media (max-width: 768px) {
            .elite-title {
                font-size: 18px;
                letter-spacing: 1px;
            }
            
            .elite-icon-bar {
                width: 4px;
                height: 40px;
                margin-right: 15px;
                margin-left: 15px;
            }
            
            .product-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 完整效果演示</h1>
            <p style="font-size: 1.2rem; opacity: 0.8;">酷炫标题 + 图片悬停切换</p>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🎯 酷炫高端标题效果</h2>
            
            <!-- 推荐产品 -->
            <div class="elite-section-header">
                <div class="elite-title-container">
                    <div class="elite-icon-bar"></div>
                    <h4><a href="#" class="elite-title">Recommend Products</a></h4>
                    <div class="elite-glow-line"></div>
                </div>
            </div>
            
            <!-- 新品 -->
            <div class="elite-section-header new-products">
                <div class="elite-title-container">
                    <div class="elite-icon-bar"></div>
                    <h4><a href="#" class="elite-title">New Products</a></h4>
                    <div class="elite-glow-line"></div>
                </div>
            </div>
            
            <!-- 热卖 -->
            <div class="elite-section-header hot-products">
                <div class="elite-title-container">
                    <div class="elite-icon-bar"></div>
                    <h4><a href="#" class="elite-title">Hot Products</a></h4>
                    <div class="elite-glow-line"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🖼️ 产品图片悬停切换效果</h2>
            <p style="color: #ccc; margin-bottom: 20px;">鼠标悬停在产品图片上查看第二张图片</p>
            
            <div class="product-grid">
                <div class="product-card">
                    <a href="#" class="product-hover-effect">
                        <img src="https://via.placeholder.com/300x200/4facfe/ffffff?text=Product+1" 
                             class="product-main-img" alt="Product 1">
                        <img src="https://via.placeholder.com/300x200/00f2fe/ffffff?text=Hover+Image+1" 
                             class="product-hover-img" alt="Product 1 Hover">
                    </a>
                    <div class="product-info">
                        <h3 class="product-title">酷炫产品 1</h3>
                        <p class="product-price">$99.99</p>
                    </div>
                </div>
                
                <div class="product-card">
                    <a href="#" class="product-hover-effect">
                        <img src="https://via.placeholder.com/300x200/ff6b6b/ffffff?text=Product+2" 
                             class="product-main-img" alt="Product 2">
                        <img src="https://via.placeholder.com/300x200/ff8e53/ffffff?text=Hover+Image+2" 
                             class="product-hover-img" alt="Product 2 Hover">
                    </a>
                    <div class="product-info">
                        <h3 class="product-title">酷炫产品 2</h3>
                        <p class="product-price">$149.99</p>
                    </div>
                </div>
                
                <div class="product-card">
                    <a href="#" class="product-hover-effect">
                        <img src="https://via.placeholder.com/300x200/00ff88/ffffff?text=Product+3" 
                             class="product-main-img" alt="Product 3">
                        <img src="https://via.placeholder.com/300x200/00cc66/ffffff?text=Hover+Image+3" 
                             class="product-hover-img" alt="Product 3 Hover">
                    </a>
                    <div class="product-info">
                        <h3 class="product-title">酷炫产品 3</h3>
                        <p class="product-price">$199.99</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">✨ 实现的效果特点</h2>
            <ul class="feature-list">
                <li>酷炫的霓虹光效标题动画</li>
                <li>流畅的产品图片悬停切换</li>
                <li>高端的暗黑科技风格</li>
                <li>响应式设计适配各种设备</li>
                <li>硬件加速的流畅动画</li>
                <li>完美的用户交互体验</li>
            </ul>
        </div>
    </div>
</body>
</html>
