<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CustomerService\AiAutoReplyRule;

class CustomerServiceSeeder extends Seeder
{
    public function run()
    {
        // 清空现有数据
        AiAutoReplyRule::truncate();

        // 创建AI自动回复规则
        $rules = [
            [
                'name' => 'Greeting',
                'keywords' => ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening'],
                'reply_message' => 'Hello! 👋 Welcome to our store! How can I help you today?',
                'priority' => 10
            ],
            [
                'name' => 'Shipping Inquiry',
                'keywords' => ['shipping', 'delivery', 'ship', 'when will', 'how long', 'tracking'],
                'reply_message' => 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days, and express delivery takes 3-7 business days. You\'ll receive a tracking number once your order ships. Would you like to know more about our shipping options?',
                'priority' => 9
            ],
            [
                'name' => 'Payment Methods',
                'keywords' => ['payment', 'pay', 'credit card', 'paypal', 'visa', 'mastercard'],
                'reply_message' => 'We accept various payment methods including: 💳 Credit/Debit Cards (Visa, Mastercard, AMEX), PayPal, and other secure payment options. All transactions are encrypted and secure. Is there a specific payment method you\'d like to use?',
                'priority' => 8
            ],
            [
                'name' => 'Return Policy',
                'keywords' => ['return', 'refund', 'exchange', 'money back', 'warranty'],
                'reply_message' => 'We have a 30-day return policy! 🔄 If you\'re not completely satisfied with your purchase, you can return it within 30 days for a full refund or exchange. Items must be in original condition. Would you like me to explain the return process?',
                'priority' => 8
            ],
            [
                'name' => 'Product Availability',
                'keywords' => ['in stock', 'available', 'out of stock', 'when available', 'restock'],
                'reply_message' => 'Let me check the availability for you! 📦 Most of our products are in stock and ready to ship. If a specific item is out of stock, we usually restock within 1-2 weeks. Which product are you interested in?',
                'priority' => 7
            ],
            [
                'name' => 'Size Guide',
                'keywords' => ['size', 'sizing', 'fit', 'measurements', 'what size'],
                'reply_message' => 'I\'d be happy to help you find the right size! 📏 We have detailed size charts for all our products. You can find the size guide on each product page, or I can help you determine the best size based on your measurements. What product are you looking at?',
                'priority' => 7
            ],
            [
                'name' => 'Discount Codes',
                'keywords' => ['discount', 'coupon', 'promo code', 'sale', 'offer', 'deal'],
                'reply_message' => 'Great question! 🎉 We regularly have special offers and discount codes. Sign up for our newsletter to get exclusive deals, or check our homepage for current promotions. I can also help you find the best deals on specific products!',
                'priority' => 6
            ],
            [
                'name' => 'Product Information',
                'keywords' => ['material', 'quality', 'made of', 'specifications', 'details'],
                'reply_message' => 'I\'d love to help you learn more about our products! 🔍 All our items come with detailed descriptions, materials, and specifications. You can find this information on each product page, or feel free to ask me about any specific product you\'re interested in!',
                'priority' => 6
            ],
            [
                'name' => 'Contact Information',
                'keywords' => ['contact', 'phone', 'email', 'address', 'location'],
                'reply_message' => 'You can reach us in several ways! 📞 Email: support@' . config('app.url', 'example.com') . ' | Live Chat: Right here! | We typically respond within 24 hours. Is there something specific I can help you with right now?',
                'priority' => 5
            ],
            [
                'name' => 'Thank You',
                'keywords' => ['thank you', 'thanks', 'appreciate', 'helpful'],
                'reply_message' => 'You\'re very welcome! 😊 I\'m so glad I could help! If you have any other questions, feel free to ask anytime. Have a wonderful day!',
                'priority' => 4
            ],
            [
                'name' => 'Goodbye',
                'keywords' => ['bye', 'goodbye', 'see you', 'talk later', 'have a good day'],
                'reply_message' => 'Goodbye! 👋 Thank you for visiting our store. Feel free to come back anytime if you have more questions. Have a great day!',
                'priority' => 4
            ],
            [
                'name' => 'Order Status',
                'keywords' => ['order', 'order status', 'my order', 'order number', 'where is my order'],
                'reply_message' => 'I can help you check your order status! 📋 Please provide your order number, and I\'ll look it up for you. You can also check your order status by logging into your account on our website.',
                'priority' => 9
            ],
            [
                'name' => 'Technical Support',
                'keywords' => ['not working', 'broken', 'problem', 'issue', 'help', 'support'],
                'reply_message' => 'I\'m sorry to hear you\'re experiencing an issue! 🛠️ I\'m here to help resolve any problems. Could you please describe what\'s happening in more detail? This will help me provide the best solution for you.',
                'priority' => 8
            ]
        ];

        foreach ($rules as $rule) {
            AiAutoReplyRule::create($rule);
        }

        $this->command->info('Customer service AI rules seeded successfully!');
    }
}
