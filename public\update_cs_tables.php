<?php
/**
 * 更新客服系统数据表 - 添加未读消息字段
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>更新客服数据表</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔄 更新客服系统数据表</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 检查是否已经有unread_count字段
    $stmt = $pdo->query("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'unread_count'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='info'>ℹ️ unread_count字段已存在，跳过添加</p>";
    } else {
        // 添加unread_count字段
        $sql = "ALTER TABLE st_customer_service_sessions ADD COLUMN unread_count int(11) NOT NULL DEFAULT 0 COMMENT '未读消息数量' AFTER assigned_admin_id";
        $pdo->exec($sql);
        echo "<p class='success'>✅ 成功添加unread_count字段</p>";
    }
    
    // 初始化未读消息数量
    echo "<h2>📊 初始化未读消息数量</h2>";
    
    $sessions = $pdo->query("SELECT id FROM st_customer_service_sessions")->fetchAll();
    foreach ($sessions as $session) {
        $sessionId = $session['id'];
        
        // 计算未读的客户消息数量
        $unreadCount = $pdo->query("
            SELECT COUNT(*) 
            FROM st_customer_service_messages 
            WHERE session_id = $sessionId 
            AND sender_type = 'customer' 
            AND is_read = 0
        ")->fetchColumn();
        
        // 更新会话的未读数量
        $pdo->exec("UPDATE st_customer_service_sessions SET unread_count = $unreadCount WHERE id = $sessionId");
        
        if ($unreadCount > 0) {
            echo "<p class='info'>会话 $sessionId: $unreadCount 条未读消息</p>";
        }
    }
    
    echo "<h2>🎉 更新完成！</h2>";
    echo "<p>现在可以测试未读消息功能了：</p>";
    echo "<ul>";
    echo "<li><a href='test_cs.php' target='_blank'>发送测试消息</a></li>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>查看后台会话管理</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
