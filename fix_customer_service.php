<?php
/**
 * 客服系统修复脚本
 * 运行: php fix_customer_service.php
 */

echo "🔧 客服系统修复工具\n";
echo "==================\n\n";

// 检查Laravel环境
if (!file_exists('artisan')) {
    echo "❌ 错误: 请在Laravel项目根目录运行此脚本\n";
    exit(1);
}

try {
    echo "1. 强制运行数据库迁移...\n";
    $output = shell_exec('php artisan migrate --force 2>&1');
    echo $output . "\n";

    echo "2. 清除所有缓存...\n";
    $commands = [
        'php artisan cache:clear',
        'php artisan config:clear', 
        'php artisan route:clear',
        'php artisan view:clear'
    ];

    foreach ($commands as $cmd) {
        $output = shell_exec($cmd . ' 2>&1');
        echo "   $cmd: " . trim($output) . "\n";
    }

    echo "\n3. 手动创建数据表...\n";
    
    // 启动Laravel应用
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

    // 手动创建表
    $sql = "
    CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `session_id` varchar(255) NOT NULL,
        `user_id` bigint(20) unsigned DEFAULT NULL,
        `visitor_name` varchar(255) DEFAULT NULL,
        `visitor_email` varchar(255) DEFAULT NULL,
        `visitor_ip` varchar(255) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active',
        `assigned_admin_id` bigint(20) unsigned DEFAULT NULL,
        `last_activity` timestamp NULL DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `session_id` (`session_id`),
        KEY `assigned_admin_id` (`assigned_admin_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

    CREATE TABLE IF NOT EXISTS `customer_service_messages` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `session_id` bigint(20) unsigned NOT NULL,
        `sender_type` enum('customer','admin','ai') NOT NULL,
        `sender_id` bigint(20) unsigned DEFAULT NULL,
        `message` text NOT NULL,
        `attachments` json DEFAULT NULL,
        `is_read` tinyint(1) NOT NULL DEFAULT 0,
        `read_at` timestamp NULL DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `session_id` (`session_id`),
        CONSTRAINT `customer_service_messages_session_id_foreign` FOREIGN KEY (`session_id`) REFERENCES `customer_service_sessions` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

    CREATE TABLE IF NOT EXISTS `ai_auto_reply_rules` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `keywords` json NOT NULL,
        `reply_message` text NOT NULL,
        `priority` int(11) NOT NULL DEFAULT 0,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `usage_count` int(11) NOT NULL DEFAULT 0,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `is_active_priority` (`is_active`,`priority`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

    CREATE TABLE IF NOT EXISTS `customer_service_settings` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `key` varchar(255) NOT NULL,
        `value` text DEFAULT NULL,
        `description` varchar(255) DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    $statements = explode(';', $sql);
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                DB::statement($statement);
                echo "✅ 执行SQL成功\n";
            } catch (Exception $e) {
                echo "⚠️ SQL执行警告: " . $e->getMessage() . "\n";
            }
        }
    }

    echo "\n4. 手动添加后台菜单...\n";
    
    // 检查菜单是否存在
    $existingMenu = DB::table('strongadmin_menu')->where('name', '在线客服')->first();
    
    if (!$existingMenu) {
        // 添加主菜单
        $parentId = DB::table('strongadmin_menu')->insertGetId([
            'level' => 1,
            'parent_id' => 0,
            'name' => '在线客服',
            'route_url' => '',
            'permissions' => '',
            'status' => 1,
            'sort' => 90,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // 添加子菜单
        $subMenus = [
            ['name' => '会话管理', 'route_url' => 'customer-service/sessions', 'sort' => 1],
            ['name' => 'AI规则管理', 'route_url' => 'customer-service/ai-rules', 'sort' => 2],
            ['name' => '统计报表', 'route_url' => 'customer-service/statistics', 'sort' => 3]
        ];

        foreach ($subMenus as $menu) {
            DB::table('strongadmin_menu')->insert([
                'level' => 2,
                'parent_id' => $parentId,
                'name' => $menu['name'],
                'route_url' => $menu['route_url'],
                'permissions' => '',
                'status' => 1,
                'sort' => $menu['sort'],
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
        
        echo "✅ 后台菜单添加成功\n";
    } else {
        echo "✅ 后台菜单已存在\n";
    }

    echo "\n5. 添加默认AI规则...\n";
    
    $rulesCount = DB::table('ai_auto_reply_rules')->count();
    if ($rulesCount == 0) {
        $rules = [
            [
                'name' => '问候语',
                'keywords' => json_encode(['hello', 'hi', 'hey', '你好', '您好']),
                'reply_message' => 'Hello! 👋 Welcome to our store! How can I help you today?',
                'priority' => 10,
                'is_active' => 1,
                'usage_count' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '物流咨询',
                'keywords' => json_encode(['shipping', 'delivery', '物流', '快递', '发货']),
                'reply_message' => 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You\'ll receive a tracking number once your order ships.',
                'priority' => 9,
                'is_active' => 1,
                'usage_count' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '退换货',
                'keywords' => json_encode(['return', 'refund', '退货', '退款', '换货']),
                'reply_message' => 'We have a 30-day return policy! 🔄 If you\'re not satisfied, you can return items within 30 days for a full refund.',
                'priority' => 8,
                'is_active' => 1,
                'usage_count' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($rules as $rule) {
            DB::table('ai_auto_reply_rules')->insert($rule);
        }
        
        echo "✅ AI规则添加成功\n";
    } else {
        echo "✅ AI规则已存在 ($rulesCount 条)\n";
    }

    echo "\n6. 创建头像目录...\n";
    $imageDir = 'public/images';
    if (!is_dir($imageDir)) {
        mkdir($imageDir, 0755, true);
        echo "✅ 创建目录: $imageDir\n";
    } else {
        echo "✅ 目录已存在: $imageDir\n";
    }

    echo "\n7. 最终检查...\n";
    
    // 检查表
    $tables = ['customer_service_sessions', 'customer_service_messages', 'ai_auto_reply_rules'];
    foreach ($tables as $table) {
        $exists = DB::select("SHOW TABLES LIKE '$table'");
        echo $exists ? "✅ 表 $table 存在\n" : "❌ 表 $table 不存在\n";
    }

    // 检查菜单
    $menu = DB::table('strongadmin_menu')->where('name', '在线客服')->first();
    echo $menu ? "✅ 后台菜单存在\n" : "❌ 后台菜单不存在\n";

    // 检查AI规则
    $rulesCount = DB::table('ai_auto_reply_rules')->count();
    echo "✅ AI规则数量: $rulesCount\n";

    echo "\n🎉 修复完成!\n\n";
    echo "📋 下一步:\n";
    echo "1. 访问网站首页，检查右下角是否有客服按钮\n";
    echo "2. 登录后台，查看是否有【在线客服】菜单\n";
    echo "3. 如果还有问题，请检查浏览器控制台错误信息\n\n";

} catch (Exception $e) {
    echo "❌ 修复过程中出现错误: " . $e->getMessage() . "\n";
    echo "详细错误: " . $e->getTraceAsString() . "\n";
}

echo "修复脚本执行完成!\n";
?>
