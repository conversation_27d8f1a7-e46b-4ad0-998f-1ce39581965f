<?php
/**
 * 检查客服系统数据
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>客服数据检查</title>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background:#f2f2f2;} .success{color:green;} .error{color:red;}</style>";
echo "</head><body>";

echo "<h1>🔍 客服系统数据检查</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 检查会话表
    echo "<h2>📋 客服会话数据</h2>";
    $stmt = $pdo->query("SELECT * FROM customer_service_sessions ORDER BY created_at DESC LIMIT 10");
    $sessions = $stmt->fetchAll();
    
    if ($sessions) {
        echo "<table>";
        echo "<tr><th>ID</th><th>会话ID</th><th>访客IP</th><th>状态</th><th>创建时间</th><th>最后活动</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>{$session['visitor_ip']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['created_at']}</td>";
            echo "<td>{$session['last_activity']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ 暂无会话数据</p>";
    }
    
    // 检查消息表
    echo "<h2>💬 客服消息数据</h2>";
    $stmt = $pdo->query("SELECT m.*, s.session_id FROM customer_service_messages m LEFT JOIN customer_service_sessions s ON m.session_id = s.id ORDER BY m.created_at DESC LIMIT 20");
    $messages = $stmt->fetchAll();
    
    if ($messages) {
        echo "<table>";
        echo "<tr><th>ID</th><th>会话ID</th><th>发送者类型</th><th>消息内容</th><th>创建时间</th></tr>";
        foreach ($messages as $message) {
            echo "<tr>";
            echo "<td>{$message['id']}</td>";
            echo "<td>{$message['session_id']} ({$message['session_id']})</td>";
            echo "<td>{$message['sender_type']}</td>";
            echo "<td>" . htmlspecialchars($message['message']) . "</td>";
            echo "<td>{$message['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ 暂无消息数据</p>";
    }
    
    // 检查AI规则
    echo "<h2>🤖 AI规则数据</h2>";
    $stmt = $pdo->query("SELECT * FROM ai_auto_reply_rules ORDER BY priority DESC");
    $rules = $stmt->fetchAll();
    
    if ($rules) {
        echo "<table>";
        echo "<tr><th>ID</th><th>规则名称</th><th>关键词</th><th>回复内容</th><th>优先级</th><th>使用次数</th><th>状态</th></tr>";
        foreach ($rules as $rule) {
            echo "<tr>";
            echo "<td>{$rule['id']}</td>";
            echo "<td>{$rule['name']}</td>";
            echo "<td>{$rule['keywords']}</td>";
            echo "<td>" . htmlspecialchars(substr($rule['reply_message'], 0, 50)) . "...</td>";
            echo "<td>{$rule['priority']}</td>";
            echo "<td>{$rule['usage_count']}</td>";
            echo "<td>" . ($rule['is_active'] ? '启用' : '禁用') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ 暂无AI规则</p>";
    }
    
    // 统计信息
    echo "<h2>📊 统计信息</h2>";
    $stats = [
        '总会话数' => $pdo->query("SELECT COUNT(*) FROM customer_service_sessions")->fetchColumn(),
        '总消息数' => $pdo->query("SELECT COUNT(*) FROM customer_service_messages")->fetchColumn(),
        '客户消息数' => $pdo->query("SELECT COUNT(*) FROM customer_service_messages WHERE sender_type = 'customer'")->fetchColumn(),
        'AI回复数' => $pdo->query("SELECT COUNT(*) FROM customer_service_messages WHERE sender_type = 'ai'")->fetchColumn(),
        'AI规则数' => $pdo->query("SELECT COUNT(*) FROM ai_auto_reply_rules")->fetchColumn(),
    ];
    
    echo "<table>";
    foreach ($stats as $label => $value) {
        echo "<tr><td><strong>$label</strong></td><td>$value</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<p><button onclick='window.close()'>关闭窗口</button> <button onclick='location.reload()'>刷新数据</button></p>";
echo "</body></html>";
?>
