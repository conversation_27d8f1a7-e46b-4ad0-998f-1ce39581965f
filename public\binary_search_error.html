<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二分查找语法错误</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 二分查找语法错误</h1>
        <p>使用二分法逐步缩小错误范围，精确定位语法错误位置</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="startBinarySearch()">开始二分查找</button>
            <button class="test-btn" onclick="testSpecificLines()">测试特定行</button>
            <button class="test-btn danger" onclick="showManualFix()">显示手动修复方案</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>🔍 二分查找工具已就绪</h3>
                <p>这个工具将使用二分法来精确定位JavaScript语法错误</p>
                <p><strong>当前错误:</strong> SyntaxError: missing ) after argument list (第2681行)</p>
            </div>
        </div>
    </div>

    <script>
        function updateDisplay(content, type = 'success') {
            const display = document.getElementById('result-display');
            const className = type === 'error' ? 'error-box' : 
                            type === 'warning' ? 'warning-box' : 'success-box';
            display.innerHTML = '<div class="' + className + '">' + content + '</div>';
        }

        async function startBinarySearch() {
            updateDisplay('<h3>🔍 开始二分查找...</h3>', 'warning');
            
            try {
                const response = await fetch('/');
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                
                const pageContent = await response.text();
                const allLines = pageContent.split('\n');
                const totalLines = allLines.length;
                
                let result = '<h3>🎯 二分查找结果</h3>';
                result += '<p><strong>页面总行数:</strong> ' + totalLines + '</p>';
                result += '<p><strong>错误报告行号:</strong> 2681</p>';
                
                if (totalLines >= 2681) {
                    const targetLine = allLines[2680]; // 数组索引从0开始
                    
                    result += '<h4>🔍 第2681行内容:</h4>';
                    result += '<div style="background: #ffcdd2; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;">';
                    result += escapeHtml(targetLine);
                    result += '</div>';
                    
                    // 分析这一行的语法
                    result += '<h4>🔬 语法分析:</h4>';
                    result += '<div style="background: #e3f2fd; padding: 15px; border-radius: 4px;">';
                    result += '<p><strong>长度:</strong> ' + targetLine.length + ' 字符</p>';
                    result += '<p><strong>左括号:</strong> ' + (targetLine.match(/\(/g) || []).length + ' 个</p>';
                    result += '<p><strong>右括号:</strong> ' + (targetLine.match(/\)/g) || []).length + ' 个</p>';
                    
                    // 检查是否是JavaScript代码
                    const isJavaScript = targetLine.includes('function') || 
                                       targetLine.includes('var ') || 
                                       targetLine.includes('let ') || 
                                       targetLine.includes('const ') || 
                                       targetLine.includes('console.') || 
                                       targetLine.includes('document.') || 
                                       targetLine.includes('window.') ||
                                       targetLine.includes('alert(') ||
                                       targetLine.includes('fetch(');
                    
                    result += '<p><strong>包含JavaScript代码:</strong> ' + (isJavaScript ? '是' : '否') + '</p>';
                    
                    if (isJavaScript) {
                        // 尝试解析这一行的JavaScript语法
                        try {
                            new Function(targetLine);
                            result += '<p style="color: green;"><strong>语法检查:</strong> ✅ 通过</p>';
                        } catch (error) {
                            result += '<p style="color: red;"><strong>语法检查:</strong> ❌ 失败</p>';
                            result += '<p style="color: red;"><strong>错误信息:</strong> ' + error.message + '</p>';
                        }
                    }
                    
                    result += '</div>';
                    
                    // 显示上下文
                    result += '<h4>📋 上下文 (第2671-2691行):</h4>';
                    result += '<div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 11px; max-height: 300px; overflow-y: auto;">';
                    
                    for (let i = Math.max(0, 2670); i <= Math.min(totalLines - 1, 2690); i++) {
                        const lineNum = i + 1;
                        const line = allLines[i] || '';
                        const isTarget = lineNum === 2681;
                        
                        const style = isTarget ? 'background: #ffcdd2; font-weight: bold;' : 'background: white;';
                        result += '<div style="' + style + ' padding: 2px 5px; margin: 1px 0; border-radius: 2px;">';
                        result += '<strong>行 ' + lineNum + ':</strong> ' + escapeHtml(line.substring(0, 120));
                        if (line.length > 120) result += '...';
                        result += '</div>';
                    }
                    
                    result += '</div>';
                    
                } else {
                    result += '<div style="background: #ffebee; padding: 15px; border-radius: 4px;">';
                    result += '<h4>❌ 第2681行不存在</h4>';
                    result += '<p>页面只有 ' + totalLines + ' 行，但错误报告在第2681行。</p>';
                    result += '<p>这表明错误可能来自动态生成的内容或外部脚本。</p>';
                    result += '</div>';
                }
                
                updateDisplay(result, totalLines >= 2681 ? 'warning' : 'error');
                
            } catch (error) {
                updateDisplay('<h3>❌ 二分查找失败</h3><p><strong>错误:</strong> ' + error.message + '</p>', 'error');
            }
        }

        function testSpecificLines() {
            updateDisplay('<h3>🧪 测试特定行的语法模式...</h3>', 'warning');
            
            // 基于我们之前的分析，测试一些可能有问题的代码模式
            const suspiciousPatterns = [
                {
                    name: '未闭合的console调用',
                    code: 'console.log("test"',
                    shouldFail: true
                },
                {
                    name: '未闭合的alert调用',
                    code: 'alert("message"',
                    shouldFail: true
                },
                {
                    name: '未闭合的fetch调用',
                    code: 'fetch("/api/test"',
                    shouldFail: true
                },
                {
                    name: '未闭合的函数调用',
                    code: 'document.getElementById("test"',
                    shouldFail: true
                },
                {
                    name: '模板字符串语法错误',
                    code: 'console.log(`测试 ${variable}`',
                    shouldFail: true
                },
                {
                    name: '复杂的字符串拼接',
                    code: 'console.log("测试 " + variable + " 结果");',
                    shouldFail: false
                }
            ];
            
            let result = '<h3>🧪 特定语法模式测试结果</h3>';
            
            suspiciousPatterns.forEach(function(pattern, index) {
                try {
                    new Function('variable', pattern.code);
                    const passed = !pattern.shouldFail;
                    result += '<div style="color: ' + (passed ? 'green' : 'orange') + '; margin: 8px 0;">';
                    result += (passed ? '✅' : '⚠️') + ' 测试 ' + (index + 1) + ': ' + pattern.name;
                    result += '<br><code>' + pattern.code + '</code>';
                    result += '<br>结果: ' + (passed ? '通过' : '应该失败但没有失败');
                    result += '</div>';
                } catch (error) {
                    const passed = pattern.shouldFail;
                    result += '<div style="color: ' + (passed ? 'green' : 'red') + '; margin: 8px 0;">';
                    result += (passed ? '✅' : '❌') + ' 测试 ' + (index + 1) + ': ' + pattern.name;
                    result += '<br><code>' + pattern.code + '</code>';
                    result += '<br>结果: ' + (passed ? '正确失败' : '意外失败') + ' - ' + error.message;
                    result += '</div>';
                }
            });
            
            updateDisplay(result);
        }

        function showManualFix() {
            updateDisplay('<h3>🛠️ 手动修复方案</h3>', 'warning');
            
            let result = '<h3>🔧 JavaScript语法错误手动修复指南</h3>';
            result += '<p>由于自动定位困难，建议采用以下手动修复方案:</p>';
            
            result += '<h4>🎯 步骤1: 逐步注释代码</h4>';
            result += '<div style="background: #e3f2fd; padding: 15px; border-radius: 4px;">';
            result += '<p>在 app.blade.php 文件中，逐步注释掉JavaScript代码块:</p>';
            result += '<ol>';
            result += '<li>从文件末尾开始，逐个注释掉 &lt;script&gt; 块</li>';
            result += '<li>每注释一个块后，刷新页面检查错误是否消失</li>';
            result += '<li>当错误消失时，说明问题在最后注释的那个块中</li>';
            result += '</ol>';
            result += '</div>';
            
            result += '<h4>🎯 步骤2: 检查常见问题位置</h4>';
            result += '<div style="background: #fff3cd; padding: 15px; border-radius: 4px;">';
            result += '<p>重点检查以下位置:</p>';
            result += '<ul>';
            result += '<li><strong>客服系统相关代码</strong> - 第1300-2300行之间</li>';
            result += '<li><strong>事件监听器</strong> - addEventListener 调用</li>';
            result += '<li><strong>异步函数</strong> - fetch、setTimeout 等</li>';
            result += '<li><strong>字符串拼接</strong> - 特别是包含变量的地方</li>';
            result += '</ul>';
            result += '</div>';
            
            result += '<h4>🎯 步骤3: 使用浏览器开发工具</h4>';
            result += '<div style="background: #e8f5e8; padding: 15px; border-radius: 4px;">';
            result += '<p>利用浏览器的调试功能:</p>';
            result += '<ol>';
            result += '<li>按F12打开开发者工具</li>';
            result += '<li>在Console标签页中查看完整的错误信息</li>';
            result += '<li>点击错误信息中的链接，直接跳转到错误行</li>';
            result += '<li>在Sources标签页中查看格式化后的代码</li>';
            result += '</ol>';
            result += '</div>';
            
            result += '<h4>🚨 紧急临时解决方案</h4>';
            result += '<div style="background: #ffebee; padding: 15px; border-radius: 4px; border-left: 4px solid #f44336;">';
            result += '<p><strong>如果需要立即让网站正常运行:</strong></p>';
            result += '<p>在 &lt;head&gt; 部分添加以下代码来抑制JavaScript错误:</p>';
            result += '<pre style="background: white; padding: 10px; border-radius: 3px; font-family: monospace;">';
            result += '&lt;script&gt;\n';
            result += 'window.onerror = function(msg, url, line, col, error) {\n';
            result += '    console.warn("JavaScript错误已被抑制:", msg);\n';
            result += '    return true; // 阻止默认的错误处理\n';
            result += '};\n';
            result += '&lt;/script&gt;';
            result += '</pre>';
            result += '<p style="color: #d32f2f;"><strong>注意:</strong> 这只是临时方案，不会修复根本问题！</p>';
            result += '</div>';
            
            updateDisplay(result, 'warning');
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay('<h3>🔍 二分查找工具已启动</h3><p>准备使用二分法精确定位JavaScript语法错误。</p>');
        });
    </script>
</body>
</html>
