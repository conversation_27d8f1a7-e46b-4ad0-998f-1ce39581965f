<?php

namespace App\Helpers;

class BrowserDetector
{
    /**
     * 解析User-Agent字符串，获取浏览器信息
     */
    public static function parse($userAgent)
    {
        if (empty($userAgent)) {
            return [
                'browser' => '未知浏览器',
                'version' => '',
                'platform' => '未知系统',
                'device' => '未知设备',
                'is_mobile' => false,
                'is_tablet' => false,
                'is_desktop' => true,
                'full_info' => '未知'
            ];
        }

        $browser = self::getBrowser($userAgent);
        $platform = self::getPlatform($userAgent);
        $device = self::getDevice($userAgent);

        return [
            'browser' => $browser['name'],
            'version' => $browser['version'],
            'platform' => $platform['name'],
            'device' => $device['type'],
            'is_mobile' => $device['is_mobile'],
            'is_tablet' => $device['is_tablet'],
            'is_desktop' => $device['is_desktop'],
            'full_info' => $browser['name'] . ' ' . $browser['version'] . ' / ' . $platform['name']
        ];
    }

    /**
     * 获取浏览器信息
     */
    private static function getBrowser($userAgent)
    {
        $browsers = [
            'Edge' => '/Edge\/([0-9\.]+)/',
            'Chrome' => '/Chrome\/([0-9\.]+)/',
            'Firefox' => '/Firefox\/([0-9\.]+)/',
            'Safari' => '/Version\/([0-9\.]+).*Safari/',
            'Opera' => '/Opera\/([0-9\.]+)/',
            'Internet Explorer' => '/MSIE ([0-9\.]+)/',
            'IE11' => '/Trident.*rv:([0-9\.]+)/',
        ];

        foreach ($browsers as $browser => $pattern) {
            if (preg_match($pattern, $userAgent, $matches)) {
                $version = isset($matches[1]) ? $matches[1] : '';
                
                // 特殊处理
                if ($browser === 'IE11') {
                    $browser = 'Internet Explorer';
                }
                
                return [
                    'name' => $browser,
                    'version' => $version
                ];
            }
        }

        return [
            'name' => '其他浏览器',
            'version' => ''
        ];
    }

    /**
     * 获取操作系统信息
     */
    private static function getPlatform($userAgent)
    {
        $platforms = [
            'Windows 11' => '/Windows NT 10\.0.*Win64.*x64/',
            'Windows 10' => '/Windows NT 10\.0/',
            'Windows 8.1' => '/Windows NT 6\.3/',
            'Windows 8' => '/Windows NT 6\.2/',
            'Windows 7' => '/Windows NT 6\.1/',
            'Windows Vista' => '/Windows NT 6\.0/',
            'Windows XP' => '/Windows NT 5\.1/',
            'macOS' => '/Mac OS X ([0-9_\.]+)/',
            'iOS' => '/iPhone OS ([0-9_\.]+)/',
            'Android' => '/Android ([0-9\.]+)/',
            'Linux' => '/Linux/',
            'Ubuntu' => '/Ubuntu/',
        ];

        foreach ($platforms as $platform => $pattern) {
            if (preg_match($pattern, $userAgent, $matches)) {
                $version = '';
                if (isset($matches[1])) {
                    $version = str_replace('_', '.', $matches[1]);
                }
                
                return [
                    'name' => $platform . ($version ? ' ' . $version : ''),
                    'version' => $version
                ];
            }
        }

        return [
            'name' => '未知系统',
            'version' => ''
        ];
    }

    /**
     * 获取设备类型
     */
    private static function getDevice($userAgent)
    {
        $isMobile = preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|Windows Phone/', $userAgent);
        $isTablet = preg_match('/iPad|Android.*Tablet|Kindle|Silk/', $userAgent);
        
        if ($isTablet) {
            return [
                'type' => '平板电脑',
                'is_mobile' => false,
                'is_tablet' => true,
                'is_desktop' => false
            ];
        } elseif ($isMobile) {
            return [
                'type' => '手机',
                'is_mobile' => true,
                'is_tablet' => false,
                'is_desktop' => false
            ];
        } else {
            return [
                'type' => '桌面电脑',
                'is_mobile' => false,
                'is_tablet' => false,
                'is_desktop' => true
            ];
        }
    }

    /**
     * 获取简化的浏览器名称（用于显示）
     */
    public static function getSimpleBrowserName($userAgent)
    {
        $info = self::parse($userAgent);
        return $info['browser'];
    }

    /**
     * 获取完整的浏览器信息字符串
     */
    public static function getFullBrowserInfo($userAgent)
    {
        $info = self::parse($userAgent);
        return $info['full_info'];
    }

    /**
     * 判断是否为移动设备
     */
    public static function isMobile($userAgent)
    {
        $info = self::parse($userAgent);
        return $info['is_mobile'] || $info['is_tablet'];
    }

    /**
     * 获取设备图标
     */
    public static function getDeviceIcon($userAgent)
    {
        $info = self::parse($userAgent);
        
        if ($info['is_mobile']) {
            return '📱';
        } elseif ($info['is_tablet']) {
            return '📱';
        } else {
            return '💻';
        }
    }

    /**
     * 获取浏览器图标
     */
    public static function getBrowserIcon($userAgent)
    {
        $browser = self::getSimpleBrowserName($userAgent);
        
        $icons = [
            'Chrome' => '🌐',
            'Firefox' => '🦊',
            'Safari' => '🧭',
            'Edge' => '🌐',
            'Opera' => '🎭',
            'Internet Explorer' => '🌐',
        ];

        return $icons[$browser] ?? '🌐';
    }
}
