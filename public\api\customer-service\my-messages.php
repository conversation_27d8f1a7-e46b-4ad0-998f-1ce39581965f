<?php
// Laravel环境初始化
require_once __DIR__ . '/../../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['session_id'])) {
        echo json_encode([
            'success' => false,
            'message' => '缺少必要参数'
        ]);
        exit;
    }

    $sessionId = $input['session_id'];
    
    // 查询用户的留言
    $messages = DB::table('customer_service_offline_messages')
                ->where('session_id', $sessionId)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function($message) {
                    return [
                        'id' => $message->id,
                        'message' => $message->message,
                        'status' => $message->status,
                        'admin_reply' => $message->admin_reply,
                        'created_at' => $message->created_at,
                        'replied_at' => $message->replied_at,
                        'visitor_name' => $message->visitor_name,
                        'visitor_email' => $message->visitor_email,
                        'visitor_phone' => $message->visitor_phone
                    ];
                })
                ->toArray();

    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'count' => count($messages)
    ]);

} catch (\Exception $e) {
    error_log('获取用户留言失败: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => '服务器错误'
    ]);
}
?>
