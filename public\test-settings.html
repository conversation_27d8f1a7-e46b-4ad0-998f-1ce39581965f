<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服设置测试</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1); 
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服设置测试</h1>
        <p>测试客服系统设置的保存功能</p>
        
        <form id="test-form">
            <div class="form-group">
                <label>系统启用状态</label>
                <label class="switch">
                    <input type="hidden" name="settings[system_enabled]" value="0">
                    <input type="checkbox" name="settings[system_enabled]" value="1" id="system_enabled">
                    <span class="slider"></span>
                </label>
                <span id="system_status">禁用</span>
            </div>
            
            <div class="form-group">
                <label>声音提醒</label>
                <label class="switch">
                    <input type="hidden" name="settings[sound_enabled]" value="0">
                    <input type="checkbox" name="settings[sound_enabled]" value="1" id="sound_enabled">
                    <span class="slider"></span>
                </label>
                <span id="sound_status">禁用</span>
            </div>
            
            <div class="form-group">
                <label>自动打开</label>
                <label class="switch">
                    <input type="hidden" name="settings[auto_open_enabled]" value="0">
                    <input type="checkbox" name="settings[auto_open_enabled]" value="1" id="auto_open_enabled">
                    <span class="slider"></span>
                </label>
                <span id="auto_open_status">禁用</span>
            </div>
            
            <div class="form-group">
                <label for="welcome_message">欢迎消息</label>
                <input type="text" name="settings[welcome_message]" id="welcome_message" 
                       value="您好！欢迎咨询，我们将竭诚为您服务！" 
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div class="form-group">
                <label for="chat_theme_color">主题颜色</label>
                <input type="color" name="settings[chat_theme_color]" id="chat_theme_color" value="#000000">
            </div>
            
            <button type="submit" class="btn">保存设置</button>
        </form>
        
        <div class="result" id="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>当前设置状态</h3>
            <button class="btn" onclick="loadCurrentSettings()">加载当前设置</button>
            <div id="current-settings" style="margin-top: 10px; font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 4px;"></div>
        </div>
    </div>

    <script>
        // 获取CSRF token
        function getCSRFToken() {
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        }

        // 更新开关状态显示
        function updateSwitchStatus() {
            const switches = [
                { id: 'system_enabled', statusId: 'system_status' },
                { id: 'sound_enabled', statusId: 'sound_status' },
                { id: 'auto_open_enabled', statusId: 'auto_open_status' }
            ];
            
            switches.forEach(sw => {
                const checkbox = document.getElementById(sw.id);
                const status = document.getElementById(sw.statusId);
                
                checkbox.addEventListener('change', function() {
                    status.textContent = this.checked ? '启用' : '禁用';
                    status.style.color = this.checked ? '#28a745' : '#dc3545';
                });
                
                // 初始状态
                status.textContent = checkbox.checked ? '启用' : '禁用';
                status.style.color = checkbox.checked ? '#28a745' : '#dc3545';
            });
        }

        // 表单提交
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            // 添加CSRF token
            formData.append('_token', getCSRFToken());
            
            // 显示要发送的数据
            console.log('发送的数据:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }
            
            submitBtn.textContent = '保存中...';
            submitBtn.disabled = true;
            
            fetch('/strongadmin/customer-service/settings', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('响应数据:', data);
                showResult(data.success ? 'success' : 'error', 
                          data.success ? '设置保存成功！' : ('保存失败: ' + (data.message || '未知错误')));
            })
            .catch(error => {
                console.error('请求错误:', error);
                showResult('error', '网络错误: ' + error.message);
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        // 显示结果
        function showResult(type, message) {
            const result = document.getElementById('result');
            result.className = `result ${type}`;
            result.textContent = message;
            result.style.display = 'block';
        }

        // 加载当前设置
        function loadCurrentSettings() {
            fetch('/api/customer-service/settings.php')
            .then(response => response.json())
            .then(data => {
                const settingsDiv = document.getElementById('current-settings');
                if (data.success) {
                    settingsDiv.innerHTML = '<pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
                    
                    // 更新表单值
                    const settings = data.data;
                    document.getElementById('system_enabled').checked = settings.system_enabled === true;
                    document.getElementById('sound_enabled').checked = settings.sound_enabled === true;
                    document.getElementById('auto_open_enabled').checked = settings.auto_open_enabled === true;
                    document.getElementById('welcome_message').value = settings.welcome_message || '';
                    document.getElementById('chat_theme_color').value = settings.chat_theme_color || '#000000';
                    
                    updateSwitchStatus();
                } else {
                    settingsDiv.textContent = '加载失败: ' + (data.message || '未知错误');
                }
            })
            .catch(error => {
                document.getElementById('current-settings').textContent = '加载错误: ' + error.message;
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSwitchStatus();
            loadCurrentSettings();
        });
    </script>
</body>
</html>
