<?php
/**
 * 修复user_id数据类型问题
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>修复user_id数据</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 修复user_id数据类型问题</h1>";

try {
    echo "<div class='info'>📋 开始检查和修复数据...</div>";
    
    // 检查是否有字符串类型的user_id（这会导致错误）
    echo "<div class='info'>🔍 检查会话表中的user_id数据...</div>";
    
    // 查找所有user_id为NULL的会话
    $nullUserIdSessions = DB::table('st_customer_service_sessions')
                           ->whereNull('user_id')
                           ->get();
    
    echo "<div class='warning'>⚠️ 找到 " . count($nullUserIdSessions) . " 个user_id为NULL的会话</div>";
    
    $fixedCount = 0;
    foreach ($nullUserIdSessions as $session) {
        // 为每个NULL的user_id生成一个唯一的数字ID
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        $newUserId = 900000000 + ($timestamp % 90000000) + $random + $fixedCount;
        
        // 匿名用户ID范围不会与真实用户ID冲突，跳过检查
        // while (DB::table('users')->where('id', $newUserId)->exists()) {
        //     $newUserId++;
        // }
        
        // 更新会话的user_id
        DB::table('st_customer_service_sessions')
          ->where('id', $session->id)
          ->update(['user_id' => $newUserId]);
        
        // 更新该会话相关消息的sender_id
        DB::table('st_customer_service_messages')
          ->where('session_id', $session->id)
          ->where('sender_type', 'customer')
          ->whereNull('sender_id')
          ->update(['sender_id' => $newUserId]);
        
        $fixedCount++;
        
        if ($fixedCount % 10 == 0) {
            echo "<div class='info'>已处理 $fixedCount 个会话...</div>";
        }
    }
    
    echo "<div class='success'>✅ 成功修复了 $fixedCount 个会话的user_id</div>";
    
    // 检查消息表中的sender_id
    echo "<div class='info'>🔍 检查消息表中的sender_id数据...</div>";
    
    $nullSenderIdMessages = DB::table('st_customer_service_messages')
                             ->where('sender_type', 'customer')
                             ->whereNull('sender_id')
                             ->get();
    
    echo "<div class='warning'>⚠️ 找到 " . count($nullSenderIdMessages) . " 条客户消息的sender_id为NULL</div>";
    
    $messageFixedCount = 0;
    foreach ($nullSenderIdMessages as $message) {
        // 获取该消息所属会话的user_id
        $session = DB::table('st_customer_service_sessions')
                    ->where('id', $message->session_id)
                    ->first();
        
        if ($session && $session->user_id) {
            DB::table('st_customer_service_messages')
              ->where('id', $message->id)
              ->update(['sender_id' => $session->user_id]);
            
            $messageFixedCount++;
        }
    }
    
    echo "<div class='success'>✅ 成功修复了 $messageFixedCount 条消息的sender_id</div>";
    
    echo "<div class='success'>🎉 所有数据修复完成！</div>";
    
    echo "<h2>📊 修复后的数据统计</h2>";
    
    $sessionStats = DB::table('st_customer_service_sessions')
                     ->selectRaw('
                         COUNT(*) as total_sessions,
                         COUNT(user_id) as sessions_with_user_id,
                         COUNT(*) - COUNT(user_id) as sessions_without_user_id
                     ')
                     ->first();
    
    $messageStats = DB::table('st_customer_service_messages')
                     ->where('sender_type', 'customer')
                     ->selectRaw('
                         COUNT(*) as total_customer_messages,
                         COUNT(sender_id) as messages_with_sender_id,
                         COUNT(*) - COUNT(sender_id) as messages_without_sender_id
                     ')
                     ->first();
    
    echo "<pre>";
    echo "会话统计:\n";
    echo "- 总会话数: {$sessionStats->total_sessions}\n";
    echo "- 有user_id的会话: {$sessionStats->sessions_with_user_id}\n";
    echo "- 无user_id的会话: {$sessionStats->sessions_without_user_id}\n\n";
    
    echo "客户消息统计:\n";
    echo "- 总客户消息数: {$messageStats->total_customer_messages}\n";
    echo "- 有sender_id的消息: {$messageStats->messages_with_sender_id}\n";
    echo "- 无sender_id的消息: {$messageStats->messages_without_sender_id}\n";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
    echo "<div class='error'>错误详情: " . $e->getTraceAsString() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
