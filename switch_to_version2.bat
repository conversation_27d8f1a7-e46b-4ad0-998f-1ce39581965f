@echo off
echo ========================================
echo StrongShop 版本切换工具
echo ========================================
echo.
echo 当前版本: 版本1 (原始版本)
echo 目标版本: 版本2 (现代简约风格)
echo.
echo 此操作将切换到现代简约风格的UI设计
echo.
set /p choice=确定要切换到版本2吗？(y/N): 

if /i "%choice%"=="y" (
    echo.
    echo 正在切换到版本2...
    echo.
    
    echo 1. 检查版本2备份是否存在...
    if not exist H:\wwwroot\mostxx.com_1111hp7hL_backup_v2 (
        echo 错误: 版本2备份不存在！
        echo 请先运行现代化UI更新。
        pause
        exit /b 1
    )
    
    echo 2. 停止可能的服务...
    taskkill /f /im php.exe 2>nul
    timeout /t 2 /nobreak >nul
    
    echo 3. 备份当前版本...
    if exist H:\wwwroot\mostxx.com_1111hp7hL_current_backup (
        rmdir /s /q H:\wwwroot\mostxx.com_1111hp7hL_current_backup
    )
    xcopy H:\wwwroot\mostxx.com_1111hp7hL H:\wwwroot\mostxx.com_1111hp7hL_current_backup /E /I /H /Y /Q
    
    echo 4. 删除当前版本...
    cd /d H:\wwwroot\
    rmdir /s /q mostxx.com_1111hp7hL
    
    echo 5. 恢复版本2...
    xcopy mostxx.com_1111hp7hL_backup_v2 mostxx.com_1111hp7hL /E /I /H /Y /Q
    
    echo.
    echo ========================================
    echo 切换完成！
    echo ========================================
    echo.
    echo 版本2 (现代简约风格) 已激活
    echo 当前备份保存在: H:\wwwroot\mostxx.com_1111hp7hL_current_backup
    echo.
    echo 请重新启动Web服务器以确保更改生效。
    echo.
) else (
    echo.
    echo 操作已取消。
    echo.
)

pause
