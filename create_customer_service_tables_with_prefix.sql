-- 客服系统数据表（带st_前缀）

-- 客服会话表：存储每个客户的会话信息
CREATE TABLE IF NOT EXISTS `st_customer_service_sessions` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话主键ID',
    `session_id` varchar(255) NOT NULL COMMENT '会话唯一标识符',
    `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联的用户ID（如果是注册用户）',
    `visitor_name` varchar(255) DEFAULT NULL COMMENT '访客姓名',
    `visitor_email` varchar(255) DEFAULT NULL COMMENT '访客邮箱',
    `visitor_ip` varchar(255) DEFAULT NULL COMMENT '访客IP地址',
    `user_agent` text DEFAULT NULL COMMENT '访客浏览器信息',
    `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active' COMMENT '会话状态：active=活跃，waiting=等待，closed=已关闭',
    `assigned_admin_id` bigint(20) unsigned DEFAULT NULL COMMENT '分配的客服管理员ID',
    `last_activity` timestamp NULL DEFAULT NULL COMMENT '最后活动时间',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `session_id` (`session_id`),
    KEY `assigned_admin_id` (`assigned_admin_id`),
    KEY `status_last_activity` (`status`, `last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服会话表';

-- 客服消息表：存储会话中的所有消息记录
CREATE TABLE IF NOT EXISTS `st_customer_service_messages` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息主键ID',
    `session_id` bigint(20) unsigned NOT NULL COMMENT '关联的会话ID',
    `sender_type` enum('customer','admin','ai') NOT NULL COMMENT '发送者类型：customer=客户，admin=客服，ai=AI自动回复',
    `sender_id` bigint(20) unsigned DEFAULT NULL COMMENT '发送者ID（客户ID或管理员ID）',
    `message` text NOT NULL COMMENT '消息内容',
    `attachments` json DEFAULT NULL COMMENT '附件信息（JSON格式）',
    `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0=未读，1=已读',
    `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `session_id` (`session_id`),
    KEY `sender_type_created` (`sender_type`, `created_at`),
    KEY `is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';

-- AI自动回复规则表：存储AI自动回复的规则配置
CREATE TABLE IF NOT EXISTS `st_ai_auto_reply_rules` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则主键ID',
    `name` varchar(255) NOT NULL COMMENT '规则名称',
    `keywords` json NOT NULL COMMENT '触发关键词（JSON数组格式）',
    `reply_message` text NOT NULL COMMENT 'AI回复的消息内容',
    `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
    `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数统计',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `is_active_priority` (`is_active`,`priority`),
    KEY `usage_count` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI自动回复规则表';

-- 添加默认AI规则
INSERT IGNORE INTO `st_ai_auto_reply_rules` (`name`, `keywords`, `reply_message`, `priority`, `is_active`, `usage_count`, `created_at`, `updated_at`) VALUES
('问候语', '["hello", "hi", "hey", "你好", "您好"]', 'Hello! 👋 Welcome to our store! How can I help you today?', 10, 1, 0, NOW(), NOW()),
('物流咨询', '["shipping", "delivery", "物流", "快递", "发货"]', 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You will receive a tracking number once your order ships.', 9, 1, 0, NOW(), NOW()),
('退换货', '["return", "refund", "退货", "退款", "换货"]', 'We have a 30-day return policy! 🔄 If you are not satisfied, you can return items within 30 days for a full refund.', 8, 1, 0, NOW(), NOW()),
('价格咨询', '["price", "cost", "价格", "多少钱", "费用"]', 'Our prices are very competitive! 💰 You can see the current price on the product page. We also offer bulk discounts for large orders.', 7, 1, 0, NOW(), NOW()),
('产品咨询', '["product", "item", "产品", "商品", "质量"]', 'We have high-quality products with detailed descriptions! 📦 Please check the product page for specifications, or let me know what specific information you need.', 6, 1, 0, NOW(), NOW());

-- 注意：后台菜单需要手动添加，因为菜单表字段可能不同
-- 请在后台手动添加以下菜单：
-- 1. 在线客服（父菜单）
-- 2. 会话管理 - customer-service/sessions
-- 3. AI规则管理 - customer-service/ai-rules
-- 4. 统计报表 - customer-service/statistics
