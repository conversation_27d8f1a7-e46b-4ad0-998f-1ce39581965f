<?php
/**
 * 清理会话状态数据，只保留online/offline
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>清理会话状态</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 清理会话状态数据</h1>";

try {
    echo "<div class='info'>📋 开始清理状态数据...</div>";
    
    // 查看当前状态分布
    echo "<div class='info'>📊 当前状态分布：</div>";
    $statusStats = DB::select("
        SELECT status, COUNT(*) as count 
        FROM st_customer_service_sessions 
        GROUP BY status
    ");
    
    echo "<pre>";
    foreach ($statusStats as $stat) {
        echo "状态: {$stat->status} - 数量: {$stat->count}\n";
    }
    echo "</pre>";
    
    // 将所有非标准状态转换为offline
    echo "<div class='info'>🔄 转换非标准状态...</div>";
    
    $updatedCount = DB::update("
        UPDATE st_customer_service_sessions 
        SET status = 'offline' 
        WHERE status NOT IN ('online', 'offline')
    ");
    
    echo "<div class='success'>✅ 已将 {$updatedCount} 个会话的状态转换为offline</div>";
    
    // 根据最后在线时间自动设置状态
    echo "<div class='info'>🔄 根据最后在线时间自动设置状态...</div>";

    // 超过30秒没有心跳的设为离线
    $offlineCount = DB::update("
        UPDATE st_customer_service_sessions
        SET status = 'offline'
        WHERE last_seen IS NOT NULL
        AND last_seen < DATE_SUB(NOW(), INTERVAL 30 SECOND)
        AND status = 'online'
    ");
    
    echo "<div class='success'>✅ 已将 {$offlineCount} 个超时会话设为离线</div>";
    
    // 没有last_seen记录的设为离线
    $nullSeenCount = DB::update("
        UPDATE st_customer_service_sessions 
        SET status = 'offline' 
        WHERE last_seen IS NULL
    ");
    
    echo "<div class='success'>✅ 已将 {$nullSeenCount} 个无心跳记录的会话设为离线</div>";
    
    echo "<div class='info'>📊 清理后的状态分布：</div>";
    $newStatusStats = DB::select("
        SELECT status, COUNT(*) as count 
        FROM st_customer_service_sessions 
        GROUP BY status
    ");
    
    echo "<pre>";
    foreach ($newStatusStats as $stat) {
        echo "状态: {$stat->status} - 数量: {$stat->count}\n";
    }
    echo "</pre>";
    
    echo "<div class='success'>🎉 状态清理完成！</div>";
    
    echo "<h2>📋 状态说明</h2>";
    echo "<ul>";
    echo "<li><strong>🟢 online</strong> - 客户正在使用聊天窗口（2分钟内有心跳）</li>";
    echo "<li><strong>⚫ offline</strong> - 客户已离开或关闭聊天窗口</li>";
    echo "</ul>";
    
    echo "<h2>📋 自动状态管理</h2>";
    echo "<ul>";
    echo "<li>客户打开聊天窗口 → 自动设为 online</li>";
    echo "<li>客户发送消息 → 自动设为 online</li>";
    echo "<li>客户关闭窗口 → 自动设为 offline</li>";
    echo "<li>超过2分钟无心跳 → 自动设为 offline</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
    echo "<div class='error'>错误详情: " . $e->getTraceAsString() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
