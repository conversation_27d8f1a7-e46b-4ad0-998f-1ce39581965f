<?php
/**
 * 测试产品图片数据加载
 * 访问: http://your-domain/test_product_images.php
 */

// 设置基本路径
$basePath = __DIR__;
require_once $basePath . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once $basePath . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// 创建请求
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

// 获取产品数据
use App\Models\Product\Product;

echo "<h1>🔍 产品图片数据测试</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.product { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.images { display: flex; gap: 10px; margin: 10px 0; }
.images img { width: 100px; height: 100px; object-fit: cover; border: 2px solid #ddd; border-radius: 4px; }
.error { color: red; background: #ffe6e6; padding: 10px; border-radius: 4px; }
.success { color: green; background: #e6ffe6; padding: 10px; border-radius: 4px; }
</style>";

try {
    // 测试推荐产品
    echo "<h2>📋 推荐产品数据</h2>";
    $recommendRows = Product::query()
        ->where('status', 1)->where('is_recommend', 1)->where('hidden', 2)
        ->select(['id', 'title', 'img_cover', 'img_photos'])
        ->limit(3)->get();
    
    if ($recommendRows->isEmpty()) {
        echo "<div class='error'>❌ 没有找到推荐产品</div>";
    } else {
        foreach ($recommendRows as $product) {
            echo "<div class='product'>";
            echo "<h3>产品: {$product->title} (ID: {$product->id})</h3>";
            echo "<p><strong>封面图:</strong> {$product->img_cover}</p>";
            
            if ($product->asset_img_photos) {
                echo "<p><strong>相册图片数量:</strong> " . count($product->asset_img_photos) . "</p>";
                echo "<div class='images'>";
                foreach ($product->asset_img_photos as $index => $photo) {
                    echo "<div>";
                    echo "<img src='{$photo['src']}' alt='图片 " . ($index + 1) . "'>";
                    echo "<br><small>图片 " . ($index + 1) . "</small>";
                    echo "</div>";
                }
                echo "</div>";
                
                if (isset($product->asset_img_photos[1])) {
                    echo "<div class='success'>✅ 第二张图片存在: {$product->asset_img_photos[1]['src']}</div>";
                } else {
                    echo "<div class='error'>❌ 第二张图片不存在</div>";
                }
            } else {
                echo "<div class='error'>❌ 没有相册图片数据</div>";
            }
            echo "</div>";
        }
    }
    
    // 测试新品
    echo "<h2>🆕 新品数据</h2>";
    $newRows = Product::query()
        ->where('status', 1)->where('is_new', 1)->where('hidden', 2)
        ->select(['id', 'title', 'img_cover', 'img_photos'])
        ->limit(3)->get();
    
    if ($newRows->isEmpty()) {
        echo "<div class='error'>❌ 没有找到新品</div>";
    } else {
        foreach ($newRows as $product) {
            echo "<div class='product'>";
            echo "<h3>产品: {$product->title} (ID: {$product->id})</h3>";
            
            if ($product->asset_img_photos && isset($product->asset_img_photos[1])) {
                echo "<div class='success'>✅ 第二张图片存在: {$product->asset_img_photos[1]['src']}</div>";
            } else {
                echo "<div class='error'>❌ 第二张图片不存在</div>";
            }
            echo "</div>";
        }
    }
    
    // 测试热卖
    echo "<h2>🔥 热卖产品数据</h2>";
    $hotRows = Product::query()
        ->where('status', 1)->where('is_hot', 1)->where('hidden', 2)
        ->select(['id', 'title', 'img_cover', 'img_photos'])
        ->limit(3)->get();
    
    if ($hotRows->isEmpty()) {
        echo "<div class='error'>❌ 没有找到热卖产品</div>";
    } else {
        foreach ($hotRows as $product) {
            echo "<div class='product'>";
            echo "<h3>产品: {$product->title} (ID: {$product->id})</h3>";
            
            if ($product->asset_img_photos && isset($product->asset_img_photos[1])) {
                echo "<div class='success'>✅ 第二张图片存在: {$product->asset_img_photos[1]['src']}</div>";
            } else {
                echo "<div class='error'>❌ 第二张图片不存在</div>";
            }
            echo "</div>";
        }
    }
    
    // 检查数据库中的原始数据
    echo "<h2>🗄️ 数据库原始数据检查</h2>";
    $rawData = Product::query()
        ->where('status', 1)
        ->whereNotNull('img_photos')
        ->select(['id', 'title', 'img_photos'])
        ->limit(5)->get();
    
    foreach ($rawData as $product) {
        echo "<div class='product'>";
        echo "<h3>产品: {$product->title} (ID: {$product->id})</h3>";
        echo "<p><strong>原始 img_photos 数据:</strong></p>";
        echo "<pre>" . htmlspecialchars($product->img_photos) . "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 错误: " . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<p><small>测试完成时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
