<?php
/**
 * 简单的消息发送工具
 */

$sessionId = $_POST['session_id'] ?? $_GET['session_id'] ?? 'default';
$message = $_POST['message'] ?? $_GET['message'] ?? '';
$senderType = $_POST['sender_type'] ?? $_GET['sender_type'] ?? 'admin';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>发送消息</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;} input,select{padding:8px;margin:5px;border:1px solid #ddd;border-radius:4px;}</style>";
echo "</head><body>";

echo "<h1>📤 发送消息工具</h1>";

if ($_POST['action'] ?? '' === 'send') {
    if ($sessionId && $message) {
        // 读取现有消息
        $messagesFile = 'sse_messages_' . $sessionId . '.json';
        $messages = [];
        if (file_exists($messagesFile)) {
            $messages = json_decode(file_get_contents($messagesFile), true) ?: [];
        }
        
        // 添加新消息
        $newMessage = [
            'id' => count($messages) + 1,
            'session_id' => $sessionId,
            'sender_type' => $senderType,
            'message' => $message,
            'created_at' => date('Y-m-d H:i:s'),
            'timestamp' => time()
        ];
        
        $messages[] = $newMessage;
        
        // 保存消息
        if (file_put_contents($messagesFile, json_encode($messages, JSON_PRETTY_PRINT))) {
            echo "<p class='success'>✅ 消息发送成功！</p>";
            echo "<p class='info'>消息ID: {$newMessage['id']}</p>";
            echo "<p class='info'>会话ID: $sessionId</p>";
            echo "<p class='info'>发送者: $senderType</p>";
            echo "<p class='info'>内容: " . htmlspecialchars($message) . "</p>";
        } else {
            echo "<p class='error'>❌ 消息保存失败</p>";
        }
    } else {
        echo "<p class='error'>❌ 请填写完整信息</p>";
    }
}

echo "<h2>💬 发送新消息</h2>";
echo "<form method='POST'>";
echo "<input type='hidden' name='action' value='send'>";
echo "<div>";
echo "<label>会话ID: <input type='text' name='session_id' value='$sessionId' required></label>";
echo "</div>";
echo "<div>";
echo "<label>发送者类型: ";
echo "<select name='sender_type'>";
echo "<option value='admin'" . ($senderType === 'admin' ? ' selected' : '') . ">管理员</option>";
echo "<option value='ai'" . ($senderType === 'ai' ? ' selected' : '') . ">AI助手</option>";
echo "<option value='customer'" . ($senderType === 'customer' ? ' selected' : '') . ">客户</option>";
echo "</select>";
echo "</label>";
echo "</div>";
echo "<div>";
echo "<label>消息内容: <input type='text' name='message' placeholder='输入消息内容...' style='width:400px;' required></label>";
echo "</div>";
echo "<div>";
echo "<button type='submit' class='test-btn'>发送消息</button>";
echo "<button type='button' class='test-btn' onclick=\"sendQuickMessage()\">快速回复</button>";
echo "</div>";
echo "</form>";

// 显示现有消息
$messagesFile = 'sse_messages_' . $sessionId . '.json';
if (file_exists($messagesFile)) {
    $messages = json_decode(file_get_contents($messagesFile), true) ?: [];
    if (!empty($messages)) {
        echo "<h2>📋 现有消息 (" . count($messages) . "条)</h2>";
        echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
        echo "<tr><th>ID</th><th>发送者</th><th>消息内容</th><th>时间</th></tr>";
        foreach (array_reverse($messages) as $msg) {
            $senderClass = $msg['sender_type'] === 'admin' ? 'success' : ($msg['sender_type'] === 'ai' ? 'info' : '');
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td class='$senderClass'>{$msg['sender_type']}</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td>{$msg['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

echo "<h2>🔗 测试链接</h2>";
echo "<ul>";
echo "<li><a href='/sse_debug.html' target='_blank'>SSE调试工具</a></li>";
echo "<li><a href='/working_sse.php?session_id=$sessionId&last_message_id=0' target='_blank'>工作SSE测试</a></li>";
echo "</ul>";

echo "<script>
function sendQuickMessage() {
    const quickReplies = [
        '您好，我是客服，有什么可以帮助您的吗？',
        '感谢您的咨询，我正在为您查询相关信息...',
        '好的，我明白了您的问题，让我为您处理一下。',
        '请稍等，我正在联系相关部门为您解决。'
    ];
    
    const randomReply = quickReplies[Math.floor(Math.random() * quickReplies.length)];
    document.querySelector('input[name=\"message\"]').value = randomReply + ' [' + new Date().toLocaleTimeString() + ']';
}
</script>";

echo "</body></html>";
?>
