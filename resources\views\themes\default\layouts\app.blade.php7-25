<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <meta name="renderer" content="webkit">
        <meta name="keywords" content="{{$_meta_keywords ?? ''}}">
        <meta name="description" content="{{$_meta_description ?? ''}}">
        <!-- CSRF Token -->
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <title>@if(isset($_title)){{$_title}} - {{ config('strongshop.storeName') }}@else {{app('strongshop')->getShopConfig('store_title')}} @endif</title>
        <!-- Styles -->
        <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
        <link rel="stylesheet" href="{{ asset('css/bootstrap-theme.min.css') }}">
        <link rel="stylesheet" href="{{ asset('css/bootstrap-icons.css') }}">
        <link rel="stylesheet" href="{{ asset('css/main.css') }}?v={{env('APP_VERSION')}}">
        <link rel="preload" href="https://www.shiptobuy.com/fonts/bootstrap-icons.woff2" as="font" type="font/woff2" crossorigin>
        <!-- Scripts 兼容 ie8 自适应 -->
        <script src="{{ asset('js/vendor/modernizr-2.8.3-respond-1.4.2.min.js') }}"></script>
        <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NNHXPH7G');</script>
<!-- End Google Tag Manager -->
        <!--统计代码-->
        {!!app('strongshop')->getShopConfig('statistical_code')!!}
        @stack('styles')
        @stack('scripts')
        <style>
/* 视频缩略图样式 */
.video-thumb {
    position: relative;
    cursor: pointer;
    list-style: none;
}
.video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    color: white;
    font-size: 24px;
    text-shadow: 0 0 5px rgba(0,0,0,0.5);
    pointer-events: none;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function(){
    // 缩略图点击切换
    document.querySelectorAll('.st-detail-img-left li').forEach(thumb => {
        thumb.addEventListener('click', function() {
            // 移除所有激活状态
            document.querySelectorAll('.st-detail-img-left li').forEach(li => li.classList.remove('active'));
            this.classList.add('active');

            // 主图切换
            const mainContainer = document.querySelector('.pic');
            if (this.classList.contains('video-thumb')) {
                // 显示视频
                mainContainer.innerHTML = `
                    <video 
                        id="mainVideo"
                        muted loop controls playsinline
                        poster="${this.querySelector('video').poster}"
                        style="width:100%"
                    >
                        <source src="${this.querySelector('source').src}" type="video/mp4">
                    </video>
                    <div class="magnify"></div>
                `;
                mainContainer.querySelector('video').play();
            } else {
                // 显示图片
                const imgSrc = this.querySelector('img').dataset.src;
                mainContainer.innerHTML = `
                    <img src="${imgSrc}" alt="">
                    <div class="magnify"></div>
                `;
            }
        });
    });

    // 自动播放视频缩略图
    document.querySelectorAll('.video-thumb video').forEach(v => {
        v.play().catch(() => v.load()); // 处理自动播放限制
    });
});
</script>
<script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "reo4jc7ny6");
</script>
    </head>
    <body id="app" class="st">
        <!--顶部-提示信息-->
        <div class="st-navtip">
            @if(!isset($_COOKIE['strongshop_browserOutdated']))
            <!--[if lte IE 8]>
            <div class="container">
                <div class="alert alert-warning alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <p data-cookie="strongshop_browserOutdated">You are using an <strong>outdated</strong> browser. Please <a href="https://www.google.com/chrome/">upgrade your browser</a> to improve your experience.</p>
                </div>
            </div>
            <![endif]-->
            @endif
            @if(app('strongshop')->getShopConfig('notice') && !isset($_COOKIE['strongshop_notice']))
            <div class="container">
                <div class="alert alert-warning alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <p data-cookie="strongshop_notice">{!! app('strongshop')->getShopConfig('notice') !!}</p>
                </div>
            </div>
            @endif
        </div>
        <!--导航区域-顶部 * 移动端隐藏-->
        <div class="st-navtop hidden-xs">
            <div class="container">
                <ul class="nav nav-pills pull-left st-navtop-items">
                    @if(empty($_languages))
                    <li>
                        <div id="st-google-translate-element"></div>
                    </li>
                    @else
                    <li class="dropdown st-navtop-items-account">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <i class="glyphicon glyphicon-globe"></i>
                            <font>{{app('strongshop')->getCurrentLanguageName()}}</font><span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu">
                            @foreach($_languages as $lang)
                            <li @if($lang['code'] == app('strongshop')->getCurrentLanguage()) class="active" @endif>
                                <a rel="nofollow" href="{{request()->fullUrlWithQuery(['locale'=>$lang['code']])}}" rel="nofollow">{{$lang['name']}}</a>
                            </li>
                            @endforeach
                        </ul>
                    </li>
                    @endif
                    <li class="dropdown st-navtop-items-account">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <font>{{$_current_currency_name}}</font>
                            <!--<span class="caret"></span>-->
                            <span aria-hidden="true" style="color: rgb(118, 118, 118);">▼</span>
                        </a>
                        <ul class="dropdown-menu">
                            @foreach($_currencies as $currency)
                            <li @if($currency['code'] == app('strongshop')->getCurrentCurrency()) class="active" @endif>
                                <a rel="nofollow" href="{{request()->fullUrlWithQuery(['currency'=>$currency['code']])}}" rel="nofollow">{{$currency['name']}}</a>
                            </li>
                            @endforeach
                        </ul>
                    </li>
                </ul>
                <ul class="nav nav-pills pull-right st-navtop-items">
                    <li>
                        <a href="{{route('user.my.feedback')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-question-sign"></i><font>@lang('Feedback Us')</font>
                        </a>
                    </li>
                    <li>
                        <a href="{{route('user.orderTracking')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-map-marker"></i><font>@lang('Order Tracking')</font>
                        </a>
                    </li>
                    <li class="hidden-sm">
                        <a href="{{route('shoppingcart')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-shopping-cart"></i>
                            <font>@lang('Shopping Cart')
                            (<span class="st-cartnum">@if($_cart['total']['cart_qty_total']>99)99+@else{{$_cart['total']['cart_qty_total']}}@endif</span>)
                            </font>
                        </a>
                    </li>
                    <li>
                        <a href="{{route('user.my.collects')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-heart-empty"></i><font>@lang('Wish List')(<span id="ST-WISH-LIST-TOTAL">{{$_wish_list_total}}</span>)</font>
                        </a>
                    </li>
                    @guest
                    <li>
                        <a href="{{route('login')}}" rel="nofollow">
                            <i class="glyphicon glyphicon-log-in"></i><font>@lang('Sign in')</font>
                        </a>
                    </li>
                    @endguest
                    <li class="dropdown st-navtop-items-account">
                        <a href="{{route('user.index')}}" class="dropdown-toggle" data-hover="dropdown">
                            <i class="glyphicon glyphicon-user"></i>
                            <font>@lang('My Account')
                            @auth
                            ({{auth()->user()->nickname}})
                            <span class="badge">{{$_unread_feedback_replies_total}}</span>
                            @endauth
                            </font>
                            <span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu">
                            @guest
                            <li><a href="{{route('login')}}" rel="nofollow">@lang('Sign in')</a></li>
                            <li><a href="{{route('register')}}" rel="nofollow">@lang('Sign up')</a></li>
                            <li role="separator" class="divider"></li>
                            @endguest
                            <li><a href="{{route('user.index')}}" rel="nofollow">@lang('User Home')</a></li>
                            <li><a href="{{route('user.my.orders')}}" rel="nofollow">@lang('My Orders')</a></li>
                            <li><a href="{{route('user.my.collects')}}" rel="nofollow">@lang('My Wish List')</a></li>
                            <li><a href="{{route('user.my.feedback')}}" rel="nofollow">@lang('My Feedback') <span class="badge">{{$_unread_feedback_replies_total}}</span></a></li>
                            @auth
                            <li role="separator" class="divider"></li>
                            <li><a rel="nofollow" href="{{route('logout')}}"><i class="glyphicon glyphicon-log-out"></i><font>@lang('Sign out')</font></a></li>
                            @endauth
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        <!--导航区域-品牌、搜索和购物车 * 移动端隐藏-->
        <div class="st-navbrand hidden-xs">
            <div class="container">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="st-navbrand-logo">
                            <a href="/"><img src="{{ asset('img/logo.272x92.png') }}"class="img-responsive" alt="{{config('app.name')}}"  title="{{config('app.name')}}" /></a>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <!--<div class="st-navbrand-slogan hidden-sm">@lang('Slogan')</div>-->
                    </div>
                    <div class="col-sm-5">
                        <form id="ST-SEARCH" method="get" action="{{route('product.list')}}">
                            <div class="input-group st-navbrand-search">
                                <input name="keywords" type="text" class="form-control" placeholder="@lang('Search Products')" required="required" value="{{request('keywords')}}" />
                                <div class="input-group-addon" onclick="document.getElementById('ST-SEARCH').submit();">
                                    <i class="glyphicon glyphicon-search"></i>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2">
                        <div id="ST-NAVCART-ICON"  class="st-navbrand-cart pull-right">
                            <a rel="nofollow" href="{{route('shoppingcart')}}"><i class="glyphicon glyphicon-shopping-cart"></i>Cart</a>
                            <span class="badge st-cartnum">@if($_cart['total']['cart_qty_total']>99) 99+ @else {{$_cart['total']['cart_qty_total']}} @endif</span>
                        </div>
                        @if(!request()->route()->named(['shoppingcart', 'shoppingcart.checkout']))
                        <!--导航区域-购物车-->
                        <div id="ST-NAVCART-PRODUCTS">
                            <div class="page-header st-navbrand-cart-total">
                                @include('layouts.includes.shoppingcartBtn')
                            </div>
                            <div class="st-navbrand-cart-product-list st-cart-product-list">
                                @include('layouts.includes.shoppingcart')
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <!--导航区域-菜单 * 移动端隐藏-->
        <div class="st-navmenu hidden-xs">
            <div class="container">
                <ul class="nav nav-pills">
                    <li ><a href="/">@lang('Home')</a></li>
                    <li id="products">
                        <a href="{{route('product.list')}}">@lang('Caregortes')</a>
                        <div class="st-allcat panel panel-default">
                            <!-- List 一级分类 -->
                            <ul class="list-group st-allcat-items">
                                @foreach($_categories as $category)
                                <li class="list-group-item">
                                    <a href="{{route('product.list.rewrite', ['catid'=>$category->id])}}">
                                        <i class="bi-life-preserver"></i>
                                        <font>{{$category->name}}</font>
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                            <div class="st-allcat-content">
                                @foreach($_categories as $category)
                                <div class="st-allcat-content-item">
                                    @foreach($category->children as $child)
                                    <dl>
                                        <!--二级分类-->
                                        <dt><a href="{{route('product.list.rewrite', ['catid'=>$child->id])}}">{{$child['name']}}</a></dt>
                                        @foreach($child['children'] as $childChild)
                                        <!--三级分类-->
                                        <dd><a href="{{route('product.list.rewrite', ['catid'=>$childChild->id])}}">{{$childChild['name']}}</a></dd>
                                        @endforeach
                                    </dl>
                                    @endforeach
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </li>
                
                   <!-- <li ><a href="#">Women</a></li>
                    <li ><a href="#">Man</a></li>
                    <li ><a href="#">@lang('Promotion')</a></li>-->
                    <li ><a href="{{route('user.my.feedback')}}">@lang('Feedback Us')</a></li>
                </ul>
            </div>
        </div>
        <!-- 头部导航 * 移动端显示 -->
        <nav class="navbar navbar-default st-header visible-xs-block">
            <div class="container-fluid">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="row">
                    <div class="navbar-header">
                        <div class="col-xs-2">
                            <button type="button" class="navbar-toggle collapsed pull-left" data-toggle="collapse" data-target="#nav-product-categories" aria-expanded="false">
                                <span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span>
                            </button>
                        </div>
                        <div class="col-xs-6 navbar-brand-container">
                            <a class="navbar-brand" href="/" style="display: block; max-width: 272px; padding:5px 0;">
                                <img src="{{asset('img/logo.272x92.png')}}" class="img-responsive" alt="{{config('app.name')}}"  title="{{config('app.name')}}" style="width: 100%; height: auto; max-height: 92px; object-fit: contain; display: block; margin: 0 auto;"/>
                            </a>
                        </div>
                        <div class="col-xs-4">
                            <span class="bi bi-person-circle pull-right st-nav-user" data-toggle="collapse" data-target="#nav-user" aria-expanded="true"></span>
                            <a href="{{route('shoppingcart')}}" class="st-header-cart pull-right">
                                <i class="glyphicon glyphicon-shopping-cart"></i>
                                <span class="st-cartnum">@if($_cart['total']['cart_qty_total']>99) 99+ @else {{$_cart['total']['cart_qty_total']}} @endif</span>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="nav-product-categories">
                    <form class="navbar-form" id="ST-SEARCH-M" method="get" action="{{route('product.list')}}">
                        <div class="input-group">
                            <input type="text" name="keywords" class="form-control" placeholder="@lang('Search Products')" required="required" value="{{request('keywords')}}">
                            <div class="input-group-addon" onclick="document.getElementById('ST-SEARCH-M').submit();"><i class="glyphicon glyphicon-search"></i></div>
                        </div>
                    </form>
                    <ul class="nav navbar-nav">
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="true">@lang('Categories')<span class="caret"></span></a>
                            <ul class="dropdown-menu open st-cat">
                                <!--一级分类-->
                                @foreach($_categories as $category)
                                <li class="dropdown">
                                    <a href="{{route('product.list.rewrite', ['catid'=>$category->id])}}">{{$category->name}}</a>
                                    <ul class="dropdown-menu show st-subcat">
                                        <!--二级分类-->
                                        @foreach($category->children as $child)
                                        <li class="dropdown">
                                            <a href="{{route('product.list.rewrite', ['catid'=>$child->id])}}">{{$child->name}}</a>
                                            <ul class="dropdown-menu show st-subsubcat">
                                                <!--三级分类-->
                                                @foreach($child['children'] as $childChild)
                                                <li><a href="{{route('product.list.rewrite', ['catid'=>$childChild->id])}}">{{$childChild->name}}</a></li>
                                                @endforeach
                                            </ul>
                                        </li>
                                        @endforeach
                                    </ul>
                                </li>
                                @endforeach
                            </ul>
                        </li>
                    </ul>
                </div><!-- /.navbar-collapse -->
                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="nav-user">
                    <ul class="nav navbar-nav">
                        <li class="dropdown"><a href="/">@lang('Home')</a></li>
                        @guest
                        <li class="dropdown"><a rel="nofollow" href="{{route('login')}}">@lang('Sign in')</a></li>
                        <li class="dropdown"><a rel="nofollow" href="{{route('register')}}">@lang('Sign up')</a></li>
                        @endguest
                        <li>
                            <a rel="nofollow" href="{{route('user.index')}}">
                                @lang('My Account')
                                @auth
                                ({{auth()->user()->nickname}})
                                @endauth
                            </a>
                        </li>
                        <li><a rel="nofollow" href="{{route('user.my.orders')}}">@lang('My Orders')</a></li>
                        <li><a rel="nofollow" href="{{route('user.my.collects')}}">@lang('My Wish List')</a></li>
                        <li><a rel="nofollow" href="{{route('user.my.feedback')}}">@lang('My Feedback') <span class="badge">{{$_unread_feedback_replies_total}}</span></a></a></li>
                        <li role="separator" class="divider"></li>
                        <li class="dropdown"><a rel="nofollow" href="/article-53.html">@lang('Contact Us')</a></li>
                        <li class="dropdown"><a rel="nofollow" href="{{route('user.my.feedback')}}">@lang('Feedback us')</a></li>
                        <li role="separator" class="divider"></li>
                        @if(!empty($_languages))
                        <li class="dropdown st-navtop-items-account">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="glyphicon glyphicon-globe"></i>
                                <font>{{app('strongshop')->getCurrentLanguageName()}}</font><span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu">
                                @foreach($_languages as $lang)
                                <li @if($lang['code'] == app('strongshop')->getCurrentLanguage()) class="active" @endif>
                                    <a rel="nofollow" href="{{request()->fullUrlWithQuery(['locale'=>$lang['code']])}}">{{$lang['name']}}</a>
                                </li>
                                @endforeach
                            </ul>
                        </li>
                        @endif
                        <li class="dropdown st-navtop-items-account">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <font>{{$_current_currency_name}}</font><span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu">
                                @foreach($_currencies as $currency)
                                <li @if($currency['code'] == app('strongshop')->getCurrentCurrency()) class="active" @endif>
                                    <a rel="nofollow" href="{{request()->fullUrlWithQuery(['currency'=>$currency['code']])}}">{{$currency['name']}}</a>
                                </li>
                                @endforeach
                            </ul>
                        </li>
                        @auth
                        <li role="separator" class="divider"></li>
                        <li><a rel="nofollow" href="{{route('logout')}}"><i class="glyphicon glyphicon-log-out"></i><font>@lang('Sign out')</font></a></li>
                        @endauth
                    </ul>
                </div><!-- /.navbar-collapse -->
            </div>
        </nav>

        @include('layouts.includes.errors')

        @yield('content')
        <!--        <div class="st-translate">
                    <div class="st-h100"></div>
                    <div class="container">
                        <p id="google_translate_element" class="pull-right"></p>
                    </div>
                </div>-->
        <!--底部信息-->
        <div class="st-footer">
            <div class="st-footer-service">
                <div class="container">
                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Services')</dt>
                                <dd><a href="{{route('user.my.feedback')}}">@lang('Feedback')</a><dd>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'privacy'])}}">@lang('Privacy & Security')</a><dd>
                            </dl>
                        </div>
                   <!--     <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Shopping with us')</dt>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'delivery'])}}">@lang('Delivery')</a><dd>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'returns'])}}">@lang('Returns')</a><dd>
                            </dl>
                        </div>-->
                        <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Customer Support')</dt>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'aboutus'])}}">@lang('About us')</a><dd>
                                <dd><a href="{{route('article.show.postid', ['postid'=>'contactus'])}}">@lang('Contact us')</a><dd>
                            </dl>
                        </div>
                        <!--<div class="col-sm-6 col-md-3">
                            <dl>
                                <dt>@lang('Connect with us')</dt>
                                <dd class="st-footer-service-icon">
                                    <a href="#" >
                                        <i class="bi-facebook"></i>
                                    </a>
                                    <a href="#" >
                                        <i class="bi-instagram"></i>
                                    </a>
                                    <a href="#" >
                                        <i class="bi-pinterest"><svg t="1624281165955" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3033" width="22" height="20"><path d="M512 0.040958c-282.75605 0-511.979521 229.223471-511.979521 511.979521 0 216.895004 134.937323 402.149674 325.393704 476.75533-4.484941-40.50782-8.519339-102.641654 1.781689-146.876685 9.297548-39.954882 60.044958-254.49478 60.044958-254.49478s-15.318427-30.657334-15.318427-75.99824c0-71.185633 41.265549-124.329107 92.647814-124.329107 43.682093 0 64.775649 32.787169 64.775649 72.107196 0 43.927843-27.954082 109.604576-42.412384 170.448222-12.062238 50.972681 25.558018 92.524939 75.813927 92.524939 91.00948 0 160.945882-95.965441 160.945882-234.466141 0-122.588376-88.080957-208.293748-213.864085-208.293748-145.668413 0-231.168993 109.276909-231.168993 222.178633 0 44.00976 16.956762 91.193792 38.091276 116.833727 4.177753 5.078837 4.792128 9.522819 3.542898 14.683573-3.891044 16.178553-12.512779 50.952202-14.212551 58.078957-2.232231 9.358986-7.413463 11.345466-17.120595 6.840046-63.956482-29.776729-103.931843-123.264189-103.931843-198.340866 0-161.49882 117.345706-309.829527 338.275109-309.829527 177.595456 0 315.625135 126.561338 315.625135 295.698892 0 176.448622-111.24291 318.451262-265.655934 318.451262-51.873765 0-100.655174-26.950602-117.345706-58.795728 0 0-25.680893 97.767609-31.886085 121.707772-11.570737 44.460302-42.76053 100.204632-63.649294 134.220551 47.900804 14.826927 98.812048 22.834287 151.607376 22.834287 282.75605 0 511.979521-229.223471 511.979521-511.979521s-229.223471-511.979521-511.979521-511.979521z" p-id="3034" fill="#707070"></path></svg></i>
                                    </a>
                                </dd>
                                <dd class="st-footer-service-signup">
                                    <p>@lang('Sign Up for Our Newsletter'):</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Enter your email">
                                        <span class="input-group-addon" id="basic-addon2">@lang('Subscribe')</span>
                                    </div>
                                </dd>
                            </dl>
                        </div>-->
                    </div>
                </div>
            </div>
            <!--备案信息-->
            <div class="st-footer-beian">
                <div class="container">
                    &copy; {{date('Y')}} {{config('app.name')}} Copyright, All Rights Reserved. Powered By <a target="_blank" href="http://www.shiptobuy.com">{{config('app.name')}} </a>
                </div>
             
            </div>
            <div class="st-h10"></div>
        </div>
        <!-- Scripts -->
        <script src="{{ asset('js/vendor/jquery-1.11.2.min.js') }}"></script>
        <script src="{{ asset('js/vendor/jquery.form.min.js') }}"></script>
        <script src="{{ asset('js/vendor/jquery.cookie.js') }}"></script>
        <script src="{{ asset('js/vendor/bootstrap.min.js') }}"></script>
        <script src="{{ asset('js/vendor/bootstrap-hover-dropdown.js') }}"></script>
        <script src="{{ asset('plugins/layer/layer.js') }}"></script>
        <script src="{{ asset('js/main.js') }}?v={{env('APP_VERSION')}}"></script>
        @if(env('BlockSimpliedChineseBrowerVisitByJS'))
        <script>
                                if (Util.maybe360Browser()) {
                                    console.log('360Browser');
                                    window.location.href = 'http://www.baidu.com';
                                }
        </script>
        @endif
        <script>
//js 初始化
            Util.init();
//产品分类菜单
            Util.allCategories();
//顶部全局通知手动关闭后不再显示
            Util.navNotice();
//导航区域-购物车(显隐)
            Util.navCart();
        </script>
        @if(empty($_languages))
        <!--谷歌翻译-->
        <script>
            function googleTranslateElementInit() {
                new google.translate.TranslateElement({
                    pageLanguage: '{{ str_replace('_', ' - ', app()->getLocale()) }}',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                    gaTrack: true
                }, 'st-google-translate-element');
            }
        </script>
        <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
        @endif
        @stack('scripts_bottom')
        <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-0193E7TWL0"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-0193E7TWL0');
</script>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-G906CL8YZ7">
</script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-G906CL8YZ7');
</script>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-17039594744"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'AW-17039594744');
</script>

      <!-- Responsive Popup Code - Perfect Proportions + Mobile Adaption 
<div id="email-popup" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.4);z-index:9999;justify-content:center;align-items:center;font-family:'Nunito', sans-serif;">
    <div style="background:#fff;border-radius:16px;width:90%;max-width:800px;max-height:90vh;display:flex;flex-direction:row;position:relative;overflow:hidden;box-shadow:0 5px 20px rgba(0,0,0,0.08);">
   
        <button id="close-popup" style="position:absolute;top:15px;right:15px;width:32px;height:32px;background:#f5f5f5;border:none;border-radius:50%;color:#888;font-size:18px;cursor:pointer;display:flex;justify-content:center;align-items:center;z-index:2;">×</button>
        
 
        <div style="width:50%;aspect-ratio:1/1;background:#f9f9f9;position:relative;overflow:hidden;">
            <img src="https://images.unsplash.com/photo-1556740738-b6a63e27c4df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                 style="width:100%;height:100%;object-fit:cover;">
   
            <div style="position:absolute;bottom:20px;left:20px;background:rgba(255,255,255,0.9);padding:6px 12px;border-radius:16px;font-size:12px;font-weight:600;color:#ff6b8b;box-shadow:0 2px 8px rgba(0,0,0,0.1);">
                ✨ Limited Offer
            </div>
        </div>
        

        <div style="width:50%;padding:30px;box-sizing:border-box;display:flex;flex-direction:column;overflow-y:auto;">
            <div style="margin-bottom:20px;">
                <h2 style="font-size:24px;color:#333;margin:0 0 10px 0;font-weight:600;">Exclusive Deal</h2>
                <p style="color:#666;margin:0;line-height:1.5;font-size:15px;">
                    Get <span style="font-weight:bold;color:#ff6b8b;">$100 OFF</span> on your first order over <span style="font-weight:bold;color:#ff6b8b;">$1,000</span>
                </p>
            </div>
            

            <form id="email-form" style="margin-top:auto;margin-bottom:15px;">
                <div style="margin-bottom:15px;position:relative;">
                    <input type="email" name="email" placeholder="Enter your email" required 
                           style="width:100%;padding:12px 15px;border:1px solid #e0e0e0;border-radius:8px;font-size:14px;background:#fafafa;padding-left:40px;">
                    <span style="position:absolute;left:15px;top:50%;transform:translateY(-50%);color:#aaa;">✉️</span>
                </div>
                <button type="submit" style="width:100%;padding:14px;background:#ff6b8b;color:white;border:none;border-radius:8px;font-size:15px;font-weight:600;cursor:pointer;transition:all 0.2s;">
                    Claim Discount
                </button>
                @csrf
            </form>
            
           
            <div id="success-message" style="display:none;background:#f8f9fa;border-radius:8px;padding:15px;text-align:center;">
                <p style="color:#4CAF50;font-weight:600;margin-bottom:5px;">🎉 Success!</p>
                <p style="color:#666;font-size:13px;">Your discount code has been sent</p>
            </div>
            
            
            <p style="color:#999;font-size:12px;text-align:center;margin-top:15px;">
                We respect your privacy and won't share your email
            </p>
        </div>
    </div>
</div>-->

<!-- Add Font -->
<link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600&display=swap" rel="stylesheet">

<style>
    #close-popup:hover {
        background: #eee;
        color: #555;
        transform: rotate(90deg);
    }
    
    button[type="submit"]:hover {
        background: #ff5b7d;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255,107,139,0.3);
    }
    
    input:focus {
        border-color: #ff6b8b !important;
        background: #fff !important;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255,107,139,0.2);
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        #email-popup > div {
            flex-direction: column;
            max-height: 90vh;
        }
        #email-popup > div > div {
            width: 100% !important;
        }
        #email-popup > div > div:first-child {
            aspect-ratio: 16/9;
            max-height: 40vh;
        }
        #email-popup > div > div:last-child {
            padding: 25px;
            max-height: 60vh;
        }
    }
    
    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 6px;
    }
    ::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.1);
        border-radius: 3px;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show popup after 3 seconds
    setTimeout(function() {
        document.getElementById('email-popup').style.display = 'flex';
    }, 3000);
    
    // Close popup
    document.getElementById('close-popup').addEventListener('click', function() {
        document.getElementById('email-popup').style.display = 'none';
    });
    
    // Close when clicking outside
    document.getElementById('email-popup').addEventListener('click', function(e) {
        if (e.target === this) {
            this.style.display = 'none';
        }
    });
    
    // Form submission
    document.getElementById('email-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Disable button to prevent multiple submissions
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = 'Processing...';
        
        fetch('/submit-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            },
            body: JSON.stringify({
                email: document.querySelector('input[name="email"]').value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('email-form').style.display = 'none';
                document.getElementById('success-message').style.display = 'block';
                
                setTimeout(function() {
                    document.getElementById('email-popup').style.display = 'none';
                }, 2000);
            } else {
                alert(data.message);
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Claim Discount';
            }
        })
        .catch(error => {
            alert('Submission failed, please try again');
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Claim Discount';
        });
    });
});
</script>
    </body>
    <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNHXPH7G"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
</html>




