body{
    font-family: 'Times New Roman', 'Microsoft YaHei', serif;

    /*background-color: #f2f2f2;*/
}


.st .pull-right .dropdown-menu{
    left:auto;
    right:0;
}
.st .pagination a,.st .breadcrumb a{
    color:#333;
}
/*div高度*/
.st-h5{
    height: 5px;
    line-height: 5px;
}
.st-h10{
    height: 10px;
    line-height: 10px;
}
.st-h20{
    height: 20px;
    line-height: 20px;
}
.st-h50{
    height: 50px;
    line-height: 50px;
}
.st-h100{
    height: 100px;
    line-height: 50px;
}
.st-navtip .alert{
    margin-bottom: 5px;
    text-align: center;
}
.st-navtip > div:first-child > div.alert{
    margin-top: 5px;
}
/*导航区域-顶部*/
.st-navtop{
    height: 30px;
    line-height: 30px;
    background-color:#FAFAFA;
    border-bottom:1px solid #e9e9e9;
}
.st-navtop li a:hover,.st-navtop li a:hover i{
    color:#4285F4;
    background:none;
}
.st-navtop-items > li > a{
    font-size:12px;
    color:#666;
    padding-top: 0;
    padding-bottom: 0;
}
.st-navtop-items > li:first-child > a{
    padding-left: 0;
}
.st-navtop-items > li:last-child > a{
    padding-right: 0;
}
.st-navtop-items > li a font{
    display: inline-block;
    padding:0 5px;
}
.st-navtop-items > li > a > i{
    color:#999;
}
/*导航区域-品牌和搜索*/
.st-navbrand{
    height: 90px;
    background: #ffffff;
}
.st-navbrand-logo{
    max-width: 200px;
    min-width: 170px;
    padding-top: 10px;
}
.st-navbrand-slogan{
    line-height: 90px;
    font-style:italic;
}
.st-navbrand-search{
    padding-top:25px;
}
.st-navbrand-search .input-group-addon{
    background-color: #666666;
    border: 2px solid #666666;
    cursor: pointer;
}
.st-navbrand-search input{
    border: 2px solid #666666;
}
.st-navbrand-search .input-group-addon i{
    color: #ffffff;
}
.st-navbrand-cart{
    position: relative;
    cursor: pointer;
    padding-top:30px;
}
.st-navbrand-cart a{
    display: inline-block;
    color:#666;
}
.st-navbrand-cart i{
    position: relative;
    font-size: 28px;
    color:#666;
}
.st-navbrand-cart .badge{
    position: absolute;
    bottom:29px;
    left: 4px;
    background-color: #EA4335;
    color:#ffffff;
    width:24px;
    padding:3px;
}
.st-navbrand-cart i:hover,.st-navbrand-cart a:hover{
    color:#999;
    text-decoration: none;
}
/*导航区域-菜单*/
.st-navmenu{
    background-color: #F9F9F9;
    border-top:1px solid #e9e9e9;
}
.st-navmenu .nav > li > a{
    height: 44px;
    line-height: 44px;
    color:#666;
    border-radius: 0;
    padding:0;
    margin-right: 30px;
}
.st-navmenu .nav > li > a:hover{
    color:#080808;
    background:none;
    border-radius: 0;
    border-bottom: 3px solid #EA4335;
}
#products{
    position: relative;
}
.st-allcat{
    display: none;
    position: absolute;
    top:44px;
    left: 0;
    z-index: 999;
    width: 240px;
    box-shadow: none;
    border-radius: 0;
    border-right:none;
}
.st-allcat-items{
    border: none;
    box-shadow: none;
}
.st-allcat-items > li{
    padding:0;
    margin:0;
    border:none;
}
.st-allcat-items > li > a{
    color:#666;
    text-decoration: none;
    display:inline-block;
    padding: 0 8px;
    width:100%;
    height: 35px;
    line-height: 35px;
    border:1px solid #ddd;
    border-left: 1px solid #ffffff;
    border-top:1px solid #ffffff;
    border-bottom:1px solid #ffffff;
}
.st-allcat-items > li > a > i{
    padding-right:5px;
    color:#BCBCBC;
    font-size: 16px;
}
.st-allcat-items > li.active > a{
    background-color:#ffffff;
    color: orange;
    text-shadow:none;
    border-right: none;
    border-top:1px solid #ddd;
    border-bottom:1px solid #ddd;
    border-radius: 0;
}
.st-allcat-items > li.active:last-child > a{
    border-bottom:1px solid #ffffff;
}
.st-allcat-items > li.active:first-child > a{
    border-top:1px solid #ffffff;
}
.st-allcat-items > li.active > a > i{
    color: orange;
}
.st-allcat-content{
    display: none;
    position: absolute;
    top:-1px;
    left:238px;
    background-color: #ffffff;
}
.st-allcat-content-item{
    width:490px;
    min-height: 400px;
    border: 1px solid #ddd;
    padding:0 15px;
    padding-right: 0;
    overflow-x: hidden;
}
.st-allcat-content-item > dl{
    width: 50%;
    /*min-height: 230px;*/
    float: left;
    margin: 15px 0;
}
.st-allcat-content-item > dl > dt{
    border-bottom: 1px solid #ddd;
    margin-right: 15px;
    margin-bottom: 7px;
    padding:5px 0;
    padding-top:0;
}
.st-allcat-content-item > dl > dt > a{
    color:#333;
    text-decoration: none;
}
.st-allcat-content-item > dl > dd{
    line-height: 25px;
}
.st-allcat-content-item > dl > dd > i{
    font-size:18px;
    color:#999;
    vertical-align: middle;
    padding-right: 2px;
}
.st-allcat-content-item > dl > dd > a{
    color:#666;
    text-decoration: none;
}
.st-allcat-content-item > dl > dd > a::before{
    content: "•";
    font-size:18px;
    color:#999;
    padding-right: 2px;
    vertical-align: middle;
}
.st-allcat-content-item > dl a:hover,.st-allcat-content-item > dl > dd > a:hover::before{
    color:orange;
}
/*导航区域-顶部-移动端*/
.st-header{
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top:none;
    margin-bottom: 0;
}
.st-header .navbar-header{
    /*padding:0 5px;*/
}



/* 响应式品牌容器 */
.st-header .navbar-brand {
  display: flex !important; /* 覆盖默认inline-block */
  padding: 10px 0;
  width: auto;
  /* max-width: 280px; 最大宽度限制 */
   /*height: 60px; 固定容器高度 */
  transition: opacity 0.3s ease; /* 交互效果 */
}

/* 图片自适应配置 */
.brand-logo {
  width: auto;
  height: 100%; /* 继承容器高度 */
  max-height: 44px; /* 移动端优化 */
  object-fit: contain; /* 保持比例 */
  image-rendering: -webkit-optimize-contrast; /* 锐化处理 */
}

/* 桌面端优化 */
@media (min-width: 768px) {
  .st-header .navbar-brand {
    max-width: 320px;
    padding: 15px 0;
  }
  .brand-logo {
    max-height: 52px;
  }
}

/* 超宽屏适配 */
@media (min-width: 1200px) {
  .st-header .navbar-brand {
    max-width: 360px;
  }
}


.st-header-cart{
    position: relative;
    color:#666;
    font-size: 26px;
    margin-right: 15px;
    padding-top:8px;
}
.st-header-cart > i{
    padding-left: 3px;
}
.st-header-cart > span{
    position: absolute;
    top:3px;
    left:9px;
    display: inline-block;
    text-align: center;
    color:#EA4335;
    font-size: 1rem;
    width:15px;
}
.st-header .navbar-form{
    border-top:none;
    border-bottom: none;
}
.st-header .navbar-nav ul.st-subcat,.st-header .navbar-nav ul.st-subsubcat{
    padding-left: 25px;
}
.st-header .navbar-nav ul.st-cat > li{
    border-bottom: 1px solid #ddd;
}
.st-header .navbar-nav ul.st-subcat > li{
    border-top: 1px solid #ddd;
}
.st-header .navbar-nav ul.st-subcat > li > ul.st-subsubcat > li:first-child{
    border-top: 1px solid #ddd;
}
/*首页推荐产品和产品列表页*/
.st-home-product > div.container > div.row,.st-main-product > div.row{
    padding:0 12px;
}
.st-home-product > div.container > div.row > div,.st-main-product > div.row > div{
    padding:0 4px;
}
.st-home-product .thumbnail,.st-main-product .thumbnail{
    padding: 0;
}
.st-home-product .thumbnail > a.st-thumb,.st-main-product .thumbnail > a.st-thumb{
    display: inline-block;
    width: 100%;
    height: 200px;
    overflow: hidden;
}
.st-home-product .thumbnail > a.st-thumb img,.st-main-product .thumbnail > a.st-thumb img{
    width:100%;
}
.st-home-product .caption,.st-main-product .caption{
    padding-top:0
}
.st-home-product .caption > h5 > a,.st-main-product .caption > h5 > a{
    display: inline-block;
    height: 30px;
    overflow-y: hidden;
    color: #333;
    font-size:1.4rem;
}
.st-home-product .caption > p,.st-main-product .caption > p{
    margin-bottom: 5px;
}
.st-home-product-price{
    color:#080808;
    font-weight: bolder;
    font-size:1.5rem;
}
.st-home-product-sold{
    color:#666;
    font-size: 12px;
}
.st-home-product-addcart{
    margin: 0 9px;
    margin-bottom:10px ;
}
.st-home-product-addcart > button{
    width: 100%;
}
.st-home-product-title{
    display: block;
    width: 100%;
    text-align: center;
    color:#000000;
}
.st-home-product-title > span{
    font-size: 13px;
    vertical-align: top;
}
/*底部*/
.st-footer{
    background-color: #FAFAFA;
    padding-top: 80px;
}
.st-footer-service{
    padding-bottom: 80px;
    color:#333;
}
.st-footer-service dt{
    padding-bottom: 5px;
}
.st-footer-service a{
    display: inline-block;
    line-height: 30px;
    color:#666;
}
.st-footer-beian{
    text-align: center;
    font-size: 12px;
    color: gray;
}
.st-footer-beian a{
    color:gray;
}
.st-footer-service-icon > a{
    text-decoration: none;
    display: inline-block;
    padding-right: 10px;
}
.st-footer-service-icon > a > i{
    font-size: 20px;
}
.st-footer-service-icon > a > i:hover{
    color:#999;
}
.st-footer-service-icon > a > img{
    width: 25px;
    display: inline-block;
    vertical-align: top;
}
.st-footer-service-icon .bi-pinterest{
    /*padding-top:3px;*/
    vertical-align: bottom;
}
.st-footer-service-signup{
    padding-top:10px;
}
.st-footer-service-signup p{
    color:#666;
}
.st-footer-service-signup span{
    background: #000000;
    color: #ffffff;
    font-weight: bolder;
    border: 1px solid #000000;
}
/*导航路径/面包屑*/
.st-breadcrumb .breadcrumb{
    margin: 5px 0;
    background-color:#ffffff;
    padding-left:0;
}
/*产品列表页*/
.st-main{}
.st-main-left>div{
    background: #F9F9F9;
    padding:10px;
    margin-bottom: 10px;
}
.st-main-left a{
    color:#666;
}
.st-main-left h5,#FILTER h5{
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    font-weight: bolder;
    margin-top: 0;
}
.st-main-left-category a,.st-main-product-header-filter a{
    color:#333;
}
.st-main-left-category dl{
    margin-bottom: 5px;
}
.st-main-left-category dt a{
    color:#333;
    font-weight: lighter;
}
.st-main-left-category dd{
    margin-left: 15px;
}
.st-main-left-category > dl > dt{
    border-bottom: 1px solid #ddd;
}
.st-main-left-category > dl > dt > a{
    font-weight: bold;
}
.st-main-left-category > dl > dd::before{
    position: absolute;
    content: "•";
    left:32px;
    color:gray;
}
.st-main-left-category .active{
    background-color: #BCBCBC;
}
.st-main-product-header{
    padding-bottom:8px;
}
.st-main-product-header > span{
    display: inline-block;
}
.st-main-product-header-pager .pager{
    margin: 0;
    padding: 0;
}
.st-main-product-header-pager .pager li a{
    padding:2px 14px;
}
.st-main-product-header-pager ul.pagination{
    margin: 0 0;
    margin-bottom: 10px;
}
.st-main-product-header-sortby{
    line-height: 34px;
}
.st-main-product-header-sortby select{
    height: 26px;
}
.st-main-product-header-filter{
    border:1px solid #ddd;
    padding:2px 14px;
    border-radius: 15px;
}
/*产品详情-图片放大镜*/
.st-detail-img{
    position:relative;
    margin-bottom: 15px;
}
.st-detail-img-left {
    position:relative;
    display:inline-block;
    *display:inline;
    *zoom:1;
    width:10%;
    height: 417px;
    overflow-x:hidden;
    overflow-y:auto;
}
.st-detail-img-left::-webkit-scrollbar{
    display: none;
}
.st-detail-img-left ul {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    font-size:0;
    padding: 0;
}
.st-detail-img-left ul li {
    display:inline-block;
    *display:inline;
    *zoom:1;
    border:1px solid #BCBCBC;
    border-color:#F0F0F0;
    margin-bottom: 2px;
    border-radius: 2px;
    min-width: 35px;
    min-height: 35px;
}
.st-detail-img-left ul li:last-of-type {
    margin-bottom:0;
}
.st-detail-img-left ul .active {
    border-color:#999;
}
.st-detail-img-left ul li img{
    width:100%;
}
.st-detail-img-right {
    vertical-align:top;
    position:relative;
    display:inline-block;
    *display:inline;
    *zoom:1;
    width:460px;
    width:89%;
    border:1px solid #ddd;
}
.st-detail-img-right .pic {
    position:relative;
    width: 100%;
    /*min-height: 318px;*/
}
.st-detail-img-right .pic img{
    width: 100%;
}
.st-detail-img-right .pic .magnify {
    width:230px;
    height:230px;
    display:none;
    position:absolute;
    top:0;
    left:0;
    cursor:crosshair;
    background-color:#000000;
    opacity:.3;
    filter:alpha(opacity=30);
}
.st-detail-img-right .bigpic {
    display:none;
    position:absolute;
    top:-1px;
    right:-460px;
    width:460px;
    height:460px;
    z-index:333;
    border:1px solid #ddd;
    border-left: none;
    overflow:hidden;
}
.st-detail-img-right .bigpic > img {
    width:920px;
    height:920px;
    position:absolute;
    top:0;
    left:0;
}
.st-detail-img-right-share{
    position: absolute;
    left:0;
    bottom:-40px;
}
.st-detail-img .swiper-container {
    /*width: 100%;*/
    min-height: 260px;
}
.st-detail-img .swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;
    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}
/*产品详情-产品属性信息*/
.st-detail-attr{
    /*padding-bottom: 10px;*/
}
.st-detail-attr > h3{
    padding-top: 0;
    margin-top: 0;
    font-size:19px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    line-height: 1.4;
}
.st-detail-attr > dl{
    display: block;
    clear: both;
    margin-bottom: 0px;
}
.st-detail-attr > dl > dt{
    display: block;
    line-height: 20px;
    padding-right: 5px;
    color:#555555;
    font-weight: normal;
}
.st-detail-attr > dl > dd{
    display: block;
    text-align: center;
    line-height: 20px;
}
.st-detail-attr .st-attr > dd{
    display: inline-block;
    margin:0 10px 10px 0;
    border:1px solid #BCBCBC;
    cursor: pointer;
    min-width: 40px;
    text-align: center;
    padding:0 3px;
}
.st-detail-attr .st-picture > dd{
    width:40px;
    height: 40px;
    overflow: hidden;
    padding:0;
}
.st-detail-attr .st-attr > dd > img{
    width:100%;
}
.st-detail-attr .st-attr > dd.st-stockout,.st-detail-attr .st-attr > dd.st-invalid{
    border-style: dotted;
    opacity: .6;
    filter:alpha(opacity=60);
}
.st-detail-attr .st-price{
    margin-top: 20px;
    margin-bottom: 20px;
}
.st-detail-attr .st-price > dt{
    font-size: 2.5rem;
    color:#333;
    font-weight: 700;
}
.st-detail-attr .st-price > dt > del{
    font-size: 1.8rem;
    color:#666;
    padding-left: 10px;
    font-weight: lighter;
}
.st-detail-attr .st-attr > dd.active{
    border:2px solid #EA4335;
}
.st-detail-attr .st-attr > dd:hover{
    box-shadow: 0px 0px 5px #BCBCBC;
}
.st-detail-attr .st-general > dt,.st-detail-attr .st-general > dd{
    display: inline-block;
}
.st-detail-attr .st-itemcode > dd{
    display: block;
    text-align: left;
    color:#999;
    font-size: 12px;
}
/*产品详情操作按钮*/
.st-detail-btn button{
    font-size: 12px;
    margin-top:5px;
    margin-bottom:5px;
}
.st-detail-btn button span{
    text-align: left;
    float: left;
    display: inline-block;
    padding-right: 8px;
}
.st-detail-btn .st-share a,.st-detail-img-right-share .st-share a{
    font-size:16px;
    color:#666;
    padding:0 5px;
}
.st-detail-btn .st-share a:hover{
    color:#999;
}
.st-detail-btn-right{
    border:1px solid #ddd;
    padding:10px 15px;;
    border-radius: 5px;
}
.st-detail-btn-right .st-qty > input{
    width:50px;
}
.st-detail-btn-right .st-instock{
    color:#34A853;
}
.st-detail-btn-right .st-stockout{
    color:#EA4335;
}
.st-detail-btn-right .st-whosale td font{
    padding-right: 5px;
    width:15px;
    display: inline-block;
}
.st-detail-btn-right .addtocart{
    /*width:100%;*/
}
.st-detail-btn-right .st-btn .buynow > span{
    padding-right: 5px;
}
.st-main-detail .nav-tabs li a{
    color:#555555;
}
.st-main-detail-reviews-content > dl{
    border-bottom: 1px solid #ddd;
    padding-top: 15px;
}
.st-main-detail-reviews-content > dl > dt{
    line-height: 35px;
}
.st-main-detail-reviews-content > dl > dt i{
    color:orange;
}
.st-main-detail-reviews-content > dl > dd.st-datetime{
    color:#666;
    font-size:12px;
    line-height: 25px;
}
#Details img{
    max-width:100%;
    height: auto;
}
/*个人中心*/
.st-user .media-heading > a{
    color:#333;
}
.st-user .panel-body .page-header{
    margin-top:0;
}
.st-user-info .st-emailnotvalidated{
    color:#EA4335;font-size: 12px;
}
.st-user-info .st-emailnotvalidated a{
    text-decoration: underline;color:#4285F4;
}
.st-user-orders .st-price{
    font-size:12px;
    color:#666;
}
.st-user-address-add select{
    margin-top: 5px;
}
.st-user-address-default .st-default dl{
    padding:5px;
}
.st-user-address-default .st-default dl:first-child{
    margin-right: 100px;
}
.st-user-address-default .st-default dl > dt{
    color:#4285F4;
}
.st-user-address-list dl{
    padding:5px;
    border: 2px dotted #999;
}
.st-user-address-list dl dd small{
    color:#666;
    font-style: italic;
    font-size: 12px;
}
.st-user-feedback-list > dl{
    border-bottom: 1px solid #ddd;
    padding-top: 15px;
}
.st-user-feedback-list > dl > dt{
    line-height: 35px;
}
.st-user-feedback-list > dl > dt i{
    color:orange;
}
.st-user-feedback-list dd.st-datetime{
    color:#666;
    font-size:12px;
    line-height: 25px;
}
.st-user-feedback-list > dl > dd.st-reply > dl{
    padding-left: 30px;
    border-top:1px dotted #999;
}
.st-user-feedback-list>dl.st-reply{
    text-align: right;
}
.st-user-feedback-list-replies>dl{
    border-bottom: none;
    padding-top:0;
    margin-bottom: 10px;
}
.st-user-feedback-list-replies>dl>dd.st-text{
    display: inline-block;
    max-width:70%;
    border: 1px solid #eee;
    padding: 5px 10px;
    border-radius: 10px;
    word-break: break-word;
    text-align: left;
}
.st-user-feedback-list-replies>dl.st-reply>dd.st-text{
    float:right;
    background: #eee;
}
.st-user-wishlist .media .media-object,.st-user-orders .media .media-object{
    width:64px;
}
.st-user-wishlist .media .media-body .st-itemcode,.st-user-orders .media .media-body .st-itemcode{
    color:#999;font-size:12px;
}
.st-user-wishlist .media .media-body .st-datetime{
    color:#666;text-align: right;
}
.st-user-orders p{
    margin-bottom: 0px;
}
.st-user-orders .panel-body > div.st-date > p > label{
    width:100px;
}
.st-user-orders .panel-body > div.st-date > p.st-shipdesc{
    text-indent: 100px;
}
.st-user-orders table>thead>tr>th{
    border-bottom:none;
}
.st-user-orders table>thead>tr:first-child>th{
    border-bottom: 2px solid #ddd;
}

/*购物车*/
.st-cart .page-header{
    margin-top: 0;
}
.st-cart-checkout{
    padding:25px 15px;
}
.st-cart-checkout a.btn{
    width: 100%;
}
.st-cart-table tr:first-child td{
    border-top:none;
}
.st-cart-table a,.st-cart-table .st-price{
    color:#333;
}
.st-cart-table .st-price{
    margin-top:5px;
}
.st-cart-table .st-itemcode{
    color:#666;
}
.st-cart-table .st-price input{
    width:80px;
}
.st-cart-table .media-object{
    width:80px;
}
.st-cart-table .media-body > p{
    margin-bottom: 0;
}
.st-cart-table .st-wholesale{
    min-width:150px;
    font-size: 12px;
    color:gray;
}
.st-cart-table .st-wholesale td{
    padding:3px;
    border-top:none;
}
.st-cart-table .st-wholesale tr td:first-child{
    text-align:right;
}
.st-cart .st-fixed{
    position: fixed;top:5px;
}
/*订单结算 checkout*/
.st-checkout .form-group > label{
    font-weight: normal;
    font-size: 12px;
    color:#666;
}
.st-checkout div.row > div > div.panel{
    padding:0 15px;
}
.st-checkout div.page-header{
    margin-top: 0;
}
.st-checkout-address > div{
    padding: 0 15px;
}
.st-checkout-address select{
    margin-top: 5px;
}
.st-checkout-address .page-header{
    margin-left:-15px; 
    margin-right:-15px; 
}
.st-checkout-address .form-group > label > span{
    color:red;font-weight: bolder;padding-left: 3px;font-size: 16px;vertical-align: middle;
}
.st-checkout-cartlist > .page-header h4 font{
    font-weight: normal;
}
.st-checkout-shipingmethod div.radio label,.st-checkout-proceed div.radio label{
    line-height: 22px;
}
.st-checkout-proceed > p > label{
    display: inline-block;
    width: 49%;
    text-align: left;
    font-weight: normal;
}
.st-checkout-proceed > p > font{
    display: inline-block;
    width: 49%;
    text-align: right;
}
.st-checkout-proceed .btn{
    width:100%;
}
.st-signin .form-group label > font{
    color:#EA4335;
    font-weight: bolder;
}
#FILTER{
    display: none;
    position: absolute;
    top:0;
    right:0;
    z-index: 666;
    background-color:#fff;
    width:80%;
    height: 100%;
    overflow: auto;
    padding: 15px;
}
#FILTER .st-close{
    position: absolute;
    top:0;
    right:15px;
    cursor: pointer;
}
div.st-form-alert p{
    text-align: center;
}
div.st-form-alert:nth-child(2){
    margin-top: 10px;
}
#ST-NAVCART{}
#ST-NAVCART-PRODUCTS{
    display: none;
    width:380px;
    position: absolute;
    top:80px;
    right:0;
    background: #ffffff;
    z-index: 666;
    border:1px solid #BCBCBC;
    border-radius: 5px;
    box-shadow: 0px 0px 5px #BCBCBC;
}
#ST-NAVCART-PRODUCTS:before{
    content: "";
    position: absolute;
    top:-9px;
    right: 27px;
    z-index: 667;
    width:0;
    height:0;
    border-right:8px solid transparent;
    border-left:8px solid transparent;
    border-bottom:8px solid #BCBCBC;
}
#ST-NAVCART-PRODUCTS .page-header{
    margin:0;
    padding:8px 10px;
    box-shadow: 0 1px 3px #eee;
}
.st-navbrand-cart-product-list{
    min-height: 200px;
    max-height: 400px;
    overflow: auto;
}
.st-navbrand-cart-total .btn{
    width:100%;
}
.st-navbrand-cart-total .st-total > span{
    display: inline-block;
    width: 50%;
    text-align: right;
    padding-right: 10px;
}
.st-navbrand-cart-product-list .st-cart-table .st-wholesale{
    display: none;
}
.st-cart-checkout .st-total > span{
    display: inline-block;
    min-width: 60%;
    text-align: left;
}
.st-article > h3{
    text-align: center;
}
.st-article > p.author{
    text-align: center;
    color:gray;
}
.st-article-content{
    padding:15px;
}
.st-article-content img{
    max-width:100%;
    height: auto;
}
.st-home-aboutus .jumbotron>h1{
    font-size:27px;
}
.st-home-aboutus .jumbotron p{
    font-size:16px;
}
#Delivery table td{
    border:1px solid gray;
}
#Delivery table td p{
    margin: 0;
    padding: 0;
}
#Delivery table tr:first-child{
    background:#70ad47;
    line-height: 40px;
}
#Delivery table tr>td:first-child{
    width:230px;
}
.st-article-list a{
    color:#333;
    text-decoration: underline;
}
.st-article-list h4{
    font-size:16px;
}
.st-article-list .media-body{
    font-size: 14px;
    color:#666;
}
.st-article-origin{
    font-size:12px;
    color:#666;
}
.st-nav-user{
    color: #666;
    font-size: 26px;
    padding-right: 5px;
    padding-top: 8px;
}
/*响应式*/
@media (min-width: 1200px) {
    .st-home-product .thumbnail > a.st-thumb{
        height: 181px;
    }
}
@media (max-width: 992px) {
    .st-main-product .thumbnail > a.st-thumb{
        height: 181px;
    }
}
@media (max-width: 768px) {
    .st-home-product .thumbnail > a.st-thumb{
        height: 234px;
    }
}
@media (max-width: 767px) {
    .st-main-detail .nav-tabs li{
        min-width: 145px;
    }
    .st-home-product .thumbnail > a.st-thumb,.st-main-product .thumbnail > a.st-thumb{
        height: 165px;
    }
}
.st-nav-default-currencypay>a{
    color:#EA4335 !important;
    text-align: center;
}
#st-google-translate-element{
    height: 29px;overflow: hidden;
}
#st-google-translate-element>div>div{
    height: 30px;
    line-height: 27px;
    border:none;
    background: none;
}
#st-google-translate-element>div>div>span>a:hover{
    text-decoration: none;
}
#st-google-translate-element>div>div>span>a>span:last-child{
    font-size:12px;
}
.st-login-third a{
    font-size:20px;
    display: inline-block;
    padding-right:10px;
    color:#555555;
}
/* 新增缩略图自适应样式 */
.st-detail-img-left {
    width: 15% !important;
    height: auto !important;
    overflow: visible !important;
}

.st-detail-img-left ul {
    position: relative !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 5px !important;
    padding: 0 5px !important;
}

.st-detail-img-left ul li {
    width: 60px !important;
    height: 60px !important;
    border: 1px solid #eee !important;
    border-radius: 4px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    overflow: hidden !important;
}

.st-detail-img-left ul li img,
.st-detail-img-left ul li video {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.3s !important;
}

/* 视频播放器固定尺寸 */
#mainVideo {
    max-width: 100% !important;
    height: 400px !important; 
    object-fit: contain !important;
    background: #000 !important;
}

/* 视频图标居中 */
.video-icon {
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
    color: #fff !important;
    font-size: 24px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

/* 主图区域尺寸锁定 */
.st-detail-img-right {
    width: 85% !important;
    max-width: 600px !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .st-detail-img-left ul li {
        width: 60px !important;
        height: 60px !important;
    }
    
    #mainVideo {
        height: 300px !important;
    }
}




/* 强制所有产品图片填满容器 */
.st-thumb img,
.st-detail-img-left li img,
.swiper-slide img,
.st-detail-img-right .pic img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;  /* 自动裁剪填充 */
    object-position: center center !important;
    background: #f8f8f8 !important;
}

/* 固定图片容器尺寸 */
.st-thumb {
    height: 200px !important;
    overflow: hidden !important;
    position: relative !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .st-thumb {
        height: 150px !important;
    }
}

/* 详情页缩略图容器 */
.st-detail-img-left li {
    width: 80px !important;
    height: 80px !important;
    overflow: hidden !important;
    border: 1px solid #eee !important;
}



/* 现代化产品展示效果 - 添加到文件最底部 */
/* 现代化无边框产品展示 - 强制覆盖所有边框 */
.modern-product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    padding: 30px 0;
}

.modern-product-item {
    width: 100%;
    background: #fff;
    transition: all 0.3s ease;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.modern-product-item:hover {
    transform: translateY(-10px);
    border: none !important;
    box-shadow: none !important;
}

.product-link {
    display: block;
    text-decoration: none;
    color: inherit;
    border: none !important;
    outline: none !important;
}

.product-link:hover,
.product-link:focus,
.product-link:active {
    text-decoration: none;
    color: inherit;
    border: none !important;
    outline: none !important;
}

.product-image {
    width: 100%;
    height: 500px;
    overflow: hidden;
    background: #f8f8f8;
    position: relative;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.modern-product-item:hover .product-image img {
    transform: scale(1.08);
}

.product-info {
    padding: 20px 0;
    text-align: center;
    border: none !important;
    box-shadow: none !important;
}

.product-title {
    font-size: 18px;
    font-weight: 400;
    color: #333;
    margin: 0 0 10px 0;
    line-height: 1.4;
    transition: color 0.3s ease;
    border: none !important;
}

.modern-product-item:hover .product-title {
    color: #000;
}

.product-price {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
    border: none !important;
}

/* 强制移除所有可能的边框和阴影 */
.modern-product-item *,
.modern-product-item *:before,
.modern-product-item *:after {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .modern-product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 20px 0;
    }
    
    .product-image {
        height: 250px;
    }
    
    .product-title {
        font-size: 16px;
    }
    
    .product-price {
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .modern-product-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }
    
    .product-image {
        height: 400px;
    }
}
/* 现代化产品展示效果 - 添加到文件最底部 */