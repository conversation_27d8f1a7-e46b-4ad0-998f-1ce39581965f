<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

// 获取离线留言数据
$messages = DB::table('customer_service_offline_messages')
             ->orderBy('created_at', 'desc')
             ->limit(20)
             ->get();

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线留言管理 - 测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f4f4f4; }
        .main-header { background: #ffc107; color: #212529; padding: 1rem 0; margin-bottom: 2rem; }
        .card { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .message-item { border-left: 4px solid #007bff; }
        .message-item.unread { border-left-color: #dc3545; background: #fff5f5; }
        .message-item.replied { border-left-color: #28a745; background: #f8fff8; }
        .message-meta { font-size: 0.875rem; color: #6c757d; }
        .reply-form { background: #f8f9fa; border-radius: 0.375rem; padding: 1rem; margin-top: 1rem; }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <h1><i class="fa fa-envelope"></i> 离线留言管理</h1>
            <p class="mb-0">管理和回复客户离线留言</p>
        </div>
    </div>

    <div class="container">
        <div class="row mb-3">
            <div class="col-md-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/strongadmin">首页</a></li>
                        <li class="breadcrumb-item">在线客服</li>
                        <li class="breadcrumb-item active">离线留言</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-right">
                <button class="btn btn-primary" onclick="refreshMessages()">
                    <i class="fa fa-sync"></i> 刷新
                </button>
                <a href="/strongadmin/customer-service/settings" class="btn btn-secondary">
                    <i class="fa fa-cog"></i> 设置
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-inbox"></i> 留言列表 
                    <span class="badge badge-primary"><?= count($messages) ?></span>
                </h3>
            </div>
            
            <div class="card-body">
                <?php if (empty($messages)): ?>
                    <div class="text-center py-5">
                        <i class="fa fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无离线留言</h5>
                        <p class="text-muted">当客户在非工作时间访问时，会显示离线留言表单</p>
                        <button class="btn btn-primary" onclick="createTestMessage()">
                            <i class="fa fa-plus"></i> 创建测试留言
                        </button>
                    </div>
                <?php else: ?>
                    <div class="messages-list">
                        <?php foreach ($messages as $message): ?>
                            <div class="message-item card mb-3 <?= $message->status === 'pending' ? 'unread' : ($message->status === 'replied' ? 'replied' : '') ?>">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6 class="card-title mb-2">
                                                <i class="fa fa-user"></i> <?= htmlspecialchars($message->visitor_name) ?>
                                                <?php if ($message->status === 'pending'): ?>
                                                    <span class="badge badge-danger ml-2">待回复</span>
                                                <?php elseif ($message->status === 'replied'): ?>
                                                    <span class="badge badge-success ml-2">已回复</span>
                                                <?php endif; ?>
                                            </h6>
                                            
                                            <div class="message-meta mb-2">
                                                <i class="fa fa-envelope"></i> <?= htmlspecialchars($message->visitor_email) ?>
                                                <?php if ($message->visitor_phone): ?>
                                                    | <i class="fa fa-phone"></i> <?= htmlspecialchars($message->visitor_phone) ?>
                                                <?php endif; ?>
                                                | <i class="fa fa-clock"></i> <?= $message->created_at ?>
                                            </div>
                                            
                                            <div class="message-content">
                                                <strong>留言内容:</strong>
                                                <p class="mt-1"><?= nl2br(htmlspecialchars($message->message)) ?></p>
                                            </div>
                                            
                                            <?php if ($message->admin_reply): ?>
                                                <div class="admin-reply mt-3 p-3" style="background: #e3f2fd; border-radius: 0.375rem;">
                                                    <strong><i class="fa fa-reply"></i> 管理员回复:</strong>
                                                    <p class="mt-1 mb-0"><?= nl2br(htmlspecialchars($message->admin_reply)) ?></p>
                                                    <small class="text-muted">
                                                        回复时间: <?= $message->replied_at ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-4 text-right">
                                            <?php if ($message->status === 'pending'): ?>
                                                <button class="btn btn-primary btn-sm" onclick="showReplyForm(<?= $message->id ?>)">
                                                    <i class="fa fa-reply"></i> 回复
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn btn-outline-secondary btn-sm ml-1" onclick="viewDetails(<?= $message->id ?>)">
                                                <i class="fa fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- 回复表单 -->
                                    <div id="reply-form-<?= $message->id ?>" class="reply-form" style="display: none;">
                                        <h6><i class="fa fa-reply"></i> 回复留言</h6>
                                        <form onsubmit="submitReply(event, <?= $message->id ?>)">
                                            <div class="form-group">
                                                <textarea class="form-control" 
                                                          name="reply" 
                                                          rows="3" 
                                                          placeholder="请输入回复内容..."
                                                          required></textarea>
                                            </div>
                                            <div class="form-group mb-0">
                                                <button type="submit" class="btn btn-success btn-sm">
                                                    <i class="fa fa-paper-plane"></i> 发送回复
                                                </button>
                                                <button type="button" class="btn btn-secondary btn-sm ml-2" onclick="hideReplyForm(<?= $message->id ?>)">
                                                    取消
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示回复表单
        function showReplyForm(messageId) {
            $('#reply-form-' + messageId).slideDown();
        }

        // 隐藏回复表单
        function hideReplyForm(messageId) {
            $('#reply-form-' + messageId).slideUp();
        }

        // 提交回复
        function submitReply(event, messageId) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 发送中...';
            submitBtn.disabled = true;
            
            $.ajax({
                url: '/strongadmin/customer-service/offline-messages/' + messageId + '/reply',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        alert('回复发送成功！');
                        location.reload();
                    } else {
                        alert('回复失败: ' + (response.message || '未知错误'));
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    alert('回复失败: ' + (response?.message || '网络错误'));
                },
                complete: function() {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        }

        // 查看详情
        function viewDetails(messageId) {
            alert('查看留言详情功能 - ID: ' + messageId);
        }

        // 刷新留言
        function refreshMessages() {
            location.reload();
        }

        // 创建测试留言
        function createTestMessage() {
            if (confirm('确定要创建一条测试留言吗？')) {
                $.ajax({
                    url: '/api/customer-service/offline-message',
                    method: 'POST',
                    data: {
                        visitor_name: '测试用户',
                        visitor_email: '<EMAIL>',
                        visitor_phone: '13800138000',
                        message: '这是一条测试离线留言，用于测试系统功能。'
                    },
                    success: function(response) {
                        alert('测试留言创建成功！');
                        location.reload();
                    },
                    error: function() {
                        alert('创建测试留言失败');
                    }
                });
            }
        }
    </script>
</body>
</html>
