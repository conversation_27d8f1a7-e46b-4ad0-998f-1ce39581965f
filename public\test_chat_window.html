<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试客服聊天窗口</title>
    <meta name="csrf-token" content="test-token">
</head>
<body style="margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f0f2f5;">
    <h1>🧪 客服聊天窗口测试页面</h1>
    <p>这个页面用来测试客服聊天窗口的显示效果。</p>
    <p>请查看右下角是否有客服按钮，点击测试聊天功能。</p>

    <!-- 在线客服系统 -->
    <div id="customer-service-widget">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <!-- 头部 -->
            <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                    </div>
                    <button onclick="toggleChatWindow()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;">×</button>
                </div>
                <div id="cs-status" style="position:absolute;bottom:8px;right:20px;font-size:12px;opacity:0.8;">
                    <span style="display:inline-block;width:8px;height:8px;background:#4ade80;border-radius:50%;margin-right:5px;"></span>在线
                </div>
            </div>

            <!-- 消息区域 -->
            <div id="cs-messages" style="height:350px;overflow-y:auto;padding:20px;background:#f8fafc;">
                <!-- 欢迎消息 -->
                <div id="cs-welcome" style="text-align:center;padding:30px 20px;color:#64748b;">
                    <div style="font-size:48px;margin-bottom:15px;">👋</div>
                    <h5 style="margin:0 0 8px 0;color:#334155;font-weight:600;">欢迎咨询！</h5>
                    <p style="margin:0;font-size:14px;line-height:1.5;">我是您的专属客服助手，请输入您的问题，我会尽快为您解答。</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div style="padding:20px;border-top:1px solid #e2e8f0;background:white;">
                <div style="display:flex;gap:12px;align-items:flex-end;">
                    <input type="text" id="cs-message-input" placeholder="输入您的问题..." style="flex:1;padding:12px 16px;border:1px solid #d1d5db;border-radius:24px;outline:none;font-size:14px;transition:border-color 0.2s;background:#f9fafb;" onfocus="this.style.borderColor='#667eea';this.style.background='white'" onblur="this.style.borderColor='#d1d5db';this.style.background='#f9fafb'">
                    <button id="cs-send-btn" style="padding:12px 20px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border:none;border-radius:24px;cursor:pointer;font-size:14px;font-weight:500;transition:all 0.2s;box-shadow:0 2px 8px rgba(102, 126, 234, 0.3);" onmouseover="this.style.transform='translateY(-1px)';this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 2px 8px rgba(102, 126, 234, 0.3)'">发送</button>
                </div>
                <div style="margin-top:8px;font-size:11px;color:#9ca3af;text-align:center;">按 Enter 快速发送</div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatButton = document.getElementById('cs-chat-button');
        const chatWindow = document.getElementById('cs-chat-window');
        const messagesDiv = document.getElementById('cs-messages');
        const messageInput = document.getElementById('cs-message-input');
        const sendBtn = document.getElementById('cs-send-btn');
        const buttonIcon = document.getElementById('cs-button-icon');
        const welcomeDiv = document.getElementById('cs-welcome');

        let sessionId = 'test_session_' + Date.now();
        let isOpen = false;
        let messageCount = 0;

        // 切换聊天窗口
        function toggleChatWindow() {
            isOpen = !isOpen;
            chatWindow.style.display = isOpen ? 'block' : 'none';
            
            // 更新按钮样式
            if (isOpen) {
                buttonIcon.textContent = '×';
                chatButton.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
                // 首次打开显示欢迎消息
                if (messageCount === 0) {
                    setTimeout(() => {
                        addMessage('ai', 'Hello! 👋 Welcome to our store! How can I help you today?');
                        messageCount++;
                    }, 500);
                }
            } else {
                buttonIcon.textContent = '💬';
                chatButton.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }
        }

        chatButton.addEventListener('click', toggleChatWindow);
        window.toggleChatWindow = toggleChatWindow; // 全局函数供HTML调用

        // 发送消息
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // 隐藏欢迎消息
            if (welcomeDiv && messageCount === 1) {
                welcomeDiv.style.display = 'none';
            }

            // 添加用户消息到界面
            addMessage('customer', message);
            messageInput.value = '';
            messageCount++;

            // 模拟AI回复
            setTimeout(() => {
                const replies = [
                    '感谢您的咨询！我正在为您查询相关信息。',
                    '好的，我明白您的问题了。让我为您详细解答。',
                    '这是一个很好的问题！请稍等，我来为您处理。',
                    '谢谢您联系我们！我会尽快为您解决这个问题。'
                ];
                const randomReply = replies[Math.floor(Math.random() * replies.length)];
                addMessage('ai', randomReply);
            }, 1000);
        }

        // 添加消息到界面
        function addMessage(sender, message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '15px';
            messageDiv.style.display = 'flex';
            messageDiv.style.alignItems = 'flex-start';
            messageDiv.style.animation = 'fadeInUp 0.3s ease-out';

            const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

            if (sender === 'customer') {
                messageDiv.style.justifyContent = 'flex-end';
                messageDiv.innerHTML = `
                    <div style="max-width:75%;">
                        <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:12px 16px;border-radius:18px 18px 4px 18px;word-wrap:break-word;box-shadow:0 2px 8px rgba(102, 126, 234, 0.3);">
                            ${message}
                        </div>
                        <div style="text-align:right;font-size:11px;color:#9ca3af;margin-top:4px;">${time}</div>
                    </div>
                `;
            } else {
                messageDiv.style.justifyContent = 'flex-start';
                messageDiv.innerHTML = `
                    <div style="width:32px;height:32px;background:linear-gradient(135deg, #10b981 0%, #059669 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:12px;flex-shrink:0;">
                        <span style="font-size:16px;">🤖</span>
                    </div>
                    <div style="max-width:75%;">
                        <div style="background:white;color:#374151;padding:12px 16px;border-radius:18px 18px 18px 4px;word-wrap:break-word;box-shadow:0 2px 8px rgba(0,0,0,0.1);border:1px solid #e5e7eb;">
                            ${message}
                        </div>
                        <div style="font-size:11px;color:#9ca3af;margin-top:4px;">AI助手 · ${time}</div>
                    </div>
                `;
            }

            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 绑定事件
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            #cs-chat-button:hover {
                transform: scale(1.05);
                box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5) !important;
            }
            #cs-chat-window {
                animation: slideInUp 0.3s ease-out;
            }
            @keyframes slideInUp {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(style);
    });
    </script>
</body>
</html>
