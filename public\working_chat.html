<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作的客服系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .chat-container { background: white; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden; }
        .chat-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .user-info { background: #e3f2fd; padding: 15px; font-size: 12px; color: #1976d2; }
        .messages { height: 400px; overflow-y: auto; padding: 20px; background: #fafafa; }
        .message { margin: 10px 0; display: flex; align-items: flex-start; animation: fadeInUp 0.3s ease-out; }
        .message.customer { justify-content: flex-end; }
        .message.admin, .message.ai { justify-content: flex-start; }
        .message-content { max-width: 70%; padding: 12px 16px; border-radius: 18px; word-wrap: break-word; }
        .message.customer .message-content { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 18px 18px 4px 18px; }
        .message.admin .message-content { background: #f3e5f5; color: #4a148c; border-radius: 18px 18px 18px 4px; }
        .message.ai .message-content { background: #e8f5e8; color: #2e7d32; border-radius: 18px 18px 18px 4px; }
        .message-time { font-size: 11px; opacity: 0.7; margin-top: 4px; }
        .input-area { padding: 20px; background: white; border-top: 1px solid #eee; display: flex; gap: 10px; }
        .input-area input { flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 25px; outline: none; }
        .input-area button { padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 25px; cursor: pointer; }
        .input-area button:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3); }
        .status { padding: 10px; background: #e8f5e8; color: #2e7d32; text-align: center; font-size: 12px; }
        .status.error { background: #ffebee; color: #c62828; }
        .controls { padding: 10px; background: #f5f5f5; text-align: center; }
        .btn { padding: 8px 16px; margin: 0 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; }
        .btn:hover { background: #1976d2; }
        .btn.danger { background: #f44336; }
        .btn.danger:hover { background: #d32f2f; }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-container">
            <div class="chat-header">
                <h2>💬 在线客服</h2>
                <p>我们随时为您服务</p>
            </div>
            
            <div class="user-info">
                <div>👤 用户ID: <span id="user-id"></span></div>
                <div>🔍 设备指纹: <span id="device-fingerprint"></span></div>
                <div>💬 会话ID: <span id="session-id"></span></div>
                <div>📊 消息数量: <span id="message-count">0</span></div>
            </div>
            
            <div id="connection-status" class="status">正在连接...</div>
            
            <div id="messages" class="messages">
                <!-- 消息将在这里显示 -->
            </div>
            
            <div class="input-area">
                <input type="text" id="message-input" placeholder="输入您的消息..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()">发送</button>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="connectSSE()">连接SSE</button>
                <button class="btn" onclick="disconnectSSE()">断开连接</button>
                <button class="btn" onclick="loadHistory()">加载历史</button>
                <button class="btn danger" onclick="clearHistory()">清除历史</button>
                <button class="btn danger" onclick="resetUser()">重置用户</button>
            </div>
        </div>
    </div>

    <script>
    // 全局变量
    let persistentUserId = null;
    let deviceFingerprint = null;
    let sessionId = null;
    let eventSource = null;
    let lastMessageId = 0;
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeUser();
        updateStatus('系统初始化完成', false);
        
        // 自动连接SSE
        setTimeout(connectSSE, 1000);
    });
    
    // 生成设备指纹
    function generateDeviceFingerprint() {
        const fingerprint = {
            screen: screen.width + 'x' + screen.height,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            platform: navigator.platform
        };
        
        const fingerprintString = JSON.stringify(fingerprint);
        let hash = 0;
        for (let i = 0; i < fingerprintString.length; i++) {
            const char = fingerprintString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        
        return 'fp_' + Math.abs(hash).toString(36);
    }
    
    // 初始化用户
    function initializeUser() {
        // 获取或创建用户ID
        persistentUserId = localStorage.getItem('customer_service_user_id');
        if (!persistentUserId) {
            deviceFingerprint = generateDeviceFingerprint();
            persistentUserId = 'user_' + deviceFingerprint + '_' + Date.now();
            localStorage.setItem('customer_service_user_id', persistentUserId);
            localStorage.setItem('device_fingerprint', deviceFingerprint);
            localStorage.setItem('created_at', new Date().toISOString());
        } else {
            deviceFingerprint = localStorage.getItem('device_fingerprint') || generateDeviceFingerprint();
        }
        
        // 获取或创建会话ID
        sessionId = localStorage.getItem('customer_service_session_id');
        if (!sessionId) {
            sessionId = 'session_' + persistentUserId + '_' + Date.now();
            localStorage.setItem('customer_service_session_id', sessionId);
        }
        
        // 更新UI
        document.getElementById('user-id').textContent = persistentUserId.substring(0, 20) + '...';
        document.getElementById('device-fingerprint').textContent = deviceFingerprint;
        document.getElementById('session-id').textContent = sessionId.substring(0, 20) + '...';
        
        // 加载历史消息
        loadHistory();
        
        // 如果没有历史消息，显示欢迎消息
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        if (messages.length === 0) {
            setTimeout(() => {
                addMessage('ai', 'Hello! 👋 欢迎来到我们的在线客服！有什么可以帮助您的吗？');
            }, 500);
        }
    }
    
    // 连接SSE
    function connectSSE() {
        if (eventSource) {
            eventSource.close();
        }
        
        const url = `/simple_sse.php?session_id=${sessionId}&last_message_id=${lastMessageId}`;
        console.log('🔄 连接SSE:', url);
        updateStatus('正在连接SSE...', false);
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            console.log('✅ SSE连接成功');
            updateStatus('✅ 实时连接已建立', false);
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                console.log('📨 收到SSE消息:', data);
                
                if (data.type === 'new_reply') {
                    addMessage(data.sender_type, data.message);
                    lastMessageId = Math.max(lastMessageId, data.message_id);
                    
                    // 播放提示音
                    playNotificationSound();
                } else if (data.type === 'connected') {
                    updateStatus('🔗 ' + data.message, false);
                } else if (data.type === 'heartbeat') {
                    console.log('💓 心跳包');
                } else if (data.type === 'disconnected') {
                    updateStatus('连接已断开', true);
                }
            } catch (e) {
                console.error('解析SSE消息失败:', e);
            }
        };
        
        eventSource.onerror = function() {
            console.error('❌ SSE连接错误');
            updateStatus('❌ 连接错误，正在重试...', true);
            
            // 5秒后重连
            setTimeout(() => {
                console.log('🔄 尝试重新连接...');
                connectSSE();
            }, 5000);
        };
    }
    
    // 断开SSE
    function disconnectSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            console.log('🛑 SSE连接已断开');
            updateStatus('连接已断开', true);
        }
    }
    
    // 发送消息
    function sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        addMessage('customer', message);
        input.value = '';
        
        // 模拟AI回复
        setTimeout(() => {
            const aiReplies = [
                '感谢您的咨询，我正在为您查询相关信息...',
                '好的，我明白了您的问题，让我为您处理一下。',
                '这个问题很常见，我来为您详细解答。',
                '请稍等，我正在联系相关部门为您解决。',
                '您的问题我已经记录，会有专人跟进处理。'
            ];
            const randomReply = aiReplies[Math.floor(Math.random() * aiReplies.length)];
            addMessage('ai', randomReply);
        }, 1000 + Math.random() * 2000);
    }
    
    // 添加消息
    function addMessage(sender, message) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender;
        
        const time = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `
            <div class="message-content">
                ${message}
                <div class="message-time">${time}</div>
            </div>
        `;
        
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
        
        // 保存到本地存储
        saveMessageToLocal(sender, message);
        updateMessageCount();
    }
    
    // 保存消息到本地
    function saveMessageToLocal(sender, message) {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        messages.push({
            sender: sender,
            message: message,
            timestamp: new Date().toISOString()
        });
        
        // 只保留最近50条消息
        if (messages.length > 50) {
            messages.splice(0, messages.length - 50);
        }
        
        localStorage.setItem('chat_messages_' + sessionId, JSON.stringify(messages));
    }
    
    // 加载历史消息
    function loadHistory() {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        const messagesDiv = document.getElementById('messages');
        messagesDiv.innerHTML = '';
        
        messages.forEach(msg => {
            if (msg && msg.sender && msg.message) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ' + msg.sender;
                
                const time = new Date(msg.timestamp).toLocaleTimeString();
                messageDiv.innerHTML = `
                    <div class="message-content">
                        ${msg.message}
                        <div class="message-time">${time}</div>
                    </div>
                `;
                
                messagesDiv.appendChild(messageDiv);
            }
        });
        
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
        updateMessageCount();
        
        if (messages.length > 0) {
            console.log('📚 加载了 ' + messages.length + ' 条历史消息');
        }
    }
    
    // 清除历史
    function clearHistory() {
        if (confirm('确定要清除所有聊天记录吗？')) {
            localStorage.removeItem('chat_messages_' + sessionId);
            document.getElementById('messages').innerHTML = '';
            updateMessageCount();
            updateStatus('聊天记录已清除', false);
        }
    }
    
    // 重置用户
    function resetUser() {
        if (confirm('确定要重置用户身份吗？这将清除所有数据。')) {
            localStorage.clear();
            location.reload();
        }
    }
    
    // 更新消息数量
    function updateMessageCount() {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        document.getElementById('message-count').textContent = messages.length;
    }
    
    // 更新状态
    function updateStatus(message, isError) {
        const statusDiv = document.getElementById('connection-status');
        statusDiv.textContent = message;
        statusDiv.className = 'status' + (isError ? ' error' : '');
    }
    
    // 播放提示音
    function playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            console.log('无法播放提示音');
        }
    }
    
    // 处理回车键
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }
    </script>
</body>
</html>
