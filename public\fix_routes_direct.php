<?php
// 直接数据库连接修复路由
$host = '127.0.0.1';
$port = '3306';
$database = 'mostxx_com';
$username = 'mostxx_com';
$password = 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>直接修复路由</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 直接修复路由前缀</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 1. 查看当前有问题的菜单
    echo "<h3>1. 当前有问题的菜单</h3>";
    $stmt = $pdo->query("
        SELECT id, name, route_url 
        FROM st_strongadmin_menu 
        WHERE route_url LIKE 'strongadmin/%'
        AND name IN ('会话管理', 'AI规则管理', '统计报表')
    ");
    $problemMenus = $stmt->fetchAll();
    
    if (empty($problemMenus)) {
        echo "<div class='info'>✅ 没有发现重复前缀的菜单项</div>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>名称</th><th>当前路由</th><th>修正后</th></tr>";
        foreach ($problemMenus as $menu) {
            $correctRoute = str_replace('strongadmin/', '', $menu['route_url']);
            echo "<tr>";
            echo "<td>{$menu['id']}</td>";
            echo "<td>{$menu['name']}</td>";
            echo "<td><code>{$menu['route_url']}</code></td>";
            echo "<td><code>{$correctRoute}</code></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 2. 执行修复
        echo "<h3>2. 执行修复</h3>";
        foreach ($problemMenus as $menu) {
            $correctRoute = str_replace('strongadmin/', '', $menu['route_url']);
            
            $stmt = $pdo->prepare("
                UPDATE st_strongadmin_menu 
                SET route_url = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$correctRoute, $menu['id']]);
            
            echo "<div class='success'>✅ 修复菜单【{$menu['name']}】: {$menu['route_url']} → {$correctRoute}</div>";
        }
    }
    
    // 3. 验证修复结果
    echo "<h3>3. 验证修复结果</h3>";
    $stmt = $pdo->query("
        SELECT id, name, route_url, parent_id, level 
        FROM st_strongadmin_menu 
        WHERE name LIKE '%客服%' OR name IN ('设置中心', '离线留言', '会话管理', 'AI规则管理', '统计报表')
        ORDER BY parent_id, sort
    ");
    $allMenus = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>ID</th><th>名称</th><th>路由URL</th><th>完整访问路径</th><th>级别</th><th>测试</th></tr>";
    foreach ($allMenus as $menu) {
        $fullPath = $menu['route_url'] ? '/strongadmin/' . $menu['route_url'] : '/strongadmin/';
        echo "<tr>";
        echo "<td>{$menu['id']}</td>";
        echo "<td><strong>{$menu['name']}</strong></td>";
        echo "<td><code>{$menu['route_url']}</code></td>";
        echo "<td>{$fullPath}</td>";
        echo "<td>{$menu['level']}</td>";
        echo "<td><a href='{$fullPath}' target='_blank' class='btn btn-sm btn-primary'>测试</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. 快速测试链接
    echo "<h3>4. 快速测试链接</h3>";
    $testLinks = [
        'customer-service/settings' => '设置中心',
        'customer-service/offline-messages' => '离线留言', 
        'customer-service/sessions' => '会话管理',
        'customer-service/ai-rules' => 'AI规则管理',
        'customer-service/statistics' => '统计报表'
    ];
    
    echo "<div class='row'>";
    foreach ($testLinks as $route => $name) {
        $fullUrl = '/strongadmin/' . $route;
        echo "<div class='col-md-4 mb-2'>";
        echo "<a href='{$fullUrl}' target='_blank' class='btn btn-block btn-outline-primary'>";
        echo "<i class='fa fa-external-link-alt'></i> {$name}";
        echo "</a>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h4>🎉 修复完成！</h4>";
    echo "<p>所有客服菜单的路由前缀已经修复。现在点击菜单应该能正常访问了。</p>";
    echo "<p><strong>注意:</strong> 如果仍然出现404错误，请检查路由文件中是否正确定义了对应的路由。</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 修复失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
