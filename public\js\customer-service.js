/**
 * 现代化在线客服系统
 * 支持AI自动回复和实时聊天
 */
class CustomerService {
    constructor() {
        this.sessionId = null;
        this.isOpen = false;
        this.isMinimized = false;
        this.messagePollingInterval = null;
        this.lastMessageId = 0;
        this.isTyping = false;
        
        this.init();
    }

    init() {
        this.createChatWidget();
        this.bindEvents();
        this.initSession();
        this.checkOnlineStatus();
    }

    createChatWidget() {
        const widget = document.createElement('div');
        widget.innerHTML = `
            <!-- 客服按钮 -->
            <div id="cs-chat-button" class="cs-chat-button">
                <div class="cs-button-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z" fill="currentColor"/>
                        <circle cx="8" cy="10" r="1.5" fill="white"/>
                        <circle cx="12" cy="10" r="1.5" fill="white"/>
                        <circle cx="16" cy="10" r="1.5" fill="white"/>
                    </svg>
                </div>
                <div class="cs-button-text">Chat</div>
                <div class="cs-online-indicator"></div>
            </div>

            <!-- 客服窗口 -->
            <div id="cs-chat-window" class="cs-chat-window">
                <div class="cs-chat-header">
                    <div class="cs-header-info">
                        <div class="cs-header-avatar">
                            <img src="/images/customer-service-avatar.png" alt="Customer Service">
                        </div>
                        <div class="cs-header-text">
                            <div class="cs-header-title">Customer Service</div>
                            <div class="cs-header-status">
                                <span class="cs-status-dot"></span>
                                <span class="cs-status-text">Online</span>
                            </div>
                        </div>
                    </div>
                    <div class="cs-header-actions">
                        <button class="cs-minimize-btn" title="Minimize">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M6 12L18 12" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="cs-close-btn" title="Close">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="cs-chat-body">
                    <div class="cs-messages-container" id="cs-messages">
                        <div class="cs-welcome-message">
                            <div class="cs-message cs-message-received">
                                <div class="cs-message-avatar">
                                    <img src="/images/customer-service-avatar.png" alt="CS">
                                </div>
                                <div class="cs-message-content">
                                    <div class="cs-message-text">
                                        Hello! 👋 How can I help you today?
                                    </div>
                                    <div class="cs-message-time">${new Date().toLocaleTimeString('en-US', {hour: '2-digit', minute:'2-digit'})}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="cs-typing-indicator" id="cs-typing" style="display: none;">
                        <div class="cs-typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="cs-typing-text">AI is typing...</span>
                    </div>
                </div>

                <div class="cs-chat-footer">
                    <div class="cs-input-container">
                        <input type="text" id="cs-message-input" placeholder="Type your message..." maxlength="1000">
                        <button id="cs-send-btn" class="cs-send-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M22 2L11 13M22 2L15 22L11 13M22 2L2 9L11 13" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <div class="cs-footer-info">
                        <small>Powered by AI • Typically replies in minutes</small>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(widget);
    }

    bindEvents() {
        // 打开/关闭聊天窗口
        document.getElementById('cs-chat-button').addEventListener('click', () => {
            this.toggleChat();
        });

        // 最小化
        document.querySelector('.cs-minimize-btn').addEventListener('click', () => {
            this.minimizeChat();
        });

        // 关闭
        document.querySelector('.cs-close-btn').addEventListener('click', () => {
            this.closeChat();
        });

        // 发送消息
        document.getElementById('cs-send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // 回车发送
        document.getElementById('cs-message-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // 输入框焦点
        document.getElementById('cs-message-input').addEventListener('focus', () => {
            this.scrollToBottom();
        });
    }

    async initSession() {
        try {
            // 获取用户信息
            const userInfo = this.getUserInfo();
            console.log('🔐 初始化会话，用户信息:', userInfo);

            const response = await fetch('/customer-service/init', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify(userInfo)
            });

            const data = await response.json();
            if (data.success) {
                this.sessionId = data.session_id;
                this.loadMessages();
            }
        } catch (error) {
            console.error('Failed to initialize customer service session:', error);
        }
    }

    getUserInfo() {
        // 尝试从多个地方获取用户信息
        let userInfo = {
            visitor_name: null,
            visitor_email: null
        };

        // 1. 从全局变量获取（如果有的话）
        if (window.user) {
            userInfo.visitor_name = window.user.name || window.user.username;
            userInfo.visitor_email = window.user.email;
        }

        // 2. 从meta标签获取
        const userNameMeta = document.querySelector('meta[name="user-name"]');
        const userEmailMeta = document.querySelector('meta[name="user-email"]');
        if (userNameMeta) userInfo.visitor_name = userNameMeta.getAttribute('content');
        if (userEmailMeta) userInfo.visitor_email = userEmailMeta.getAttribute('content');

        // 3. 从localStorage获取
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
            try {
                const userData = JSON.parse(storedUser);
                userInfo.visitor_name = userInfo.visitor_name || userData.name || userData.username;
                userInfo.visitor_email = userInfo.visitor_email || userData.email;
            } catch (e) {
                console.warn('解析localStorage用户数据失败:', e);
            }
        }

        // 4. 从页面元素获取（如果有用户信息显示）
        const userNameElement = document.querySelector('.user-name, .username, [data-user-name]');
        const userEmailElement = document.querySelector('.user-email, [data-user-email]');
        if (userNameElement) userInfo.visitor_name = userInfo.visitor_name || userNameElement.textContent.trim();
        if (userEmailElement) userInfo.visitor_email = userInfo.visitor_email || userEmailElement.textContent.trim();

        return userInfo;
    }

    async checkOnlineStatus() {
        try {
            const response = await fetch('/customer-service/status');
            const data = await response.json();
            
            if (data.success) {
                this.updateOnlineStatus(data.is_online);
            }
        } catch (error) {
            console.error('Failed to check online status:', error);
        }
    }

    updateOnlineStatus(isOnline) {
        const indicator = document.querySelector('.cs-online-indicator');
        const statusDot = document.querySelector('.cs-status-dot');
        const statusText = document.querySelector('.cs-status-text');

        if (isOnline) {
            indicator.classList.add('online');
            statusDot.classList.add('online');
            statusText.textContent = 'Online';
        } else {
            indicator.classList.remove('online');
            statusDot.classList.remove('online');
            statusText.textContent = 'Offline';
        }
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    openChat() {
        const window = document.getElementById('cs-chat-window');
        const button = document.getElementById('cs-chat-button');
        
        window.classList.add('open');
        button.classList.add('hidden');
        this.isOpen = true;
        this.isMinimized = false;

        // 聚焦输入框
        setTimeout(() => {
            document.getElementById('cs-message-input').focus();
        }, 300);

        // 立即发送在线状态
        this.sendHeartbeat();

        // 开始轮询消息
        this.startMessagePolling();
    }

    closeChat() {
        const window = document.getElementById('cs-chat-window');
        const button = document.getElementById('cs-chat-button');
        
        window.classList.remove('open', 'minimized');
        button.classList.remove('hidden');
        this.isOpen = false;
        this.isMinimized = false;

        // 停止轮询
        this.stopMessagePolling();
    }

    minimizeChat() {
        const window = document.getElementById('cs-chat-window');
        window.classList.add('minimized');
        this.isMinimized = true;
    }

    async sendMessage() {
        const input = document.getElementById('cs-message-input');
        const message = input.value.trim();

        if (!message || !this.sessionId) return;

        // 清空输入框
        input.value = '';

        // 添加用户消息到界面
        this.addMessage({
            sender_type: 'customer',
            message: message,
            sender_info: { name: 'You', avatar: '/images/user-avatar.png' },
            formatted_time: new Date().toLocaleTimeString('en-US', {hour: '2-digit', minute:'2-digit'})
        });

        try {
            const response = await fetch('/customer-service/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    message: message
                })
            });

            const data = await response.json();
            if (!data.success) {
                this.showError('Failed to send message');
            } else {
                // 发送消息成功后，发送心跳表示用户在线
                this.sendHeartbeat();
            }
        } catch (error) {
            console.error('Failed to send message:', error);
            this.showError('Network error');
        }
    }

    async loadMessages() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`/customer-service/messages?session_id=${this.sessionId}`);
            const data = await response.json();

            if (data.success) {
                const container = document.getElementById('cs-messages');
                // 清空现有消息（保留欢迎消息）
                const welcomeMsg = container.querySelector('.cs-welcome-message');
                container.innerHTML = '';
                if (welcomeMsg) {
                    container.appendChild(welcomeMsg);
                }

                // 添加历史消息
                data.messages.forEach(message => {
                    this.addMessage(message);
                });
            }
        } catch (error) {
            console.error('Failed to load messages:', error);
        }
    }

    addMessage(message) {
        const container = document.getElementById('cs-messages');
        const messageEl = document.createElement('div');
        
        const isReceived = message.sender_type !== 'customer';
        const senderInfo = message.sender_info || { name: 'Unknown', avatar: '/images/default-avatar.png' };

        messageEl.className = `cs-message ${isReceived ? 'cs-message-received' : 'cs-message-sent'}`;
        messageEl.innerHTML = `
            ${isReceived ? `<div class="cs-message-avatar"><img src="${senderInfo.avatar}" alt="${senderInfo.name}"></div>` : ''}
            <div class="cs-message-content">
                <div class="cs-message-text">${this.escapeHtml(message.message)}</div>
                <div class="cs-message-time">${message.formatted_time}</div>
            </div>
        `;

        container.appendChild(messageEl);
        this.scrollToBottom();

        // 如果是AI消息，显示打字效果
        if (message.sender_type === 'ai') {
            this.showTypingIndicator();
            setTimeout(() => {
                this.hideTypingIndicator();
            }, 1000);
        }
    }

    showTypingIndicator() {
        document.getElementById('cs-typing').style.display = 'flex';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        document.getElementById('cs-typing').style.display = 'none';
    }

    scrollToBottom() {
        const container = document.getElementById('cs-messages');
        container.scrollTop = container.scrollHeight;
    }

    startMessagePolling() {
        this.messagePollingInterval = setInterval(() => {
            this.loadMessages();
        }, 3000); // 每3秒检查新消息

        // 启动心跳检测
        this.startHeartbeat();
    }

    startHeartbeat() {
        // 立即发送一次心跳
        this.sendHeartbeat();

        // 每15秒发送心跳，告诉服务器客户还在线
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, 15000);

        // 页面关闭时发送离线状态 - 多重保险
        window.addEventListener('beforeunload', (event) => {
            console.log('🚪 beforeunload: 页面即将关闭，发送离线状态');
            this.sendOfflineStatusImmediate();
        });

        // 页面隐藏时也发送离线状态
        window.addEventListener('pagehide', (event) => {
            console.log('📱 pagehide: 页面隐藏，发送离线状态');
            this.sendOfflineStatusImmediate();
        });

        // 窗口失去焦点时发送离线状态
        window.addEventListener('blur', () => {
            console.log('👁️ blur: 窗口失去焦点，发送离线状态');
            this.sendOfflineStatus();
        });

        // 页面卸载时发送离线状态
        window.addEventListener('unload', () => {
            console.log('🗑️ unload: 页面卸载，发送离线状态');
            this.sendOfflineStatusImmediate();
        });

        // 页面可见性变化时处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('📱 页面隐藏，发送离线状态');
                this.sendOfflineStatus();
            } else {
                console.log('👁️ 页面显示，发送在线心跳');
                this.sendHeartbeat();
            }
        });

        // 页面获得焦点时也发送心跳
        window.addEventListener('focus', () => {
            console.log('🎯 页面获得焦点，发送心跳');
            this.sendHeartbeat();
        });
    }

    sendHeartbeat() {
        if (!this.sessionId) {
            console.log('❌ 心跳发送跳过：没有sessionId');
            return;
        }

        const now = new Date().toLocaleTimeString();
        console.log(`🔄 [${now}] 发送心跳，sessionId: ${this.sessionId}`);

        fetch('/customer-service/heartbeat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            body: JSON.stringify({
                session_id: this.sessionId,
                status: 'online'
            })
        })
        .then(response => {
            console.log(`📡 [${now}] 心跳响应状态:`, response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log(`✅ [${now}] 心跳发送成功，更新了 ${data.updated || 0} 行`);
            } else {
                console.error(`❌ [${now}] 心跳发送失败:`, data);
            }
        })
        .catch(error => {
            console.error(`💥 [${now}] 心跳发送异常:`, error);
        });
    }

    sendOfflineStatus() {
        if (!this.sessionId) return;

        // 使用sendBeacon确保在页面关闭时也能发送
        const data = JSON.stringify({
            session_id: this.sessionId,
            status: 'offline'
        });

        console.log('📤 发送离线状态');

        if (navigator.sendBeacon) {
            const blob = new Blob([data], { type: 'application/json' });
            const success = navigator.sendBeacon('/customer-service/heartbeat', blob);
            console.log('📡 sendBeacon 发送结果:', success);
        } else {
            fetch('/customer-service/heartbeat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: data
            }).catch(error => {
                console.error('离线状态发送失败:', error);
            });
        }
    }

    sendOfflineStatusImmediate() {
        if (!this.sessionId) return;

        console.log('🚨 立即发送离线状态 (多种方式)');

        const data = JSON.stringify({
            session_id: this.sessionId,
            status: 'offline'
        });

        // 方式1: sendBeacon (最可靠)
        if (navigator.sendBeacon) {
            try {
                const blob = new Blob([data], { type: 'application/json' });
                const success = navigator.sendBeacon('/customer-service/heartbeat', blob);
                console.log('📡 sendBeacon 发送结果:', success);

                // 如果sendBeacon失败，尝试其他方式
                if (!success) {
                    throw new Error('sendBeacon failed');
                }
                return; // 成功就返回
            } catch (e) {
                console.warn('sendBeacon 失败:', e);
            }
        }

        // 方式2: 同步XMLHttpRequest (阻塞但可靠)
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/customer-service/heartbeat', false); // 同步请求
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
            xhr.send(data);
            console.log('📡 同步XHR 发送完成, 状态:', xhr.status);
            return;
        } catch (e) {
            console.warn('同步XHR 失败:', e);
        }

        // 方式3: 图片请求 (最后的备选)
        try {
            const img = new Image();
            const params = new URLSearchParams({
                session_id: this.sessionId,
                status: 'offline'
            });
            img.src = '/customer-service/heartbeat-img?' + params.toString();
            console.log('📡 图片请求 发送完成');
        } catch (e) {
            console.warn('图片请求 失败:', e);
        }
    }

    stopMessagePolling() {
        if (this.messagePollingInterval) {
            clearInterval(this.messagePollingInterval);
            this.messagePollingInterval = null;
        }

        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        // 发送离线状态
        this.sendOfflineStatus();
    }

    showError(message) {
        // 简单的错误提示
        console.error('Customer Service Error:', message);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 页面加载完成后初始化客服系统
document.addEventListener('DOMContentLoaded', function() {
    // 确保CSRF token存在
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (!csrfToken) {
        console.warn('CSRF token not found');
        return;
    }

    try {
        window.customerService = new CustomerService();
        console.log('Customer service initialized successfully');
    } catch (error) {
        console.error('Failed to initialize customer service:', error);
    }
});
