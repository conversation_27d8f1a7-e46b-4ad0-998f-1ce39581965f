<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 产品图片调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .product-item {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .error { color: red; background: #ffe6e6; }
        .success { color: green; background: #e6ffe6; }
        .warning { color: orange; background: #fff3cd; }
        .image-preview {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .image-preview img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 产品图片调试工具</h1>
        <p>这个工具帮助您检查产品图片数据是否正确加载</p>
        
        <div class="debug-section">
            <h2>🛠️ 调试操作</h2>
            <button onclick="checkProductData()">检查产品数据</button>
            <button onclick="testImageHover()">测试图片悬停</button>
            <button onclick="clearCache()">清除缓存</button>
            <button onclick="checkConsole()">检查控制台</button>
        </div>
        
        <div class="debug-section">
            <h2>📋 检查结果</h2>
            <div id="results"></div>
        </div>
        
        <div class="debug-section">
            <h2>🖼️ 产品图片测试</h2>
            <div id="imageTest"></div>
        </div>
        
        <div class="debug-section">
            <h2>📝 调试日志</h2>
            <div id="debugLog" class="log"></div>
        </div>
    </div>

    <script>
        let debugLog = document.getElementById('debugLog');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function checkProductData() {
            log('开始检查产品数据...');
            const results = document.getElementById('results');
            
            // 检查页面中的产品元素
            const productElements = document.querySelectorAll('.product-hover-effect');
            log(`找到 ${productElements.length} 个产品悬停元素`);
            
            let hasSecondImage = 0;
            let totalProducts = 0;
            
            productElements.forEach((element, index) => {
                totalProducts++;
                const mainImg = element.querySelector('.product-main-img');
                const hoverImg = element.querySelector('.product-hover-img');
                
                if (mainImg && hoverImg) {
                    hasSecondImage++;
                    log(`产品 ${index + 1}: ✅ 有第二张图片`);
                    log(`  - 主图: ${mainImg.src}`);
                    log(`  - 悬停图: ${hoverImg.src}`);
                } else {
                    log(`产品 ${index + 1}: ❌ 缺少第二张图片`);
                    if (mainImg) log(`  - 主图: ${mainImg.src}`);
                }
            });
            
            results.innerHTML = `
                <div class="product-item ${hasSecondImage > 0 ? 'success' : 'error'}">
                    <h3>检查结果</h3>
                    <p>总产品数: ${totalProducts}</p>
                    <p>有第二张图片的产品: ${hasSecondImage}</p>
                    <p>成功率: ${totalProducts > 0 ? Math.round(hasSecondImage / totalProducts * 100) : 0}%</p>
                </div>
            `;
            
            log(`检查完成: ${hasSecondImage}/${totalProducts} 产品有第二张图片`);
        }
        
        function testImageHover() {
            log('开始测试图片悬停效果...');
            const imageTest = document.getElementById('imageTest');
            
            // 创建测试产品
            imageTest.innerHTML = `
                <h3>测试产品 (模拟数据)</h3>
                <div style="width: 200px; margin: 20px 0;">
                    <a href="#" class="product-hover-effect test-product" style="position: relative; display: block; overflow: hidden; height: 200px; background: #f8f9fa;">
                        <img src="https://via.placeholder.com/200x200/4facfe/ffffff?text=Main+Image" 
                             class="product-main-img" 
                             style="width: 100%; height: 100%; object-fit: cover; transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);">
                        <img src="https://via.placeholder.com/200x200/ff6b6b/ffffff?text=Hover+Image" 
                             class="product-hover-img" 
                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; opacity: 0; transform: scale(1.1); transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);">
                    </a>
                    <p>悬停查看效果</p>
                </div>
            `;
            
            // 添加悬停事件
            const testElement = imageTest.querySelector('.test-product');
            const mainImg = testElement.querySelector('.product-main-img');
            const hoverImg = testElement.querySelector('.product-hover-img');
            
            testElement.addEventListener('mouseenter', function() {
                log('测试: 鼠标进入，切换到第二张图片');
                mainImg.style.transform = 'scale(1.1)';
                mainImg.style.opacity = '0';
                hoverImg.style.opacity = '1';
                hoverImg.style.transform = 'scale(1)';
            });
            
            testElement.addEventListener('mouseleave', function() {
                log('测试: 鼠标离开，恢复第一张图片');
                mainImg.style.transform = 'scale(1)';
                mainImg.style.opacity = '1';
                hoverImg.style.opacity = '0';
                hoverImg.style.transform = 'scale(1.1)';
            });
            
            log('测试产品已创建，请悬停查看效果');
        }
        
        function clearCache() {
            log('尝试清除浏览器缓存...');
            
            // 清除页面缓存
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    for (let name of names) {
                        caches.delete(name);
                    }
                    log('浏览器缓存已清除');
                });
            }
            
            // 重新加载页面
            setTimeout(() => {
                log('3秒后重新加载页面...');
                setTimeout(() => {
                    window.location.reload(true);
                }, 3000);
            }, 1000);
        }
        
        function checkConsole() {
            log('检查浏览器控制台...');
            console.log('🔍 产品图片调试信息:');
            console.log('产品悬停元素:', document.querySelectorAll('.product-hover-effect'));
            console.log('主图片元素:', document.querySelectorAll('.product-main-img'));
            console.log('悬停图片元素:', document.querySelectorAll('.product-hover-img'));
            
            // 检查是否有JavaScript错误
            window.addEventListener('error', function(e) {
                log(`JavaScript错误: ${e.message}`);
                console.error('JavaScript错误:', e);
            });
            
            log('控制台检查完成，请打开浏览器开发者工具查看详细信息');
        }
        
        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动检查...');
            setTimeout(checkProductData, 1000);
        });
        
        // 监听图片加载错误
        document.addEventListener('error', function(e) {
            if (e.target.tagName === 'IMG') {
                log(`图片加载失败: ${e.target.src}`);
            }
        }, true);
    </script>
</body>
</html>
