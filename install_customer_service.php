<?php
/**
 * 在线客服系统安装脚本
 * 运行: php install_customer_service.php
 */

echo "🤖 在线客服 + AI自动回复系统安装程序\n";
echo "=====================================\n\n";

// 检查Laravel环境
if (!file_exists('artisan')) {
    echo "❌ 错误: 请在Laravel项目根目录运行此脚本\n";
    exit(1);
}

echo "1. 运行数据库迁移...\n";
$output = shell_exec('php artisan migrate --path=database/migrations/2024_01_25_000001_create_customer_service_tables.php 2>&1');
echo $output . "\n";

echo "2. 添加后台菜单...\n";
$output = shell_exec('php artisan migrate --path=database/migrations/2024_01_25_000002_add_customer_service_menu.php 2>&1');
echo $output . "\n";

echo "3. 填充AI自动回复规则...\n";
$output = shell_exec('php artisan db:seed --class=CustomerServiceSeeder 2>&1');
echo $output . "\n";

echo "4. 清除缓存...\n";
$output = shell_exec('php artisan cache:clear 2>&1');
echo $output . "\n";

echo "5. 创建默认头像图片目录...\n";
$imageDir = 'public/images';
if (!is_dir($imageDir)) {
    mkdir($imageDir, 0755, true);
    echo "✅ 创建图片目录: $imageDir\n";
}

// 创建默认头像文件（简单的SVG）
$avatars = [
    'customer-service-avatar.png' => '<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle cx="20" cy="20" r="20" fill="#007bff"/><circle cx="20" cy="15" r="6" fill="white"/><path d="M8 32c0-6.627 5.373-12 12-12s12 5.373 12 12" fill="white"/></svg>',
    'ai-avatar.png' => '<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle cx="20" cy="20" r="20" fill="#28a745"/><text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="Arial">AI</text></svg>',
    'user-avatar.png' => '<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle cx="20" cy="20" r="20" fill="#6c757d"/><circle cx="20" cy="15" r="6" fill="white"/><path d="M8 32c0-6.627 5.373-12 12-12s12 5.373 12 12" fill="white"/></svg>',
    'admin-avatar.png' => '<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle cx="20" cy="20" r="20" fill="#dc3545"/><circle cx="20" cy="15" r="6" fill="white"/><path d="M8 32c0-6.627 5.373-12 12-12s12 5.373 12 12" fill="white"/></svg>'
];

foreach ($avatars as $filename => $svg) {
    $filepath = "$imageDir/$filename";
    if (!file_exists($filepath)) {
        file_put_contents($filepath, $svg);
        echo "✅ 创建头像: $filename\n";
    }
}

echo "\n🎉 安装完成!\n\n";
echo "📋 安装内容:\n";
echo "   ✅ 数据库表已创建\n";
echo "   ✅ 后台菜单已添加\n";
echo "   ✅ AI自动回复规则已添加\n";
echo "   ✅ 前端客服组件已集成\n";
echo "   ✅ 默认头像已创建\n\n";

echo "🚀 使用说明:\n";
echo "   1. 访问您的网站，右下角会出现客服按钮\n";
echo "   2. 点击按钮开始聊天，AI会自动回复常见问题\n";
echo "   3. 后台管理: 登录后台 → 在线客服菜单\n\n";

echo "🔧 下一步:\n";
echo "   1. 登录后台查看【在线客服】菜单\n";
echo "   2. 根据需要调整AI回复规则\n";
echo "   3. 自定义客服按钮样式和位置\n\n";

echo "💡 提示: 如果需要真人客服，可以在后台查看和回复用户消息\n\n";

echo "安装完成! 🎊\n";
?>
