<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="csrf-token" content="test-token">
    <title>测试客服系统</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .chat-box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .message { margin: 10px 0; padding: 8px; border-radius: 5px; }
        .customer { background: #e3f2fd; text-align: right; }
        .ai { background: #f1f8e9; text-align: left; }
        input, button { padding: 8px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🤖 客服系统测试</h1>
    
    <div class="chat-box">
        <h3>发送测试消息</h3>
        <input type="text" id="test-message" placeholder="输入测试消息" value="你好">
        <button onclick="sendTestMessage()">发送消息</button>
        <button onclick="checkDatabase()">检查数据库</button>
        <button onclick="clearMessages()">清空聊天</button>
    </div>
    
    <div id="chat-messages"></div>
    <div id="debug-info"></div>

    <script>
    let sessionId = 'test_session_' + Date.now();
    
    function addMessage(type, message) {
        const messagesDiv = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + type;
        messageDiv.innerHTML = '<strong>' + type.toUpperCase() + ':</strong> ' + message;
        messagesDiv.appendChild(messageDiv);
    }
    
    function addDebug(message, isError = false) {
        const debugDiv = document.getElementById('debug-info');
        const div = document.createElement('div');
        div.className = isError ? 'error' : 'success';
        div.innerHTML = new Date().toLocaleTimeString() + ' - ' + message;
        debugDiv.appendChild(div);
    }
    
    function sendTestMessage() {
        const message = document.getElementById('test-message').value;
        if (!message) return;
        
        addMessage('customer', message);
        addDebug('发送消息: ' + message);
        
        // 发送到服务器
        fetch('/customer-service/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': 'test-token'
            },
            body: JSON.stringify({
                session_id: sessionId,
                message: message
            })
        })
        .then(response => {
            addDebug('响应状态: ' + response.status);
            return response.json();
        })
        .then(data => {
            addDebug('服务器响应: ' + JSON.stringify(data));
            if (data.success && data.ai_reply) {
                setTimeout(() => {
                    addMessage('ai', data.ai_reply);
                }, 500);
            } else if (data.error) {
                addDebug('错误: ' + data.error, true);
            }
        })
        .catch(error => {
            addDebug('请求失败: ' + error.message, true);
        });
        
        document.getElementById('test-message').value = '';
    }
    
    function checkDatabase() {
        addDebug('检查数据库...');
        fetch('/check_cs_data.php')
        .then(response => response.text())
        .then(data => {
            const popup = window.open('', '_blank', 'width=800,height=600');
            popup.document.write(data);
        })
        .catch(error => {
            addDebug('检查失败: ' + error.message, true);
        });
    }
    
    function clearMessages() {
        document.getElementById('chat-messages').innerHTML = '';
        document.getElementById('debug-info').innerHTML = '';
    }
    
    // 页面加载时显示会话ID
    addDebug('会话ID: ' + sessionId);
    </script>
</body>
</html>
