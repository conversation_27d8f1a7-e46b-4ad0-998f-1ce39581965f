<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误隔离测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 错误隔离测试</h1>
        <p>逐步添加代码来找出导致语法错误的具体位置</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="testStep1()">步骤1: 基础代码</button>
            <button class="test-btn" onclick="testStep2()">步骤2: 添加变量</button>
            <button class="test-btn" onclick="testStep3()">步骤3: 添加函数</button>
            <button class="test-btn" onclick="testStep4()">步骤4: 添加复杂逻辑</button>
            <button class="test-btn danger" onclick="clearAll()">清空测试</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>📋 错误隔离测试已就绪</h3>
                <p>这个工具将逐步添加代码来找出导致语法错误的具体位置</p>
            </div>
        </div>
        
        <div id="dynamic-script-area">
            <!-- 动态脚本将在这里添加 -->
        </div>
    </div>

    <script>
        let errorLog = [];
        let testStep = 0;
        
        // 捕获所有JavaScript错误
        window.addEventListener('error', function(event) {
            errorLog.push({
                step: testStep,
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error ? event.error.toString() : 'Unknown error'
            });
            
            updateDisplay(`
                <h3>❌ 检测到JavaScript错误</h3>
                <div style="background: #f8d7da; padding: 15px; border-radius: 4px;">
                    <strong>步骤:</strong> ${testStep}<br>
                    <strong>错误:</strong> ${event.message}<br>
                    <strong>行号:</strong> ${event.lineno}<br>
                    <strong>列号:</strong> ${event.colno}<br>
                    <strong>文件:</strong> ${event.filename}
                </div>
                <p>在步骤 ${testStep} 发现了语法错误！</p>
            `, true);
        });

        function updateDisplay(content, isError = false) {
            const display = document.getElementById('result-display');
            const className = isError ? 'error-box' : 'success-box';
            display.innerHTML = `<div class="${className}">${content}</div>`;
        }

        function addScript(scriptContent, stepName) {
            const scriptArea = document.getElementById('dynamic-script-area');
            const scriptElement = document.createElement('script');
            scriptElement.textContent = scriptContent;
            scriptElement.setAttribute('data-step', stepName);
            
            try {
                scriptArea.appendChild(scriptElement);
                return true;
            } catch (e) {
                errorLog.push({
                    step: testStep,
                    message: e.message,
                    type: 'script_injection_error'
                });
                return false;
            }
        }

        function testStep1() {
            testStep = 1;
            updateDisplay('<h3>🔍 步骤1: 测试基础JavaScript代码...</h3>');
            
            const basicCode = `
                // 步骤1: 基础变量和函数
                console.log('步骤1: 基础代码测试');
                
                let testVar = 'hello';
                const testConst = 'world';
                var testOldVar = 'test';
                
                function testFunction() {
                    return 'test function';
                }
                
                const arrowFunction = () => 'arrow function';
                
                console.log('步骤1完成');
            `;
            
            setTimeout(() => {
                if (addScript(basicCode, 'step1')) {
                    if (errorLog.filter(e => e.step === 1).length === 0) {
                        updateDisplay('<h3>✅ 步骤1: 基础代码测试通过</h3><p>基础JavaScript语法正常</p>');
                    }
                }
            }, 100);
        }

        function testStep2() {
            testStep = 2;
            updateDisplay('<h3>🔍 步骤2: 测试模板字符串和复杂表达式...</h3>');
            
            const templateCode = `
                // 步骤2: 模板字符串和表达式
                console.log('步骤2: 模板字符串测试');
                
                const name = 'test';
                const templateString = \`Hello \${name}\`;
                
                const obj = { prop: 'value' };
                const complexTemplate = \`Result: \${obj && obj.prop ? obj.prop : 'default'}\`;
                
                // 测试三元运算符
                const field = 'name';
                const fieldKey = field === 'name' ? 'visitor_name' :
                               field === 'email' ? 'visitor_email' :
                               field === 'phone' ? 'visitor_phone' : field;
                
                const fieldName = field === 'name' ? '姓名' :
                                field === 'email' ? '邮箱' :
                                field === 'phone' ? '电话' : '其他';
                
                console.log('步骤2完成:', templateString, complexTemplate, fieldKey, fieldName);
            `;
            
            setTimeout(() => {
                if (addScript(templateCode, 'step2')) {
                    if (errorLog.filter(e => e.step === 2).length === 0) {
                        updateDisplay('<h3>✅ 步骤2: 模板字符串测试通过</h3><p>模板字符串和三元运算符语法正常</p>');
                    }
                }
            }, 100);
        }

        function testStep3() {
            testStep = 3;
            updateDisplay('<h3>🔍 步骤3: 测试Console调用和Fetch语法...</h3>');
            
            const consoleCode = `
                // 步骤3: Console调用和Fetch语法
                console.log('步骤3: Console和Fetch测试');
                
                const now = new Date().toLocaleTimeString();
                const error = new Error('测试错误');
                
                // 测试各种console调用
                console.log(\`时间: \${now}\`);
                console.error(\`❌ [\${now}] 错误:\`, error);
                console.warn(\`⚠️ [\${now}] 警告:\`, '测试警告');
                
                // 测试Fetch语法（不实际执行）
                const sessionId = 'test123';
                const lastMessageId = 0;
                const apiUrl = \`/api/customer-service/messages/\${sessionId}?last_id=\${lastMessageId}\`;
                
                // 模拟fetch调用语法
                const fetchSyntax = \`
                    fetch(apiUrl)
                        .then(response => response.json())
                        .then(data => {
                            console.log('数据:', data);
                        })
                        .catch(error => {
                            console.error('错误:', error);
                        });
                \`;
                
                console.log('步骤3完成');
            `;
            
            setTimeout(() => {
                if (addScript(consoleCode, 'step3')) {
                    if (errorLog.filter(e => e.step === 3).length === 0) {
                        updateDisplay('<h3>✅ 步骤3: Console和Fetch测试通过</h3><p>Console调用和Fetch语法正常</p>');
                    }
                }
            }, 100);
        }

        function testStep4() {
            testStep = 4;
            updateDisplay('<h3>🔍 步骤4: 测试复杂逻辑和事件处理...</h3>');
            
            const complexCode = `
                // 步骤4: 复杂逻辑和事件处理
                console.log('步骤4: 复杂逻辑测试');
                
                // 模拟客服系统的复杂逻辑
                let csSettings = {
                    system_enabled: true,
                    offline_form_required: ['message']
                };
                
                // 模拟表单验证逻辑
                function validateForm(data) {
                    const required = csSettings.offline_form_required || ['message'];
                    
                    for (const field of required) {
                        const fieldKey = field === 'name' ? 'visitor_name' :
                                       field === 'email' ? 'visitor_email' :
                                       field === 'phone' ? 'visitor_phone' :
                                       field === 'whatsapp' ? 'visitor_whatsapp' : field;
                        
                        if (!data[fieldKey]) {
                            const fieldName = field === 'name' ? '姓名' :
                                            field === 'email' ? '邮箱' :
                                            field === 'phone' ? '电话' :
                                            field === 'whatsapp' ? 'WhatsApp' : '留言内容';
                            
                            const alertMessage = \`请填写\${fieldName}\`;
                            console.log('验证失败:', alertMessage);
                            return false;
                        }
                    }
                    return true;
                }
                
                // 测试事件监听器语法
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('DOM加载完成');
                });
                
                // 测试异步函数语法
                async function testAsync() {
                    try {
                        const response = await fetch('/api/test');
                        const data = await response.json();
                        return data;
                    } catch (error) {
                        console.error('异步错误:', error);
                    }
                }
                
                console.log('步骤4完成');
            `;
            
            setTimeout(() => {
                if (addScript(complexCode, 'step4')) {
                    if (errorLog.filter(e => e.step === 4).length === 0) {
                        updateDisplay('<h3>✅ 步骤4: 复杂逻辑测试通过</h3><p>所有测试代码语法正常！</p>');
                    }
                }
            }, 100);
        }

        function clearAll() {
            testStep = 0;
            errorLog = [];
            
            // 清除所有动态添加的脚本
            const scriptArea = document.getElementById('dynamic-script-area');
            scriptArea.innerHTML = '';
            
            updateDisplay(`
                <h3>🧹 测试已清空</h3>
                <p>所有动态脚本已移除，错误日志已清空。</p>
                <p>可以重新开始测试。</p>
            `);
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay(`
                <h3>🚀 错误隔离测试已启动</h3>
                <p>这个工具将逐步添加JavaScript代码来找出导致语法错误的具体位置。</p>
                <p><strong>方法</strong>: 逐步添加代码，观察在哪一步出现语法错误</p>
                <p><strong>目标</strong>: 精确定位 "missing ) after argument list" 错误的源头</p>
            `);
        });
    </script>
</body>
</html>
