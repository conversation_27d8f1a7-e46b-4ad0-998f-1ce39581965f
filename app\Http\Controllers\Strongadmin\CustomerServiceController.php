<?php

namespace App\Http\Controllers\Strongadmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\WorkingHoursService;

class CustomerServiceController extends Controller
{
    // 客服会话列表
    public function sessions(Request $request)
    {
        try {
            // 自动添加admin_read字段（如果不存在）
            $this->ensureAdminReadField();

            // 获取会话列表，按最新活动时间排序
            $sessions = DB::table('customer_service_sessions')
                         ->select([
                             'id', 'session_id', 'user_id', 'visitor_name', 'visitor_email',
                             'visitor_ip', 'user_agent', 'status', 'last_activity', 'last_seen',
                             'geo_country', 'geo_region', 'geo_city', 'internal_notes',
                             'admin_read', 'admin_read_at', 'created_at', 'updated_at'
                         ])
                         ->orderBy('created_at', 'desc')
                         ->orderBy('last_activity', 'desc')
                         ->limit(50)
                         ->get();

            // 为每个会话添加额外信息
            foreach ($sessions as $session) {
                // 改进访客命名：区分真实用户和访客
                if (empty($session->visitor_name)) {
                    // 没有名字，生成访客名
                    $randomNum = substr(md5($session->session_id), 0, 6);
                    $session->display_name = '访客-' . strtoupper($randomNum);
                } elseif (str_starts_with($session->visitor_name, '访客-')) {
                    // 已经是访客格式，保持不变
                    $session->display_name = $session->visitor_name;
                } else {
                    // 真实用户名，直接使用
                    $session->display_name = $session->visitor_name;
                }

                $session->contact_info = $session->visitor_email ?: $session->visitor_ip;

                // 获取最新消息
                $latestMessage = DB::table('customer_service_messages')
                                  ->where('session_id', $session->id)
                                  ->orderBy('created_at', 'desc')
                                  ->first();
                $session->latest_message = $latestMessage ? $latestMessage->message : '暂无消息';
                $session->latest_message_time = $latestMessage ? $latestMessage->created_at : null;

                // 统计该会话中客户发送的未读消息数量
                $unreadCount = DB::table('customer_service_messages')
                                ->where('session_id', $session->id)
                                ->where('sender_type', 'customer')
                                ->where('is_read', 0)
                                ->count();

                // 角标显示真实的未读消息数量
                $session->unread_count = $unreadCount;

                // 检查在线状态：如果超过8秒没有心跳，设为离线
                if ($session->last_seen && $session->status === 'online') {
                    $lastSeenTime = \Carbon\Carbon::parse($session->last_seen);
                    if ($lastSeenTime->diffInSeconds(now()) > 8) {
                        DB::table('customer_service_sessions')
                          ->where('id', $session->id)
                          ->update(['status' => 'offline']);
                        $session->status = 'offline';
                        \Log::info("会话 {$session->id} 超时设为离线 (超过8秒)");
                    }
                }

                // 调试信息
                \Log::info("会话 {$session->id}: 未读={$unreadCount}, 状态={$session->status}");
            }

            // 统计数据
            $stats = [
                'total' => DB::table('customer_service_sessions')->count(),
                'online' => DB::table('customer_service_sessions')->where('status', 'online')->count(),
                'offline' => DB::table('customer_service_sessions')->where('status', 'offline')->count(),
            ];

            // 转换为数组以便前端使用
            $sessionsArray = $sessions->map(function($session) {
                return [
                    'id' => $session->id,
                    'session_id' => $session->session_id,
                    'display_name' => $session->display_name ?? $session->visitor_name ?? '访客',
                    'status' => $session->status,
                    'unread_count' => $session->unread_count,
                    'visitor_ip' => $session->visitor_ip,
                    'geo_country' => $session->geo_country,
                    'geo_region' => $session->geo_region,
                    'geo_city' => $session->geo_city,
                    'last_seen' => $session->last_seen,
                    'created_at' => $session->created_at
                ];
            })->toArray();

            return view('strongadmin.customer-service.sessions', [
                'sessions' => $sessions,
                'sessionsArray' => $sessionsArray,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            \Log::error('Sessions page error: ' . $e->getMessage());

            return view('strongadmin.customer-service.sessions', [
                'sessions' => collect([]),
                'stats' => ['total' => 0, 'active' => 0, 'waiting' => 0, 'closed' => 0]
            ]);
        }
    }

    // 查看具体会话
    public function showSession($id, Request $request)
    {
        $session = CustomerServiceSession::with(['messages.senderUser', 'messages.senderAdmin', 'user', 'assignedAdmin'])
                                         ->findOrFail($id);

        // 如果是POST请求，发送消息
        if ($request->isMethod('post') && $request->message) {
            $message = CustomerServiceMessage::create([
                'session_id' => $session->id,
                'sender_type' => 'admin',
                'sender_id' => auth()->id(),
                'message' => $request->message
            ]);

            // 分配客服
            if (!$session->assigned_admin_id) {
                $session->update(['assigned_admin_id' => auth()->id()]);
            }

            $session->updateLastActivity();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message->load('senderAdmin')
                ]);
            }

            return redirect()->back()->with('success', 'Message sent successfully!');
        }

        // 标记管理员消息为已读
        $session->messages()
               ->where('sender_type', '!=', 'admin')
               ->where('is_read', false)
               ->update(['is_read' => true, 'read_at' => now()]);

        return $this->view('customer-service.session-detail', compact('session'));
    }

    // AI规则管理
    public function aiRules(Request $request)
    {
        $query = AiAutoReplyRule::orderBy('priority', 'desc')
                               ->orderBy('created_at', 'desc');

        if ($request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('reply_message', 'like', "%{$search}%");
            });
        }

        $rules = $query->paginate(20);

        return $this->view('customer-service.ai-rules', compact('rules'));
    }

    // 创建AI规则
    public function createAiRule(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'name' => 'required|string|max:255',
                'keywords' => 'required|string',
                'reply_message' => 'required|string|max:1000',
                'priority' => 'required|integer|min:0|max:100'
            ]);

            // 处理关键词
            $keywords = array_filter(array_map('trim', explode(',', $request->keywords)));

            AiAutoReplyRule::create([
                'name' => $request->name,
                'keywords' => $keywords,
                'reply_message' => $request->reply_message,
                'priority' => $request->priority,
                'is_active' => $request->has('is_active')
            ]);

            return redirect()->route('admin.customer-service.ai-rules')
                            ->with('success', 'AI rule created successfully!');
        }

        return $this->view('customer-service.ai-rule-form');
    }

    // 编辑AI规则
    public function editAiRule($id, Request $request)
    {
        $rule = AiAutoReplyRule::findOrFail($id);

        if ($request->isMethod('post')) {
            $request->validate([
                'name' => 'required|string|max:255',
                'keywords' => 'required|string',
                'reply_message' => 'required|string|max:1000',
                'priority' => 'required|integer|min:0|max:100'
            ]);

            // 处理关键词
            $keywords = array_filter(array_map('trim', explode(',', $request->keywords)));

            $rule->update([
                'name' => $request->name,
                'keywords' => $keywords,
                'reply_message' => $request->reply_message,
                'priority' => $request->priority,
                'is_active' => $request->has('is_active')
            ]);

            return redirect()->route('admin.customer-service.ai-rules')
                            ->with('success', 'AI rule updated successfully!');
        }

        return $this->view('customer-service.ai-rule-form', compact('rule'));
    }

    // 删除AI规则
    public function deleteAiRule($id)
    {
        $rule = AiAutoReplyRule::findOrFail($id);
        $rule->delete();

        return redirect()->route('admin.customer-service.ai-rules')
                        ->with('success', 'AI rule deleted successfully!');
    }

    // 切换AI规则状态
    public function toggleAiRule($id)
    {
        $rule = AiAutoReplyRule::findOrFail($id);
        $rule->update(['is_active' => !$rule->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $rule->is_active
        ]);
    }

    // 客服统计
    public function statistics()
    {
        $stats = [
            'total_sessions' => CustomerServiceSession::count(),
            'active_sessions' => CustomerServiceSession::where('status', 'active')->count(),
            'total_messages' => CustomerServiceMessage::count(),
            'ai_messages' => CustomerServiceMessage::where('sender_type', 'ai')->count(),
            'admin_messages' => CustomerServiceMessage::where('sender_type', 'admin')->count(),
            'customer_messages' => CustomerServiceMessage::where('sender_type', 'customer')->count(),
        ];

        // 最近会话
        $recentSessions = CustomerServiceSession::with(['latestMessage', 'user'])
                                               ->orderBy('last_activity', 'desc')
                                               ->limit(10)
                                               ->get();

        // 热门AI规则
        $popularRules = AiAutoReplyRule::where('is_active', true)
                                      ->orderBy('usage_count', 'desc')
                                      ->limit(10)
                                      ->get();

        return $this->view('customer-service.statistics', compact('stats', 'recentSessions', 'popularRules'));
    }

    // 发送管理员消息 (AJAX)
    public function sendAdminMessage(Request $request, $sessionId)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'message_type' => 'sometimes|string|in:text,image',
            'image_url' => 'sometimes|string|max:500'
        ]);

        $session = DB::table('customer_service_sessions')->where('id', $sessionId)->first();
        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => '会话不存在'
            ], 404);
        }

        // 准备消息数据
        $messageData = [
            'session_id' => $session->id,
            'sender_type' => 'admin',
            'sender_id' => auth()->id(),
            'message' => $request->message,
            'message_type' => $request->input('message_type', 'text'),
            'created_at' => now(),
            'updated_at' => now()
        ];

        // 如果是图片消息，添加图片URL
        if ($request->input('message_type') === 'image' && $request->image_url) {
            $messageData['image_url'] = $request->image_url;
        }

        $messageId = DB::table('customer_service_messages')->insertGetId($messageData);

        // 分配客服
        if (!$session->assigned_admin_id) {
            DB::table('customer_service_sessions')
              ->where('id', $session->id)
              ->update(['assigned_admin_id' => auth()->id()]);
        }

        // 更新会话最后活动时间
        DB::table('customer_service_sessions')
          ->where('id', $session->id)
          ->update(['last_activity' => now()]);

        // 获取刚创建的消息
        $message = DB::table('customer_service_messages')
                    ->where('id', $messageId)
                    ->first();

        return response()->json([
            'success' => true,
            'message' => $message,
            'message_id' => $messageId
        ]);
    }

    // 获取会话消息（API）
    public function getSessionMessages($id)
    {
        try {
            \Log::info("Getting messages for session ID: $id");

            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                \Log::warning("Session not found: $id");
                return response()->json(['success' => false, 'error' => '会话不存在'], 404);
            }

            \Log::info("Session found: " . json_encode($session));

            // 获取消息记录
            $messages = DB::table('customer_service_messages')
                         ->where('session_id', $id)
                         ->orderBy('created_at', 'asc')
                         ->get();

            // 处理消息数据，确保包含message_type字段
            $processedMessages = $messages->map(function($message) {
                $messageArray = (array) $message;

                // 如果没有message_type字段，根据消息内容判断
                if (!isset($messageArray['message_type'])) {
                    if (isset($messageArray['image_url']) && $messageArray['image_url']) {
                        $messageArray['message_type'] = 'image';
                    } else if (isset($messageArray['message']) && preg_match('/^\{.+\}$/', $messageArray['message'])) {
                        // 如果消息是大括号格式，标记为图片
                        $messageArray['message_type'] = 'image';
                    } else {
                        $messageArray['message_type'] = 'text';
                    }
                }

                return $messageArray;
            });

            \Log::info("Found " . $messages->count() . " messages for session $id");

            return response()->json(['success' => true, 'messages' => $processedMessages]);

        } catch (\Exception $e) {
            \Log::error('Get session messages error: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json(['success' => false, 'error' => '获取消息失败: ' . $e->getMessage()], 500);
        }
    }

    // 回复消息（API）
    public function replyMessageApi(Request $request, $id)
    {
        try {
            // 记录请求数据用于调试
            \Log::info('客服回复消息请求', [
                'session_id' => $id,
                'request_data' => $request->all()
            ]);

            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                \Log::warning('会话不存在', ['session_id' => $id]);
                return response()->json(['success' => false, 'error' => '会话不存在'], 404);
            }

            $message = $request->input('message');
            if (!$message) {
                \Log::warning('消息内容为空', ['session_id' => $id]);
                return response()->json(['success' => false, 'error' => '消息内容不能为空'], 400);
            }

            // 准备消息数据 - 只使用确定存在的字段
            $messageData = [
                'session_id' => $id,
                'sender_type' => 'admin',
                'sender_id' => auth()->id() ?? 1,
                'message' => $message,
                'is_read' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ];

            // 检查字段是否存在再添加
            try {
                $columns = DB::select("SHOW COLUMNS FROM st_customer_service_messages LIKE 'message_type'");
                if (!empty($columns)) {
                    $messageData['message_type'] = $request->input('message_type', 'text');

                    // 如果是图片消息且image_url字段存在
                    if ($request->input('message_type') === 'image' && $request->input('image_url')) {
                        $imageColumns = DB::select("SHOW COLUMNS FROM st_customer_service_messages LIKE 'image_url'");
                        if (!empty($imageColumns)) {
                            $messageData['image_url'] = $request->input('image_url');
                        }
                    }
                }
            } catch (Exception $e) {
                \Log::warning('检查数据库字段失败', ['error' => $e->getMessage()]);
            }

            // 插入管理员回复消息
            $messageId = DB::table('customer_service_messages')->insertGetId($messageData);

            // 更新会话最后活动时间
            DB::table('customer_service_sessions')
              ->where('id', $id)
              ->update([
                  'last_activity' => now(),
                  'updated_at' => now()
              ]);

            \Log::info("Admin replied to session $id, message ID: $messageId, content: $message");

            return response()->json(['success' => true, 'message' => '回复成功']);

        } catch (\Exception $e) {
            \Log::error('回复消息失败', [
                'session_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'error' => '发送失败: ' . $e->getMessage()], 500);
        }
    }

    // 保存会话备注
    public function saveSessionNotes(Request $request, $id)
    {
        try {
            $request->validate([
                'notes' => 'nullable|string|max:1000'
            ]);

            $session = DB::table('customer_service_sessions')->where('id', $id)->first();
            if (!$session) {
                return response()->json(['success' => false, 'message' => '会话不存在'], 404);
            }

            DB::table('customer_service_sessions')
              ->where('id', $id)
              ->update([
                  'internal_notes' => $request->input('notes'),
                  'updated_at' => now()
              ]);

            return response()->json(['success' => true, 'message' => '备注保存成功']);

        } catch (\Exception $e) {
            \Log::error('保存会话备注失败', [
                'session_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => '保存失败: ' . $e->getMessage()], 500);
        }
    }

    // 更新会话状态
    public function updateSessionStatus(Request $request, $id)
    {
        try {
            $request->validate([
                'status' => 'required|string|in:active,waiting,closed,resolved'
            ]);

            $session = DB::table('customer_service_sessions')->where('id', $id)->first();
            if (!$session) {
                return response()->json(['success' => false, 'message' => '会话不存在'], 404);
            }

            DB::table('customer_service_sessions')
              ->where('id', $id)
              ->update([
                  'status' => $request->input('status'),
                  'updated_at' => now()
              ]);

            return response()->json(['success' => true, 'message' => '状态更新成功']);

        } catch (\Exception $e) {
            \Log::error('更新会话状态失败', [
                'session_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => '更新失败: ' . $e->getMessage()], 500);
        }
    }

    // 获取单个会话的完整信息
    public function getSessionInfo($id)
    {
        try {
            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                return response()->json(['success' => false, 'message' => '会话不存在'], 404);
            }

            return response()->json([
                'success' => true,
                'session' => $session
            ]);

        } catch (\Exception $e) {
            \Log::error('获取会话信息失败', [
                'session_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => '获取失败: ' . $e->getMessage()], 500);
        }
    }

    // 获取会话统计信息
    public function getSessionStats($id)
    {
        try {
            $session = DB::table('customer_service_sessions')->where('id', $id)->first();

            if (!$session) {
                return response()->json(['success' => false, 'message' => '会话不存在'], 404);
            }

            // 获取消息统计
            $messageStats = DB::table('customer_service_messages')
                             ->where('session_id', $id)
                             ->selectRaw('
                                 COUNT(*) as total_messages,
                                 SUM(CASE WHEN sender_type = "customer" THEN 1 ELSE 0 END) as customer_messages,
                                 SUM(CASE WHEN sender_type = "admin" THEN 1 ELSE 0 END) as admin_messages,
                                 SUM(CASE WHEN sender_type = "ai" THEN 1 ELSE 0 END) as ai_messages,
                                 MIN(created_at) as first_message_time,
                                 MAX(created_at) as last_message_time
                             ')
                             ->first();

            // 计算平均响应时间（客服回复客户消息的时间）
            $avgResponseTime = $this->calculateAverageResponseTime($id);

            $stats = [
                'total_messages' => $messageStats->total_messages ?? 0,
                'customer_messages' => $messageStats->customer_messages ?? 0,
                'admin_messages' => $messageStats->admin_messages ?? 0,
                'ai_messages' => $messageStats->ai_messages ?? 0,
                'first_message_time' => $messageStats->first_message_time,
                'last_message_time' => $messageStats->last_message_time,
                'average_response_time' => $avgResponseTime,
                'session_duration' => $this->calculateSessionDuration($session),
                'unread_count' => $session->unread_count ?? 0
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            \Log::error('获取会话统计失败', [
                'session_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => '获取失败: ' . $e->getMessage()], 500);
        }
    }

    // 计算平均响应时间
    private function calculateAverageResponseTime($sessionId)
    {
        try {
            // 获取客户消息和紧随其后的客服回复
            $messages = DB::table('customer_service_messages')
                         ->where('session_id', $sessionId)
                         ->orderBy('created_at', 'asc')
                         ->get(['sender_type', 'created_at']);

            $responseTimes = [];
            $lastCustomerMessageTime = null;

            foreach ($messages as $message) {
                if ($message->sender_type === 'customer') {
                    $lastCustomerMessageTime = $message->created_at;
                } elseif ($message->sender_type === 'admin' && $lastCustomerMessageTime) {
                    // 计算响应时间（秒）
                    $responseTime = strtotime($message->created_at) - strtotime($lastCustomerMessageTime);
                    if ($responseTime > 0 && $responseTime < 3600) { // 只统计1小时内的响应
                        $responseTimes[] = $responseTime;
                    }
                    $lastCustomerMessageTime = null; // 重置
                }
            }

            if (empty($responseTimes)) {
                return null;
            }

            $avgSeconds = array_sum($responseTimes) / count($responseTimes);

            // 转换为可读格式
            if ($avgSeconds < 60) {
                return round($avgSeconds) . '秒';
            } elseif ($avgSeconds < 3600) {
                return round($avgSeconds / 60) . '分钟';
            } else {
                return round($avgSeconds / 3600, 1) . '小时';
            }

        } catch (\Exception $e) {
            \Log::warning('计算响应时间失败', ['error' => $e->getMessage()]);
            return null;
        }
    }

    // 计算会话持续时间
    private function calculateSessionDuration($session)
    {
        try {
            $start = strtotime($session->created_at);
            $end = $session->last_activity ? strtotime($session->last_activity) : time();

            $duration = $end - $start;

            if ($duration < 60) {
                return $duration . '秒';
            } elseif ($duration < 3600) {
                return round($duration / 60) . '分钟';
            } else {
                return round($duration / 3600, 1) . '小时';
            }

        } catch (\Exception $e) {
            return '未知';
        }
    }

    /**
     * 获取新消息API - 用于轮询
     */
    public function getNewMessages($sessionId)
    {
        try {
            $lastId = request('last_id', 0);

            // 获取会话
            $session = DB::table('customer_service_sessions')->where('session_id', $sessionId)->first();
            if (!$session) {
                return response()->json(['success' => false, 'error' => '会话不存在']);
            }

            // 获取新消息
            $messages = DB::table('customer_service_messages')
                         ->where('session_id', $session->id)
                         ->where('id', '>', $lastId)
                         ->where('sender_type', 'admin')
                         ->orderBy('id', 'asc')
                         ->get();

            // 处理消息数据，确保包含message_type字段
            $processedMessages = $messages->map(function($message) {
                $messageArray = (array) $message;

                // 如果没有message_type字段，根据消息内容判断
                if (!isset($messageArray['message_type'])) {
                    if (isset($messageArray['image_url']) && $messageArray['image_url']) {
                        $messageArray['message_type'] = 'image';
                    } else {
                        $messageArray['message_type'] = 'text';
                    }
                }

                return $messageArray;
            });

            return response()->json([
                'success' => true,
                'messages' => $processedMessages
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => '获取消息失败']);
        }
    }

    // 标记消息为已读
    public function markMessagesAsRead($id)
    {
        try {
            // 获取未读消息数量
            $unreadCount = DB::table('customer_service_messages')
                            ->where('session_id', $id)
                            ->where('sender_type', 'customer')
                            ->where('is_read', 0)
                            ->count();

            if ($unreadCount > 0) {
                // 标记该会话的所有客户消息为已读
                $updatedCount = DB::table('customer_service_messages')
                                 ->where('session_id', $id)
                                 ->where('sender_type', 'customer')
                                 ->where('is_read', 0)
                                 ->update(['is_read' => 1, 'read_at' => now()]);

                // 不再需要更新unread_count，因为现在实时计算

                \Log::info("Marked $updatedCount messages as read for session $id");
            }

            return response()->json([
                'success' => true,
                'marked_count' => $unreadCount,
                'message' => "已标记 $unreadCount 条消息为已读"
            ]);

        } catch (\Exception $e) {
            \Log::error('Mark messages as read error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '标记失败: ' . $e->getMessage()], 500);
        }
    }

    // 系统配置管理
    public function systemConfig(Request $request)
    {
        try {
            if ($request->isMethod('POST')) {
                // 保存配置
                $configs = $request->input('configs', []);

                foreach ($configs as $key => $value) {
                    DB::table('customer_service_config')
                      ->updateOrInsert(
                          ['config_key' => $key],
                          [
                              'config_value' => $value,
                              'updated_at' => now()
                          ]
                      );
                }

                return redirect()->back()->with('success', '配置保存成功');
            }

            // 获取所有配置
            $configs = DB::table('customer_service_config')
                        ->where('is_active', 1)
                        ->pluck('config_value', 'config_key')
                        ->toArray();

            return view('strongadmin.customer-service.system-config', compact('configs'));

        } catch (\Exception $e) {
            \Log::error('System config error: ' . $e->getMessage());
            return redirect()->back()->with('error', '操作失败：' . $e->getMessage());
        }
    }

    // 切换系统开关
    public function toggleSystem(Request $request)
    {
        try {
            $enabled = $request->input('enabled', false);

            // 更新设置表（Laravel会自动添加st_前缀）
            DB::table('customer_service_settings')
              ->updateOrInsert(
                  ['setting_key' => 'system_enabled'],
                  [
                      'setting_value' => $enabled ? '1' : '0',
                      'setting_type' => 'boolean',
                      'category' => 'basic',
                      'title' => '启用客服系统',
                      'description' => '是否启用客服系统功能',
                      'is_active' => 1,
                      'sort_order' => 1,
                      'updated_at' => now()
                  ]
              );

            // 同时更新配置表（向后兼容）
            DB::table('customer_service_config')
              ->updateOrInsert(
                  ['config_key' => 'system_enabled'],
                  [
                      'config_value' => $enabled ? '1' : '0',
                      'config_type' => 'boolean',
                      'description' => '在线客服系统总开关',
                      'is_active' => 1,
                      'updated_at' => now()
                  ]
              );

            $status = $enabled ? '启用' : '禁用';
            return response()->json([
                'success' => true,
                'message' => "客服系统已{$status}",
                'enabled' => $enabled
            ]);

        } catch (\Exception $e) {
            \Log::error('Toggle system error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '操作失败'], 500);
        }
    }

    // 获取系统状态
    public function getSystemStatus()
    {
        try {
            // 首先尝试从设置表获取（Laravel会自动添加st_前缀）
            $enabled = DB::table('customer_service_settings')
                        ->where('setting_key', 'system_enabled')
                        ->value('setting_value');

            // 如果设置表没有数据，尝试从配置表获取
            if ($enabled === null) {
                $enabled = DB::table('customer_service_config')
                            ->where('config_key', 'system_enabled')
                            ->value('config_value');
            }

            return response()->json([
                'success' => true,
                'enabled' => $enabled === '1'
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => '获取状态失败'], 500);
        }
    }

    // SSE实时消息推送
    public function messageStream(Request $request)
    {
        // 设置SSE响应头
        $response = response()->stream(function () use ($request) {
            // 设置无限执行时间
            set_time_limit(0);

            // 获取最后消息ID
            $lastMessageId = $request->input('last_message_id', 0);
            $checkInterval = 2; // 2秒检查一次
            $maxDuration = 300; // 最大连接时间5分钟
            $startTime = time();

            while (time() - $startTime < $maxDuration) {
                try {
                    // 检查新消息
                    $newMessages = DB::table('customer_service_messages')
                                    ->where('id', '>', $lastMessageId)
                                    ->where('sender_type', 'customer')
                                    ->orderBy('id', 'asc')
                                    ->get();

                    if ($newMessages->count() > 0) {
                        foreach ($newMessages as $message) {
                            // 获取会话信息
                            $session = DB::table('customer_service_sessions')
                                        ->where('id', $message->session_id)
                                        ->first();

                            if ($session) {
                                // 生成访客名称
                                $visitorName = $session->visitor_name ?:
                                              '访客-' . strtoupper(substr(md5($session->session_id), 0, 6));

                                $data = [
                                    'type' => 'new_message',
                                    'message_id' => $message->id,
                                    'session_id' => $session->id,
                                    'session_key' => $session->session_id,
                                    'visitor_name' => $visitorName,
                                    'message' => $message->message,
                                    'created_at' => $message->created_at,
                                    'timestamp' => time()
                                ];

                                echo "data: " . json_encode($data) . "\n\n";
                                $lastMessageId = $message->id;
                            }
                        }

                        // 刷新输出缓冲区
                        if (ob_get_level()) {
                            ob_flush();
                        }
                        flush();
                    }

                    // 发送心跳包
                    if (time() % 30 == 0) {
                        echo "data: " . json_encode(['type' => 'heartbeat', 'timestamp' => time()]) . "\n\n";
                        if (ob_get_level()) {
                            ob_flush();
                        }
                        flush();
                    }

                    // 检查连接是否还活着
                    if (connection_aborted()) {
                        break;
                    }

                    sleep($checkInterval);

                } catch (\Exception $e) {
                    \Log::error('SSE Stream error: ' . $e->getMessage());
                    echo "data: " . json_encode(['type' => 'error', 'message' => 'Stream error']) . "\n\n";
                    break;
                }
            }

        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no', // 禁用Nginx缓冲
        ]);

        return $response;
    }

    // 获取统计数据API
    public function getStatistics()
    {
        try {
            $stats = [
                'total_sessions' => DB::table('customer_service_sessions')->count(),
                'active_sessions' => DB::table('customer_service_sessions')->where('status', 'active')->count(),
                'total_messages' => DB::table('customer_service_messages')->count(),
                'ai_messages' => DB::table('customer_service_messages')->where('sender_type', 'ai')->count(),
                'unread_messages' => DB::table('customer_service_sessions')->sum('unread_count'),
            ];

            return response()->json(['success' => true, 'stats' => $stats]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => '获取统计失败'], 500);
        }
    }

    /**
     * 删除单个会话
     */
    public function deleteSession($id)
    {
        try {
            \Log::info("尝试删除会话，ID: $id, 请求方法: " . request()->method());

            // 删除会话相关的消息
            $messagesDeleted = DB::table('customer_service_messages')->where('session_id', $id)->delete();

            // 删除会话
            $deleted = DB::table('customer_service_sessions')->where('id', $id)->delete();

            \Log::info("删除结果 - 消息: $messagesDeleted 条, 会话: $deleted 条");

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => '会话删除成功',
                    'deleted_messages' => $messagesDeleted,
                    'session_id' => $id
                ], 200, ['Content-Type' => 'application/json']);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => '会话不存在'
                ], 200, ['Content-Type' => 'application/json']);
            }

        } catch (\Exception $e) {
            \Log::error('Delete session error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => '删除失败'
            ], 200, ['Content-Type' => 'application/json']);
        }
    }

    /**
     * 清空所有会话
     */
    public function clearAllSessions()
    {
        try {
            // 删除所有消息
            DB::table('customer_service_messages')->delete();

            // 删除所有会话
            DB::table('customer_service_sessions')->delete();

            return response()->json(['success' => true, 'message' => '所有会话已清空']);

        } catch (\Exception $e) {
            \Log::error('Clear all sessions error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '清空失败']);
        }
    }

    /**
     * 标记所有消息为已读
     */
    public function markAllAsRead()
    {
        try {
            // 只标记客户发送的未读消息为已读
            $updatedCount = DB::table('customer_service_messages')
                             ->where('sender_type', 'customer')
                             ->where('is_read', 0)
                             ->update(['is_read' => 1, 'read_at' => now()]);

            \Log::info("标记了 $updatedCount 条客户消息为已读");

            return response()->json([
                'success' => true,
                'message' => '所有消息已标记为已读',
                'marked_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            \Log::error('Mark all as read error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '操作失败']);
        }
    }

    /**
     * 标记单个会话为已读
     */
    public function markSessionAsRead($id)
    {
        try {
            \Log::info("标记会话为已读，会话ID: $id");

            // 标记该会话所有客户消息为已读
            $updatedCount = DB::table('customer_service_messages')
                             ->where('session_id', $id)
                             ->where('sender_type', 'customer')
                             ->where('is_read', 0)
                             ->update(['is_read' => 1, 'read_at' => now()]);

            \Log::info("会话 $id 标记了 $updatedCount 条客户消息为已读");

            // 计算剩余未读消息数量（应该为0）
            $remainingUnread = DB::table('customer_service_messages')
                                ->where('session_id', $id)
                                ->where('sender_type', 'customer')
                                ->where('is_read', 0)
                                ->count();

            return response()->json([
                'success' => true,
                'message' => '会话已标记为已读',
                'marked_count' => $updatedCount,
                'remaining_unread' => $remainingUnread
            ]);

        } catch (\Exception $e) {
            \Log::error('Mark session as read error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '操作失败']);
        }
    }

    /**
     * 获取所有会话的未读数量
     */
    public function getUnreadCounts()
    {
        try {
            $sessions = DB::table('customer_service_sessions')
                         ->select('*') // 明确选择所有字段
                         ->orderBy('created_at', 'desc')
                         ->get();

            $sessionData = [];
            foreach ($sessions as $session) {
                // 检查在线状态：如果超过8秒没有心跳，设为离线
                if ($session->last_seen && $session->status === 'online') {
                    $lastSeenTime = \Carbon\Carbon::parse($session->last_seen);
                    if ($lastSeenTime->diffInSeconds(now()) > 8) {
                        DB::table('customer_service_sessions')
                          ->where('id', $session->id)
                          ->update(['status' => 'offline']);
                        $session->status = 'offline';
                    }
                }

                // 统计该会话中客户发送的未读消息数量
                $unreadCount = DB::table('customer_service_messages')
                                ->where('session_id', $session->id)
                                ->where('sender_type', 'customer')
                                ->where('is_read', 0)
                                ->count();

                $sessionData[] = [
                    'id' => $session->id,
                    'unread_count' => $unreadCount,
                    'status' => $session->status
                ];
            }

            return response()->json([
                'success' => true,
                'sessions' => $sessionData
            ]);

        } catch (\Exception $e) {
            \Log::error('Get unread counts error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => '获取失败']);
        }
    }

    /**
     * 确保admin_read字段存在
     */
    private function ensureAdminReadField()
    {
        try {
            // 使用原生SQL检查admin_read字段
            $adminReadExists = DB::select("SHOW COLUMNS FROM customer_service_sessions LIKE 'admin_read'");
            if (empty($adminReadExists)) {
                DB::statement("
                    ALTER TABLE customer_service_sessions
                    ADD COLUMN admin_read tinyint(1) NOT NULL DEFAULT 0
                    COMMENT '管理员是否已读该会话：0=未读，1=已读'
                ");
                \Log::info('自动添加admin_read字段成功');
            }

            // 使用原生SQL检查admin_read_at字段
            $adminReadAtExists = DB::select("SHOW COLUMNS FROM customer_service_sessions LIKE 'admin_read_at'");
            if (empty($adminReadAtExists)) {
                DB::statement("
                    ALTER TABLE customer_service_sessions
                    ADD COLUMN admin_read_at timestamp NULL DEFAULT NULL
                    COMMENT '管理员阅读该会话的时间'
                ");
                \Log::info('自动添加admin_read_at字段成功');
            }
        } catch (\Exception $e) {
            \Log::error('添加admin_read字段失败: ' . $e->getMessage());
        }
    }

    /**
     * 离线留言列表
     */
    public function offlineMessages()
    {
        $messages = DB::table('customer_service_offline_messages')
                     ->orderBy('created_at', 'desc')
                     ->paginate(20);

        return view('strongadmin.customer-service.offline-messages', compact('messages'));
    }

    /**
     * 回复离线留言
     */
    public function replyOfflineMessage(Request $request, $id)
    {
        $request->validate([
            'reply' => 'required|string|max:2000'
        ]);

        try {
            $message = DB::table('customer_service_offline_messages')
                        ->where('id', $id)
                        ->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => '留言不存在'
                ], 404);
            }

            // 更新留言状态
            DB::table('customer_service_offline_messages')
              ->where('id', $id)
              ->update([
                  'admin_reply' => $request->input('reply'),
                  'status' => 'replied',
                  'replied_at' => now(),
                  'replied_by' => auth()->id(),
                  'updated_at' => now()
              ]);

            // 这里可以添加邮件通知逻辑
            if ($message->visitor_email) {
                \Log::info("发送邮件回复给 {$message->visitor_email}: {$request->input('reply')}");
            }

            return response()->json([
                'success' => true,
                'message' => '回复成功'
            ]);

        } catch (\Exception $e) {
            \Log::error('回复离线留言失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '回复失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除离线留言
     */
    public function deleteOfflineMessage($id)
    {
        try {
            $message = DB::table('customer_service_offline_messages')
                        ->where('id', $id)
                        ->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => '留言不存在'
                ], 404);
            }

            // 删除留言
            $deleted = DB::table('customer_service_offline_messages')
                        ->where('id', $id)
                        ->delete();

            if ($deleted) {
                \Log::info("管理员删除离线留言，ID: $id, 访客: {$message->visitor_name}");

                return response()->json([
                    'success' => true,
                    'message' => '留言删除成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '删除失败'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('删除离线留言失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '删除失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取工作时间状态
     */
    public function getWorkingStatus()
    {
        try {
            $workingHoursService = new WorkingHoursService();
            $statusInfo = $workingHoursService->getStatusInfo();

            return response()->json([
                'success' => true,
                'data' => $statusInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 手动设置在线状态
     */
    public function setManualStatus(Request $request)
    {
        try {
            $isOnline = $request->input('is_online', true);

            $workingHoursService = new WorkingHoursService();
            $workingHoursService->setManualStatus($isOnline);

            return response()->json([
                'success' => true,
                'message' => $isOnline ? '已设置为在线状态' : '已设置为离线状态'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '设置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 工作时间管理页面
     */
    public function workingHours()
    {
        try {
            $workingHoursService = new WorkingHoursService();
            $statusInfo = $workingHoursService->getStatusInfo();

            // 获取工作时间设置
            $settings = DB::table('st_customer_service_settings')
                ->where('category', 'working_hours')
                ->orderBy('sort_order')
                ->get();

            return view('strongadmin.customer-service.working-hours', [
                'statusInfo' => $statusInfo,
                'settings' => $settings
            ]);
        } catch (\Exception $e) {
            return back()->with('error', '加载失败: ' . $e->getMessage());
        }
    }
}
