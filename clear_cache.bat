@echo off
echo ========================================
echo StrongShop 缓存清理工具
echo ========================================
echo.

cd /d H:\wwwroot\mostxx.com_1111hp7hL

echo 1. 清理Laravel缓存...
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear

echo.
echo 2. 清理产品首页缓存...
php artisan tinker --execute="Cache::forget('product:home'); echo 'Home cache cleared';"

echo.
echo 3. 重新生成配置缓存...
php artisan config:cache

echo.
echo ========================================
echo 缓存清理完成！
echo ========================================
echo.
echo 现在可以刷新网站查看最新的产品图片数据
echo.
pause
