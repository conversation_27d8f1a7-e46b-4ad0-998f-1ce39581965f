<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>清理测试数据</title>
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        button { padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #c82333; }
        .safe-btn { background: #28a745; }
        .safe-btn:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 清理测试数据</h1>
        
        <?php
        if (isset($_POST['action'])) {
            try {
                if ($_POST['action'] === 'clean_old') {
                    // 清理1小时前的数据
                    $deleted = DB::table('customer_service_sessions')
                                ->where('created_at', '<', now()->subHour())
                                ->delete();
                    
                    echo "<div class='success'>✅ 已清理 {$deleted} 条1小时前的会话记录</div>";
                    
                } elseif ($_POST['action'] === 'clean_test') {
                    // 清理测试数据
                    $deleted = DB::table('customer_service_sessions')
                                ->where('visitor_name', 'like', '测试%')
                                ->orWhere('visitor_name', 'like', 'test%')
                                ->orWhere('session_id', 'like', 'test_%')
                                ->delete();
                    
                    echo "<div class='success'>✅ 已清理 {$deleted} 条测试会话记录</div>";
                    
                } elseif ($_POST['action'] === 'clean_all') {
                    // 清理所有数据（危险操作）
                    $sessionCount = DB::table('customer_service_sessions')->count();
                    $messageCount = DB::table('customer_service_messages')->count();
                    
                    DB::table('customer_service_messages')->delete();
                    DB::table('customer_service_sessions')->delete();
                    
                    echo "<div class='warning'>⚠️ 已清理所有数据：{$sessionCount} 个会话，{$messageCount} 条消息</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ 清理失败: " . $e->getMessage() . "</div>";
            }
        }
        
        // 显示当前数据统计
        try {
            $totalSessions = DB::table('customer_service_sessions')->count();
            $onlineSessions = DB::table('customer_service_sessions')->where('status', 'online')->count();
            $recentSessions = DB::table('customer_service_sessions')
                               ->where('created_at', '>', now()->subHour())
                               ->count();
            
            echo "<div class='info'>";
            echo "<h3>📊 当前数据统计</h3>";
            echo "<ul>";
            echo "<li>总会话数: {$totalSessions}</li>";
            echo "<li>在线会话: {$onlineSessions}</li>";
            echo "<li>最近1小时: {$recentSessions}</li>";
            echo "</ul>";
            echo "</div>";
            
            // 显示最近的会话
            if ($totalSessions > 0) {
                $recentSessions = DB::select("
                    SELECT id, session_id, visitor_name, visitor_ip, 
                           geo_country, geo_region, geo_city, status, created_at
                    FROM st_customer_service_sessions 
                    ORDER BY created_at DESC 
                    LIMIT 5
                ");
                
                echo "<div class='info'>";
                echo "<h3>📋 最近的会话</h3>";
                echo "<table border='1' style='width:100%;border-collapse:collapse;'>";
                echo "<tr style='background:#f8f9fa;'>";
                echo "<th style='padding:8px;'>ID</th>";
                echo "<th style='padding:8px;'>访客</th>";
                echo "<th style='padding:8px;'>IP</th>";
                echo "<th style='padding:8px;'>位置</th>";
                echo "<th style='padding:8px;'>状态</th>";
                echo "<th style='padding:8px;'>创建时间</th>";
                echo "</tr>";
                
                foreach ($recentSessions as $session) {
                    $location = $session->geo_country;
                    if ($session->geo_region && $session->geo_region !== '未知') {
                        $location .= ' ' . $session->geo_region;
                    }
                    if ($session->geo_city && $session->geo_city !== '未知') {
                        $location .= ' ' . $session->geo_city;
                    }
                    
                    $statusIcon = $session->status === 'online' ? '🟢' : '⚫';
                    
                    echo "<tr>";
                    echo "<td style='padding:8px;'>{$session->id}</td>";
                    echo "<td style='padding:8px;'>{$session->visitor_name}</td>";
                    echo "<td style='padding:8px; font-family:monospace;'>{$session->visitor_ip}</td>";
                    echo "<td style='padding:8px;'>{$location}</td>";
                    echo "<td style='padding:8px;'>{$statusIcon} {$session->status}</td>";
                    echo "<td style='padding:8px;'>{$session->created_at}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ 获取统计失败: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <div class="info">
            <h3>🛠️ 清理操作</h3>
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="clean_old">
                <button type="submit" class="safe-btn">清理1小时前的数据</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="clean_test">
                <button type="submit" class="safe-btn">清理测试数据</button>
            </form>
            
            <form method="post" style="display: inline;" onsubmit="return confirm('确定要清理所有数据吗？这个操作不可恢复！')">
                <input type="hidden" name="action" value="clean_all">
                <button type="submit">清理所有数据（危险）</button>
            </form>
        </div>
        
        <div class="info">
            <h3>🔗 测试链接</h3>
            <ul>
                <li><a href="/test_ip.php" target="_blank">IP获取测试</a></li>
                <li><a href="/" target="_blank">网站首页（测试客服）</a></li>
                <li><a href="/live_status.php" target="_blank">实时状态监控</a></li>
                <li><a href="/strongadmin/customer-service/sessions" target="_blank">后台管理</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
