<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展错误最终测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 600px; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        .result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🛡️ 扩展错误最终测试</h1>
    
    <div class="result success">
        <h3>✅ 修复方案</h3>
        <p>采用最简单最直接的方法：在页面头部内联JavaScript代码，立即拦截和静默扩展相关错误。</p>
    </div>
    
    <div class="result warning">
        <h3>🧪 测试按钮</h3>
        <button class="btn danger" onclick="testExtensionError()">测试扩展错误</button>
        <button class="btn" onclick="testNormalError()">测试正常错误</button>
        <button class="btn" onclick="checkConsole()">检查控制台</button>
    </div>
    
    <div id="results"></div>
    
    <div class="result success">
        <h3>📋 预期效果</h3>
        <ul>
            <li>✅ 扩展错误：完全静默，控制台不显示</li>
            <li>✅ 正常错误：正常显示在控制台</li>
            <li>✅ 网站功能：完全不受影响</li>
        </ul>
        
        <h3>🔧 技术实现</h3>
        <p>通过重写 <code>console.error</code>、<code>window.onerror</code> 和 <code>window.onunhandledrejection</code>，在错误输出前进行过滤。</p>
    </div>

    <script>
        function showResult(message, type = 'success') {
            const div = document.createElement('div');
            div.className = 'result ' + type;
            div.innerHTML = '<p>' + message + '</p>';
            document.getElementById('results').appendChild(div);
        }
        
        function testExtensionError() {
            showResult('🧪 正在测试扩展错误...', 'warning');
            
            // 测试console.error
            console.error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
            console.error('Extension context invalidated');
            console.error('message channel closed before a response was received');
            
            // 测试window.onerror
            setTimeout(() => {
                try {
                    throw new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
                } catch (e) {
                    window.onerror(e.message, 'test', 1, 1, e);
                }
            }, 100);
            
            // 测试Promise rejection
            setTimeout(() => {
                const error = new Error('Extension context invalidated. message channel closed');
                window.onunhandledrejection({
                    reason: error,
                    preventDefault: function() {}
                });
            }, 200);
            
            setTimeout(() => {
                showResult('✅ 扩展错误测试完成！如果控制台没有显示扩展相关错误，说明修复成功。', 'success');
            }, 500);
        }
        
        function testNormalError() {
            showResult('🧪 正在测试正常错误...', 'warning');
            
            console.error('这是一个正常的业务错误，应该显示在控制台');
            console.log('这是一个正常的日志信息');
            console.warn('这是一个正常的警告信息');
            
            setTimeout(() => {
                showResult('✅ 正常错误测试完成！这些错误应该在控制台正常显示。', 'success');
            }, 100);
        }
        
        function checkConsole() {
            showResult('📋 请检查浏览器控制台：', 'warning');
            showResult('• 扩展相关错误应该被过滤掉（不显示）', 'warning');
            showResult('• 正常的错误和日志应该正常显示', 'warning');
        }
        
        // 页面加载提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 扩展错误最终测试页面已加载');
            console.log('💡 现在可以测试扩展错误是否被正确过滤');
        });
    </script>
</body>
</html>
