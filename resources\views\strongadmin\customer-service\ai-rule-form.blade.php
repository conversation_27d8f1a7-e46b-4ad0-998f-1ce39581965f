@extends('strongadmin::layouts.app')

@push('styles')
<style>
.st-form-card {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0,0,0,.1);
    padding: 20px;
    margin-bottom: 15px;
}
.st-form-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}
.st-form-group {
    margin-bottom: 20px;
}
.st-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}
.st-required {
    color: #FF5722;
}
.st-form-help {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}
.st-preview-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}
.st-keyword-preview {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 3px;
}
</style>
@endpush

@section('content')
<div class="st-h15"></div>

<!-- 返回按钮 -->
<div class="layui-btn-container" style="margin-bottom: 15px;">
    <a href="../ai-rules" class="layui-btn layui-btn-primary">
        <i class="layui-icon layui-icon-return"></i> 返回列表
    </a>
</div>

<div class="st-form-card">
    <div class="st-form-title">
        <i class="layui-icon layui-icon-face-smile"></i> {{ isset($rule) ? '编辑AI规则' : '添加AI规则' }}
    </div>

    <form class="layui-form" method="POST" lay-filter="ai-rule-form">
        @csrf
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md8">
                <!-- 规则名称 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">规则名称 <span class="st-required">*</span></label>
                    <div class="layui-input-block">
                        <input type="text" name="name" value="{{ old('name', $rule->name ?? '') }}"
                               placeholder="请输入规则名称" autocomplete="off" class="layui-input" lay-verify="required">
                        <div class="st-form-help">例如：问候语、物流咨询、退换货等</div>
                    </div>
                </div>

                <!-- 关键词 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">触发关键词 <span class="st-required">*</span></label>
                    <div class="layui-input-block">
                        <input type="text" name="keywords" id="keywords-input"
                               value="{{ old('keywords', isset($rule) ? implode(', ', $rule->keywords) : '') }}"
                               placeholder="请输入关键词，用逗号分隔" autocomplete="off" class="layui-input" lay-verify="required">
                        <div class="st-form-help">例如：hello, hi, 你好, 您好（用英文逗号分隔）</div>
                        <div id="keywords-preview" class="st-preview-card" style="display: none;">
                            <strong>关键词预览：</strong>
                            <div id="keywords-tags"></div>
                        </div>
                    </div>
                </div>

                <!-- 回复内容 -->
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">回复内容 <span class="st-required">*</span></label>
                    <div class="layui-input-block">
                        <textarea name="reply_message" placeholder="请输入AI自动回复的内容"
                                  class="layui-textarea" lay-verify="required" rows="6">{{ old('reply_message', $rule->reply_message ?? '') }}</textarea>
                        <div class="st-form-help">支持表情符号，例如：👋 🚚 🔄</div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md4">
                <!-- 优先级 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">优先级</label>
                    <div class="layui-input-block">
                        <select name="priority" lay-verify="">
                            <option value="1" {{ old('priority', $rule->priority ?? 5) == 1 ? 'selected' : '' }}>1 - 最低</option>
                            <option value="3" {{ old('priority', $rule->priority ?? 5) == 3 ? 'selected' : '' }}>3 - 低</option>
                            <option value="5" {{ old('priority', $rule->priority ?? 5) == 5 ? 'selected' : '' }}>5 - 中等</option>
                            <option value="8" {{ old('priority', $rule->priority ?? 5) == 8 ? 'selected' : '' }}>8 - 高</option>
                            <option value="10" {{ old('priority', $rule->priority ?? 5) == 10 ? 'selected' : '' }}>10 - 最高</option>
                        </select>
                        <div class="st-form-help">数字越大优先级越高</div>
                    </div>
                </div>

                <!-- 状态 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <select name="is_active" lay-verify="">
                            <option value="1" {{ old('is_active', $rule->is_active ?? 1) == 1 ? 'selected' : '' }}>启用</option>
                            <option value="0" {{ old('is_active', $rule->is_active ?? 1) == 0 ? 'selected' : '' }}>禁用</option>
                        </select>
                    </div>
                </div>

                @if(isset($rule))
                <!-- 使用统计 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">使用统计</label>
                    <div class="layui-input-block">
                        <div class="st-stats-card" style="text-align: center; padding: 15px;">
                            <div style="font-size: 24px; font-weight: bold; color: #1E9FFF; margin-bottom: 5px;">
                                {{ $rule->usage_count ?? 0 }}
                            </div>
                            <div style="color: #666; font-size: 12px;">使用次数</div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- 预览区域 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">效果预览</label>
                    <div class="layui-input-block">
                        <div class="st-preview-card">
                            <div style="margin-bottom: 10px;">
                                <strong>客户：</strong>
                                <span style="color: #666;">你好</span>
                            </div>
                            <div>
                                <strong>AI回复：</strong>
                                <div id="reply-preview" style="color: #1E9FFF; margin-top: 5px; font-style: italic;">
                                    {{ $rule->reply_message ?? '请输入回复内容...' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="layui-form-item" style="margin-top: 30px;">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submit">
                    <i class="layui-icon layui-icon-ok"></i> {{ isset($rule) ? '更新规则' : '创建规则' }}
                </button>
                <a href="../ai-rules" class="layui-btn layui-btn-primary">
                    <i class="layui-icon layui-icon-close"></i> 取消
                </a>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
layui.use(['form'], function(){
    var form = layui.form;

    // 关键词预览
    function updateKeywordsPreview() {
        var keywords = document.getElementById('keywords-input').value;
        var previewDiv = document.getElementById('keywords-preview');
        var tagsDiv = document.getElementById('keywords-tags');

        if (keywords.trim()) {
            var keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
            var tagsHtml = keywordArray.map(k => '<span class="st-keyword-preview">' + k + '</span>').join('');
            tagsDiv.innerHTML = tagsHtml;
            previewDiv.style.display = 'block';
        } else {
            previewDiv.style.display = 'none';
        }
    }

    // 回复内容预览
    function updateReplyPreview() {
        var replyText = document.querySelector('textarea[name="reply_message"]').value;
        var previewDiv = document.getElementById('reply-preview');
        previewDiv.textContent = replyText || '请输入回复内容...';
    }

    // 绑定事件
    document.getElementById('keywords-input').addEventListener('input', updateKeywordsPreview);
    document.querySelector('textarea[name="reply_message"]').addEventListener('input', updateReplyPreview);

    // 初始化预览
    updateKeywordsPreview();
    updateReplyPreview();

    // 表单提交
    form.on('submit(submit)', function(data){
        // 这里可以添加表单验证逻辑
        return true; // 允许表单提交
    });
});
</script>
@endpush

@endsection
