<?php
// Laravel环境初始化
require_once __DIR__ . '/../../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 调试：记录查询开始
    error_log("API Debug: Starting settings query");

    // 获取前台可用的设置
    $rawSettings = DB::table('customer_service_settings')
                    ->where('is_active', 1)
                    ->whereIn('category', ['basic', 'appearance', 'sound', 'offline'])
                    ->get();

    // 调试：记录查询结果
    error_log("API Debug: Found " . $rawSettings->count() . " settings");

    $settings = $rawSettings->keyBy('setting_key')
                           ->map(function ($item) {
                     // 处理不同类型的值
                     switch ($item->setting_type) {
                         case 'boolean':
                             $result = $item->setting_value === '1';
                             // 调试日志
                             error_log("Boolean conversion: {$item->setting_key} = '{$item->setting_value}' -> " . ($result ? 'true' : 'false'));
                             return $result;
                         case 'number':
                             return (float) $item->setting_value;
                         case 'json':
                             return json_decode($item->setting_value, true) ?: [];
                         case 'file':
                             if ($item->setting_value) {
                                 // 检查文件是否存在
                                 $fullPath = storage_path('app/public/' . $item->setting_value);
                                 if (file_exists($fullPath)) {
                                     return '/storage/' . $item->setting_value;
                                 }
                             }
                             return '';
                         case 'color':
                             return $item->setting_value ?: '#667eea';
                         default:
                             return $item->setting_value;
                     }
                 });

    echo json_encode([
        'success' => true,
        'data' => $settings
    ]);
    
} catch (Exception $e) {
    error_log('获取客服设置失败: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '获取设置失败',
        'data' => []
    ]);
}
