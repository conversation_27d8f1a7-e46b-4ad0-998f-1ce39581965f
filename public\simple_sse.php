<?php
/**
 * 最简单的SSE实现 - 纯PHP
 */

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '简单SSE连接成功',
    'session_id' => $sessionId,
    'timestamp' => time()
]) . "\n\n";
flush();

// 模拟消息检查循环
for ($i = 0; $i < 30; $i++) {
    // 每10秒发送一个模拟消息
    if ($i % 5 == 0 && $i > 0) {
        echo "data: " . json_encode([
            'type' => 'new_reply',
            'message_id' => $lastMessageId + $i,
            'sender_type' => 'admin',
            'message' => '模拟管理员回复 ' . date('H:i:s'),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
    // 心跳包
    if ($i % 15 == 0) {
        echo "data: " . json_encode([
            'type' => 'heartbeat', 
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
    // 检查连接
    if (connection_aborted()) {
        break;
    }
    
    sleep(2);
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => 'SSE连接结束',
    'timestamp' => time()
]) . "\n\n";
flush();
?>
