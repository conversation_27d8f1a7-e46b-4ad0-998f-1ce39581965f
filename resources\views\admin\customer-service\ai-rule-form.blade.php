@extends('admin.layouts.app')

@section('title', isset($rule) ? '编辑AI规则' : '添加AI规则')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-robot"></i> {{ isset($rule) ? '编辑AI规则' : '添加AI规则' }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.customer-service.ai-rules') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>

                <form method="POST" action="{{ isset($rule) ? route('admin.customer-service.ai-rule.update', $rule->id) : route('admin.customer-service.ai-rule.store') }}">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 规则名称 -->
                                <div class="form-group">
                                    <label for="name">规则名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $rule->name ?? '') }}" 
                                           placeholder="例如：问候语、物流咨询等" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- 关键词 -->
                                <div class="form-group">
                                    <label for="keywords">触发关键词 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('keywords') is-invalid @enderror" 
                                           id="keywords" name="keywords" 
                                           value="{{ old('keywords', isset($rule) ? implode(', ', $rule->keywords) : '') }}" 
                                           placeholder="用逗号分隔，例如：hello, hi, 你好" required>
                                    <small class="form-text text-muted">
                                        用户消息包含任一关键词时将触发此规则。多个关键词用逗号分隔。
                                    </small>
                                    @error('keywords')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- 回复内容 -->
                                <div class="form-group">
                                    <label for="reply_message">回复内容 <span class="text-danger">*</span></label>
                                    <textarea class="form-control @error('reply_message') is-invalid @enderror" 
                                              id="reply_message" name="reply_message" rows="5" 
                                              placeholder="输入AI自动回复的内容..." required>{{ old('reply_message', $rule->reply_message ?? '') }}</textarea>
                                    <small class="form-text text-muted">
                                        支持emoji表情，建议内容友好、专业。最多1000字符。
                                    </small>
                                    @error('reply_message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 优先级 -->
                                <div class="form-group">
                                    <label for="priority">优先级 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('priority') is-invalid @enderror" 
                                           id="priority" name="priority" min="0" max="100" 
                                           value="{{ old('priority', $rule->priority ?? 5) }}" required>
                                    <small class="form-text text-muted">
                                        0-100，数字越大优先级越高
                                    </small>
                                    @error('priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- 状态 -->
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                               {{ old('is_active', $rule->is_active ?? true) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">启用规则</label>
                                    </div>
                                    <small class="form-text text-muted">
                                        只有启用的规则才会自动回复
                                    </small>
                                </div>

                                @if(isset($rule))
                                <!-- 使用统计 -->
                                <div class="form-group">
                                    <label>使用统计</label>
                                    <div class="info-box bg-info">
                                        <span class="info-box-icon"><i class="fas fa-chart-bar"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">使用次数</span>
                                            <span class="info-box-number">{{ $rule->usage_count }}</span>
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <!-- 预设模板 -->
                                <div class="form-group">
                                    <label>快速模板</label>
                                    <div class="btn-group-vertical btn-block">
                                        <button type="button" class="btn btn-outline-primary btn-sm template-btn" 
                                                data-keywords="hello, hi, hey, 你好" 
                                                data-message="Hello! 👋 Welcome to our store! How can I help you today?">
                                            问候语
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm template-btn" 
                                                data-keywords="shipping, delivery, 物流, 快递" 
                                                data-message="We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You'll receive a tracking number once your order ships.">
                                            物流咨询
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm template-btn" 
                                                data-keywords="return, refund, 退货, 退款" 
                                                data-message="We have a 30-day return policy! 🔄 If you're not satisfied, you can return items within 30 days for a full refund.">
                                            退换货
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ isset($rule) ? '更新规则' : '创建规则' }}
                        </button>
                        <a href="{{ route('admin.customer-service.ai-rules') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 模板按钮点击事件
    document.querySelectorAll('.template-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const keywords = this.dataset.keywords;
            const message = this.dataset.message;
            
            document.getElementById('keywords').value = keywords;
            document.getElementById('reply_message').value = message;
        });
    });

    // 字符计数
    const textarea = document.getElementById('reply_message');
    const counter = document.createElement('small');
    counter.className = 'form-text text-muted';
    textarea.parentNode.appendChild(counter);

    function updateCounter() {
        const length = textarea.value.length;
        counter.textContent = `${length}/1000 字符`;
        if (length > 1000) {
            counter.className = 'form-text text-danger';
        } else {
            counter.className = 'form-text text-muted';
        }
    }

    textarea.addEventListener('input', updateCounter);
    updateCounter();
});
</script>
@endsection
