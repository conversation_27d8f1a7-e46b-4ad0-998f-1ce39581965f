<?php
/**
 * 测试用户信息获取
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>用户信息测试</title>";
echo "<meta name='csrf-token' content='" . csrf_token() . "'>";
echo "<meta name='user-name' content='测试用户'>";
echo "<meta name='user-email' content='<EMAIL>'>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;white-space:pre-wrap;}
button{padding:10px 20px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;margin:5px;}
button:hover{background:#0056b3;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>👤 用户信息获取测试</h1>";

echo "<div class='info'>📋 测试客服系统是否能正确获取登录用户信息</div>";

// 模拟设置用户信息到全局变量
echo "<script>
// 模拟用户登录状态
window.user = {
    name: '张三',
    username: 'zhangsan',
    email: '<EMAIL>',
    id: 123
};

// 模拟localStorage用户数据
localStorage.setItem('user', JSON.stringify({
    name: '李四',
    email: '<EMAIL>',
    id: 456
}));
</script>";

echo "<h2>🔍 用户信息检测</h2>";

echo "<div style='display:flex;gap:10px;margin:20px 0;'>";
echo "<button onclick='testUserInfo()'>检测用户信息</button>";
echo "<button onclick='testInitSession()'>测试初始化会话</button>";
echo "<button onclick='clearUserInfo()'>清除用户信息</button>";
echo "</div>";

echo "<div id='userInfoResult'></div>";

echo "<h2>📊 当前会话数据</h2>";

try {
    $sessions = DB::select("
        SELECT 
            id,
            session_id,
            user_id,
            visitor_name,
            visitor_email,
            visitor_ip,
            status,
            created_at,
            updated_at
        FROM st_customer_service_sessions 
        ORDER BY updated_at DESC
        LIMIT 10
    ");
    
    if (empty($sessions)) {
        echo "<div class='warning'>⚠️ 暂无会话数据</div>";
    } else {
        echo "<table border='1' style='width:100%;border-collapse:collapse;'>";
        echo "<tr style='background:#f8f9fa;'>";
        echo "<th style='padding:8px;'>ID</th>";
        echo "<th style='padding:8px;'>会话ID</th>";
        echo "<th style='padding:8px;'>用户ID</th>";
        echo "<th style='padding:8px;'>访客名称</th>";
        echo "<th style='padding:8px;'>邮箱</th>";
        echo "<th style='padding:8px;'>IP</th>";
        echo "<th style='padding:8px;'>状态</th>";
        echo "<th style='padding:8px;'>更新时间</th>";
        echo "</tr>";
        
        foreach ($sessions as $session) {
            $statusIcon = $session->status === 'online' ? '🟢' : '⚫';
            $userType = empty($session->user_id) ? '访客' : '用户';
            
            echo "<tr>";
            echo "<td style='padding:8px;'>{$session->id}</td>";
            echo "<td style='padding:8px;'>" . substr($session->session_id, 0, 8) . "...</td>";
            echo "<td style='padding:8px;'>{$userType}({$session->user_id})</td>";
            echo "<td style='padding:8px;'>{$session->visitor_name}</td>";
            echo "<td style='padding:8px;'>{$session->visitor_email}</td>";
            echo "<td style='padding:8px;'>{$session->visitor_ip}</td>";
            echo "<td style='padding:8px;'>{$statusIcon} {$session->status}</td>";
            echo "<td style='padding:8px;'>{$session->updated_at}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 查询失败: " . $e->getMessage() . "</div>";
}

echo "<script>
function testUserInfo() {
    const resultDiv = document.getElementById('userInfoResult');
    
    // 模拟客服系统的用户信息获取逻辑
    let userInfo = {
        visitor_name: null,
        visitor_email: null
    };

    // 1. 从全局变量获取
    if (window.user) {
        userInfo.visitor_name = window.user.name || window.user.username;
        userInfo.visitor_email = window.user.email;
    }

    // 2. 从meta标签获取
    const userNameMeta = document.querySelector('meta[name=\"user-name\"]');
    const userEmailMeta = document.querySelector('meta[name=\"user-email\"]');
    if (userNameMeta) userInfo.visitor_name = userNameMeta.getAttribute('content');
    if (userEmailMeta) userInfo.visitor_email = userEmailMeta.getAttribute('content');

    // 3. 从localStorage获取
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
        try {
            const userData = JSON.parse(storedUser);
            userInfo.visitor_name = userInfo.visitor_name || userData.name || userData.username;
            userInfo.visitor_email = userInfo.visitor_email || userData.email;
        } catch (e) {
            console.warn('解析localStorage用户数据失败:', e);
        }
    }

    resultDiv.innerHTML = '<div class=\"success\">✅ 用户信息检测完成</div><pre>' + JSON.stringify(userInfo, null, 2) + '</pre>';
}

function testInitSession() {
    const resultDiv = document.getElementById('userInfoResult');
    resultDiv.innerHTML = '<div class=\"info\">🔄 正在测试会话初始化...</div>';
    
    // 获取用户信息
    let userInfo = {
        visitor_name: null,
        visitor_email: null
    };

    if (window.user) {
        userInfo.visitor_name = window.user.name || window.user.username;
        userInfo.visitor_email = window.user.email;
    }

    const userNameMeta = document.querySelector('meta[name=\"user-name\"]');
    const userEmailMeta = document.querySelector('meta[name=\"user-email\"]');
    if (userNameMeta) userInfo.visitor_name = userNameMeta.getAttribute('content');
    if (userEmailMeta) userInfo.visitor_email = userEmailMeta.getAttribute('content');

    console.log('发送用户信息:', userInfo);
    
    fetch('/customer-service/init', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content')
        },
        body: JSON.stringify(userInfo)
    })
    .then(response => response.json())
    .then(data => {
        console.log('会话初始化响应:', data);
        resultDiv.innerHTML = '<div class=\"success\">✅ 会话初始化成功</div><pre>' + JSON.stringify(data, null, 2) + '</pre><div class=\"info\">💡 请刷新页面查看会话数据更新</div>';
    })
    .catch(error => {
        console.error('会话初始化失败:', error);
        resultDiv.innerHTML = '<div class=\"error\">❌ 会话初始化失败: ' + error.message + '</div>';
    });
}

function clearUserInfo() {
    window.user = null;
    localStorage.removeItem('user');
    document.getElementById('userInfoResult').innerHTML = '<div class=\"warning\">⚠️ 用户信息已清除</div>';
}
</script>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "<li><a href='/test_heartbeat.php' target='_blank'>心跳状态测试</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
