<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>检查菜单路由</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:1000px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔍 检查菜单路由配置</h1>";

try {
    // 1. 查看客服相关菜单
    echo "<h3>1. 客服相关菜单</h3>";
    $menus = DB::select("
        SELECT id, name, route_url, parent_id, level, status
        FROM st_strongadmin_menu 
        WHERE name LIKE '%客服%' OR name IN ('设置中心', '离线留言', '会话管理', 'AI规则管理', '统计报表')
        ORDER BY parent_id, sort
    ");
    
    echo "<table>";
    echo "<tr><th>ID</th><th>名称</th><th>路由URL</th><th>完整URL</th><th>父级ID</th><th>级别</th><th>状态</th></tr>";
    foreach ($menus as $menu) {
        $fullUrl = '/strongadmin/' . $menu->route_url;
        echo "<tr>";
        echo "<td>{$menu->id}</td>";
        echo "<td><strong>{$menu->name}</strong></td>";
        echo "<td><code>{$menu->route_url}</code></td>";
        echo "<td><a href='{$fullUrl}' target='_blank'>{$fullUrl}</a></td>";
        echo "<td>{$menu->parent_id}</td>";
        echo "<td>{$menu->level}</td>";
        echo "<td>" . ($menu->status ? '启用' : '禁用') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 检查路由文件中的定义
    echo "<h3>2. 路由文件检查</h3>";
    $routeFile = file_get_contents(__DIR__ . '/../routes/admin.php');
    
    echo "<h4>客服相关路由定义:</h4>";
    echo "<pre style='background:#f8f9fa;padding:10px;border-radius:4px;overflow-x:auto;'>";
    
    // 提取客服相关路由
    $lines = explode("\n", $routeFile);
    $inCustomerService = false;
    $customerServiceRoutes = [];
    
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, 'customer-service') !== false || 
            strpos($line, 'CustomerService') !== false ||
            $inCustomerService) {
            
            if (strpos($line, 'Route::') !== false) {
                $customerServiceRoutes[] = ($lineNum + 1) . ": " . trim($line);
            }
            
            // 检查是否在客服路由组内
            if (strpos($line, "Route::prefix('customer-service')") !== false) {
                $inCustomerService = true;
            }
            if (strpos($line, '});') !== false && $inCustomerService) {
                $inCustomerService = false;
            }
        }
    }
    
    foreach ($customerServiceRoutes as $route) {
        echo htmlspecialchars($route) . "\n";
    }
    echo "</pre>";
    
    // 3. 测试路由可访问性
    echo "<h3>3. 路由可访问性测试</h3>";
    $testRoutes = [
        'customer-service/settings' => '设置中心',
        'customer-service/offline-messages' => '离线留言',
        'customer-service/sessions' => '会话管理',
        'customer-service/ai-rules' => 'AI规则管理',
        'customer-service/statistics' => '统计报表'
    ];
    
    echo "<table>";
    echo "<tr><th>路由</th><th>名称</th><th>完整URL</th><th>测试链接</th></tr>";
    foreach ($testRoutes as $route => $name) {
        $fullUrl = '/strongadmin/' . $route;
        echo "<tr>";
        echo "<td><code>{$route}</code></td>";
        echo "<td>{$name}</td>";
        echo "<td>{$fullUrl}</td>";
        echo "<td><a href='{$fullUrl}' target='_blank' class='btn btn-sm btn-primary'>测试访问</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. 修复建议
    echo "<h3>4. 修复建议</h3>";
    echo "<div class='info'>";
    echo "<p><strong>问题分析:</strong></p>";
    echo "<ul>";
    echo "<li>菜单中的路由应该是相对路径，如 <code>customer-service/offline-messages</code></li>";
    echo "<li>实际访问时会自动加上 <code>/strongadmin/</code> 前缀</li>";
    echo "<li>确保路由文件中定义了对应的路由</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<p><strong>正确的菜单路由配置:</strong></p>";
    echo "<ul>";
    echo "<li>设置中心: <code>customer-service/settings</code></li>";
    echo "<li>离线留言: <code>customer-service/offline-messages</code></li>";
    echo "<li>会话管理: <code>customer-service/sessions</code></li>";
    echo "<li>AI规则管理: <code>customer-service/ai-rules</code></li>";
    echo "<li>统计报表: <code>customer-service/statistics</code></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 检查失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
