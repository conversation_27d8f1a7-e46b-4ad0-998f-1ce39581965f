<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前台SSE连接</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .test-btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 测试前台SSE连接</h1>
        <p>这个页面专门测试前台实时消息接收功能。</p>
        
        <div class="test-panel">
            <h2>📊 连接状态</h2>
            <div class="grid">
                <div>
                    <h4>前台SSE连接</h4>
                    <div>状态: <span id="sse-status">未连接</span></div>
                    <button class="test-btn" onclick="startSSE()">启动SSE连接</button>
                    <button class="test-btn" onclick="stopSSE()">停止SSE连接</button>
                    <div id="sse-log" class="log"></div>
                </div>
                <div>
                    <h4>测试消息</h4>
                    <div>
                        <label>会话ID: <input type="text" id="session-id" value="session_test_" placeholder="会话ID"></label><br>
                        <label>消息内容: <input type="text" id="message-content" value="测试消息" placeholder="消息内容"></label><br>
                        <button class="test-btn" onclick="sendTestMessage()">发送测试消息</button>
                    </div>
                    <div id="message-log" class="log"></div>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>🧪 完整测试流程</h2>
            <ol>
                <li><button class="test-btn" onclick="step1()">步骤1: 创建会话并启动SSE</button></li>
                <li><button class="test-btn" onclick="step2()">步骤2: 发送客户消息</button></li>
                <li><button class="test-btn" onclick="step3()">步骤3: 模拟管理员回复</button></li>
                <li><button class="test-btn" onclick="step4()">步骤4: 检查前台是否收到</button></li>
            </ol>
            <div id="test-flow-log" class="log"></div>
        </div>
        
        <div class="test-panel">
            <h2>🔗 相关链接</h2>
            <a href="/" target="_blank" class="test-btn">前台首页</a>
            <a href="/test_backend_reply.html" target="_blank" class="test-btn">后台回复测试</a>
            <a href="/strongadmin/customer-service/sessions" target="_blank" class="test-btn">后台管理</a>
        </div>
    </div>

    <script>
    let eventSource = null;
    let currentSessionId = null;
    let lastMessageId = 0;
    
    // 启动SSE连接
    function startSSE() {
        const sessionId = document.getElementById('session-id').value || 'session_test_' + Date.now();
        document.getElementById('session-id').value = sessionId;
        currentSessionId = sessionId;
        
        logSSE('🔄 启动SSE连接，会话ID: ' + sessionId);
        
        if (eventSource) {
            eventSource.close();
        }
        
        const url = `/customer-service-sse-test?session_id=${sessionId}&last_message_id=${lastMessageId}`;
        logSSE('📡 连接URL: ' + url);
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function(event) {
            logSSE('✅ SSE连接已建立');
            document.getElementById('sse-status').textContent = '已连接';
            document.getElementById('sse-status').style.color = 'green';
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                logSSE('📨 收到SSE消息: ' + JSON.stringify(data, null, 2));
                
                if (data.type === 'new_reply') {
                    logSSE('🎉 收到新回复！发送者: ' + data.sender_type + ', 内容: ' + data.message);
                    lastMessageId = Math.max(lastMessageId, data.message_id);
                    
                    // 播放提示音
                    playNotificationSound();
                } else if (data.type === 'heartbeat') {
                    logSSE('💓 心跳包: ' + new Date().toLocaleTimeString());
                }
            } catch (e) {
                logSSE('❌ 解析SSE消息失败: ' + e.message);
            }
        };
        
        eventSource.onerror = function(event) {
            logSSE('❌ SSE连接错误: ' + JSON.stringify(event));
            document.getElementById('sse-status').textContent = '连接错误';
            document.getElementById('sse-status').style.color = 'red';
        };
    }
    
    // 停止SSE连接
    function stopSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            logSSE('🛑 SSE连接已关闭');
            document.getElementById('sse-status').textContent = '已断开';
            document.getElementById('sse-status').style.color = 'gray';
        }
    }
    
    // 发送测试消息
    function sendTestMessage() {
        const sessionId = document.getElementById('session-id').value;
        const message = document.getElementById('message-content').value;
        
        if (!sessionId || !message) {
            logMessage('❌ 请输入会话ID和消息内容');
            return;
        }
        
        logMessage('📤 发送消息: ' + message);
        
        fetch('/customer-service/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                session_id: sessionId,
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('✅ 消息发送成功');
                if (data.ai_reply) {
                    logMessage('🤖 AI自动回复: ' + data.ai_reply);
                }
            } else {
                logMessage('❌ 发送失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            logMessage('❌ 网络错误: ' + error.message);
        });
    }
    
    // 测试步骤
    function step1() {
        logFlow('🚀 步骤1: 创建会话并启动SSE');
        const sessionId = 'session_test_' + Date.now();
        document.getElementById('session-id').value = sessionId;
        startSSE();
    }
    
    function step2() {
        logFlow('📝 步骤2: 发送客户消息');
        document.getElementById('message-content').value = '测试客户消息 ' + new Date().toLocaleTimeString();
        sendTestMessage();
    }
    
    function step3() {
        logFlow('👨‍💼 步骤3: 请在后台管理页面手动回复消息');
        logFlow('🔗 打开: /strongadmin/customer-service/sessions');
        window.open('/strongadmin/customer-service/sessions', '_blank');
    }
    
    function step4() {
        logFlow('👀 步骤4: 检查上面的SSE日志是否收到回复消息');
        logFlow('✅ 如果收到"收到新回复"消息，说明前台SSE工作正常');
    }
    
    // 播放提示音
    function playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            logSSE('🔇 无法播放提示音: ' + e.message);
        }
    }
    
    // 日志函数
    function logSSE(message) {
        const log = document.getElementById('sse-log');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] ${message}\n`;
        log.scrollTop = log.scrollHeight;
    }
    
    function logMessage(message) {
        const log = document.getElementById('message-log');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] ${message}\n`;
        log.scrollTop = log.scrollHeight;
    }
    
    function logFlow(message) {
        const log = document.getElementById('test-flow-log');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] ${message}\n`;
        log.scrollTop = log.scrollHeight;
    }
    
    // 页面加载时自动开始
    document.addEventListener('DOMContentLoaded', function() {
        logFlow('📋 页面加载完成，准备开始测试');
        logFlow('💡 建议按顺序执行测试步骤');
    });
    </script>
</body>
</html>
