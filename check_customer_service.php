<?php
/**
 * 客服系统检查脚本
 * 运行: php check_customer_service.php
 */

echo "🔍 客服系统检查工具\n";
echo "==================\n\n";

// 检查Laravel环境
if (!file_exists('artisan')) {
    echo "❌ 错误: 请在Laravel项目根目录运行此脚本\n";
    exit(1);
}

// 启动Laravel应用
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

try {
    echo "1. 检查数据库连接...\n";
    $pdo = DB::connection()->getPdo();
    echo "✅ 数据库连接正常\n\n";

    echo "2. 检查客服相关数据表...\n";
    $tables = [
        'customer_service_sessions',
        'customer_service_messages', 
        'ai_auto_reply_rules',
        'customer_service_settings'
    ];

    foreach ($tables as $table) {
        try {
            $exists = DB::select("SHOW TABLES LIKE '$table'");
            if ($exists) {
                echo "✅ 表 $table 存在\n";
            } else {
                echo "❌ 表 $table 不存在\n";
            }
        } catch (Exception $e) {
            echo "❌ 检查表 $table 失败: " . $e->getMessage() . "\n";
        }
    }

    echo "\n3. 检查后台菜单...\n";
    try {
        $menu = DB::table('strongadmin_menu')->where('name', '在线客服')->first();
        if ($menu) {
            echo "✅ 后台菜单【在线客服】存在 (ID: {$menu->id})\n";
            
            // 检查子菜单
            $subMenus = DB::table('strongadmin_menu')->where('parent_id', $menu->id)->get();
            echo "   子菜单数量: " . $subMenus->count() . "\n";
            foreach ($subMenus as $sub) {
                echo "   - {$sub->name} ({$sub->route_url})\n";
            }
        } else {
            echo "❌ 后台菜单【在线客服】不存在\n";
        }
    } catch (Exception $e) {
        echo "❌ 检查菜单失败: " . $e->getMessage() . "\n";
    }

    echo "\n4. 检查AI规则...\n";
    try {
        $rulesCount = DB::table('ai_auto_reply_rules')->count();
        echo "✅ AI规则数量: $rulesCount\n";
        
        if ($rulesCount > 0) {
            $activeRules = DB::table('ai_auto_reply_rules')->where('is_active', 1)->count();
            echo "   活跃规则: $activeRules\n";
        }
    } catch (Exception $e) {
        echo "❌ 检查AI规则失败: " . $e->getMessage() . "\n";
    }

    echo "\n5. 检查文件是否存在...\n";
    $files = [
        'public/js/customer-service.js',
        'app/Http/Controllers/CustomerServiceController.php',
        'app/Http/Controllers/Strongadmin/CustomerServiceController.php',
        'app/Models/CustomerService/CustomerServiceSession.php',
        'routes/web.php',
        'routes/admin.php'
    ];

    foreach ($files as $file) {
        if (file_exists($file)) {
            echo "✅ 文件存在: $file\n";
        } else {
            echo "❌ 文件缺失: $file\n";
        }
    }

    echo "\n6. 检查路由...\n";
    try {
        // 检查前端路由
        $routes = [
            '/customer-service/init',
            '/customer-service/send',
            '/customer-service/messages',
            '/customer-service/status'
        ];

        foreach ($routes as $route) {
            echo "   前端路由: $route\n";
        }

        echo "   后台路由: /admin/customer-service/sessions\n";
        echo "   后台路由: /admin/customer-service/ai-rules\n";
        
    } catch (Exception $e) {
        echo "❌ 检查路由失败: " . $e->getMessage() . "\n";
    }

    echo "\n7. 检查权限和配置...\n";
    
    // 检查storage权限
    if (is_writable('storage')) {
        echo "✅ storage目录可写\n";
    } else {
        echo "❌ storage目录不可写\n";
    }

    // 检查public权限
    if (is_writable('public')) {
        echo "✅ public目录可写\n";
    } else {
        echo "❌ public目录不可写\n";
    }

    echo "\n📋 修复建议:\n";
    echo "1. 如果数据表不存在，运行: php artisan migrate\n";
    echo "2. 如果后台菜单不存在，运行菜单迁移\n";
    echo "3. 如果AI规则为空，运行: php artisan db:seed --class=CustomerServiceSeeder\n";
    echo "4. 如果文件缺失，请重新上传相关文件\n";
    echo "5. 清除缓存: php artisan cache:clear\n";
    echo "6. 重新生成路由缓存: php artisan route:clear\n\n";

} catch (Exception $e) {
    echo "❌ 检查过程中出现错误: " . $e->getMessage() . "\n";
    echo "详细错误: " . $e->getTraceAsString() . "\n";
}

echo "检查完成!\n";
?>
