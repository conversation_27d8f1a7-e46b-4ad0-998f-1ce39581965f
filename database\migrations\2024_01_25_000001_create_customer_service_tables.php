<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomerServiceTables extends Migration
{
    public function up()
    {
        // 客服会话表
        Schema::create('customer_service_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->unique();
            $table->unsignedBigInteger('user_id')->nullable(); // 注册用户ID
            $table->string('visitor_name')->nullable(); // 访客姓名
            $table->string('visitor_email')->nullable(); // 访客邮箱
            $table->string('visitor_ip')->nullable(); // 访客IP
            $table->string('user_agent')->nullable(); // 浏览器信息
            $table->enum('status', ['active', 'waiting', 'closed'])->default('active');
            $table->unsignedBigInteger('assigned_admin_id')->nullable(); // 分配的客服ID
            $table->timestamp('last_activity')->nullable();
            $table->timestamps();
            
            $table->index(['session_id', 'status']);
            $table->index('assigned_admin_id');
        });

        // 客服消息表
        Schema::create('customer_service_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('session_id');
            $table->enum('sender_type', ['customer', 'admin', 'ai']); // 发送者类型
            $table->unsignedBigInteger('sender_id')->nullable(); // 发送者ID
            $table->text('message'); // 消息内容
            $table->json('attachments')->nullable(); // 附件
            $table->boolean('is_read')->default(false); // 是否已读
            $table->timestamp('read_at')->nullable(); // 阅读时间
            $table->timestamps();
            
            $table->foreign('session_id')->references('id')->on('customer_service_sessions')->onDelete('cascade');
            $table->index(['session_id', 'created_at']);
        });

        // AI自动回复规则表
        Schema::create('ai_auto_reply_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // 规则名称
            $table->json('keywords'); // 关键词数组
            $table->text('reply_message'); // 回复内容
            $table->integer('priority')->default(0); // 优先级
            $table->boolean('is_active')->default(true); // 是否启用
            $table->integer('usage_count')->default(0); // 使用次数
            $table->timestamps();
            
            $table->index(['is_active', 'priority']);
        });

        // 客服设置表
        Schema::create('customer_service_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('customer_service_messages');
        Schema::dropIfExists('customer_service_sessions');
        Schema::dropIfExists('ai_auto_reply_rules');
        Schema::dropIfExists('customer_service_settings');
    }
}
