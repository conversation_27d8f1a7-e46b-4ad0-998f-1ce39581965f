<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试自动打开聊天窗口</title>
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .success { background: #f6ffed; border-color: #b7eb8f; }
        .info { background: #e6f7ff; border-color: #91d5ff; }
        .warning { background: #fffbe6; border-color: #ffe58f; }
        button { padding: 10px 20px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #40a9ff; }
        .danger-btn { background: #ff4d4f; }
        .danger-btn:hover { background: #ff7875; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .step { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 测试自动打开聊天窗口</h1>
        
        <div class="test-card info">
            <h3>📋 测试说明</h3>
            <p>这个页面用来测试当客服发送消息时，客户端聊天窗口是否会自动打开。</p>
            <ul>
                <li><strong>功能:</strong> 客服发消息 → 客户聊天窗口自动打开</li>
                <li><strong>触发条件:</strong> 客户聊天窗口处于关闭状态</li>
                <li><strong>预期效果:</strong> 自动打开 + 弹跳动画 + 桌面通知</li>
            </ul>
        </div>
        
        <div class="test-card">
            <h3>🎯 测试步骤</h3>
            <div class="step">
                <strong>步骤1:</strong> 
                <button onclick="createTestSession()">创建测试会话</button>
                <span id="step1-status">等待执行</span>
            </div>
            
            <div class="step">
                <strong>步骤2:</strong> 
                <button onclick="openHomePage()">打开首页（新标签页）</button>
                <span>在新标签页中打开客服，然后关闭聊天窗口</span>
            </div>
            
            <div class="step">
                <strong>步骤3:</strong> 
                <button onclick="sendTestMessage()">发送测试消息</button>
                <span id="step3-status">等待执行</span>
            </div>
            
            <div class="step">
                <strong>步骤4:</strong> 
                <span>观察首页标签页，聊天窗口应该自动打开</span>
            </div>
        </div>
        
        <div class="test-card">
            <h3>📊 当前会话状态</h3>
            <button onclick="refreshSessions()">刷新会话列表</button>
            <div id="sessions-list">加载中...</div>
        </div>
        
        <div class="test-card">
            <h3>📝 操作日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log"></div>
        </div>
        
        <div class="test-card warning">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>确保浏览器允许桌面通知</li>
                <li>测试时保持首页标签页可见</li>
                <li>如果没有自动打开，检查控制台错误</li>
                <li>测试完成后可以清理测试数据</li>
            </ul>
        </div>
    </div>

    <script>
        let testSessionId = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logDiv.innerHTML += `<div style="color: ${color};">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${time}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function createTestSession() {
            try {
                log('🔄 创建测试会话...');
                
                const response = await fetch('/customer-service/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        visitor_name: '自动打开测试用户',
                        visitor_email: '<EMAIL>'
                    })
                });

                const data = await response.json();
                if (data.success) {
                    testSessionId = data.session_id;
                    log(`✅ 测试会话创建成功: ${testSessionId}`, 'success');
                    document.getElementById('step1-status').textContent = '✅ 完成';
                    document.getElementById('step1-status').style.color = 'green';
                } else {
                    log(`❌ 创建会话失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`💥 创建会话异常: ${error.message}`, 'error');
            }
        }

        function openHomePage() {
            window.open('/', '_blank');
            log('🌐 已在新标签页打开首页');
        }

        async function sendTestMessage() {
            if (!testSessionId) {
                log('❌ 请先创建测试会话', 'error');
                return;
            }

            try {
                log('📤 发送测试消息...');
                
                // 首先获取会话ID
                const sessionsResponse = await fetch('/strongadmin/customer-service/sessions');
                const sessionsData = await sessionsResponse.json();

                let sessionDbId = null;
                if (sessionsData.success && sessionsData.sessions) {
                    const session = sessionsData.sessions.find(s => s.session_id === testSessionId);
                    if (session) {
                        sessionDbId = session.id;
                    }
                }

                if (!sessionDbId) {
                    log('❌ 找不到对应的会话记录', 'error');
                    return;
                }

                // 模拟客服发送消息
                const response = await fetch(`/strongadmin/customer-service/session/${sessionDbId}/reply`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: '🔔 这是一条测试消息，用于测试自动打开聊天窗口功能！'
                    })
                });

                const data = await response.json();
                if (data.success) {
                    log('✅ 测试消息发送成功', 'success');
                    log('👀 请观察首页标签页，聊天窗口应该自动打开');
                    document.getElementById('step3-status').textContent = '✅ 完成';
                    document.getElementById('step3-status').style.color = 'green';
                } else {
                    log(`❌ 发送消息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`💥 发送消息异常: ${error.message}`, 'error');
            }
        }

        async function refreshSessions() {
            try {
                const response = await fetch('/strongadmin/customer-service/sessions-api');
                const data = await response.json();
                
                const listDiv = document.getElementById('sessions-list');
                if (data.success && data.sessions.length > 0) {
                    let html = '<table border="1" style="width:100%;border-collapse:collapse;margin-top:10px;">';
                    html += '<tr style="background:#f8f9fa;"><th style="padding:8px;">ID</th><th style="padding:8px;">访客</th><th style="padding:8px;">状态</th><th style="padding:8px;">最后活动</th></tr>';
                    
                    data.sessions.slice(0, 5).forEach(session => {
                        const statusIcon = session.status === 'online' ? '🟢' : '⚫';
                        html += `<tr>
                            <td style="padding:8px;">${session.id}</td>
                            <td style="padding:8px;">${session.visitor_name}</td>
                            <td style="padding:8px;">${statusIcon} ${session.status}</td>
                            <td style="padding:8px;">${session.last_activity || '无'}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    listDiv.innerHTML = html;
                } else {
                    listDiv.innerHTML = '<p>暂无会话记录</p>';
                }
            } catch (error) {
                document.getElementById('sessions-list').innerHTML = '<p style="color:red;">获取会话列表失败</p>';
            }
        }

        // 页面加载时刷新会话列表
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            refreshSessions();
        });
    </script>
</body>
</html>
