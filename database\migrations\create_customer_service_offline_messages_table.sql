-- 客服系统离线留言表
CREATE TABLE IF NOT EXISTS `st_customer_service_offline_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL COMMENT '会话ID',
  `visitor_name` varchar(100) DEFAULT NULL COMMENT '访客姓名',
  `visitor_email` varchar(200) DEFAULT NULL COMMENT '访客邮箱',
  `visitor_phone` varchar(50) DEFAULT NULL COMMENT '访客电话',
  `visitor_whatsapp` varchar(50) DEFAULT NULL COMMENT '访客WhatsApp',
  `visitor_ip` varchar(45) DEFAULT NULL COMMENT '访客IP',
  `message` text NOT NULL COMMENT '留言内容',
  `status` enum('pending','replied','closed') NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待处理，replied=已回复，closed=已关闭',
  `admin_reply` text DEFAULT NULL COMMENT '管理员回复',
  `replied_at` timestamp NULL DEFAULT NULL COMMENT '回复时间',
  `replied_by` int(11) DEFAULT NULL COMMENT '回复人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服系统离线留言表';
