<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>清理缓存</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🧹 清理Laravel缓存</h1>";

try {
    // 清理视图缓存
    $viewCachePath = __DIR__ . '/../storage/framework/views';
    if (is_dir($viewCachePath)) {
        $files = glob($viewCachePath . '/*');
        $deletedViews = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $deletedViews++;
            }
        }
        echo "<div class='success'>✅ 清理视图缓存: 删除了 {$deletedViews} 个文件</div>";
    } else {
        echo "<div class='info'>ℹ️ 视图缓存目录不存在</div>";
    }

    echo "<div class='success'>";
    echo "<h3>🎉 缓存清理完成！</h3>";
    echo "<p>现在可以重新测试客服系统页面了。</p>";
    echo "<ul>";
    echo "<li><a href='/strongadmin/customer-service/settings' target='_blank'>设置中心</a></li>";
    echo "<li><a href='/strongadmin/customer-service/offline-messages' target='_blank'>离线留言</a></li>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>会话管理</a></li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>❌ 清理缓存失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
