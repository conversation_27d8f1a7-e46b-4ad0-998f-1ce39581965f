<?php
/**
 * 添加会话表的新字段：内部备注和地理位置信息
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>添加会话表新字段</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 添加会话表新字段</h1>";

try {
    echo "<div class='info'>📋 开始添加新字段...</div>";
    
    // 检查并添加internal_notes字段
    $hasInternalNotes = DB::select("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'internal_notes'");
    if (empty($hasInternalNotes)) {
        DB::statement("
            ALTER TABLE st_customer_service_sessions 
            ADD COLUMN internal_notes text NULL 
            COMMENT '管理员内部备注（仅管理员可见）'
        ");
        echo "<div class='success'>✅ 成功添加internal_notes字段</div>";
    } else {
        echo "<div class='info'>ℹ️ internal_notes字段已存在</div>";
    }
    
    // 检查并添加last_seen字段
    $hasLastSeen = DB::select("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'last_seen'");
    if (empty($hasLastSeen)) {
        DB::statement("
            ALTER TABLE st_customer_service_sessions 
            ADD COLUMN last_seen timestamp NULL DEFAULT NULL 
            COMMENT '客户最后在线时间'
        ");
        echo "<div class='success'>✅ 成功添加last_seen字段</div>";
    } else {
        echo "<div class='info'>ℹ️ last_seen字段已存在</div>";
    }
    
    // 检查并添加geo_country字段
    $hasGeoCountry = DB::select("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'geo_country'");
    if (empty($hasGeoCountry)) {
        DB::statement("
            ALTER TABLE st_customer_service_sessions 
            ADD COLUMN geo_country varchar(100) NULL 
            COMMENT '客户所在国家'
        ");
        echo "<div class='success'>✅ 成功添加geo_country字段</div>";
    } else {
        echo "<div class='info'>ℹ️ geo_country字段已存在</div>";
    }
    
    // 检查并添加geo_region字段
    $hasGeoRegion = DB::select("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'geo_region'");
    if (empty($hasGeoRegion)) {
        DB::statement("
            ALTER TABLE st_customer_service_sessions 
            ADD COLUMN geo_region varchar(100) NULL 
            COMMENT '客户所在省/州'
        ");
        echo "<div class='success'>✅ 成功添加geo_region字段</div>";
    } else {
        echo "<div class='info'>ℹ️ geo_region字段已存在</div>";
    }
    
    // 检查并添加geo_city字段
    $hasGeoCity = DB::select("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'geo_city'");
    if (empty($hasGeoCity)) {
        DB::statement("
            ALTER TABLE st_customer_service_sessions 
            ADD COLUMN geo_city varchar(100) NULL 
            COMMENT '客户所在城市'
        ");
        echo "<div class='success'>✅ 成功添加geo_city字段</div>";
    } else {
        echo "<div class='info'>ℹ️ geo_city字段已存在</div>";
    }
    
    // 修改status字段的枚举值
    echo "<div class='info'>🔄 更新status字段枚举值...</div>";
    DB::statement("
        ALTER TABLE st_customer_service_sessions
        MODIFY COLUMN status enum('online','offline')
        NOT NULL DEFAULT 'offline'
        COMMENT '会话状态：online=在线，offline=离线'
    ");
    echo "<div class='success'>✅ 成功更新status字段</div>";
    
    echo "<div class='info'>📊 查看更新后的表结构...</div>";
    
    // 显示表结构
    $columns = DB::select("DESCRIBE st_customer_service_sessions");
    echo "<pre>";
    echo "字段名\t\t\t类型\t\t\t空值\t键\t默认值\t\t备注\n";
    echo str_repeat("-", 100) . "\n";
    foreach ($columns as $column) {
        printf("%-20s %-25s %-8s %-8s %-15s %s\n", 
            $column->Field, 
            $column->Type, 
            $column->Null, 
            $column->Key, 
            $column->Default ?? 'NULL',
            $column->Extra
        );
    }
    echo "</pre>";
    
    echo "<div class='success'>🎉 所有字段添加完成！</div>";
    
    echo "<h2>📋 新增功能说明</h2>";
    echo "<ul>";
    echo "<li><strong>internal_notes</strong> - 管理员内部备注，仅管理员可见</li>";
    echo "<li><strong>last_seen</strong> - 客户最后在线时间，用于判断在线状态</li>";
    echo "<li><strong>geo_country/geo_region/geo_city</strong> - 地理位置信息</li>";
    echo "<li><strong>status</strong> - 更新为：online(在线)/offline(离线)/waiting(等待)/closed(关闭)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
    echo "<div class='error'>错误详情: " . $e->getTraceAsString() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
