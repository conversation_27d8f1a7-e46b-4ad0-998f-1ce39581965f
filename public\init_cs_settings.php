<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>初始化客服设置</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.btn{background:#007bff;color:white;border:none;padding:10px 20px;border-radius:4px;cursor:pointer;margin:5px;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 初始化客服系统设置</h1>";

try {
    // 数据库连接
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'mostxx_com';
    $username = 'mostxx_com';
    $password = 'fHnrmH9w5nw1pd53';

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 检查是否需要初始化
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_settings");
    $result = $stmt->fetch();
    $count = $result['count'];
    
    if ($count > 0) {
        echo "<div class='info'>ℹ️ 设置表已有 {$count} 条记录，将更新关键设置</div>";
        
        // 更新关键设置
        $updates = [
            ['system_enabled', '1'],
            ['auto_open_enabled', '1'],
            ['sound_enabled', '1'],
            ['show_avatar', '1'],
            ['offline_form_enabled', '1']
        ];
        
        foreach ($updates as $update) {
            $stmt = $pdo->prepare("UPDATE customer_service_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$update[1], $update[0]]);
            echo "<div class='success'>✅ 更新设置: {$update[0]} = {$update[1]}</div>";
        }
        
    } else {
        echo "<div class='info'>ℹ️ 设置表为空，开始初始化...</div>";
        
        // 初始化设置数据
        $settings = [
            // 基础功能
            ['basic', 'system_enabled', '系统启用', '1', 'boolean', '启用客服系统'],
            ['basic', 'welcome_message', '欢迎消息', '您好！欢迎咨询，我们将竭诚为您服务！', 'text', '客服系统欢迎消息'],
            ['basic', 'offline_message', '离线消息', '客服暂时离线，请留下您的联系方式，我们会尽快回复您！', 'text', '客服离线时显示的消息'],
            ['basic', 'auto_open_enabled', '自动打开', '1', 'boolean', '是否自动打开聊天窗口'],
            
            // 声音提醒
            ['sound', 'sound_enabled', '声音提醒', '1', 'boolean', '启用新消息声音提醒'],
            ['sound', 'sound_type', '提醒音类型', 'default', 'select', '新消息提醒音类型', 'default:默认,ding:叮咚,bell:铃声'],
            ['sound', 'sound_file', '自定义音效', '', 'file', '上传自定义提醒音效文件'],
            ['sound', 'sound_volume', '音量大小', '0.5', 'number', '提醒音音量(0-1)'],
            
            // 外观样式
            ['appearance', 'chat_position', '聊天位置', 'bottom-right', 'select', '聊天窗口显示位置', 'bottom-right:右下角,bottom-left:左下角'],
            ['appearance', 'chat_theme_color', '主题颜色', '#667eea', 'color', '聊天窗口主题颜色'],
            ['appearance', 'chat_button_color', '按钮颜色', '#667eea', 'color', '聊天按钮颜色'],
            ['appearance', 'chat_font_size', '字体大小', '14', 'number', '聊天字体大小(px)'],
            ['appearance', 'chat_window_width', '窗口宽度', '380', 'number', '聊天窗口宽度(px)'],
            ['appearance', 'chat_window_height', '窗口高度', '520', 'number', '聊天窗口高度(px)'],
            ['appearance', 'show_avatar', '显示头像', '1', 'boolean', '是否显示客服头像'],
            ['appearance', 'admin_avatar', '客服头像', '', 'file', '客服头像图片'],
            ['appearance', 'admin_name', '客服名称', '客服小助手', 'text', '客服显示名称'],
            ['appearance', 'chat_border_radius', '圆角大小', '16', 'number', '聊天窗口圆角大小(px)'],
            
            // 离线留言
            ['offline', 'offline_form_enabled', '离线表单', '1', 'boolean', '启用离线留言表单'],
            ['offline', 'offline_form_title', '表单标题', '客服离线留言', 'text', '离线留言表单标题'],
            ['offline', 'offline_form_fields', '表单字段', '["name","email","phone","whatsapp","message"]', 'json', '离线表单显示字段'],
            ['offline', 'offline_form_required', '必填字段', '["message"]', 'json', '离线表单必填字段'],
            ['offline', 'offline_auto_reply', '自动回复', '感谢您的留言，我们会尽快回复您！', 'text', '离线留言自动回复内容']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO customer_service_settings 
            (category, setting_key, title, setting_value, setting_type, description, options, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $insertCount = 0;
        foreach ($settings as $setting) {
            $options = isset($setting[6]) ? $setting[6] : null;
            $stmt->execute([
                $setting[0], // category
                $setting[1], // setting_key
                $setting[2], // title
                $setting[3], // setting_value
                $setting[4], // setting_type
                $setting[5], // description
                $options     // options
            ]);
            $insertCount++;
        }
        
        echo "<div class='success'>✅ 成功初始化 {$insertCount} 个设置项</div>";
    }
    
    // 验证设置
    echo "<h3>验证设置结果</h3>";
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM customer_service_settings WHERE setting_key IN ('system_enabled', 'auto_open_enabled', 'sound_enabled')");
    $keySettings = $stmt->fetchAll();
    
    foreach ($keySettings as $setting) {
        $status = $setting['setting_value'] === '1' ? '✅ 已启用' : '❌ 已禁用';
        echo "<div class='info'>{$setting['setting_key']}: {$status}</div>";
    }
    
    echo "<div class='success'>";
    echo "<h4>🎉 初始化完成！</h4>";
    echo "<p>现在可以测试前端客服系统了：</p>";
    echo "<ul>";
    echo "<li><a href='/' target='_blank'>访问首页查看客服按钮</a></li>";
    echo "<li><a href='/debug_frontend_cs.php' target='_blank'>运行前端调试</a></li>";
    echo "<li><a href='/strongadmin/customer-service/settings' target='_blank'>访问设置中心</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
