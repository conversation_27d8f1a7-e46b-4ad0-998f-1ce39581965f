<?php
/**
 * 直接导入客服系统数据库
 */

echo "🗄️ 导入客服系统数据库...\n";

// 启动Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';

try {
    echo "1. 连接数据库...\n";
    $pdo = DB::connection()->getPdo();
    echo "✅ 数据库连接成功\n";

    echo "2. 创建客服会话表...\n";
    DB::statement("
        CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `session_id` varchar(255) NOT NULL,
            `user_id` bigint(20) unsigned DEFAULT NULL,
            `visitor_name` varchar(255) DEFAULT NULL,
            `visitor_email` varchar(255) DEFAULT NULL,
            `visitor_ip` varchar(255) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active',
            `assigned_admin_id` bigint(20) unsigned DEFAULT NULL,
            `last_activity` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `session_id` (`session_id`),
            KEY `assigned_admin_id` (`assigned_admin_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ 客服会话表创建成功\n";

    echo "3. 创建客服消息表...\n";
    DB::statement("
        CREATE TABLE IF NOT EXISTS `customer_service_messages` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `session_id` bigint(20) unsigned NOT NULL,
            `sender_type` enum('customer','admin','ai') NOT NULL,
            `sender_id` bigint(20) unsigned DEFAULT NULL,
            `message` text NOT NULL,
            `attachments` json DEFAULT NULL,
            `is_read` tinyint(1) NOT NULL DEFAULT 0,
            `read_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `session_id` (`session_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ 客服消息表创建成功\n";

    echo "4. 创建AI规则表...\n";
    DB::statement("
        CREATE TABLE IF NOT EXISTS `ai_auto_reply_rules` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `keywords` json NOT NULL,
            `reply_message` text NOT NULL,
            `priority` int(11) NOT NULL DEFAULT 0,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `usage_count` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `is_active_priority` (`is_active`,`priority`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    echo "✅ AI规则表创建成功\n";

    echo "5. 添加默认AI规则...\n";
    $rules = [
        [
            'name' => '问候语',
            'keywords' => '["hello", "hi", "hey", "你好", "您好"]',
            'reply_message' => 'Hello! 👋 Welcome to our store! How can I help you today?',
            'priority' => 10,
            'is_active' => 1,
            'usage_count' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ],
        [
            'name' => '物流咨询',
            'keywords' => '["shipping", "delivery", "物流", "快递", "发货"]',
            'reply_message' => 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You will receive a tracking number once your order ships.',
            'priority' => 9,
            'is_active' => 1,
            'usage_count' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ],
        [
            'name' => '退换货',
            'keywords' => '["return", "refund", "退货", "退款", "换货"]',
            'reply_message' => 'We have a 30-day return policy! 🔄 If you are not satisfied, you can return items within 30 days for a full refund.',
            'priority' => 8,
            'is_active' => 1,
            'usage_count' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]
    ];

    foreach ($rules as $rule) {
        DB::table('ai_auto_reply_rules')->insertOrIgnore($rule);
    }
    echo "✅ AI规则添加成功\n";

    echo "6. 添加后台菜单...\n";
    
    // 检查菜单是否存在
    $existingMenu = DB::table('strongadmin_menu')->where('name', '在线客服')->where('level', 1)->first();
    
    if (!$existingMenu) {
        // 添加主菜单
        $parentId = DB::table('strongadmin_menu')->insertGetId([
            'level' => 1,
            'parent_id' => 0,
            'name' => '在线客服',
            'route_url' => '',
            'permissions' => '',
            'status' => 1,
            'sort' => 90,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // 添加子菜单
        $subMenus = [
            ['name' => '会话管理', 'route_url' => 'customer-service/sessions', 'sort' => 1],
            ['name' => 'AI规则管理', 'route_url' => 'customer-service/ai-rules', 'sort' => 2],
            ['name' => '统计报表', 'route_url' => 'customer-service/statistics', 'sort' => 3]
        ];

        foreach ($subMenus as $menu) {
            DB::table('strongadmin_menu')->insert([
                'level' => 2,
                'parent_id' => $parentId,
                'name' => $menu['name'],
                'route_url' => $menu['route_url'],
                'permissions' => '',
                'status' => 1,
                'sort' => $menu['sort'],
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
        
        echo "✅ 后台菜单添加成功\n";
    } else {
        echo "✅ 后台菜单已存在\n";
    }

    echo "7. 清除缓存...\n";
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    echo "✅ 缓存清除成功\n";

    echo "8. 创建图片目录...\n";
    if (!is_dir('public/images')) {
        mkdir('public/images', 0755, true);
    }
    echo "✅ 图片目录创建成功\n";

    echo "\n🎉 数据库导入完成！\n\n";
    
    echo "📋 检查结果:\n";
    
    // 检查表
    $tables = ['customer_service_sessions', 'customer_service_messages', 'ai_auto_reply_rules'];
    foreach ($tables as $table) {
        $exists = DB::select("SHOW TABLES LIKE '$table'");
        echo $exists ? "✅ 表 $table 存在\n" : "❌ 表 $table 不存在\n";
    }

    // 检查菜单
    $menu = DB::table('strongadmin_menu')->where('name', '在线客服')->first();
    echo $menu ? "✅ 后台菜单存在\n" : "❌ 后台菜单不存在\n";

    // 检查AI规则
    $rulesCount = DB::table('ai_auto_reply_rules')->count();
    echo "✅ AI规则数量: $rulesCount\n";

    echo "\n🚀 现在可以:\n";
    echo "1. 访问网站首页，查看右下角客服按钮\n";
    echo "2. 登录后台，查看【在线客服】菜单\n";
    echo "3. 测试客服对话功能\n\n";

} catch (Exception $e) {
    echo "❌ 导入失败: " . $e->getMessage() . "\n";
    echo "详细错误: " . $e->getTraceAsString() . "\n";
}

echo "导入脚本执行完成!\n";
?>
