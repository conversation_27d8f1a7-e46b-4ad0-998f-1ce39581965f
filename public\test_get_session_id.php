<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

header('Content-Type: application/json');

try {
    $id = $_GET['id'] ?? null;
    $sessionId = $_GET['session_id'] ?? null;

    if (!$id && !$sessionId) {
        echo json_encode(['success' => false, 'error' => '缺少ID或session_id参数']);
        exit;
    }

    if ($id) {
        // 按数据库ID查询
        $session = DB::table('st_customer_service_sessions')
                     ->where('id', $id)
                     ->first();
    } else {
        // 按session_id查询
        $session = DB::table('st_customer_service_sessions')
                     ->where('session_id', $sessionId)
                     ->first();
    }

    if (!$session) {
        echo json_encode(['success' => false, 'error' => '找不到会话']);
        exit;
    }

    echo json_encode([
        'success' => true,
        'id' => $session->id,
        'session_id' => $session->session_id,
        'visitor_name' => $session->visitor_name,
        'status' => $session->status
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
