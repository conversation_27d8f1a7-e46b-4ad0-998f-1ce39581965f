<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .success { 
            background: #d4edda; 
            border-color: #c3e6cb; 
            color: #155724; 
        }
        .error { 
            background: #f8d7da; 
            border-color: #f5c6cb; 
            color: #721c24; 
        }
        .warning { 
            background: #fff3cd; 
            border-color: #ffeaa7; 
            color: #856404; 
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .test-btn:hover { 
            background: #0056b3; 
            transform: translateY(-2px);
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服系统最终修复测试</h1>
        <p>测试消息重复问题是否彻底解决</p>
        
        <div class="status-card">
            <h3>🎯 修复目标</h3>
            <ul>
                <li>✅ 消息不再重复显示（从11条变回1条）</li>
                <li>✅ 只有一个轮询实例在运行</li>
                <li>✅ 样式正确应用（黑色主题）</li>
                <li>✅ 前端客服按钮正常显示</li>
            </ul>
        </div>
        
        <div class="status-card" id="status-display">
            <h3>📊 当前状态</h3>
            <div id="status-content">准备测试...</div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="test-btn" onclick="testPollingStatus()">检查轮询状态</button>
            <button class="test-btn" onclick="testAPI()">测试API</button>
            <button class="test-btn" onclick="simulateMessage()">模拟消息</button>
            <button class="test-btn danger" onclick="resetSystem()">重置系统</button>
        </div>
        
        <div class="status-card">
            <h3>🔍 检查清单</h3>
            <div id="checklist">
                <div>⏳ 正在检查...</div>
            </div>
        </div>
        
        <div class="status-card">
            <h3>📋 测试说明</h3>
            <ol>
                <li>打开浏览器开发者工具（F12）</li>
                <li>切换到Console标签页</li>
                <li>访问主页面：<a href="/" target="_blank">http://www.strongshop.local/</a></li>
                <li>在后台发送一条测试消息</li>
                <li>观察前端是否只显示1条消息（不是11条）</li>
            </ol>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusContent = document.getElementById('status-content');
            const statusDisplay = document.getElementById('status-display');
            
            // 移除所有状态类
            statusDisplay.classList.remove('success', 'error', 'warning');
            
            // 添加新状态类
            if (type !== 'info') {
                statusDisplay.classList.add(type);
            }
            
            statusContent.innerHTML = message;
        }

        function updateChecklist(items) {
            const checklist = document.getElementById('checklist');
            checklist.innerHTML = items.map(item => `<div>${item}</div>`).join('');
        }

        async function testPollingStatus() {
            updateStatus('🔍 检查轮询状态...', 'info');
            
            const checks = [];
            
            // 检查全局变量
            if (typeof window.customerServicePolling !== 'undefined') {
                checks.push(`✅ 轮询状态变量存在: ${window.customerServicePolling}`);
            } else {
                checks.push('❌ 轮询状态变量不存在');
            }
            
            if (typeof window.customerServicePollingInterval !== 'undefined') {
                checks.push(`✅ 轮询间隔ID存在: ${window.customerServicePollingInterval}`);
            } else {
                checks.push('❌ 轮询间隔ID不存在');
            }
            
            updateChecklist(checks);
            updateStatus('轮询状态检查完成', 'success');
        }

        async function testAPI() {
            updateStatus('🔍 测试API连接...', 'info');
            
            try {
                // 测试设置API
                const settingsResponse = await fetch('/api/test-settings.php');
                const settingsData = await settingsResponse.json();
                
                // 测试状态API
                const statusResponse = await fetch('/api/customer-service/status');
                const statusData = await statusResponse.json();
                
                const checks = [
                    settingsData.success ? '✅ 设置API正常' : '❌ 设置API异常',
                    statusData.success ? '✅ 状态API正常' : '❌ 状态API异常',
                    settingsData.data?.system_enabled ? '✅ 系统已启用' : '❌ 系统未启用',
                    statusData.enabled ? '✅ 服务已启用' : '❌ 服务未启用'
                ];
                
                updateChecklist(checks);
                updateStatus('API测试完成', 'success');
                
            } catch (error) {
                updateStatus('API测试失败: ' + error.message, 'error');
                updateChecklist(['❌ API连接失败']);
            }
        }

        function simulateMessage() {
            updateStatus('🎭 模拟消息测试...', 'info');
            
            const checks = [
                '📝 这是一个模拟测试',
                '🔍 请在主页面测试真实消息',
                '⚠️ 观察是否只显示1条消息',
                '📊 检查浏览器控制台日志'
            ];
            
            updateChecklist(checks);
            updateStatus('请在主页面进行真实测试', 'warning');
        }

        function resetSystem() {
            updateStatus('🔄 重置系统状态...', 'warning');
            
            // 清理全局变量
            if (window.customerServicePollingInterval) {
                clearInterval(window.customerServicePollingInterval);
                delete window.customerServicePollingInterval;
            }
            
            if (typeof window.customerServicePolling !== 'undefined') {
                window.customerServicePolling = false;
            }
            
            const checks = [
                '🧹 已清理轮询间隔',
                '🔄 已重置轮询状态',
                '✅ 系统已重置',
                '📝 可以重新测试'
            ];
            
            updateChecklist(checks);
            updateStatus('系统已重置，可以重新测试', 'success');
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testPollingStatus();
                testAPI();
            }, 1000);
        });
    </script>
</body>
</html>
