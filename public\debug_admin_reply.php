<?php
/**
 * 调试后台回复功能
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>调试后台回复功能</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 调试后台回复功能</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 处理测试回复
    if ($_POST['action'] ?? '' === 'test_reply') {
        $sessionId = $_POST['session_id'] ?? '';
        $message = $_POST['message'] ?? '';
        
        if ($sessionId && $message) {
            try {
                // 插入管理员回复消息
                $stmt = $pdo->prepare("
                    INSERT INTO st_customer_service_messages 
                    (session_id, sender_type, sender_id, message, is_read, created_at, updated_at) 
                    VALUES (?, 'admin', 1, ?, 0, NOW(), NOW())
                ");
                $stmt->execute([$sessionId, $message]);
                
                $messageId = $pdo->lastInsertId();
                echo "<p class='success'>✅ 测试回复发送成功！消息ID: $messageId</p>";
                
                // 更新会话活动时间
                $stmt = $pdo->prepare("
                    UPDATE st_customer_service_sessions 
                    SET last_activity = NOW(), updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$sessionId]);
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ 发送失败: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // 获取会话列表
    echo "<h2>📋 会话列表</h2>";
    $sessions = $pdo->query("
        SELECT s.*, 
               (SELECT COUNT(*) FROM st_customer_service_messages m WHERE m.session_id = s.id) as message_count,
               (SELECT m.message FROM st_customer_service_messages m WHERE m.session_id = s.id ORDER BY m.created_at DESC LIMIT 1) as latest_message
        FROM st_customer_service_sessions s 
        ORDER BY s.created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    if (empty($sessions)) {
        echo "<p class='info'>ℹ️ 暂无会话数据</p>";
    } else {
        echo "<table><tr><th>会话ID</th><th>访客名称</th><th>消息数</th><th>最新消息</th><th>状态</th><th>操作</th></tr>";
        foreach ($sessions as $session) {
            $visitorName = $session['visitor_name'] ?: '访客-' . strtoupper(substr(md5($session['session_id']), 0, 6));
            $latestMessage = $session['latest_message'] ? substr($session['latest_message'], 0, 30) . '...' : '无消息';
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>$visitorName</td>";
            echo "<td>{$session['message_count']}</td>";
            echo "<td>$latestMessage</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td><button class='test-btn' onclick=\"testReply({$session['id']})\">测试回复</button></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 获取最新消息
    echo "<h2>📨 最新消息记录</h2>";
    $messages = $pdo->query("
        SELECT m.*, s.session_id as session_key 
        FROM st_customer_service_messages m 
        LEFT JOIN st_customer_service_sessions s ON m.session_id = s.id 
        ORDER BY m.created_at DESC 
        LIMIT 20
    ")->fetchAll();
    
    if (empty($messages)) {
        echo "<p class='info'>ℹ️ 暂无消息数据</p>";
    } else {
        echo "<table><tr><th>消息ID</th><th>会话</th><th>发送者</th><th>消息内容</th><th>创建时间</th></tr>";
        foreach ($messages as $msg) {
            $shortMessage = substr($msg['message'], 0, 50) . (strlen($msg['message']) > 50 ? '...' : '');
            $senderClass = $msg['sender_type'] === 'admin' ? 'success' : ($msg['sender_type'] === 'ai' ? 'info' : '');
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['session_key']}</td>";
            echo "<td class='$senderClass'>{$msg['sender_type']}</td>";
            echo "<td>" . htmlspecialchars($shortMessage) . "</td>";
            echo "<td>{$msg['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>🧪 测试功能</h2>";
    echo "<form method='POST' style='margin:20px 0;'>";
    echo "<input type='hidden' name='action' value='test_reply'>";
    echo "<label>会话ID: <input type='number' name='session_id' required></label><br><br>";
    echo "<label>回复消息: <input type='text' name='message' placeholder='输入测试回复消息' required style='width:300px;'></label><br><br>";
    echo "<button type='submit' class='test-btn'>发送测试回复</button>";
    echo "</form>";
    
    echo "<h2>🔗 相关链接</h2>";
    echo "<ul>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
    echo "<li><a href='/test_cs.php' target='_blank'>发送客户消息</a></li>";
    echo "<li><a href='/test_realtime_sync.html' target='_blank'>测试实时同步</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<script>
function testReply(sessionId) {
    const message = '测试管理员回复 ' + new Date().toLocaleTimeString();
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type='hidden' name='action' value='test_reply'>
        <input type='hidden' name='session_id' value='${sessionId}'>
        <input type='hidden' name='message' value='${message}'>
    `;
    document.body.appendChild(form);
    form.submit();
}
</script>";

echo "</body></html>";
?>
