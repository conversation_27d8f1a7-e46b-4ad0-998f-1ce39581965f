<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            font-size: 1.1em;
            margin: 0;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }
        .feature-card h3 {
            color: #333;
            margin: 0 0 15px 0;
            font-size: 1.3em;
        }
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin: 0 0 15px 0;
        }
        .feature-card .status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status.completed {
            background: #d4edda;
            color: #155724;
        }
        .status.testing {
            background: #fff3cd;
            color: #856404;
        }
        .test-buttons {
            text-align: center;
            margin-top: 30px;
        }
        .test-btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            transition: all 0.3s;
        }
        .test-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .test-btn.secondary {
            background: #6c757d;
        }
        .test-btn.secondary:hover {
            background: #5a6268;
        }
        .api-test {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        .api-test h3 {
            color: #1976d2;
            margin: 0 0 15px 0;
        }
        .api-result {
            background: #f5f5f5;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 客服系统功能测试</h1>
            <p>测试所有新增的客服系统功能</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>🎵 消息提示音</h3>
                <p>支持默认音效和自定义MP3文件上传，可调节音量大小</p>
                <span class="status completed">✅ 已完成</span>
            </div>

            <div class="feature-card">
                <h3>🎨 外观自定义</h3>
                <p>主题颜色、字体大小、窗口尺寸、位置等完全可控</p>
                <span class="status completed">✅ 已完成</span>
            </div>

            <div class="feature-card">
                <h3>📧 离线留言</h3>
                <p>客服离线时收集访客联系方式和留言，支持邮件回复</p>
                <span class="status completed">✅ 已完成</span>
            </div>

            <div class="feature-card">
                <h3>⚙️ 设置中心</h3>
                <p>统一管理所有客服系统配置，分类清晰，操作简单</p>
                <span class="status completed">✅ 已完成</span>
            </div>

            <div class="feature-card">
                <h3>🖼️ 图片和表情</h3>
                <p>管理员可发送图片和表情给客户（开发中）</p>
                <span class="status testing">🔄 开发中</span>
            </div>

            <div class="feature-card">
                <h3>🔧 智能检测</h3>
                <p>自动检测客服在线状态，智能切换离线表单</p>
                <span class="status completed">✅ 已完成</span>
            </div>
        </div>

        <div class="api-test">
            <h3>📡 API测试</h3>
            <button onclick="testSettingsAPI()" class="test-btn">测试设置API</button>
            <button onclick="testOfflineMessageAPI()" class="test-btn">测试离线留言API</button>
            <div id="api-result" class="api-result" style="display: none;"></div>
        </div>

        <div class="test-buttons">
            <a href="/" class="test-btn">🏠 前台测试</a>
            <a href="/strongadmin/customer-service/settings" class="test-btn">⚙️ 设置中心</a>
            <a href="/strongadmin/customer-service/offline-messages" class="test-btn">📧 离线留言</a>
            <a href="/strongadmin/customer-service/sessions" class="test-btn secondary">💬 会话管理</a>
        </div>
    </div>

    <script>
        async function testSettingsAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试设置API...';

            try {
                const response = await fetch('/api/customer-service/get-settings.php');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>设置API测试结果:</strong><br>
                    状态: ${data.success ? '✅ 成功' : '❌ 失败'}<br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <strong>设置API测试失败:</strong><br>
                    错误: ${error.message}
                `;
            }
        }

        async function testOfflineMessageAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试离线留言API...';

            const testData = {
                session_id: 'test_session_' + Date.now(),
                visitor_name: '测试用户',
                visitor_email: '<EMAIL>',
                visitor_phone: '13800138000',
                message: '这是一条测试离线留言'
            };

            try {
                const response = await fetch('/api/customer-service/submit-offline-message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>离线留言API测试结果:</strong><br>
                    状态: ${data.success ? '✅ 成功' : '❌ 失败'}<br>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <strong>离线留言API测试失败:</strong><br>
                    错误: ${error.message}
                `;
            }
        }

        // 页面加载时显示欢迎信息
        window.addEventListener('load', function() {
            console.log('🎉 客服系统功能测试页面加载完成');
            console.log('📋 可用功能:');
            console.log('  - 消息提示音 (默认 + 自定义MP3)');
            console.log('  - 外观自定义 (颜色、尺寸、位置等)');
            console.log('  - 离线留言 (表单收集 + 邮件回复)');
            console.log('  - 设置中心 (统一配置管理)');
            console.log('  - 智能检测 (在线状态自动切换)');
        });
    </script>
</body>
</html>
