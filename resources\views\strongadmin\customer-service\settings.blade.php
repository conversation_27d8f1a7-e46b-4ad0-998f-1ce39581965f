@extends('strongadmin::layouts.app')

@push('styles')
<style>
.st-settings-tabs .layui-tab-title li {
    min-width: 120px;
    text-align: center;
}
.st-settings-form .layui-form-item {
    margin-bottom: 20px;
}
.st-settings-form .layui-form-label {
    width: 140px;
    font-weight: bold;
}
.st-settings-form .layui-input-block {
    margin-left: 160px;
}
.st-setting-desc {
    color: #999;
    font-size: 12px;
    margin-top: 5px;
}
.st-color-preview {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表单提交
    var settingsForm = document.getElementById('settings-form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();

            var formData = new FormData(this);
            var submitBtn = this.querySelector('button[type="submit"]');
            var originalText = submitBtn.innerHTML;

            // 显示加载状态
            submitBtn.innerHTML = '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 保存中...';
            submitBtn.disabled = true;

            // 获取CSRF token
            var csrfToken = document.querySelector('meta[name="csrf-token"]');
            var token = csrfToken ? csrfToken.getAttribute('content') : '';
            formData.append('_token', token);

            // 处理开关状态 - 确保未选中的开关发送0值
            var switches = this.querySelectorAll('input[type="checkbox"][lay-skin="switch"]');
            switches.forEach(function(switchInput) {
                if (!switchInput.checked) {
                    // 如果开关未选中，确保发送0值
                    var hiddenInput = switchInput.parentNode.querySelector('input[type="hidden"][name="' + switchInput.name + '"]');
                    if (hiddenInput) {
                        formData.set(switchInput.name, '0');
                    }
                }
            });

            // 调试：显示要发送的数据
            console.log('准备发送的设置数据:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }

            fetch('/strongadmin/customer-service/settings', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('服务器响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('服务器响应数据:', data);
                if(data.success) {
                    alert('设置保存成功！');
                    // 可选：刷新页面以显示最新设置
                    // location.reload();
                } else {
                    alert('保存失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                alert('网络错误，请重试');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // 标签页切换
    var tabTitles = document.querySelectorAll('.layui-tab-title li');
    var tabItems = document.querySelectorAll('.layui-tab-item');

    tabTitles.forEach(function(tab, index) {
        tab.addEventListener('click', function() {
            // 移除所有活动状态
            tabTitles.forEach(function(t) { t.classList.remove('layui-this'); });
            tabItems.forEach(function(item) {
                item.classList.remove('layui-show');
            });

            // 添加当前活动状态
            this.classList.add('layui-this');
            if (tabItems[index]) {
                tabItems[index].classList.add('layui-show');
            }
        });
    });

    // 颜色选择器联动
    var colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(function(colorInput) {
        colorInput.addEventListener('change', function() {
            var colorDisplay = this.parentNode.querySelector('.color-display');
            var colorPreview = this.parentNode.querySelector('.st-color-preview');

            if (colorDisplay) {
                colorDisplay.value = this.value;
            }
            if (colorPreview) {
                colorPreview.style.backgroundColor = this.value;
            }
        });
    });

    // 开关切换
    var switches = document.querySelectorAll('input[lay-skin="switch"]');
    switches.forEach(function(switchInput) {
        switchInput.addEventListener('change', function() {
            console.log('开关状态:', this.checked);
        });
    });
});

// 播放音效
function playSound(url) {
    var player = document.getElementById('sound-player');
    if (player) {
        player.src = url;
        player.play().catch(function(e) {
            console.error('播放音效失败:', e);
            alert('播放音效失败');
        });
    }
}
</script>
@endpush

@section('content')
<div class="st-h15"></div>

<form id="settings-form" class="layui-form st-settings-form">
    <div class="layui-tab layui-tab-brief st-settings-tabs" lay-filter="settings-tabs">
        <ul class="layui-tab-title">
            @php
                $categoryNames = [
                    'basic' => '基础功能',
                    'working_hours' => '工作时间',
                    'sound' => '声音提醒',
                    'appearance' => '外观样式',
                    'offline' => '离线留言',
                    'advanced' => '高级选项'
                ];
                $categoryIcons = [
                    'basic' => 'layui-icon-set',
                    'working_hours' => 'layui-icon-time',
                    'sound' => 'layui-icon-voice',
                    'appearance' => 'layui-icon-theme',
                    'offline' => 'layui-icon-email',
                    'advanced' => 'layui-icon-slider'
                ];
            @endphp
            
            @foreach($settings as $category => $categorySettings)
                <li {{ $loop->first ? 'class=layui-this' : '' }}>
                    <i class="layui-icon {{ $categoryIcons[$category] ?? 'layui-icon-set' }}"></i>
                    {{ $categoryNames[$category] ?? ucfirst($category) }}
                </li>
            @endforeach
        </ul>
        
        <div class="layui-tab-content">
            @foreach($settings as $category => $categorySettings)
                <div class="layui-tab-item {{ $loop->first ? 'layui-show' : '' }}">
                    @foreach($categorySettings as $setting)
                        <div class="layui-form-item">
                            <label class="layui-form-label">{{ $setting->title }}</label>
                            <div class="layui-input-block">
                                @if($setting->setting_type === 'boolean')
                                    <input type="hidden" name="settings[{{ $setting->setting_key }}]" value="0">
                                    <input type="checkbox"
                                           name="settings[{{ $setting->setting_key }}]"
                                           value="1"
                                           lay-skin="switch"
                                           lay-text="启用|禁用"
                                           {{ $setting->setting_value == '1' ? 'checked' : '' }}>
                                    
                                @elseif($setting->setting_type === 'number')
                                    <input type="number" 
                                           name="settings[{{ $setting->setting_key }}]"
                                           value="{{ $setting->setting_value }}"
                                           placeholder="请输入{{ $setting->title }}"
                                           class="layui-input"
                                           step="0.1"
                                           min="0">
                                           
                                @elseif($setting->setting_type === 'color')
                                    <div class="layui-input-inline" style="width: 100px;">
                                        <input type="color" 
                                               name="settings[{{ $setting->setting_key }}]"
                                               value="{{ $setting->setting_value }}"
                                               class="layui-input">
                                    </div>
                                    <div class="layui-input-inline" style="width: 150px;">
                                        <input type="text" 
                                               value="{{ $setting->setting_value }}"
                                               class="layui-input color-display"
                                               readonly>
                                    </div>
                                    <span class="st-color-preview" style="background-color: {{ $setting->setting_value }};"></span>
                                
                                @elseif($setting->setting_type === 'select')
                                    <select name="settings[{{ $setting->setting_key }}]" lay-verify="">
                                        @if($setting->options)
                                            @foreach(explode(',', $setting->options) as $option)
                                                @php
                                                    list($value, $label) = explode(':', $option);
                                                @endphp
                                                <option value="{{ $value }}" 
                                                        {{ $setting->setting_value == $value ? 'selected' : '' }}>
                                                    {{ $label }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                           
                                @elseif($setting->setting_type === 'file')
                                    <button type="button" class="layui-btn" id="upload-{{ $setting->setting_key }}">
                                        <i class="layui-icon">&#xe67c;</i>上传文件
                                    </button>
                                    @if($setting->setting_value)
                                        <div class="st-setting-desc">
                                            当前文件: {{ basename($setting->setting_value) }}
                                            @if(strpos($setting->setting_key, 'sound') !== false)
                                                <a href="javascript:;" onclick="playSound('{{ Storage::url($setting->setting_value) }}')" class="layui-btn layui-btn-xs">试听</a>
                                            @endif
                                        </div>
                                    @endif
                                
                                @elseif($setting->setting_type === 'json')
                                    @if($setting->setting_key === 'working_days')
                                        {{-- 工作日选择器 --}}
                                        <div class="layui-form-item">
                                            @php
                                                $selectedDays = json_decode($setting->setting_value, true) ?: [];
                                                $dayNames = ['1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'];
                                            @endphp
                                            @foreach($dayNames as $dayValue => $dayName)
                                                <input type="checkbox"
                                                       name="working_days[]"
                                                       value="{{ $dayValue }}"
                                                       title="{{ $dayName }}"
                                                       {{ in_array($dayValue, $selectedDays) ? 'checked' : '' }}>
                                            @endforeach
                                        </div>
                                        <input type="hidden" name="settings[{{ $setting->setting_key }}]" id="working_days_hidden">
                                    @elseif($setting->setting_key === 'holiday_dates')
                                        {{-- 节假日日期选择器 --}}
                                        <div class="layui-input-inline" style="width: 200px;">
                                            <input type="text"
                                                   id="holiday_date_picker"
                                                   placeholder="选择节假日日期"
                                                   class="layui-input">
                                        </div>
                                        <button type="button" class="layui-btn layui-btn-sm" onclick="addHolidayDate()">添加</button>
                                        <div id="holiday_dates_list" style="margin-top: 10px;">
                                            @php
                                                $holidayDates = json_decode($setting->setting_value, true) ?: [];
                                            @endphp
                                            @foreach($holidayDates as $date)
                                                <span class="layui-badge layui-bg-gray" style="margin: 2px;">
                                                    {{ $date }}
                                                    <i class="layui-icon layui-icon-close" onclick="removeHolidayDate('{{ $date }}')" style="cursor: pointer; margin-left: 5px;"></i>
                                                </span>
                                            @endforeach
                                        </div>
                                        <input type="hidden" name="settings[{{ $setting->setting_key }}]" id="holiday_dates_hidden" value="{{ $setting->setting_value }}">
                                    @else
                                        <textarea name="settings[{{ $setting->setting_key }}]"
                                                  placeholder='例: ["email","phone","message"]'
                                                  class="layui-textarea">{{ $setting->setting_value }}</textarea>
                                    @endif

                                @elseif($setting->setting_key === 'working_start_time' || $setting->setting_key === 'working_end_time' || $setting->setting_key === 'lunch_start_time' || $setting->setting_key === 'lunch_end_time')
                                    {{-- 时间选择器 --}}
                                    <input type="time"
                                           name="settings[{{ $setting->setting_key }}]"
                                           value="{{ $setting->setting_value }}"
                                           class="layui-input"
                                           style="width: 150px;">

                                @elseif($setting->setting_key === 'working_hours_mode')
                                    {{-- 工作时间模式选择 --}}
                                    <select name="settings[{{ $setting->setting_key }}]" lay-verify="">
                                        <option value="auto" {{ $setting->setting_value == 'auto' ? 'selected' : '' }}>自动判断（根据设定时间）</option>
                                        <option value="manual" {{ $setting->setting_value == 'manual' ? 'selected' : '' }}>手动控制（管理员手动开关）</option>
                                    </select>

                                @elseif($setting->setting_key === 'working_timezone')
                                    {{-- 时区选择器 --}}
                                    <select name="settings[{{ $setting->setting_key }}]" lay-verify="">
                                        <option value="Asia/Shanghai" {{ $setting->setting_value == 'Asia/Shanghai' ? 'selected' : '' }}>中国标准时间 (UTC+8)</option>
                                        <option value="Asia/Tokyo" {{ $setting->setting_value == 'Asia/Tokyo' ? 'selected' : '' }}>日本标准时间 (UTC+9)</option>
                                        <option value="Asia/Seoul" {{ $setting->setting_value == 'Asia/Seoul' ? 'selected' : '' }}>韩国标准时间 (UTC+9)</option>
                                        <option value="Asia/Hong_Kong" {{ $setting->setting_value == 'Asia/Hong_Kong' ? 'selected' : '' }}>香港时间 (UTC+8)</option>
                                        <option value="Asia/Singapore" {{ $setting->setting_value == 'Asia/Singapore' ? 'selected' : '' }}>新加坡时间 (UTC+8)</option>
                                        <option value="America/New_York" {{ $setting->setting_value == 'America/New_York' ? 'selected' : '' }}>美国东部时间 (UTC-5)</option>
                                        <option value="America/Los_Angeles" {{ $setting->setting_value == 'America/Los_Angeles' ? 'selected' : '' }}>美国西部时间 (UTC-8)</option>
                                        <option value="Europe/London" {{ $setting->setting_value == 'Europe/London' ? 'selected' : '' }}>英国时间 (UTC+0)</option>
                                        <option value="UTC" {{ $setting->setting_value == 'UTC' ? 'selected' : '' }}>协调世界时 (UTC+0)</option>
                                    </select>

                                @else
                                    <input type="text"
                                           name="settings[{{ $setting->setting_key }}]"
                                           value="{{ $setting->setting_value }}"
                                           placeholder="请输入{{ $setting->title }}"
                                           class="layui-input">
                                @endif
                                
                                @if($setting->description)
                                    <div class="st-setting-desc">{{ $setting->description }}</div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @endforeach
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn">
                <i class="layui-icon layui-icon-ok"></i> 保存设置
            </button>
            <button type="reset" class="layui-btn layui-btn-primary">
                <i class="layui-icon layui-icon-refresh"></i> 重置
            </button>
        </div>
    </div>
</form>

<!-- 音频播放器 -->
<audio id="sound-player" style="display: none;"></audio>

@section('scripts')
<script>
layui.use(['form', 'laydate', 'layer'], function(){
    var form = layui.form;
    var laydate = layui.laydate;
    var layer = layui.layer;

    // 初始化日期选择器
    laydate.render({
        elem: '#holiday_date_picker',
        type: 'date',
        format: 'yyyy-MM-dd',
        done: function(value, date, endDate){
            // 日期选择完成后不自动添加，等用户点击添加按钮
        }
    });

    // 监听工作日复选框变化
    form.on('checkbox()', function(data){
        if(data.elem.name === 'working_days[]') {
            updateWorkingDaysHidden();
        }
    });

    // 更新工作日隐藏字段
    function updateWorkingDaysHidden() {
        var selectedDays = [];
        $('input[name="working_days[]"]:checked').each(function(){
            selectedDays.push($(this).val());
        });
        $('#working_days_hidden').val(JSON.stringify(selectedDays));
    }

    // 初始化工作日隐藏字段
    updateWorkingDaysHidden();

    // 添加节假日
    window.addHolidayDate = function() {
        var date = $('#holiday_date_picker').val();
        if(!date) {
            layer.msg('请先选择日期');
            return;
        }

        var currentDates = JSON.parse($('#holiday_dates_hidden').val() || '[]');
        if(currentDates.indexOf(date) !== -1) {
            layer.msg('该日期已存在');
            return;
        }

        currentDates.push(date);
        $('#holiday_dates_hidden').val(JSON.stringify(currentDates));

        // 添加到显示列表
        var badge = '<span class="layui-badge layui-bg-gray" style="margin: 2px;">' +
                   date +
                   '<i class="layui-icon layui-icon-close" onclick="removeHolidayDate(\'' + date + '\')" style="cursor: pointer; margin-left: 5px;"></i>' +
                   '</span>';
        $('#holiday_dates_list').append(badge);

        // 清空输入框
        $('#holiday_date_picker').val('');
        layer.msg('添加成功');
    };

    // 删除节假日
    window.removeHolidayDate = function(date) {
        var currentDates = JSON.parse($('#holiday_dates_hidden').val() || '[]');
        var index = currentDates.indexOf(date);
        if(index !== -1) {
            currentDates.splice(index, 1);
            $('#holiday_dates_hidden').val(JSON.stringify(currentDates));

            // 从显示列表中移除
            $('#holiday_dates_list .layui-badge').each(function(){
                if($(this).text().indexOf(date) !== -1) {
                    $(this).remove();
                    return false;
                }
            });

            layer.msg('删除成功');
        }
    };

    // 监听工作时间模式变化
    form.on('select()', function(data){
        if(data.elem.name === 'settings[working_hours_mode]') {
            var mode = data.value;
            if(mode === 'manual') {
                layer.msg('手动模式下，您需要在客服管理页面手动控制在线状态', {time: 3000});
            }
        }
    });

    // 表单提交前验证
    form.on('submit()', function(data){
        // 验证工作时间设置
        var startTime = $('input[name="settings[working_start_time]"]').val();
        var endTime = $('input[name="settings[working_end_time]"]').val();
        var lunchStart = $('input[name="settings[lunch_start_time]"]').val();
        var lunchEnd = $('input[name="settings[lunch_end_time]"]').val();

        if(startTime && endTime) {
            if(startTime >= endTime) {
                layer.msg('下班时间必须晚于上班时间');
                return false;
            }
        }

        if(lunchStart && lunchEnd) {
            if(lunchStart >= lunchEnd) {
                layer.msg('午休结束时间必须晚于开始时间');
                return false;
            }
        }

        return true;
    });
});

// 播放声音
function playSound(url) {
    var player = document.getElementById('sound-player');
    player.src = url;
    player.play().catch(function(error) {
        layui.layer.msg('播放失败：' + error.message);
    });
}

// 颜色选择器联动
$(document).on('input', 'input[type="color"]', function(){
    var color = $(this).val();
    $(this).siblings('.color-display').val(color);
    $(this).siblings('.st-color-preview').css('background-color', color);
});
</script>
@endsection
