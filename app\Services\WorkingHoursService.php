<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class WorkingHoursService
{
    private $settings = [];
    
    public function __construct()
    {
        $this->loadSettings();
    }
    
    /**
     * 加载工作时间设置
     */
    private function loadSettings()
    {
        $this->settings = Cache::remember('customer_service_working_hours', 300, function () {
            $settings = DB::table('st_customer_service_settings')
                ->where('category', 'working_hours')
                ->pluck('setting_value', 'setting_key')
                ->toArray();
            
            // 处理JSON格式的设置
            foreach ($settings as $key => $value) {
                if (in_array($key, ['working_days', 'holiday_dates'])) {
                    $settings[$key] = json_decode($value, true) ?: [];
                }
            }
            
            return $settings;
        });
    }
    
    /**
     * 判断当前是否为工作时间
     */
    public function isWorkingTime($timestamp = null)
    {
        // 如果未启用工作时间控制，默认为工作时间
        if (!$this->getSetting('working_hours_enabled', true)) {
            return true;
        }
        
        // 手动模式下，返回手动设置的状态
        if ($this->getSetting('working_hours_mode') === 'manual') {
            return $this->getSetting('manual_online_status', true);
        }
        
        // 自动模式下，根据时间判断
        return $this->isAutoWorkingTime($timestamp);
    }
    
    /**
     * 自动判断是否为工作时间
     */
    private function isAutoWorkingTime($timestamp = null)
    {
        $timezone = $this->getSetting('working_timezone', 'Asia/Shanghai');
        $now = $timestamp ? Carbon::createFromTimestamp($timestamp, $timezone) : Carbon::now($timezone);
        
        // 检查是否为节假日
        if ($this->isHoliday($now)) {
            return false;
        }
        
        // 检查是否为工作日
        if (!$this->isWorkingDay($now)) {
            return false;
        }
        
        // 检查是否在工作时间内
        if (!$this->isInWorkingHours($now)) {
            return false;
        }
        
        // 检查是否在午休时间
        if ($this->isLunchBreak($now)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否为节假日
     */
    private function isHoliday($date)
    {
        if (!$this->getSetting('holiday_mode_enabled', true)) {
            return false;
        }
        
        $holidayDates = $this->getSetting('holiday_dates', []);
        $dateString = $date->format('Y-m-d');
        
        return in_array($dateString, $holidayDates);
    }
    
    /**
     * 检查是否为工作日
     */
    private function isWorkingDay($date)
    {
        $workingDays = $this->getSetting('working_days', ['1', '2', '3', '4', '5']);
        $dayOfWeek = $date->dayOfWeek; // 0=周日, 1=周一, ..., 6=周六
        
        // 转换为我们的格式 (1=周一, 7=周日)
        $dayNumber = $dayOfWeek == 0 ? '7' : (string)$dayOfWeek;
        
        return in_array($dayNumber, $workingDays);
    }
    
    /**
     * 检查是否在工作时间内
     */
    private function isInWorkingHours($date)
    {
        $startTime = $this->getSetting('working_start_time', '09:00');
        $endTime = $this->getSetting('working_end_time', '18:00');
        
        if (!$startTime || !$endTime) {
            return true;
        }
        
        $currentTime = $date->format('H:i');
        
        return $currentTime >= $startTime && $currentTime <= $endTime;
    }
    
    /**
     * 检查是否在午休时间
     */
    private function isLunchBreak($date)
    {
        if (!$this->getSetting('lunch_break_enabled', true)) {
            return false;
        }
        
        $lunchStart = $this->getSetting('lunch_start_time', '12:00');
        $lunchEnd = $this->getSetting('lunch_end_time', '13:30');
        
        if (!$lunchStart || !$lunchEnd) {
            return false;
        }
        
        $currentTime = $date->format('H:i');
        
        return $currentTime >= $lunchStart && $currentTime <= $lunchEnd;
    }
    
    /**
     * 获取当前状态信息
     */
    public function getStatusInfo($timestamp = null)
    {
        $isWorking = $this->isWorkingTime($timestamp);
        $timezone = $this->getSetting('working_timezone', 'Asia/Shanghai');
        $now = $timestamp ? Carbon::createFromTimestamp($timestamp, $timezone) : Carbon::now($timezone);
        
        $info = [
            'is_working' => $isWorking,
            'current_time' => $now->format('Y-m-d H:i:s'),
            'timezone' => $timezone,
            'mode' => $this->getSetting('working_hours_mode', 'auto'),
            'message' => $this->getStatusMessage($now, $isWorking)
        ];
        
        if (!$isWorking) {
            $info['next_working_time'] = $this->getNextWorkingTime($now);
        }
        
        return $info;
    }
    
    /**
     * 获取状态消息
     */
    private function getStatusMessage($date, $isWorking)
    {
        if ($isWorking) {
            return '客服在线，欢迎咨询！';
        }
        
        // 检查具体原因
        if ($this->isHoliday($date)) {
            return $this->getSetting('holiday_message', '今天是节假日，客服暂时休息。');
        }
        
        if (!$this->isWorkingDay($date)) {
            return $this->getSetting('weekend_message', '今天是周末，客服暂时休息。');
        }
        
        if ($this->isLunchBreak($date)) {
            return '现在是午休时间，请稍后再试或留言。';
        }
        
        return $this->getSetting('offline_custom_message', '现在是非工作时间，请留言或稍后再试。');
    }
    
    /**
     * 获取下次工作时间
     */
    private function getNextWorkingTime($currentDate)
    {
        $timezone = $this->getSetting('working_timezone', 'Asia/Shanghai');
        $date = $currentDate->copy();
        
        // 最多查找7天
        for ($i = 0; $i < 7; $i++) {
            if ($i > 0) {
                $date->addDay();
            }
            
            // 跳过节假日
            if ($this->isHoliday($date)) {
                continue;
            }
            
            // 跳过非工作日
            if (!$this->isWorkingDay($date)) {
                continue;
            }
            
            // 设置为工作开始时间
            $startTime = $this->getSetting('working_start_time', '09:00');
            list($hour, $minute) = explode(':', $startTime);
            $workingStart = $date->copy()->setTime($hour, $minute);
            
            // 如果是今天且还没到工作时间
            if ($date->isSameDay($currentDate) && $currentDate->lt($workingStart)) {
                return $workingStart->format('Y-m-d H:i:s');
            }
            
            // 如果是今天且在午休时间
            if ($date->isSameDay($currentDate) && $this->isLunchBreak($currentDate)) {
                $lunchEnd = $this->getSetting('lunch_end_time', '13:30');
                list($hour, $minute) = explode(':', $lunchEnd);
                return $date->copy()->setTime($hour, $minute)->format('Y-m-d H:i:s');
            }
            
            // 如果不是今天，返回该天的工作开始时间
            if (!$date->isSameDay($currentDate)) {
                return $workingStart->format('Y-m-d H:i:s');
            }
        }
        
        return null;
    }
    
    /**
     * 手动设置在线状态
     */
    public function setManualStatus($isOnline)
    {
        DB::table('st_customer_service_settings')
            ->where('setting_key', 'manual_online_status')
            ->update(['setting_value' => $isOnline ? '1' : '0']);
        
        // 清除缓存
        Cache::forget('customer_service_working_hours');
        
        // 记录日志
        if ($this->getSetting('status_change_log', true)) {
            $this->logStatusChange($isOnline ? 'online' : 'offline', 'manual');
        }
        
        return true;
    }
    
    /**
     * 记录状态变更日志
     */
    private function logStatusChange($status, $reason)
    {
        DB::table('st_customer_service_logs')->insert([
            'log_type' => 'status_change',
            'content' => json_encode([
                'status' => $status,
                'reason' => $reason,
                'timestamp' => Carbon::now()->toDateTimeString()
            ]),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }
    
    /**
     * 获取设置值
     */
    private function getSetting($key, $default = null)
    {
        return $this->settings[$key] ?? $default;
    }
    
    /**
     * 清除缓存
     */
    public function clearCache()
    {
        Cache::forget('customer_service_working_hours');
        $this->loadSettings();
    }
}
