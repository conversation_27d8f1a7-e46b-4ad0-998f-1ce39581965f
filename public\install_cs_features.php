<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>安装客服系统功能</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🚀 安装客服系统扩展功能</h1>";

try {
    echo "<div class='info'>📋 开始安装客服系统扩展功能...</div>";
    
    // 1. 创建设置表
    echo "<h3>1. 创建设置表</h3>";
    DB::statement("
        CREATE TABLE IF NOT EXISTS `customer_service_settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
          `setting_value` text COMMENT '设置值',
          `setting_type` enum('text','number','boolean','json','file','color','select') NOT NULL DEFAULT 'text' COMMENT '设置类型',
          `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '设置分类',
          `title` varchar(200) NOT NULL COMMENT '设置标题',
          `description` text COMMENT '设置描述',
          `options` text COMMENT '选项（用于select类型）',
          `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
          `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `setting_key` (`setting_key`),
          KEY `category` (`category`),
          KEY `is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服系统设置表'
    ");
    echo "<div class='success'>✅ 设置表创建成功</div>";
    
    // 2. 创建离线留言表
    echo "<h3>2. 创建离线留言表</h3>";
    DB::statement("
        CREATE TABLE IF NOT EXISTS `customer_service_offline_messages` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `session_id` varchar(100) NOT NULL COMMENT '会话ID',
          `visitor_name` varchar(100) DEFAULT NULL COMMENT '访客姓名',
          `visitor_email` varchar(200) DEFAULT NULL COMMENT '访客邮箱',
          `visitor_phone` varchar(50) DEFAULT NULL COMMENT '访客电话',
          `visitor_whatsapp` varchar(50) DEFAULT NULL COMMENT '访客WhatsApp',
          `visitor_ip` varchar(45) DEFAULT NULL COMMENT '访客IP',
          `message` text NOT NULL COMMENT '留言内容',
          `status` enum('pending','replied','closed') NOT NULL DEFAULT 'pending' COMMENT '状态',
          `admin_reply` text DEFAULT NULL COMMENT '管理员回复',
          `replied_at` timestamp NULL DEFAULT NULL COMMENT '回复时间',
          `replied_by` int(11) DEFAULT NULL COMMENT '回复人ID',
          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `session_id` (`session_id`),
          KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服系统离线留言表'
    ");
    echo "<div class='success'>✅ 离线留言表创建成功</div>";
    
    // 3. 插入默认设置
    echo "<h3>3. 插入默认设置</h3>";
    
    $defaultSettings = [
        // 基础功能设置
        ['system_enabled', '1', 'boolean', 'basic', '启用客服系统', '是否启用客服系统功能', '', 1],
        ['welcome_message', '您好！欢迎咨询，我们将竭诚为您服务！', 'text', 'basic', '欢迎消息', '客户打开聊天窗口时显示的欢迎消息', '', 2],
        ['offline_message', '客服暂时离线，请留下您的联系方式，我们会尽快回复您！', 'text', 'basic', '离线消息', '客服离线时显示的提示消息', '', 3],
        ['auto_open_enabled', '1', 'boolean', 'basic', '自动打开聊天窗口', '收到客服消息时是否自动打开聊天窗口', '', 4],
        
        // 声音设置
        ['sound_enabled', '1', 'boolean', 'sound', '启用提示音', '是否启用消息提示音', '', 1],
        ['sound_type', 'default', 'select', 'sound', '提示音类型', '选择提示音类型', 'default:默认音效,custom:自定义音效', 2],
        ['sound_file', '', 'file', 'sound', '自定义音效文件', '支持MP3格式，建议文件大小不超过2MB', '', 3],
        ['sound_volume', '0.3', 'number', 'sound', '音量大小', '提示音音量，范围0-1', '', 4],
        
        // 外观设置
        ['chat_position', 'bottom-right', 'select', 'appearance', '聊天窗口位置', '聊天窗口在页面中的位置', 'bottom-right:右下角,bottom-left:左下角,top-right:右上角,top-left:左上角', 1],
        ['chat_theme_color', '#667eea', 'color', 'appearance', '主题颜色', '聊天窗口的主题颜色', '', 2],
        ['chat_button_color', '#667eea', 'color', 'appearance', '按钮颜色', '客服按钮的颜色', '', 3],
        ['chat_font_size', '14', 'number', 'appearance', '字体大小(px)', '聊天消息的字体大小', '', 4],
        ['chat_window_width', '380', 'number', 'appearance', '窗口宽度(px)', '聊天窗口的宽度', '', 5],
        ['chat_window_height', '520', 'number', 'appearance', '窗口高度(px)', '聊天窗口的高度', '', 6],
        ['show_avatar', '1', 'boolean', 'appearance', '显示头像', '是否在消息中显示头像', '', 7],
        ['admin_avatar', '/images/admin-avatar.png', 'file', 'appearance', '客服头像', '客服的头像图片', '', 8],
        ['admin_name', '客服小助手', 'text', 'appearance', '客服名称', '显示的客服名称', '', 9],
        ['chat_border_radius', '16', 'number', 'appearance', '窗口圆角(px)', '聊天窗口的圆角大小', '', 10],
        
        // 离线设置
        ['offline_form_enabled', '1', 'boolean', 'offline', '启用离线表单', '客服离线时是否显示留言表单', '', 1],
        ['offline_form_title', '客服离线留言', 'text', 'offline', '离线表单标题', '离线留言表单的标题', '', 2],
        ['offline_form_fields', '["name","email","phone","whatsapp","message"]', 'json', 'offline', '表单字段', '离线表单包含的字段', '', 3],
        ['offline_form_required', '["message"]', 'json', 'offline', '必填字段', '离线表单的必填字段', '', 4],
        ['offline_auto_reply', '感谢您的留言，我们会尽快回复您！', 'text', 'offline', '自动回复消息', '提交离线留言后的自动回复', '', 5],
        
        // 高级设置
        ['heartbeat_interval', '5', 'number', 'advanced', '心跳间隔(秒)', '客户端发送心跳的间隔时间', '', 1],
        ['offline_timeout', '8', 'number', 'advanced', '离线超时(秒)', '多少秒无心跳后判定为离线', '', 2],
        ['max_message_length', '1000', 'number', 'advanced', '消息最大长度', '单条消息的最大字符数', '', 3],
        ['session_timeout', '1800', 'number', 'advanced', '会话超时(秒)', '会话无活动后的超时时间', '', 4],
        ['enable_emoji', '1', 'boolean', 'advanced', '启用表情包', '是否允许发送表情包', '', 5],
        ['enable_image', '1', 'boolean', 'advanced', '启用图片发送', '是否允许发送图片', '', 6],
        ['max_image_size', '5', 'number', 'advanced', '图片最大大小(MB)', '允许上传的图片最大大小', '', 7],
    ];
    
    foreach ($defaultSettings as $setting) {
        DB::table('customer_service_settings')->updateOrInsert(
            ['setting_key' => $setting[0]],
            [
                'setting_key' => $setting[0],
                'setting_value' => $setting[1],
                'setting_type' => $setting[2],
                'category' => $setting[3],
                'title' => $setting[4],
                'description' => $setting[5],
                'options' => $setting[6],
                'sort_order' => $setting[7],
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );
    }
    echo "<div class='success'>✅ 默认设置插入成功</div>";

    // 4. 添加菜单项
    echo "<h3>4. 添加菜单项</h3>";

    // 查找在线客服主菜单
    $parentMenu = DB::table('strongadmin_menu')
                   ->where('name', '在线客服')
                   ->where('level', 1)
                   ->first();

    if ($parentMenu) {
        // 添加设置中心菜单
        $existingSettings = DB::table('strongadmin_menu')
                             ->where('name', '设置中心')
                             ->where('parent_id', $parentMenu->id)
                             ->first();

        if (!$existingSettings) {
            DB::table('strongadmin_menu')->insert([
                'level' => 2,
                'parent_id' => $parentMenu->id,
                'name' => '设置中心',
                'route_url' => 'customer-service/settings',
                'status' => 1,
                'sort' => 4,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            echo "<div class='success'>✅ 设置中心菜单添加成功</div>";
        } else {
            echo "<div class='info'>📋 设置中心菜单已存在</div>";
        }

        // 添加离线留言菜单
        $existingOffline = DB::table('strongadmin_menu')
                            ->where('name', '离线留言')
                            ->where('parent_id', $parentMenu->id)
                            ->first();

        if (!$existingOffline) {
            DB::table('strongadmin_menu')->insert([
                'level' => 2,
                'parent_id' => $parentMenu->id,
                'name' => '离线留言',
                'route_url' => 'customer-service/offline-messages',
                'status' => 1,
                'sort' => 5,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            echo "<div class='success'>✅ 离线留言菜单添加成功</div>";
        } else {
            echo "<div class='info'>📋 离线留言菜单已存在</div>";
        }
    } else {
        echo "<div class='error'>❌ 找不到在线客服主菜单，请先安装基础客服系统</div>";
    }

    // 5. 创建必要的目录
    echo "<h3>5. 创建存储目录</h3>";
    $directories = [
        'storage/app/public/customer-service',
        'storage/app/public/customer-service/sounds',
        'storage/app/public/customer-service/avatars',
        'storage/app/public/customer-service/images',
        'public/storage/customer-service',
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "<div class='success'>✅ 创建目录: {$dir}</div>";
        } else {
            echo "<div class='info'>📁 目录已存在: {$dir}</div>";
        }
    }
    
    echo "<div class='success'>🎉 客服系统扩展功能安装完成！</div>";
    
    echo "<h3>📋 安装总结</h3>";
    echo "<ul>";
    echo "<li>✅ 设置表创建完成</li>";
    echo "<li>✅ 离线留言表创建完成</li>";
    echo "<li>✅ 默认设置插入完成</li>";
    echo "<li>✅ 菜单项添加完成</li>";
    echo "<li>✅ 存储目录创建完成</li>";
    echo "</ul>";

    echo "<h3>🎯 新增功能</h3>";
    echo "<ul>";
    echo "<li>🎵 <strong>消息提示音</strong> - 支持默认音效和自定义MP3文件</li>";
    echo "<li>🎨 <strong>外观自定义</strong> - 主题颜色、字体大小、窗口尺寸等完全可控</li>";
    echo "<li>📧 <strong>离线留言</strong> - 客服离线时收集访客联系方式和留言</li>";
    echo "<li>⚙️ <strong>设置中心</strong> - 统一管理所有客服系统配置</li>";
    echo "<li>🖼️ <strong>图片和表情</strong> - 管理员可发送图片和表情给客户</li>";
    echo "</ul>";

    echo "<h3>🔗 相关链接</h3>";
    echo "<ul>";
    echo "<li><a href='/strongadmin/customer-service/settings' target='_blank' style='color:#007bff;'>⚙️ 设置中心</a></li>";
    echo "<li><a href='/strongadmin/customer-service/offline-messages' target='_blank' style='color:#28a745;'>📧 离线留言</a></li>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank' style='color:#17a2b8;'>💬 会话管理</a></li>";
    echo "<li><a href='/' target='_blank' style='color:#6c757d;'>🏠 前台测试</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 安装失败: " . $e->getMessage() . "</div>";
    echo "<div class='error'>错误详情: " . $e->getTraceAsString() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
