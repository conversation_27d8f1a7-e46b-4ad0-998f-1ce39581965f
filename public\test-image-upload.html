<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服图片上传测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1); 
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #5a67d8;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #667eea;
            width: 0%;
            transition: width 0.3s;
        }
        .image-preview {
            max-width: 300px;
            max-height: 300px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 客服图片上传测试</h1>
        <p>测试客服系统的图片上传功能</p>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div>📷</div>
            <div>点击选择图片或拖拽图片到这里</div>
            <div style="font-size: 12px; color: #666; margin-top: 10px;">
                支持 JPEG, PNG, GIF, WebP 格式，最大 5MB
            </div>
        </div>
        
        <input type="file" id="fileInput" accept="image/*" style="display: none;">
        
        <div class="progress" id="progressBar">
            <div class="progress-bar" id="progressBarFill"></div>
        </div>
        
        <div class="result" id="result"></div>
        
        <div style="margin-top: 20px;">
            <button class="btn" onclick="testAPI()">测试API连接</button>
            <button class="btn" onclick="clearResult()">清除结果</button>
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.querySelector('.upload-area');
        const result = document.getElementById('result');
        const progressBar = document.getElementById('progressBar');
        const progressBarFill = document.getElementById('progressBarFill');
        
        // 模拟会话ID
        const sessionId = 'test-session-' + Date.now();
        
        // 文件选择事件
        fileInput.addEventListener('change', handleFileSelect);
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }
        
        function handleFile(file) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showResult('error', '请选择图片文件！');
                return;
            }
            
            // 验证文件大小
            if (file.size > 5 * 1024 * 1024) {
                showResult('error', '图片文件不能超过5MB！');
                return;
            }
            
            // 显示文件信息
            showResult('success', `准备上传: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            
            // 预览图片
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'image-preview';
                result.appendChild(img);
            };
            reader.readAsDataURL(file);
            
            // 上传文件
            uploadFile(file);
        }
        
        function uploadFile(file) {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('session_id', sessionId);
            
            // 显示进度条
            progressBar.style.display = 'block';
            progressBarFill.style.width = '0%';
            
            // 模拟进度
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressBarFill.style.width = progress + '%';
            }, 200);
            
            fetch('/api/customer-service/upload-image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBarFill.style.width = '100%';
                
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    
                    if (data.success) {
                        showResult('success', 
                            `✅ 上传成功！\n` +
                            `图片ID: ${data.data.image_id}\n` +
                            `访问地址: ${data.data.image_url}\n` +
                            `文件大小: ${(data.data.file_size / 1024).toFixed(2)} KB\n` +
                            `图片尺寸: ${data.data.width} × ${data.data.height}`
                        );
                        
                        // 显示上传后的图片
                        const uploadedImg = document.createElement('img');
                        uploadedImg.src = data.data.image_url;
                        uploadedImg.className = 'image-preview';
                        uploadedImg.style.border = '2px solid #28a745';
                        result.appendChild(uploadedImg);
                        
                    } else {
                        showResult('error', `❌ 上传失败: ${data.message}`);
                    }
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                progressBar.style.display = 'none';
                console.error('上传错误:', error);
                showResult('error', `❌ 上传失败: ${error.message}`);
            });
        }
        
        function testAPI() {
            showResult('success', '正在测试API连接...');
            
            fetch('/api/customer-service/upload-image.php', {
                method: 'POST',
                body: new FormData() // 空的FormData来测试API响应
            })
            .then(response => response.json())
            .then(data => {
                if (data.success === false && data.message.includes('没有上传文件')) {
                    showResult('success', '✅ API连接正常！服务器响应正确。');
                } else {
                    showResult('error', `API响应异常: ${JSON.stringify(data)}`);
                }
            })
            .catch(error => {
                showResult('error', `❌ API连接失败: ${error.message}`);
            });
        }
        
        function showResult(type, message) {
            result.className = `result ${type}`;
            result.style.display = 'block';
            result.innerHTML = message.replace(/\n/g, '<br>');
        }
        
        function clearResult() {
            result.style.display = 'none';
            result.innerHTML = '';
            progressBar.style.display = 'none';
            fileInput.value = '';
        }
    </script>
</body>
</html>
