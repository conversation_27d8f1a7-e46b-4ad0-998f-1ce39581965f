<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛍️ 现代电商产品动画效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.08);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #333;
            font-weight: 300;
        }
        
        /* 现代产品网格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        /* 高质量产品卡片 */
        .home-product-card {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 20px rgba(0,0,0,0.08);
            background: #fff;
            border: 1px solid rgba(0,0,0,0.05);
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }
        
        .home-product-card:nth-child(1) { animation-delay: 0.1s; }
        .home-product-card:nth-child(2) { animation-delay: 0.2s; }
        .home-product-card:nth-child(3) { animation-delay: 0.3s; }
        .home-product-card:nth-child(4) { animation-delay: 0.4s; }
        .home-product-card:nth-child(5) { animation-delay: 0.5s; }
        .home-product-card:nth-child(6) { animation-delay: 0.6s; }
        
        .home-product-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            border-color: rgba(0,0,0,0.1);
        }
        
        /* 产品图片悬停效果 */
        .product-hover-effect {
            position: relative;
            overflow: hidden;
            display: block;
            background: #f8f9fa;
            height: 250px;
        }
        
        .product-main-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            z-index: 1;
        }
        
        .product-hover-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transform: scale(1.1);
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 2;
        }
        
        .product-hover-effect:hover .product-main-img {
            transform: scale(1.1);
            opacity: 0;
        }
        
        .product-hover-effect:hover .product-hover-img {
            opacity: 1;
            transform: scale(1);
        }
        
        /* 光效扫过动画 */
        .product-hover-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
            z-index: 3;
            pointer-events: none;
        }
        
        .product-hover-effect:hover::before {
            left: 100%;
        }
        
        /* 产品信息区域 */
        .home-product-caption {
            padding: 16px;
            background: #fff;
            transition: all 0.3s ease;
        }
        
        .home-product-title {
            margin: 0 0 8px 0;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .home-product-title a {
            color: #333;
            text-decoration: none;
            transition: color 0.3s ease;
            font-weight: 500;
        }
        
        .home-product-card:hover .home-product-title a {
            color: #007bff;
        }
        
        .home-product-price {
            color: #e74c3c;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            transition: all 0.3s ease;
        }
        
        .home-product-card:hover .home-product-price {
            color: #c0392b;
            transform: translateY(-1px);
        }
        
        /* 动画定义 */
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 特色标签 */
        .product-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            z-index: 5;
        }
        
        .product-badge.new {
            background: #27ae60;
        }
        
        .product-badge.hot {
            background: #f39c12;
        }
        
        /* 手机端优化 */
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
            
            .home-product-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 25px rgba(0,0,0,0.12);
            }
            
            .product-hover-effect {
                height: 200px;
            }
            
            .home-product-caption {
                padding: 12px;
            }
            
            .home-product-title {
                font-size: 13px;
            }
            
            .home-product-price {
                font-size: 15px;
            }
        }
        
        .demo-section {
            background: #fff;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.08);
        }
        
        .demo-title {
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .feature-list {
            list-style: none;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:before {
            content: "✨";
            margin-right: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ 现代电商产品动画效果</h1>
            <p style="font-size: 1.1rem; color: #666;">参考顶级电商网站的产品展示动画</p>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🎯 产品图片动画效果</h2>
            <p style="color: #666; margin-bottom: 30px;">鼠标悬停查看图片切换动画，体验现代电商的流畅效果</p>
            
            <div class="product-grid">
                <div class="home-product-card">
                    <a href="#" class="product-hover-effect">
                        <div class="product-badge new">NEW</div>
                        <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop" 
                             class="product-main-img" alt="Product 1">
                        <img src="https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=300&fit=crop" 
                             class="product-hover-img" alt="Product 1 Hover">
                    </a>
                    <div class="home-product-caption">
                        <h3 class="home-product-title">
                            <a href="#">Premium Sunglasses</a>
                        </h3>
                        <p class="home-product-price">$129.99</p>
                    </div>
                </div>
                
                <div class="home-product-card">
                    <a href="#" class="product-hover-effect">
                        <div class="product-badge hot">HOT</div>
                        <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop" 
                             class="product-main-img" alt="Product 2">
                        <img src="https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=300&fit=crop" 
                             class="product-hover-img" alt="Product 2 Hover">
                    </a>
                    <div class="home-product-caption">
                        <h3 class="home-product-title">
                            <a href="#">Running Shoes</a>
                        </h3>
                        <p class="home-product-price">$89.99</p>
                    </div>
                </div>
                
                <div class="home-product-card">
                    <a href="#" class="product-hover-effect">
                        <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=300&fit=crop" 
                             class="product-main-img" alt="Product 3">
                        <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=300&fit=crop" 
                             class="product-hover-img" alt="Product 3 Hover">
                    </a>
                    <div class="home-product-caption">
                        <h3 class="home-product-title">
                            <a href="#">Designer Bag</a>
                        </h3>
                        <p class="home-product-price">$199.99</p>
                    </div>
                </div>
                
                <div class="home-product-card">
                    <a href="#" class="product-hover-effect">
                        <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop" 
                             class="product-main-img" alt="Product 4">
                        <img src="https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=300&fit=crop" 
                             class="product-hover-img" alt="Product 4 Hover">
                    </a>
                    <div class="home-product-caption">
                        <h3 class="home-product-title">
                            <a href="#">Smart Watch</a>
                        </h3>
                        <p class="home-product-price">$299.99</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">✨ 动画效果特点</h2>
            <ul class="feature-list">
                <li>流畅的图片切换动画 (0.5s 贝塞尔曲线)</li>
                <li>优雅的卡片悬停效果 (上浮 + 缩放)</li>
                <li>光效扫过动画增强视觉冲击</li>
                <li>图片预加载确保切换流畅</li>
                <li>响应式设计适配各种设备</li>
                <li>进入动画让页面更生动</li>
                <li>微妙的阴影和边框变化</li>
                <li>现代电商级别的用户体验</li>
            </ul>
        </div>
    </div>
</body>
</html>
