<?php
/**
 * 测试后台API响应
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>API测试</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;white-space:pre-wrap;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔌 API测试页面</h1>";

echo "<div class='info'>📋 测试后台API是否正确返回在线状态</div>";

echo "<h2>🌐 API调用测试</h2>";

// 模拟API调用
$apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/strongadmin/customer-service/sessions/unread-counts';

echo "<p><strong>API地址:</strong> <code>{$apiUrl}</code></p>";

// 使用cURL调用API
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-Requested-With: XMLHttpRequest',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<div class='error'>❌ cURL错误: {$error}</div>";
} else {
    echo "<div class='success'>✅ HTTP状态码: {$httpCode}</div>";
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        
        if ($data) {
            echo "<h3>📊 API响应数据:</h3>";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            
            if (isset($data['success']) && $data['success'] && isset($data['sessions'])) {
                echo "<h3>📋 会话状态分析:</h3>";
                echo "<table border='1' style='width:100%;border-collapse:collapse;'>";
                echo "<tr style='background:#f8f9fa;'>";
                echo "<th style='padding:8px;'>会话ID</th>";
                echo "<th style='padding:8px;'>未读数量</th>";
                echo "<th style='padding:8px;'>在线状态</th>";
                echo "</tr>";
                
                foreach ($data['sessions'] as $session) {
                    $statusIcon = isset($session['status']) && $session['status'] === 'online' ? '🟢' : '⚫';
                    $statusText = isset($session['status']) ? $session['status'] : '未知';
                    
                    echo "<tr>";
                    echo "<td style='padding:8px;'>{$session['id']}</td>";
                    echo "<td style='padding:8px;'>{$session['unread_count']}</td>";
                    echo "<td style='padding:8px;'>{$statusIcon} {$statusText}</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
                
                // 统计
                $onlineCount = 0;
                $offlineCount = 0;
                foreach ($data['sessions'] as $session) {
                    if (isset($session['status']) && $session['status'] === 'online') {
                        $onlineCount++;
                    } else {
                        $offlineCount++;
                    }
                }
                
                echo "<h3>📈 状态统计:</h3>";
                echo "<ul>";
                echo "<li>🟢 在线: {$onlineCount} 个会话</li>";
                echo "<li>⚫ 离线: {$offlineCount} 个会话</li>";
                echo "<li>📊 总计: " . count($data['sessions']) . " 个会话</li>";
                echo "</ul>";
                
            } else {
                echo "<div class='error'>❌ API返回格式错误或无数据</div>";
            }
        } else {
            echo "<div class='error'>❌ 无法解析JSON响应</div>";
            echo "<h3>原始响应:</h3>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<div class='error'>❌ HTTP错误: {$httpCode}</div>";
        echo "<h3>响应内容:</h3>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

echo "<h2>🔧 JavaScript测试</h2>";
echo "<button onclick='testAPI()' style='padding:10px 20px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>测试API调用</button>";
echo "<div id='jsResult' style='margin-top:10px;'></div>";

echo "<script>
function testAPI() {
    const resultDiv = document.getElementById('jsResult');
    resultDiv.innerHTML = '<div style=\"color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;\">🔄 正在调用API...</div>';
    
    fetch('/strongadmin/customer-service/sessions/unread-counts', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('API响应:', data);
        resultDiv.innerHTML = '<div style=\"color:#28a745;background:#d4edda;padding:10px;border-radius:4px;\">✅ API调用成功</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        console.error('API错误:', error);
        resultDiv.innerHTML = '<div style=\"color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;\">❌ API调用失败: ' + error.message + '</div>';
    });
}
</script>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/test_heartbeat.php' target='_blank'>心跳状态测试</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
