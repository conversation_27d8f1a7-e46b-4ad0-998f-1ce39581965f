<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统修复测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .log { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 4px; 
            margin: 10px 0; 
            font-family: monospace; 
            font-size: 12px; 
            max-height: 400px; 
            overflow-y: auto; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
        }
        .test-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服系统修复测试</h1>
        <p>测试JavaScript变量初始化和API调用</p>
        
        <div style="margin-bottom: 20px;">
            <button class="test-btn" onclick="testVariables()">测试变量</button>
            <button class="test-btn" onclick="testAPI()">测试API</button>
            <button class="test-btn" onclick="testInitialization()">测试初始化</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log" class="log">
            <div class="info">📋 测试日志:</div>
        </div>
    </div>

    <!-- 客服组件 -->
    <div id="customer-service-widget" style="display: none;">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                    </div>
                    <button onclick="toggleChat()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;">×</button>
                </div>
            </div>
            <div id="cs-messages" style="flex:1;padding:20px;overflow-y:auto;max-height:360px;background:#fafafa;">
                <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                    <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);">
                        <div style="font-weight:600;margin-bottom:5px;">👋 欢迎咨询</div>
                        <div>我们随时为您提供帮助！</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量声明
        let csSettings = {};
        let isOffline = false;
        let sessionId = null;
        let isOpen = false;
        let heartbeatInterval = null;
        let eventSource = null;
        let lastMessageId = 0;
        let lastNotificationSound = 0;
        const NOTIFICATION_SOUND_COOLDOWN = 2000;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            logDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">📋 测试日志:</div>';
        }

        function testVariables() {
            log('🔍 开始测试变量...', 'info');
            
            try {
                log('csSettings类型: ' + typeof csSettings, 'info');
                log('csSettings值: ' + JSON.stringify(csSettings), 'info');
                log('window.csSettings: ' + typeof window.csSettings, 'info');
                
                // 测试变量赋值
                csSettings = { test: true };
                log('赋值后csSettings: ' + JSON.stringify(csSettings), 'success');
                
                // 测试全局访问
                window.csSettings = csSettings;
                log('全局变量设置成功', 'success');
                
            } catch (error) {
                log('❌ 变量测试失败: ' + error.message, 'error');
            }
        }

        async function testAPI() {
            log('🔍 开始测试API...', 'info');
            
            try {
                const response = await fetch('/api/test-settings.php');
                log('API响应状态: ' + response.status, 'info');
                
                const data = await response.json();
                log('API响应成功', 'success');
                log('system_enabled: ' + data.data.system_enabled, 'info');
                
            } catch (error) {
                log('❌ API测试失败: ' + error.message, 'error');
            }
        }

        async function testInitialization() {
            log('🚀 开始测试完整初始化...', 'info');
            
            try {
                // 初始化会话ID
                sessionId = getOrCreatePersistentSessionId();
                log('会话ID: ' + sessionId, 'info');

                // 加载设置
                await loadCustomerServiceSettings();
                
                // 检查系统启用状态
                if (!csSettings || !csSettings.system_enabled) {
                    log('❌ 客服系统已禁用或设置加载失败', 'error');
                    return;
                }

                log('✅ 客服系统已启用，继续初始化', 'success');
                
                // 显示客服组件
                checkCustomerServiceStatus();
                
            } catch (error) {
                log('❌ 初始化失败: ' + error.message, 'error');
            }
        }

        // 加载客服系统设置
        async function loadCustomerServiceSettings() {
            try {
                log('📡 正在加载客服设置...', 'info');
                log('🔍 csSettings变量状态: ' + typeof csSettings, 'info');
                
                // 确保csSettings已初始化
                if (typeof csSettings === 'undefined') {
                    log('⚠️ csSettings未定义，重新初始化', 'warning');
                    window.csSettings = {};
                    csSettings = window.csSettings;
                }
                
                const response = await fetch('/api/test-settings.php');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success) {
                    csSettings = data.data;
                    window.csSettings = csSettings;
                    log('✅ 客服设置加载成功', 'success');
                    log('🔍 system_enabled状态: ' + csSettings.system_enabled, 'info');
                } else {
                    log('❌ 加载客服设置失败: ' + (data.error || data.message), 'error');
                    csSettings = getDefaultSettings();
                    window.csSettings = csSettings;
                }
            } catch (error) {
                log('💥 加载客服设置异常: ' + error.message, 'error');
                csSettings = getDefaultSettings();
                window.csSettings = csSettings;
            }
        }

        function getDefaultSettings() {
            return {
                system_enabled: true,
                welcome_message: '您好！欢迎咨询，我们将竭诚为您服务！'
            };
        }

        function checkCustomerServiceStatus() {
            log('🔍 检查客服系统状态...', 'info');
            
            fetch('/api/customer-service/status')
                .then(response => response.json())
                .then(data => {
                    log('客服系统状态: ' + JSON.stringify(data), 'info');
                    
                    if (data.success && data.enabled) {
                        document.getElementById('customer-service-widget').style.display = 'block';
                        log('✅ 客服组件已显示', 'success');
                    } else {
                        log('❌ 客服系统服务被禁用', 'warning');
                    }
                })
                .catch(error => {
                    log('❌ 检查状态失败: ' + error.message, 'error');
                    // 出错时默认显示
                    document.getElementById('customer-service-widget').style.display = 'block';
                    log('⚠️ 状态检查失败，但强制显示客服', 'warning');
                });
        }

        function getOrCreatePersistentSessionId() {
            let sessionId = localStorage.getItem('customer_service_session_id');
            if (!sessionId) {
                sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('customer_service_session_id', sessionId);
            }
            return sessionId;
        }

        function toggleChat() {
            const chatWindow = document.getElementById('cs-chat-window');
            if (chatWindow.style.display === 'none' || !chatWindow.style.display) {
                chatWindow.style.display = 'block';
                log('💬 聊天窗口已打开', 'info');
            } else {
                chatWindow.style.display = 'none';
                log('💬 聊天窗口已关闭', 'info');
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成', 'info');
            setTimeout(() => {
                testVariables();
            }, 500);
        });
    </script>
</body>
</html>
