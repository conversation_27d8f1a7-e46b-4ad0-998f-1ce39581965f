<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成提示音文件</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🔊 生成客服提示音</h1>
    <p>点击下面的按钮生成和测试提示音：</p>
    
    <button onclick="testSound()">🎵 测试提示音</button>
    <button onclick="downloadSound()">💾 下载提示音文件</button>
    <button onclick="testNotification()">🔔 测试浏览器通知</button>
    
    <div id="status"></div>
    
    <h2>📝 使用说明：</h2>
    <ol>
        <li>点击"测试提示音"听听效果</li>
        <li>点击"下载提示音文件"保存为MP3</li>
        <li>将下载的文件重命名为 notification.mp3</li>
        <li>上传到 public/assets/sounds/ 目录</li>
    </ol>

    <script>
    function testSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            function playTone(frequency, startTime, duration) {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = frequency;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0, startTime);
                gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);
                
                oscillator.start(startTime);
                oscillator.stop(startTime + duration);
            }
            
            const now = audioContext.currentTime;
            playTone(800, now, 0.15);        // 第一个音
            playTone(1000, now + 0.2, 0.15); // 第二个音
            
            document.getElementById('status').innerHTML = '<p class="success">✅ 提示音播放成功！</p>';
        } catch (e) {
            document.getElementById('status').innerHTML = '<p class="error">❌ 播放失败: ' + e.message + '</p>';
        }
    }
    
    function downloadSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const sampleRate = audioContext.sampleRate;
            const duration = 0.5; // 0.5秒
            const length = sampleRate * duration;
            const buffer = audioContext.createBuffer(1, length, sampleRate);
            const data = buffer.getChannelData(0);
            
            // 生成双音调提示音
            for (let i = 0; i < length; i++) {
                const time = i / sampleRate;
                let value = 0;
                
                if (time < 0.15) {
                    // 第一个音调 (800Hz)
                    value = Math.sin(2 * Math.PI * 800 * time) * Math.exp(-time * 5);
                } else if (time > 0.2 && time < 0.35) {
                    // 第二个音调 (1000Hz)
                    const t2 = time - 0.2;
                    value = Math.sin(2 * Math.PI * 1000 * t2) * Math.exp(-t2 * 5);
                }
                
                data[i] = value * 0.3; // 降低音量
            }
            
            // 转换为WAV格式并下载
            const wav = audioBufferToWav(buffer);
            const blob = new Blob([wav], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'notification.wav';
            a.click();
            
            document.getElementById('status').innerHTML = '<p class="success">✅ 提示音文件已生成并下载！</p>';
        } catch (e) {
            document.getElementById('status').innerHTML = '<p class="error">❌ 生成失败: ' + e.message + '</p>';
        }
    }
    
    function testNotification() {
        if ("Notification" in window) {
            if (Notification.permission === "granted") {
                new Notification("测试通知", {
                    body: "这是一个测试通知消息",
                    icon: '/favicon.ico'
                });
                document.getElementById('status').innerHTML = '<p class="success">✅ 通知已发送！</p>';
            } else {
                Notification.requestPermission().then(function (permission) {
                    if (permission === "granted") {
                        new Notification("测试通知", {
                            body: "通知权限已获取，这是测试消息",
                            icon: '/favicon.ico'
                        });
                        document.getElementById('status').innerHTML = '<p class="success">✅ 通知权限已获取并发送测试通知！</p>';
                    } else {
                        document.getElementById('status').innerHTML = '<p class="error">❌ 通知权限被拒绝</p>';
                    }
                });
            }
        } else {
            document.getElementById('status').innerHTML = '<p class="error">❌ 浏览器不支持通知</p>';
        }
    }
    
    // 将AudioBuffer转换为WAV格式
    function audioBufferToWav(buffer) {
        const length = buffer.length;
        const arrayBuffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(arrayBuffer);
        const channels = buffer.numberOfChannels;
        const sampleRate = buffer.sampleRate;
        
        // WAV文件头
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, channels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);
        
        // 音频数据
        const channelData = buffer.getChannelData(0);
        let offset = 44;
        for (let i = 0; i < length; i++) {
            const sample = Math.max(-1, Math.min(1, channelData[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
        }
        
        return arrayBuffer;
    }
    </script>
</body>
</html>
