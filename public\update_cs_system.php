<?php
/**
 * 更新客服系统 - 添加系统配置表和开关功能
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>更新客服系统</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 更新客服系统 - 添加系统开关</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 检查配置表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'st_customer_service_config'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='info'>ℹ️ 配置表已存在</p>";
    } else {
        // 创建配置表
        $sql = "
        CREATE TABLE `st_customer_service_config` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置主键ID',
            `config_key` varchar(255) NOT NULL COMMENT '配置键名',
            `config_value` text DEFAULT NULL COMMENT '配置值',
            `config_type` enum('string','integer','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
            `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
            `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
            `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `config_key` (`config_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服系统配置表'
        ";
        $pdo->exec($sql);
        echo "<p class='success'>✅ 成功创建配置表</p>";
    }
    
    // 插入默认配置
    $configs = [
        ['system_enabled', '1', 'boolean', '在线客服系统总开关'],
        ['auto_reply_enabled', '1', 'boolean', 'AI自动回复功能开关'],
        ['welcome_message', '您好！欢迎咨询，我是智能客服助手，有什么可以帮助您的吗？', 'string', '欢迎消息'],
        ['offline_message', '客服系统暂时离线，请稍后再试或留言给我们。', 'string', '离线提示消息'],
        ['max_sessions_per_ip', '5', 'integer', '每个IP最大会话数限制'],
        ['session_timeout_minutes', '30', 'integer', '会话超时时间（分钟）']
    ];
    
    foreach ($configs as $config) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO st_customer_service_config 
            (config_key, config_value, config_type, description, is_active, created_at, updated_at) 
            VALUES (?, ?, ?, ?, 1, NOW(), NOW())
        ");
        $stmt->execute($config);
    }
    echo "<p class='success'>✅ 默认配置已插入</p>";
    
    echo "<h2>🎉 系统开关功能已完成！</h2>";
    echo "<p>现在可以在后台控制客服系统的开启/关闭：</p>";
    echo "<ul>";
    echo "<li><a href='/strongadmin/customer-service/system-config' target='_blank'>系统配置页面</a></li>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>会话管理页面</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
