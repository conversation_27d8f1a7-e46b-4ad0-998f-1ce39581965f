<?php
/**
 * 最终工作的SSE - 确保后台消息前台能收到
 */

set_time_limit(0);
ini_set('memory_limit', '128M');

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');

while (ob_get_level()) {
    ob_end_clean();
}

$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 获取或创建会话
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
        $stmt->execute([$sessionId, $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', $_SERVER['HTTP_USER_AGENT'] ?? 'SSE Client']);
        $sessionDbId = $pdo->lastInsertId();
    } else {
        $sessionDbId = $session['id'];
    }
    
    echo "data: " . json_encode([
        'type' => 'connected',
        'message' => '最终SSE连接成功',
        'session_id' => $sessionId,
        'session_db_id' => $sessionDbId,
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    // 主循环 - 快速检查
    $startTime = time();
    $maxDuration = 300;
    $checkCount = 0;
    
    while (time() - $startTime < $maxDuration) {
        $checkCount++;
        
        try {
            // 检查新消息
            $stmt = $pdo->prepare("SELECT * FROM st_customer_service_messages WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') ORDER BY id ASC");
            $stmt->execute([$sessionDbId, $lastMessageId]);
            $newMessages = $stmt->fetchAll();
            
            if (!empty($newMessages)) {
                foreach ($newMessages as $message) {
                    echo "data: " . json_encode([
                        'type' => 'new_reply',
                        'message_id' => $message['id'],
                        'sender_type' => $message['sender_type'],
                        'message' => $message['message'],
                        'created_at' => $message['created_at'],
                        'timestamp' => time()
                    ]) . "\n\n";
                    
                    $lastMessageId = max($lastMessageId, $message['id']);
                }
                flush();
            }
            
            // 每100次检查发送心跳
            if ($checkCount % 100 == 0) {
                echo "data: " . json_encode([
                    'type' => 'heartbeat',
                    'message' => '心跳包',
                    'checks' => $checkCount,
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
            }
            
            if (connection_aborted()) break;
            
            // 100毫秒检查一次
            usleep(100000);
            
        } catch (Exception $e) {
            usleep(100000);
        }
    }
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'error',
        'message' => '连接失败: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
}
?>
