<?php
/**
 * 极速SSE - 0.1秒检查一次，最快响应！
 */

// 设置执行时间和内存限制
set_time_limit(0);
ini_set('memory_limit', '128M');

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');

// 禁用所有输出缓冲
while (ob_get_level()) {
    ob_end_clean();
}

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

$pdo = null;
$sessionDbId = null;

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 获取或创建会话
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        // 创建新会话
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
        $stmt->execute([
            $sessionId, 
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', 
            $_SERVER['HTTP_USER_AGENT'] ?? 'SSE Client'
        ]);
        $sessionDbId = $pdo->lastInsertId();
    } else {
        $sessionDbId = $session['id'];
    }
    
    // 发送连接成功消息
    echo "data: " . json_encode([
        'type' => 'connected',
        'message' => '极速SSE已连接！',
        'session_id' => $sessionId,
        'session_db_id' => $sessionDbId,
        'speed' => '0.1秒检查间隔',
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'error',
        'message' => '连接失败: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    exit;
}

// 极速主循环
$startTime = time();
$maxDuration = 300; // 5分钟
$checkCount = 0;

// 预编译SQL语句，提高查询速度
$messageStmt = $pdo->prepare("
    SELECT * FROM st_customer_service_messages 
    WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') 
    ORDER BY id ASC
");

while (time() - $startTime < $maxDuration) {
    $checkCount++;
    
    try {
        // 极速检查新消息
        $messageStmt->execute([$sessionDbId, $lastMessageId]);
        $newMessages = $messageStmt->fetchAll();
        
        if (!empty($newMessages)) {
            foreach ($newMessages as $message) {
                echo "data: " . json_encode([
                    'type' => 'new_reply',
                    'message_id' => $message['id'],
                    'sender_type' => $message['sender_type'],
                    'message' => $message['message'],
                    'created_at' => $message['created_at'],
                    'speed' => '极速响应',
                    'timestamp' => time()
                ]) . "\n\n";
                
                $lastMessageId = max($lastMessageId, $message['id']);
            }
            flush();
        }
        
        // 每100次检查（10秒）发送一次心跳
        if ($checkCount % 100 == 0) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'message' => '⚡ 极速心跳',
                'checks_per_second' => 10,
                'total_checks' => $checkCount,
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 检查客户端连接
        if (connection_aborted()) {
            break;
        }
        
        // 0.1秒间隔 = 100毫秒
        usleep(100000);
        
    } catch (Exception $e) {
        echo "data: " . json_encode([
            'type' => 'error',
            'message' => '查询错误: ' . $e->getMessage(),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        usleep(100000);
    }
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '极速SSE连接结束',
    'connection_time' => time() - $startTime,
    'total_checks' => $checkCount,
    'avg_checks_per_second' => round($checkCount / (time() - $startTime), 2),
    'timestamp' => time()
]) . "\n\n";
flush();
?>
