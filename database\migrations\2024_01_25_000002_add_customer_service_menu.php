<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddCustomerServiceMenu extends Migration
{
    public function up()
    {
        // 添加在线客服菜单
        $menuData = [
            [
                'level' => 1,
                'parent_id' => 0,
                'name' => '在线客服',
                'route_url' => '',
                'permissions' => '',
                'status' => 1,
                'sort' => 90,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // 插入一级菜单
        $parentId = DB::table('st_strongadmin_menu')->insertGetId($menuData[0]);

        // 添加二级菜单
        $subMenus = [
            [
                'level' => 2,
                'parent_id' => $parentId,
                'name' => '会话管理',
                'route_url' => 'customer-service/sessions',
                'permissions' => '',
                'status' => 1,
                'sort' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'level' => 2,
                'parent_id' => $parentId,
                'name' => 'AI规则管理',
                'route_url' => 'customer-service/ai-rules',
                'permissions' => '',
                'status' => 1,
                'sort' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'level' => 2,
                'parent_id' => $parentId,
                'name' => '统计报表',
                'route_url' => 'customer-service/statistics',
                'permissions' => '',
                'status' => 1,
                'sort' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // 插入二级菜单
        foreach ($subMenus as $menu) {
            DB::table('st_strongadmin_menu')->insert($menu);
        }
    }

    public function down()
    {
        // 删除在线客服相关菜单
        $parentMenu = DB::table('st_strongadmin_menu')
                       ->where('name', '在线客服')
                       ->where('level', 1)
                       ->first();

        if ($parentMenu) {
            // 删除子菜单
            DB::table('st_strongadmin_menu')
              ->where('parent_id', $parentMenu->id)
              ->delete();

            // 删除父菜单
            DB::table('st_strongadmin_menu')
              ->where('id', $parentMenu->id)
              ->delete();
        }
    }
}
