<?php
/**
 * 真正的SSE实现 - 连接数据库检查消息
 */

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 获取或创建会话
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        // 创建新会话
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
        $stmt->execute([$sessionId, $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown']);
        $sessionDbId = $pdo->lastInsertId();
        
        echo "data: " . json_encode([
            'type' => 'session_created',
            'message' => '新会话已创建',
            'session_db_id' => $sessionDbId,
            'session_id' => $sessionId
        ]) . "\n\n";
        flush();
    } else {
        $sessionDbId = $session['id'];
    }
    
    // 发送连接成功消息
    echo "data: " . json_encode([
        'type' => 'connected',
        'message' => '真实SSE连接成功',
        'session_id' => $sessionId,
        'session_db_id' => $sessionDbId,
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    // 消息检查循环
    $checkInterval = 2; // 2秒检查一次
    $maxDuration = 300; // 最大连接时间5分钟
    $startTime = time();
    
    while (time() - $startTime < $maxDuration) {
        try {
            // 检查新的回复消息（AI或管理员回复）
            $stmt = $pdo->prepare("
                SELECT * FROM st_customer_service_messages 
                WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') 
                ORDER BY id ASC
            ");
            $stmt->execute([$sessionDbId, $lastMessageId]);
            $newMessages = $stmt->fetchAll();
            
            if (!empty($newMessages)) {
                foreach ($newMessages as $message) {
                    echo "data: " . json_encode([
                        'type' => 'new_reply',
                        'message_id' => $message['id'],
                        'sender_type' => $message['sender_type'],
                        'message' => $message['message'],
                        'created_at' => $message['created_at'],
                        'timestamp' => time()
                    ]) . "\n\n";
                    
                    $lastMessageId = max($lastMessageId, $message['id']);
                }
                flush();
            }
            
            // 发送心跳包
            if (time() % 30 == 0) {
                echo "data: " . json_encode([
                    'type' => 'heartbeat',
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
            }
            
            // 检查连接是否还活着
            if (connection_aborted()) {
                break;
            }
            
            sleep($checkInterval);
            
        } catch (Exception $e) {
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => 'Stream error: ' . $e->getMessage()
            ]) . "\n\n";
            flush();
            break;
        }
    }
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'error',
        'message' => 'Database connection error: ' . $e->getMessage()
    ]) . "\n\n";
    flush();
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => 'SSE连接结束',
    'timestamp' => time()
]) . "\n\n";
flush();
?>
