<?php
/**
 * 基础SSE测试 - 不依赖数据库
 */

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 禁用输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '基础SSE连接成功！',
    'session_id' => $sessionId,
    'last_message_id' => $lastMessageId,
    'timestamp' => time(),
    'server_time' => date('Y-m-d H:i:s')
]) . "\n\n";
flush();

// 主循环
$checkInterval = 2; // 2秒检查一次
$maxDuration = 60; // 最大连接时间1分钟（测试用）
$startTime = time();
$heartbeatCounter = 0;

echo "data: " . json_encode([
    'type' => 'loop_start',
    'message' => '开始基础消息循环',
    'max_duration' => $maxDuration,
    'timestamp' => time()
]) . "\n\n";
flush();

while (time() - $startTime < $maxDuration) {
    $heartbeatCounter++;
    
    // 每5次循环（10秒）发送心跳包
    if ($heartbeatCounter % 5 == 0) {
        echo "data: " . json_encode([
            'type' => 'heartbeat',
            'message' => '💓 心跳包 #' . ($heartbeatCounter / 5),
            'connection_time' => time() - $startTime,
            'memory_usage' => memory_get_usage(true),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
    // 每10次循环（20秒）发送模拟消息
    if ($heartbeatCounter % 10 == 0) {
        echo "data: " . json_encode([
            'type' => 'new_reply',
            'message_id' => $lastMessageId + $heartbeatCounter,
            'sender_type' => 'admin',
            'message' => '模拟管理员回复 #' . ($heartbeatCounter / 10) . ' - ' . date('H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
    // 每次循环发送调试信息
    if ($heartbeatCounter % 1 == 0) {
        echo "data: " . json_encode([
            'type' => 'debug',
            'message' => '循环 #' . $heartbeatCounter . ' - 连接正常',
            'timestamp' => time()
        ]) . "\n\n";
        flush();
    }
    
    // 检查连接是否还活着
    if (connection_aborted()) {
        echo "data: " . json_encode([
            'type' => 'connection_aborted',
            'message' => '客户端连接已断开',
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        break;
    }
    
    sleep($checkInterval);
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '基础SSE连接正常结束',
    'connection_time' => time() - $startTime,
    'total_loops' => $heartbeatCounter,
    'timestamp' => time()
]) . "\n\n";
flush();
?>
