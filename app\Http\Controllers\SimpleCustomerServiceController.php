<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\BrowserDetector;

class SimpleCustomerServiceController extends Controller
{
    // 发送消息
    public function sendMessage(Request $request)
    {
        try {
            // 获取真实IP
            $realIp = $this->getRealClientIp($request);

            // 记录请求日志
            \Log::info('Customer service message received', [
                'session_id' => $request->input('session_id'),
                'message' => $request->input('message'),
                'ip' => $realIp,
                'user_agent' => $request->userAgent()
            ]);

            $sessionId = $request->input('session_id');
            $message = $request->input('message');
            $visitorName = $request->input('visitor_name'); // 可选的访客姓名
            $visitorEmail = $request->input('visitor_email'); // 可选的访客邮箱

            if (!$sessionId || !$message) {
                return response()->json(['error' => 'Missing required fields'], 400);
            }

            // 获取或生成用户ID
            $userId = auth()->id(); // 如果用户已登录，获取用户ID

            // 如果没有登录，为匿名访客生成一个唯一的数字ID
            if (!$userId) {
                // 生成一个大的数字ID，避免与真实用户ID冲突
                // 使用时间戳 + 随机数确保唯一性，范围：900000000 - 999999999
                $timestamp = time();
                $random = mt_rand(10000, 99999);
                $userId = 900000000 + ($timestamp % 90000000) + $random;
            }

            // 获取或创建会话
            $session = DB::table('customer_service_sessions')->where('session_id', $sessionId)->first();

            if (!$session) {
                // 获取真实IP
                $realIp = $this->getRealClientIp($request);

                // 如果没有提供访客姓名，为匿名访客生成一个
                if (!$visitorName && !auth()->id()) {
                    $randomNum = substr(md5($sessionId . $realIp), 0, 6);
                    $visitorName = '访客-' . strtoupper($randomNum);
                }

                // 获取地理位置信息
                $geoInfo = $this->getGeoLocation($realIp);

                // 解析浏览器信息
                $browserInfo = BrowserDetector::parse($request->userAgent());

                $sessionDbId = DB::table('customer_service_sessions')->insertGetId([
                    'session_id' => $sessionId,
                    'user_id' => $userId, // 设置用户ID（数字）
                    'visitor_name' => $visitorName, // 设置访客姓名
                    'visitor_email' => $visitorEmail, // 设置访客邮箱
                    'visitor_ip' => $realIp,
                    'user_agent' => $request->userAgent(),
                    'browser_name' => $browserInfo['browser'],
                    'browser_version' => $browserInfo['version'],
                    'platform_name' => $browserInfo['platform'],
                    'device_type' => $browserInfo['device'],
                    'is_mobile' => $browserInfo['is_mobile'] ? 1 : 0,
                    'status' => 'online', // 新会话默认在线
                    'last_seen' => now(),
                    'last_activity' => now(),
                    'geo_country' => $geoInfo['country'],
                    'geo_region' => $geoInfo['region'],
                    'geo_city' => $geoInfo['city'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } else {
                $sessionDbId = $session->id;

                // 准备更新数据
                $updateData = [
                    'status' => 'online',
                    'last_seen' => now(),
                    'last_activity' => now(),
                    'user_id' => $userId ?: $session->user_id
                ];

                // 如果提供了新的用户信息，更新它们
                if ($visitorName && $visitorName !== $session->visitor_name) {
                    $updateData['visitor_name'] = $visitorName;
                }
                if ($visitorEmail && $visitorEmail !== $session->visitor_email) {
                    $updateData['visitor_email'] = $visitorEmail;
                }

                // 更新现有会话
                DB::table('customer_service_sessions')
                  ->where('id', $sessionDbId)
                  ->update($updateData);

                \Log::info("更新现有会话: sessionId={$sessionId}, dbId={$sessionDbId}, 用户={$visitorName}");
            }

            // 保存客户消息（默认未读）
            DB::table('customer_service_messages')->insert([
                'session_id' => $sessionDbId,
                'sender_type' => 'customer',
                'sender_id' => $userId, // 设置发送者ID（如果已登录）
                'message' => $message,
                'is_read' => 0, // 新消息默认未读
                'read_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // 更新会话的最后活动时间（保持当前在线状态）
            DB::table('customer_service_sessions')
              ->where('id', $sessionDbId)
              ->update([
                  'last_activity' => now(),
                  'last_seen' => now() // 发送消息表示用户在线
              ]);

            // 检查AI自动回复
            $aiReply = $this->getAiReply($message);
            
            if ($aiReply) {
                DB::table('customer_service_messages')->insert([
                    'session_id' => $sessionDbId,
                    'sender_type' => 'ai',
                    'message' => $aiReply['message'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // 更新规则使用次数
                DB::table('ai_auto_reply_rules')->where('id', $aiReply['rule_id'])->increment('usage_count');
            }

            // 更新会话活动时间
            DB::table('customer_service_sessions')->where('id', $sessionDbId)->update([
                'last_activity' => now(),
                'updated_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'ai_reply' => $aiReply ? $aiReply['message'] : null,
                'session_id' => $sessionId
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
    }

    // AI自动回复
    private function getAiReply($message)
    {
        $rules = DB::table('ai_auto_reply_rules')
                   ->where('is_active', 1)
                   ->orderBy('priority', 'desc')
                   ->get();

        $message = strtolower($message);

        foreach ($rules as $rule) {
            $keywords = json_decode($rule->keywords, true);
            
            foreach ($keywords as $keyword) {
                if (strpos($message, strtolower($keyword)) !== false) {
                    return [
                        'message' => $rule->reply_message,
                        'rule_id' => $rule->id
                    ];
                }
            }
        }

        return null;
    }

    // 获取在线状态
    public function getOnlineStatus()
    {
        return response()->json([
            'online' => true,
            'message' => 'Customer service is online'
        ]);
    }

    // 初始化会话
    public function initSession(Request $request)
    {
        // 从请求中获取前台生成的sessionId，如果没有则生成新的
        $sessionId = $request->input('session_id');
        if (!$sessionId) {
            $sessionId = 'session_' . time() . '_' . rand(1000, 9999);
        }

        \Log::info("initSession: 收到sessionId={$sessionId}");

        try {
            // 检查是否已存在该会话
            $existingSession = DB::table('customer_service_sessions')
                                ->where('session_id', $sessionId)
                                ->first();

            if ($existingSession) {
                \Log::info("initSession: 会话已存在，更新状态");
                // 会话已存在，更新状态和时间
                DB::table('customer_service_sessions')
                  ->where('session_id', $sessionId)
                  ->update([
                      'status' => 'online',
                      'last_seen' => now(),
                      'last_activity' => now(),
                      'updated_at' => now(),
                  ]);

                return response()->json([
                    'success' => true,
                    'session_id' => $sessionId,
                    'visitor_name' => $existingSession->visitor_name,
                    'message' => '会话已存在，状态已更新'
                ]);
            }

            // 获取用户信息
            $visitorName = $request->input('visitor_name');
            $visitorEmail = $request->input('visitor_email');
            $userId = auth()->id();

            // 获取真实IP
            $realIp = $this->getRealClientIp($request);

            // 如果没有提供访客姓名，生成一个
            if (!$visitorName && !$userId) {
                $randomNum = substr(md5($sessionId . $realIp), 0, 6);
                $visitorName = '访客-' . strtoupper($randomNum);
            }

            // 获取地理位置信息
            $geoInfo = $this->getGeoLocation($realIp);

            DB::table('customer_service_sessions')->insert([
                'session_id' => $sessionId,
                'user_id' => $userId,
                'visitor_name' => $visitorName,
                'visitor_email' => $visitorEmail,
                'visitor_ip' => $realIp,
                'geo_country' => $geoInfo['country'] ?? null,
                'geo_region' => $geoInfo['region'] ?? null,
                'geo_city' => $geoInfo['city'] ?? null,
                'status' => 'online',
                'last_seen' => now(),
                'last_activity' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            \Log::info("会话初始化成功: sessionId={$sessionId}, 用户={$visitorName}");

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'visitor_name' => $visitorName
            ]);

        } catch (\Exception $e) {
            \Log::error('会话初始化失败: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create session: ' . $e->getMessage()], 500);
        }
    }

    // 获取系统状态（前台API）
    public function getSystemStatus()
    {
        try {
            // 首先尝试从设置表获取（Laravel会自动添加st_前缀）
            $enabled = DB::table('customer_service_settings')
                        ->where('setting_key', 'system_enabled')
                        ->value('setting_value');

            // 如果设置表没有数据，尝试从配置表获取
            if ($enabled === null) {
                $enabled = DB::table('customer_service_config')
                            ->where('config_key', 'system_enabled')
                            ->value('config_value');
            }

            return response()->json([
                'success' => true,
                'enabled' => $enabled === '1'
            ]);

        } catch (\Exception $e) {
            // 如果配置表不存在或出错，默认禁用（更安全）
            return response()->json([
                'success' => true,
                'enabled' => false
            ]);
        }
    }

    // 前台SSE消息流 - 接收客服回复
    public function messageStream(Request $request)
    {
        $sessionId = $request->input('session_id');
        if (!$sessionId) {
            return response('Session ID required', 400);
        }

        // 获取会话数据库ID
        $session = DB::table('customer_service_sessions')->where('session_id', $sessionId)->first();
        if (!$session) {
            return response('Session not found', 404);
        }

        $sessionDbId = $session->id;

        // 设置SSE响应头
        $response = response()->stream(function () use ($sessionDbId, $request) {
            set_time_limit(0);

            $lastMessageId = $request->input('last_message_id', 0);
            $checkInterval = 2; // 2秒检查一次
            $maxDuration = 300; // 最大连接时间5分钟
            $startTime = time();

            while (time() - $startTime < $maxDuration) {
                try {
                    // 检查该会话的新回复消息（AI或管理员回复）
                    $newMessages = DB::table('customer_service_messages')
                                    ->where('session_id', $sessionDbId)
                                    ->where('id', '>', $lastMessageId)
                                    ->whereIn('sender_type', ['ai', 'admin'])
                                    ->orderBy('id', 'asc')
                                    ->get();

                    if ($newMessages->count() > 0) {
                        foreach ($newMessages as $message) {
                            $data = [
                                'type' => 'new_reply',
                                'message_id' => $message->id,
                                'sender_type' => $message->sender_type,
                                'message' => $message->message,
                                'created_at' => $message->created_at,
                                'timestamp' => time()
                            ];

                            echo "data: " . json_encode($data) . "\n\n";
                            $lastMessageId = $message->id;
                        }

                        // 刷新输出缓冲区
                        if (ob_get_level()) {
                            ob_flush();
                        }
                        flush();
                    }

                    // 发送心跳包
                    if (time() % 30 == 0) {
                        echo "data: " . json_encode(['type' => 'heartbeat', 'timestamp' => time()]) . "\n\n";
                        if (ob_get_level()) {
                            ob_flush();
                        }
                        flush();
                    }

                    // 检查连接是否还活着
                    if (connection_aborted()) {
                        break;
                    }

                    sleep($checkInterval);

                } catch (\Exception $e) {
                    \Log::error('Frontend SSE Stream error: ' . $e->getMessage());
                    echo "data: " . json_encode(['type' => 'error', 'message' => 'Stream error']) . "\n\n";
                    break;
                }
            }

        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
        ]);

        return $response;
    }

    /**
     * 处理客户心跳检测
     */
    public function heartbeat(Request $request)
    {
        try {
            // 处理不同的Content-Type (sendBeacon可能发送blob)
            $input = $request->all();
            $rawContent = $request->getContent();

            if (empty($input) && $rawContent) {
                $input = json_decode($rawContent, true) ?: [];
            }

            $sessionId = $input['session_id'] ?? $request->input('session_id');
            $status = $input['status'] ?? $request->input('status', 'online');

            $realIp = $this->getRealClientIp($request);
            \Log::info("收到心跳: sessionId={$sessionId}, status={$status}, IP={$realIp}, ContentType=" . $request->header('Content-Type') . ", RawContent=" . substr($rawContent, 0, 100));

            if (!$sessionId) {
                return response()->json(['error' => 'Missing session_id'], 400);
            }

            // 检查会话是否存在
            $session = DB::table('customer_service_sessions')->where('session_id', $sessionId)->first();
            if (!$session) {
                \Log::warning("心跳失败: 会话不存在 sessionId={$sessionId}");
                return response()->json(['error' => 'Session not found'], 404);
            }

            // 更新会话状态和最后在线时间
            $updated = DB::table('customer_service_sessions')
                        ->where('session_id', $sessionId)
                        ->update([
                            'status' => $status,
                            'last_seen' => now(),
                            'last_activity' => now()
                        ]);

            \Log::info("心跳处理完成: sessionId={$sessionId}, 更新了{$updated}行, 当前状态={$status}");

            return response()->json(['success' => true, 'updated' => $updated]);

        } catch (\Exception $e) {
            \Log::error('Heartbeat error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * 图片请求方式的心跳 (备选方案)
     */
    public function heartbeatImg(Request $request)
    {
        try {
            $sessionId = $request->input('session_id');
            $status = $request->input('status', 'offline');

            \Log::info("收到图片心跳: sessionId={$sessionId}, status={$status}");

            if ($sessionId) {
                DB::table('customer_service_sessions')
                  ->where('session_id', $sessionId)
                  ->update([
                      'status' => $status,
                      'last_seen' => now(),
                      'last_activity' => now()
                  ]);
            }

            // 返回1x1透明图片
            $img = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
            return response($img)->header('Content-Type', 'image/gif');

        } catch (\Exception $e) {
            \Log::error('Heartbeat img error: ' . $e->getMessage());
            // 即使出错也返回图片
            $img = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
            return response($img)->header('Content-Type', 'image/gif');
        }
    }

    /**
     * 获取真实客户端IP
     */
    private function getRealClientIp($request)
    {
        // 检查各种可能的IP头
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // 代理服务器
            'HTTP_X_FORWARDED',          // 代理服务器
            'HTTP_X_CLUSTER_CLIENT_IP',  // 集群
            'HTTP_FORWARDED_FOR',        // 代理服务器
            'HTTP_FORWARDED',            // 代理服务器
            'HTTP_CLIENT_IP',            // 代理服务器
            'REMOTE_ADDR'                // 标准方法
        ];

        foreach ($ipHeaders as $header) {
            $ip = $request->server($header);
            if (!empty($ip) && $ip !== 'unknown') {
                // 如果有多个IP，取第一个
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }

                // 验证IP格式并排除内网IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    \Log::info("获取到真实IP: {$ip} (来源: {$header})");
                    return $ip;
                }
            }
        }

        // 如果都获取不到，使用Laravel默认方法
        $defaultIp = $request->ip();

        // 如果是本地IP，尝试获取外网IP
        if (in_array($defaultIp, ['127.0.0.1', '::1', 'localhost']) || strpos($defaultIp, '192.168.') === 0) {
            $externalIp = $this->getExternalIp();
            if ($externalIp) {
                \Log::info("本地环境，使用外网IP: {$externalIp}");
                return $externalIp;
            }
        }

        \Log::info("使用默认IP: {$defaultIp}");
        return $defaultIp;
    }

    /**
     * 获取服务器外网IP（用于本地开发环境）
     */
    private function getExternalIp()
    {
        try {
            // 尝试多个IP查询服务
            $services = [
                'https://api.ipify.org',
                'https://icanhazip.com',
                'https://ipecho.net/plain'
            ];

            foreach ($services as $service) {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 3,
                        'user_agent' => 'Mozilla/5.0 (compatible; CustomerService/1.0)'
                    ]
                ]);

                $ip = @file_get_contents($service, false, $context);
                if ($ip && filter_var(trim($ip), FILTER_VALIDATE_IP)) {
                    return trim($ip);
                }
            }
        } catch (\Exception $e) {
            \Log::warning('获取外网IP失败: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * 获取IP地理位置信息
     */
    private function getGeoLocation($ip)
    {
        try {
            // 跳过本地IP
            if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0) {
                return [
                    'country' => '本地',
                    'region' => '本地',
                    'city' => '本地'
                ];
            }

            // 使用免费的IP地理位置API
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5, // 5秒超时
                    'method' => 'GET',
                    'header' => 'User-Agent: Mozilla/5.0'
                ]
            ]);

            $response = file_get_contents("http://ip-api.com/json/{$ip}?lang=zh-CN", false, $context);
            $data = json_decode($response, true);

            if ($data && $data['status'] === 'success') {
                return [
                    'country' => $data['country'] ?? '未知',
                    'region' => $data['regionName'] ?? '未知',
                    'city' => $data['city'] ?? '未知'
                ];
            }
        } catch (\Exception $e) {
            \Log::error('GeoLocation error: ' . $e->getMessage());
        }

        return [
            'country' => '未知',
            'region' => '未知',
            'city' => '未知'
        ];
    }

    /**
     * 获取客服系统状态
     */
    public function getStatus(Request $request)
    {
        try {
            return response()->json([
                'success' => true,
                'enabled' => true,
                'available' => true,
                'message' => '客服系统可用'
            ]);
        } catch (\Exception $e) {
            \Log::error('Get status error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'enabled' => false,
                'available' => false,
                'message' => '客服系统不可用'
            ], 500);
        }
    }

    /**
     * 获取消息列表
     */
    public function getMessages(Request $request, $sessionId)
    {
        try {
            $session = DB::table('customer_service_sessions')
                        ->where('session_id', $sessionId)
                        ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'messages' => []
                ]);
            }

            $messages = DB::table('customer_service_messages')
                         ->where('session_id', $session->id)
                         ->orderBy('created_at', 'asc')
                         ->get();

            return response()->json([
                'success' => true,
                'messages' => $messages
            ]);

        } catch (\Exception $e) {
            \Log::error('Get messages error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'messages' => []
            ], 500);
        }
    }
}
