@extends('strongadmin.layouts.app')

@section('title', '工作时间管理')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <h2><i class="layui-icon layui-icon-time"></i> 工作时间管理</h2>
    </div>
    <div class="layui-card-body">
        
        <!-- 当前状态显示 -->
        <div class="layui-row layui-col-space15" style="margin-bottom: 20px;">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-survey"></i> 当前状态
                    </div>
                    <div class="layui-card-body">
                        <div class="st-status-display">
                            <div class="st-status-indicator {{ $statusInfo['is_working'] ? 'online' : 'offline' }}">
                                <i class="layui-icon {{ $statusInfo['is_working'] ? 'layui-icon-ok-circle' : 'layui-icon-close-circle' }}"></i>
                                <span>{{ $statusInfo['is_working'] ? '在线' : '离线' }}</span>
                            </div>
                            <div class="st-status-info">
                                <p><strong>当前时间:</strong> {{ $statusInfo['current_time'] }}</p>
                                <p><strong>时区:</strong> {{ $statusInfo['timezone'] }}</p>
                                <p><strong>工作模式:</strong> {{ $statusInfo['mode'] === 'auto' ? '自动判断' : '手动控制' }}</p>
                                <p><strong>状态说明:</strong> {{ $statusInfo['message'] }}</p>
                                @if(!$statusInfo['is_working'] && isset($statusInfo['next_working_time']))
                                    <p><strong>下次工作时间:</strong> {{ $statusInfo['next_working_time'] }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-set"></i> 快速操作
                    </div>
                    <div class="layui-card-body">
                        @if($statusInfo['mode'] === 'manual')
                            <div class="st-manual-controls">
                                <p>手动控制模式下，您可以直接设置在线状态：</p>
                                <div class="layui-btn-group">
                                    <button class="layui-btn layui-btn-normal" onclick="setManualStatus(true)">
                                        <i class="layui-icon layui-icon-ok-circle"></i> 设为在线
                                    </button>
                                    <button class="layui-btn layui-btn-primary" onclick="setManualStatus(false)">
                                        <i class="layui-icon layui-icon-close-circle"></i> 设为离线
                                    </button>
                                </div>
                            </div>
                        @else
                            <div class="st-auto-info">
                                <p>自动判断模式下，系统会根据设定的工作时间自动控制在线状态。</p>
                                <p>如需手动控制，请在设置中切换到手动模式。</p>
                                <a href="{{ route('strongadmin.customer-service.settings') }}" class="layui-btn layui-btn-normal">
                                    <i class="layui-icon layui-icon-set"></i> 修改设置
                                </a>
                            </div>
                        @endif
                        
                        <div style="margin-top: 15px;">
                            <button class="layui-btn layui-btn-sm" onclick="refreshStatus()">
                                <i class="layui-icon layui-icon-refresh"></i> 刷新状态
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工作时间设置概览 -->
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-list"></i> 工作时间设置概览
                <div class="layui-btn-group" style="float: right;">
                    <a href="{{ route('strongadmin.customer-service.settings') }}" class="layui-btn layui-btn-sm layui-btn-normal">
                        <i class="layui-icon layui-icon-edit"></i> 编辑设置
                    </a>
                </div>
            </div>
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    @php
                        $settingGroups = [
                            '基本设置' => ['working_hours_enabled', 'working_hours_mode', 'working_timezone'],
                            '工作时间' => ['working_start_time', 'working_end_time', 'working_days'],
                            '午休时间' => ['lunch_break_enabled', 'lunch_start_time', 'lunch_end_time'],
                            '离线设置' => ['offline_auto_message', 'offline_custom_message'],
                            '节假日' => ['holiday_mode_enabled', 'holiday_dates']
                        ];
                    @endphp
                    
                    @foreach($settingGroups as $groupName => $groupKeys)
                        <div class="layui-col-md6">
                            <div class="st-setting-group">
                                <h4>{{ $groupName }}</h4>
                                <table class="layui-table" lay-size="sm">
                                    <tbody>
                                        @foreach($groupKeys as $key)
                                            @php
                                                $setting = $settings->where('setting_key', $key)->first();
                                            @endphp
                                            @if($setting)
                                                <tr>
                                                    <td style="width: 40%;">{{ $setting->title }}</td>
                                                    <td>
                                                        @if($setting->setting_type === 'boolean')
                                                            <span class="layui-badge {{ $setting->setting_value == '1' ? 'layui-bg-green' : 'layui-bg-gray' }}">
                                                                {{ $setting->setting_value == '1' ? '启用' : '禁用' }}
                                                            </span>
                                                        @elseif($setting->setting_key === 'working_days')
                                                            @php
                                                                $days = json_decode($setting->setting_value, true) ?: [];
                                                                $dayNames = ['1' => '一', '2' => '二', '3' => '三', '4' => '四', '5' => '五', '6' => '六', '7' => '日'];
                                                                $dayLabels = array_map(function($day) use ($dayNames) {
                                                                    return '周' . ($dayNames[$day] ?? $day);
                                                                }, $days);
                                                            @endphp
                                                            {{ implode(', ', $dayLabels) }}
                                                        @elseif($setting->setting_key === 'holiday_dates')
                                                            @php
                                                                $dates = json_decode($setting->setting_value, true) ?: [];
                                                            @endphp
                                                            {{ count($dates) }} 个节假日
                                                        @else
                                                            {{ $setting->setting_value }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        
        <!-- 状态变更日志 -->
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-log"></i> 最近状态变更
            </div>
            <div class="layui-card-body">
                <div id="status-logs">
                    <p class="layui-text">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.st-status-display {
    text-align: center;
    padding: 20px;
}

.st-status-indicator {
    font-size: 24px;
    margin-bottom: 20px;
}

.st-status-indicator.online {
    color: #5FB878;
}

.st-status-indicator.offline {
    color: #FF5722;
}

.st-status-indicator i {
    font-size: 32px;
    margin-right: 10px;
}

.st-status-info {
    text-align: left;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
}

.st-status-info p {
    margin: 5px 0;
}

.st-manual-controls, .st-auto-info {
    text-align: center;
    padding: 20px;
}

.st-setting-group h4 {
    color: #333;
    border-bottom: 1px solid #e6e6e6;
    padding-bottom: 5px;
    margin-bottom: 10px;
}

.st-color-preview {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    border: 1px solid #ddd;
    margin-left: 10px;
    vertical-align: middle;
}
</style>
@endsection

@section('scripts')
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 设置手动状态
    window.setManualStatus = function(isOnline) {
        layer.confirm(isOnline ? '确定设置为在线状态？' : '确定设置为离线状态？', function(index){
            $.ajax({
                url: '{{ route("strongadmin.customer-service.set-manual-status") }}',
                type: 'POST',
                data: {
                    is_online: isOnline,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if(response.success) {
                        layer.msg(response.message, {icon: 1});
                        setTimeout(function(){
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg(response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('操作失败，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    };
    
    // 刷新状态
    window.refreshStatus = function() {
        layer.load();
        $.ajax({
            url: '{{ route("strongadmin.customer-service.working-status") }}',
            type: 'GET',
            success: function(response) {
                layer.closeAll();
                if(response.success) {
                    layer.msg('状态已刷新', {icon: 1});
                    setTimeout(function(){
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg('刷新失败', {icon: 2});
                }
            },
            error: function() {
                layer.closeAll();
                layer.msg('刷新失败，请重试', {icon: 2});
            }
        });
    };
    
    // 自动刷新状态（每5分钟）
    setInterval(function(){
        $.ajax({
            url: '{{ route("strongadmin.customer-service.working-status") }}',
            type: 'GET',
            success: function(response) {
                if(response.success) {
                    // 静默更新状态显示
                    console.log('状态已更新:', response.data);
                }
            }
        });
    }, 300000); // 5分钟
});
</script>
@endsection
