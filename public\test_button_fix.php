<?php
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 测试按钮遮挡修复</h1>";

echo "<div style='background: #f8f8f8; padding: 25px; margin: 20px 0;'>";
echo "<h2>📊 当前状态</h2>";

$now = new DateTime();
$currentTime = $now->format('H:i');
$dayOfWeek = $now->format('N');

echo "<p><strong>当前时间:</strong> " . $now->format('Y-m-d H:i:s') . " (星期" . ['', '一', '二', '三', '四', '五', '六', '日'][$dayOfWeek] . ")</p>";

$isWorkingDay = $dayOfWeek >= 1 && $dayOfWeek <= 5;
$isWorkingHours = $currentTime >= '09:00' && $currentTime <= '18:00';

echo "<p><strong>是否工作日:</strong> " . ($isWorkingDay ? '✅ 是' : '❌ 否') . "</p>";
echo "<p><strong>是否工作时间:</strong> " . ($isWorkingHours ? '✅ 是' : '❌ 否') . "</p>";

if (!$isWorkingDay || !$isWorkingHours) {
    echo "<div style='background: #000; color: #fff; padding: 20px; text-align: center; font-weight: 300; letter-spacing: 1px;'>";
    echo "当前是非工作时间，应该显示修复后的离线表单";
    echo "</div>";
} else {
    echo "<div style='background: #fff; color: #000; padding: 20px; text-align: center; font-weight: 300; letter-spacing: 1px;'>";
    echo "当前是工作时间，应该显示聊天界面";
    echo "</div>";
}
echo "</div>";

echo "<div style='background: #fff; padding: 25px; margin: 20px 0;'>";
echo "<h2>🔧 修复内容</h2>";
echo "<ul>";
echo "<li>✅ <strong>Flexbox布局</strong>：使用flex布局替代绝对定位</li>";
echo "<li>✅ <strong>头部固定</strong>：头部区域 flex-shrink:0，不会被压缩</li>";
echo "<li>✅ <strong>内容可滚动</strong>：中间内容区域 flex:1，可以滚动</li>";
echo "<li>✅ <strong>按钮固定底部</strong>：底部按钮 flex-shrink:0，固定在最底部</li>";
echo "<li>✅ <strong>防止遮挡</strong>：表单内容添加底部间距，确保不被遮挡</li>";
echo "<li>✅ <strong>优化间距</strong>：调整各区域的内边距，更加紧凑</li>";
echo "<li>✅ <strong>滚动体验</strong>：内容超出时可以平滑滚动</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f8f8; padding: 25px; margin: 20px 0;'>";
echo "<h2>🧪 测试重点</h2>";
echo "<ol>";
echo "<li><strong>打开首页</strong>：<a href='/' target='_blank' style='background: #000; color: #fff; padding: 12px 24px; text-decoration: none; letter-spacing: 1px;'>点击打开首页</a></li>";
echo "<li><strong>点击客服按钮</strong>：右下角的客服按钮</li>";
echo "<li><strong>验证布局修复</strong>：";
echo "<ul style='margin: 10px 0;'>";
if (!$isWorkingDay || !$isWorkingHours) {
    echo "<li>✅ 应该直接显示修复后的离线表单</li>";
    echo "<li>✅ 头部应该固定在顶部</li>";
    echo "<li>✅ 按钮应该固定在最底部</li>";
    echo "<li>✅ 中间内容区域应该可以滚动</li>";
    echo "<li>✅ 输入框不应该被按钮遮挡</li>";
} else {
    echo "<li>✅ 应该显示聊天界面</li>";
    echo "<li>✅ 可以点击📝按钮测试修复后的表单</li>";
}
echo "</ul>";
echo "</li>";
echo "<li><strong>测试滚动功能</strong>：";
echo "<ul>";
echo "<li>填写所有字段</li>";
echo "<li>尝试滚动查看所有内容</li>";
echo "<li>确认最后一个输入框完全可见</li>";
echo "<li>确认按钮始终在底部</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>测试交互</strong>：";
echo "<ul>";
echo "<li>点击各个输入框</li>";
echo "<li>输入内容测试</li>";
echo "<li>点击提交按钮</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff; padding: 25px; margin: 20px 0;'>";
echo "<h2>📐 布局结构</h2>";
echo "<div style='border: 2px solid #000; max-width: 350px; margin: 20px auto; background: #f9f9f9;'>";
echo "<div style='background: #000; color: #fff; padding: 10px; text-align: center; font-size: 12px;'>";
echo "<strong>头部区域 (flex-shrink: 0)</strong><br>";
echo "客服离线留言<br>";
echo "请留下联系方式，我们会尽快回复您";
echo "</div>";
echo "<div style='background: #fff; padding: 15px; height: 150px; overflow-y: auto; border-top: 1px solid #ccc; border-bottom: 1px solid #ccc;'>";
echo "<div style='font-size: 11px; color: #666; margin-bottom: 5px;'><strong>内容区域 (flex: 1, 可滚动)</strong></div>";
echo "<div style='margin-bottom: 8px;'>";
echo "<div style='font-size: 10px; margin-bottom: 2px;'>姓名 *</div>";
echo "<div style='border-bottom: 1px solid #e0e0e0; padding: 3px 0; font-size: 10px; color: #999;'>请输入姓名</div>";
echo "</div>";
echo "<div style='margin-bottom: 8px;'>";
echo "<div style='font-size: 10px; margin-bottom: 2px;'>邮箱</div>";
echo "<div style='border-bottom: 1px solid #e0e0e0; padding: 3px 0; font-size: 10px; color: #999;'>请输入邮箱</div>";
echo "</div>";
echo "<div style='margin-bottom: 8px;'>";
echo "<div style='font-size: 10px; margin-bottom: 2px;'>电话</div>";
echo "<div style='border-bottom: 1px solid #e0e0e0; padding: 3px 0; font-size: 10px; color: #999;'>请输入电话</div>";
echo "</div>";
echo "<div style='margin-bottom: 8px;'>";
echo "<div style='font-size: 10px; margin-bottom: 2px;'>留言内容 *</div>";
echo "<div style='border-bottom: 1px solid #e0e0e0; padding: 8px 0; font-size: 10px; color: #999;'>请详细描述您的问题...</div>";
echo "</div>";
echo "<div style='height: 20px; background: #f0f0f0; text-align: center; font-size: 9px; color: #999; line-height: 20px;'>底部间距防遮挡</div>";
echo "</div>";
echo "<div style='background: #000; color: #fff; padding: 10px; text-align: center; font-size: 12px;'>";
echo "<strong>按钮区域 (flex-shrink: 0)</strong><br>";
echo "<div style='background: #fff; color: #000; padding: 5px; margin-top: 5px;'>提交留言</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 25px; margin: 20px 0;'>";
echo "<h2>✅ 预期效果</h2>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 30px;'>";
echo "<div>";
echo "<h4>🎯 布局特点</h4>";
echo "<ul>";
echo "<li>头部固定不动</li>";
echo "<li>内容区域可滚动</li>";
echo "<li>按钮固定底部</li>";
echo "<li>无内容遮挡</li>";
echo "<li>流畅滚动体验</li>";
echo "</ul>";
echo "</div>";
echo "<div>";
echo "<h4>🔧 技术实现</h4>";
echo "<ul>";
echo "<li>Flexbox布局</li>";
echo "<li>flex-direction: column</li>";
echo "<li>flex: 1 (内容区)</li>";
echo "<li>flex-shrink: 0 (固定区)</li>";
echo "<li>overflow-y: auto</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 25px; margin: 20px 0;'>";
echo "<h2>⚠️ 注意事项</h2>";
echo "<ul>";
echo "<li><strong>浏览器兼容性</strong>：Flexbox在现代浏览器中支持良好</li>";
echo "<li><strong>滚动行为</strong>：内容超出时会出现滚动条</li>";
echo "<li><strong>触摸设备</strong>：移动设备上滚动体验更佳</li>";
echo "<li><strong>内容长度</strong>：无论内容多少，布局都保持稳定</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h2>🔗 相关链接</h2>";
echo "<a href='/' target='_blank' style='background: #000; color: #fff; padding: 15px 30px; text-decoration: none; margin: 10px; display: inline-block; letter-spacing: 1px; font-weight: 300;'>首页测试</a>";
echo "<a href='/strongadmin/customer-service/settings' target='_blank' style='background: #fff; color: #000; padding: 15px 30px; text-decoration: none; margin: 10px; display: inline-block; letter-spacing: 1px; font-weight: 300; border: 1px solid #000;'>客服设置</a>";
echo "</div>";

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #000;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
}

h1, h2 {
    color: #000;
    font-weight: 300;
    letter-spacing: 1px;
}

h4 {
    color: #000;
    margin-bottom: 15px;
    font-weight: 400;
}

a:hover {
    opacity: 0.8;
    transition: all 0.3s ease;
}

li {
    margin: 8px 0;
}

strong {
    font-weight: 500;
}
</style>
