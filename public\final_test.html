<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - 持久化客服系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 5px 10px; border-radius: 15px; font-size: 12px; margin: 5px; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; border: 1px solid #ddd; }
        .chat-box { height: 200px; border: 1px solid #ddd; padding: 10px; overflow-y: auto; background: #fafafa; margin: 10px 0; }
        .message { margin: 5px 0; padding: 8px; border-radius: 8px; }
        .message.customer { background: #e3f2fd; text-align: right; }
        .message.admin { background: #f3e5f5; text-align: left; }
        .message.ai { background: #e8f5e8; text-align: left; }
        input[type="text"] { width: 70%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 最终测试 - 持久化客服系统</h1>
        
        <div class="panel">
            <h2>👤 用户信息</h2>
            <div class="grid">
                <div>
                    <div>用户ID: <span id="user-id"></span></div>
                    <div>设备指纹: <span id="device-fingerprint"></span></div>
                    <div>会话ID: <span id="session-id"></span></div>
                </div>
                <div>
                    <div>SSE状态: <span id="sse-status" class="status offline">未连接</span></div>
                    <div>消息数量: <span id="message-count">0</span></div>
                    <button class="btn" onclick="resetUser()">重置用户</button>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <h2>💬 聊天测试</h2>
            <div class="grid">
                <div>
                    <h4>聊天窗口</h4>
                    <div id="chat-messages" class="chat-box"></div>
                    <div>
                        <input type="text" id="message-input" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                        <button class="btn" onclick="sendMessage()">发送</button>
                    </div>
                </div>
                <div>
                    <h4>连接控制</h4>
                    <button class="btn" onclick="connectSSE()">连接SSE</button>
                    <button class="btn" onclick="disconnectSSE()">断开SSE</button>
                    <button class="btn success" onclick="loadHistory()">加载历史</button>
                    <button class="btn danger" onclick="clearHistory()">清除历史</button>
                    <div id="connection-log" class="log"></div>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <h2>🧪 功能测试</h2>
            <div class="grid">
                <div>
                    <h4>基础功能</h4>
                    <button class="btn" onclick="testPersistence()">测试持久化</button>
                    <button class="btn" onclick="testSSEConnection()">测试SSE连接</button>
                    <button class="btn" onclick="testMessageSaving()">测试消息保存</button>
                </div>
                <div>
                    <h4>高级功能</h4>
                    <button class="btn" onclick="simulateRefresh()">模拟刷新</button>
                    <button class="btn" onclick="testMultipleMessages()">批量消息测试</button>
                    <button class="btn" onclick="showUserDetails()">显示用户详情</button>
                </div>
            </div>
            <div id="test-results" class="log"></div>
        </div>
        
        <div class="panel">
            <h2>🔗 相关链接</h2>
            <a href="/" target="_blank" class="btn">前台首页</a>
            <a href="/test_sse_simple.html" target="_blank" class="btn">SSE测试</a>
            <a href="/strongadmin/customer-service/sessions" target="_blank" class="btn">后台管理</a>
        </div>
    </div>

    <script>
    // 全局变量
    let persistentUserId = null;
    let deviceFingerprint = null;
    let sessionId = null;
    let eventSource = null;
    let lastMessageId = 0;
    
    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeUser();
        logTest('🚀 系统初始化完成');
    });
    
    // 生成设备指纹
    function generateDeviceFingerprint() {
        const fingerprint = {
            screen: screen.width + 'x' + screen.height,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            platform: navigator.platform
        };
        
        const fingerprintString = JSON.stringify(fingerprint);
        let hash = 0;
        for (let i = 0; i < fingerprintString.length; i++) {
            const char = fingerprintString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        
        return 'fp_' + Math.abs(hash).toString(36);
    }
    
    // 初始化用户
    function initializeUser() {
        // 获取或创建用户ID
        persistentUserId = localStorage.getItem('customer_service_user_id');
        if (!persistentUserId) {
            deviceFingerprint = generateDeviceFingerprint();
            persistentUserId = 'user_' + deviceFingerprint + '_' + Date.now();
            localStorage.setItem('customer_service_user_id', persistentUserId);
            localStorage.setItem('device_fingerprint', deviceFingerprint);
        } else {
            deviceFingerprint = localStorage.getItem('device_fingerprint') || generateDeviceFingerprint();
        }
        
        // 获取或创建会话ID
        sessionId = localStorage.getItem('customer_service_session_id');
        if (!sessionId) {
            sessionId = 'session_' + persistentUserId + '_' + Date.now();
            localStorage.setItem('customer_service_session_id', sessionId);
        }
        
        // 更新UI
        document.getElementById('user-id').textContent = persistentUserId;
        document.getElementById('device-fingerprint').textContent = deviceFingerprint;
        document.getElementById('session-id').textContent = sessionId;
        
        // 加载历史消息
        loadHistory();
        
        // 自动连接SSE
        setTimeout(connectSSE, 1000);
    }
    
    // 连接SSE
    function connectSSE() {
        if (eventSource) {
            eventSource.close();
        }
        
        const url = `/working-sse?session_id=${sessionId}&last_message_id=${lastMessageId}`;
        logConnection('🔄 连接SSE: ' + url);
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            logConnection('✅ SSE连接成功');
            updateSSEStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                logConnection('📨 收到SSE消息: ' + data.type);
                
                if (data.type === 'new_reply') {
                    addMessage(data.sender_type, data.message);
                    lastMessageId = Math.max(lastMessageId, data.message_id);
                } else if (data.type === 'connected') {
                    logConnection('🎉 SSE连接确认');
                } else if (data.type === 'heartbeat') {
                    logConnection('💓 心跳包');
                }
            } catch (e) {
                logConnection('❌ 解析消息失败: ' + e.message);
            }
        };
        
        eventSource.onerror = function() {
            logConnection('❌ SSE连接错误');
            updateSSEStatus('连接错误', 'offline');
        };
    }
    
    // 断开SSE
    function disconnectSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            logConnection('🛑 SSE连接已断开');
            updateSSEStatus('已断开', 'offline');
        }
    }
    
    // 发送消息
    function sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        addMessage('customer', message);
        input.value = '';
        
        // 这里可以添加发送到服务器的逻辑
        logTest('📤 消息已发送: ' + message);
    }
    
    // 添加消息
    function addMessage(sender, message) {
        const chatDiv = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender;
        messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${new Date().toLocaleTimeString()})</small>`;
        chatDiv.appendChild(messageDiv);
        chatDiv.scrollTop = chatDiv.scrollHeight;
        
        // 保存到本地存储
        saveMessageToLocal(sender, message);
        updateMessageCount();
    }
    
    // 保存消息到本地
    function saveMessageToLocal(sender, message) {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        messages.push({
            sender: sender,
            message: message,
            timestamp: new Date().toISOString()
        });
        
        if (messages.length > 50) {
            messages.splice(0, messages.length - 50);
        }
        
        localStorage.setItem('chat_messages_' + sessionId, JSON.stringify(messages));
    }
    
    // 加载历史消息
    function loadHistory() {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        const chatDiv = document.getElementById('chat-messages');
        chatDiv.innerHTML = '';
        
        messages.forEach(msg => {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + msg.sender;
            messageDiv.innerHTML = `<strong>${msg.sender}:</strong> ${msg.message} <small>(${new Date(msg.timestamp).toLocaleTimeString()})</small>`;
            chatDiv.appendChild(messageDiv);
        });
        
        chatDiv.scrollTop = chatDiv.scrollHeight;
        updateMessageCount();
        logTest('📚 加载了 ' + messages.length + ' 条历史消息');
    }
    
    // 清除历史
    function clearHistory() {
        localStorage.removeItem('chat_messages_' + sessionId);
        document.getElementById('chat-messages').innerHTML = '';
        updateMessageCount();
        logTest('🗑️ 历史消息已清除');
    }
    
    // 重置用户
    function resetUser() {
        if (confirm('确定要重置用户吗？')) {
            localStorage.clear();
            location.reload();
        }
    }
    
    // 更新消息数量
    function updateMessageCount() {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        document.getElementById('message-count').textContent = messages.length;
    }
    
    // 更新SSE状态
    function updateSSEStatus(text, className) {
        const statusElement = document.getElementById('sse-status');
        statusElement.textContent = text;
        statusElement.className = 'status ' + className;
    }
    
    // 测试函数
    function testPersistence() {
        logTest('🧪 测试持久化功能...');
        logTest('✅ 用户ID: ' + persistentUserId);
        logTest('✅ 设备指纹: ' + deviceFingerprint);
        logTest('✅ 会话ID: ' + sessionId);
    }
    
    function testSSEConnection() {
        logTest('🧪 测试SSE连接...');
        connectSSE();
    }
    
    function testMessageSaving() {
        logTest('🧪 测试消息保存...');
        addMessage('test', '测试消息 ' + Date.now());
    }
    
    function simulateRefresh() {
        logTest('🧪 模拟页面刷新...');
        setTimeout(() => location.reload(), 1000);
    }
    
    function testMultipleMessages() {
        logTest('🧪 批量消息测试...');
        for (let i = 1; i <= 5; i++) {
            setTimeout(() => {
                addMessage('test', `批量测试消息 ${i}`);
            }, i * 500);
        }
    }
    
    function showUserDetails() {
        const details = {
            userId: persistentUserId,
            deviceFingerprint: deviceFingerprint,
            sessionId: sessionId,
            messageCount: JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]').length,
            createdAt: localStorage.getItem('created_at')
        };
        alert('用户详情:\n' + JSON.stringify(details, null, 2));
    }
    
    // 处理回车键
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }
    
    // 日志函数
    function logConnection(message) {
        const logDiv = document.getElementById('connection-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function logTest(message) {
        const logDiv = document.getElementById('test-results');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    </script>
</body>
</html>
