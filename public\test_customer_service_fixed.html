<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统测试 - 修复版</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            border-radius: 8px; 
            margin: 10px 0; 
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .test-btn:hover { 
            background: #0056b3; 
            transform: translateY(-2px);
        }
        .log { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            font-family: 'Courier New', monospace; 
            font-size: 13px; 
            max-height: 400px; 
            overflow-y: auto; 
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服系统测试 - 修复版</h1>
        <p>测试前端客服系统的显示和功能</p>
        
        <div style="margin-bottom: 20px;">
            <button class="test-btn" onclick="testInitialization()">测试初始化</button>
            <button class="test-btn" onclick="testAPIs()">测试API</button>
            <button class="test-btn" onclick="simulatePageLoad()">模拟页面加载</button>
            <button class="test-btn" onclick="forceShow()">强制显示客服</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status-display">
            <div class="info">🚀 准备就绪，点击按钮开始测试</div>
        </div>
        
        <div id="log" class="log">
            <div style="color: #28a745; font-weight: bold;">📋 测试日志:</div>
        </div>
    </div>

    <!-- 客服组件 -->
    <div id="customer-service-widget" style="display: none;">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                    </div>
                    <button onclick="toggleChat()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;">×</button>
                </div>
            </div>
            <div id="cs-messages" style="flex:1;padding:20px;overflow-y:auto;max-height:360px;background:#fafafa;">
                <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                    <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);">
                        <div style="font-weight:600;margin-bottom:5px;">👋 欢迎咨询</div>
                        <div>我们随时为您提供帮助！</div>
                    </div>
                </div>
            </div>
            <div style="padding:15px;border-top:1px solid #eee;background:white;">
                <div style="display:flex;gap:10px;align-items:center;">
                    <input type="text" id="cs-message-input" placeholder="请输入您的问题..." style="flex:1;padding:12px;border:1px solid #ddd;border-radius:25px;outline:none;font-size:14px;">
                    <button onclick="sendMessage()" style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border:none;width:40px;height:40px;border-radius:50%;cursor:pointer;">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let csSettings = {};
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            logDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div style="color: #28a745; font-weight: bold;">📋 测试日志:</div>';
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        async function testAPIs() {
            log('🔍 开始测试API...', 'info');
            
            try {
                // 测试状态API
                const statusResponse = await fetch('/api/customer-service/status');
                const statusData = await statusResponse.json();
                log('✅ 状态API响应: ' + JSON.stringify(statusData), 'success');
                
                // 测试设置API
                const settingsResponse = await fetch('/api/customer-service/get-settings.php');
                const settingsData = await settingsResponse.json();
                log('✅ 设置API响应: ' + (settingsData.success ? '成功' : '失败'), settingsData.success ? 'success' : 'error');
                
                if (settingsData.success) {
                    csSettings = settingsData.data;
                    log('📊 系统启用状态: ' + csSettings.system_enabled, 'info');
                    updateStatus('✅ API测试通过，系统状态正常', 'success');
                } else {
                    updateStatus('❌ API测试失败', 'error');
                }
                
            } catch (error) {
                log('❌ API测试失败: ' + error.message, 'error');
                updateStatus('❌ API连接失败: ' + error.message, 'error');
            }
        }

        async function testInitialization() {
            log('🚀 开始测试初始化流程...', 'info');
            
            // 模拟修复后的初始化逻辑
            await loadCustomerServiceSettings();
            
            if (!csSettings || !csSettings.system_enabled) {
                log('❌ 客服系统已禁用或设置加载失败', 'error');
                updateStatus('❌ 客服系统初始化失败', 'error');
                return;
            }
            
            log('✅ 客服系统已启用，继续初始化', 'success');
            checkCustomerServiceStatus();
        }

        async function loadCustomerServiceSettings() {
            try {
                log('📡 正在加载客服设置...', 'info');
                const response = await fetch('/api/customer-service/get-settings.php');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();

                if (data.success) {
                    csSettings = data.data;
                    log('✅ 客服设置加载成功', 'success');
                } else {
                    log('❌ 加载客服设置失败: ' + data.message, 'error');
                    csSettings = getDefaultSettings();
                }
            } catch (error) {
                log('💥 加载客服设置异常: ' + error.message, 'error');
                csSettings = getDefaultSettings();
            }
        }

        function checkCustomerServiceStatus() {
            fetch('/api/customer-service/status')
                .then(response => response.json())
                .then(data => {
                    log('客服系统状态: ' + JSON.stringify(data), 'info');
                    if (data.success && data.enabled) {
                        document.getElementById('customer-service-widget').style.display = 'block';
                        log('✅ 客服系统已启用并显示', 'success');
                        updateStatus('✅ 客服系统正常运行，右下角应该可以看到客服按钮', 'success');
                    } else {
                        document.getElementById('customer-service-widget').style.display = 'none';
                        log('❌ 客服系统已禁用', 'warning');
                        updateStatus('⚠️ 客服系统被禁用', 'warning');
                    }
                })
                .catch(error => {
                    log('❌ 检查客服系统状态失败: ' + error.message, 'error');
                    document.getElementById('customer-service-widget').style.display = 'block';
                    updateStatus('⚠️ 状态检查失败，但强制显示客服', 'warning');
                });
        }

        function getDefaultSettings() {
            return {
                system_enabled: true,
                welcome_message: '您好！欢迎咨询，我们将竭诚为您服务！'
            };
        }

        function simulatePageLoad() {
            log('🔄 模拟页面加载事件...', 'info');
            testInitialization();
        }

        function forceShow() {
            log('🔧 强制显示客服组件...', 'warning');
            document.getElementById('customer-service-widget').style.display = 'block';
            updateStatus('🔧 客服组件已强制显示', 'warning');
        }

        function toggleChat() {
            const chatWindow = document.getElementById('cs-chat-window');
            if (chatWindow.style.display === 'none' || !chatWindow.style.display) {
                chatWindow.style.display = 'block';
                log('💬 聊天窗口已打开', 'info');
            } else {
                chatWindow.style.display = 'none';
                log('💬 聊天窗口已关闭', 'info');
            }
        }

        function sendMessage() {
            const input = document.getElementById('cs-message-input');
            const message = input.value.trim();
            if (message) {
                log('📤 发送消息: ' + message, 'info');
                input.value = '';
            }
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成，开始自动测试', 'info');
            setTimeout(testAPIs, 500);
        });
    </script>
</body>
</html>
