@extends('admin.layouts.app')

@section('title', '在线客服会话')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comments"></i> 在线客服会话
                    </h3>
                </div>

                <!-- 统计卡片 -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-comments"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总会话数</span>
                                    <span class="info-box-number">{{ $stats['total'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">活跃会话</span>
                                    <span class="info-box-number">{{ $stats['active'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">等待中</span>
                                    <span class="info-box-number">{{ $stats['waiting'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-secondary">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已关闭</span>
                                    <span class="info-box-number">{{ $stats['closed'] }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选和搜索 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="form-inline">
                                <div class="form-group mr-2">
                                    <select name="status" class="form-control" onchange="this.form.submit()">
                                        <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>所有状态</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>活跃</option>
                                        <option value="waiting" {{ request('status') == 'waiting' ? 'selected' : '' }}>等待中</option>
                                        <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>已关闭</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="form-inline float-right">
                                <input type="hidden" name="status" value="{{ request('status') }}">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="搜索会话..." value="{{ request('search') }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 会话列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>会话ID</th>
                                    <th>客户信息</th>
                                    <th>最新消息</th>
                                    <th>分配客服</th>
                                    <th>状态</th>
                                    <th>最后活动</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sessions as $session)
                                <tr>
                                    <td>
                                        <code>{{ $session->session_id }}</code>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $session->display_name }}</strong>
                                            @if($session->user)
                                                <span class="badge badge-primary">注册用户</span>
                                            @else
                                                <span class="badge badge-secondary">访客</span>
                                            @endif
                                        </div>
                                        <small class="text-muted">{{ $session->contact_info }}</small>
                                    </td>
                                    <td>
                                        @if($session->latestMessage)
                                            <div class="text-truncate" style="max-width: 200px;">
                                                {{ $session->latestMessage->message }}
                                            </div>
                                            <small class="text-muted">
                                                {{ $session->latestMessage->created_at->diffForHumans() }}
                                            </small>
                                        @else
                                            <span class="text-muted">暂无消息</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($session->assignedAdmin)
                                            <span class="badge badge-success">{{ $session->assignedAdmin->name }}</span>
                                        @else
                                            <span class="badge badge-warning">未分配</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($session->status == 'active')
                                            <span class="badge badge-success">活跃</span>
                                        @elseif($session->status == 'waiting')
                                            <span class="badge badge-warning">等待中</span>
                                        @else
                                            <span class="badge badge-secondary">已关闭</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $session->last_activity ? $session->last_activity->diffForHumans() : '无' }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.customer-service.session', $session->id) }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        @if($session->unreadMessagesCount() > 0)
                                            <span class="badge badge-danger">{{ $session->unreadMessagesCount() }}</span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>暂无客服会话</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="d-flex justify-content-center">
                        {{ $sessions->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-box {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.badge {
    font-size: 0.75em;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
@endsection
