<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>持久化用户客服系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .chat-panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .user-info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .messages { height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #fafafa; }
        .message { margin: 10px 0; padding: 8px; border-radius: 8px; }
        .message.customer { background: #e1f5fe; text-align: right; }
        .message.admin { background: #f3e5f5; text-align: left; }
        .message.ai { background: #e8f5e8; text-align: left; }
        .input-area { display: flex; gap: 10px; margin-top: 10px; }
        .input-area input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .status { padding: 5px 10px; border-radius: 15px; font-size: 12px; margin: 5px; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💬 持久化用户客服系统</h1>
        
        <div class="user-info">
            <h3>👤 用户信息</h3>
            <div>用户ID: <span id="user-id"></span></div>
            <div>设备指纹: <span id="device-fingerprint"></span></div>
            <div>会话ID: <span id="session-id"></span></div>
            <div>连接状态: <span id="connection-status" class="status offline">未连接</span></div>
            <button class="btn" onclick="resetUser()">重置用户</button>
            <button class="btn" onclick="showUserInfo()">显示详细信息</button>
        </div>
        
        <div class="chat-panel">
            <h3>💬 聊天窗口</h3>
            <div id="messages" class="messages"></div>
            <div class="input-area">
                <input type="text" id="message-input" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                <button class="btn" onclick="sendMessage()">发送</button>
                <button class="btn" onclick="connectSSE()">连接SSE</button>
                <button class="btn" onclick="disconnectSSE()">断开连接</button>
                <button class="btn" onclick="startPolling()">开始轮询</button>
                <button class="btn" onclick="stopPolling()">停止轮询</button>
            </div>
        </div>
        
        <div class="chat-panel">
            <h3>📊 系统日志</h3>
            <div id="system-log" class="log"></div>
        </div>
    </div>

    <script>
    // 全局变量
    let persistentUserId = null;
    let deviceFingerprint = null;
    let sessionId = null;
    let eventSource = null;
    let lastMessageId = 0;
    let pollingInterval = null;
    
    // 生成设备指纹
    function generateDeviceFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = {
            screen: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            platform: navigator.platform,
            userAgent: navigator.userAgent.substring(0, 100),
            canvas: canvas.toDataURL().substring(0, 50),
            memory: navigator.deviceMemory || 'unknown',
            cores: navigator.hardwareConcurrency || 'unknown'
        };
        
        // 生成哈希
        const fingerprintString = JSON.stringify(fingerprint);
        let hash = 0;
        for (let i = 0; i < fingerprintString.length; i++) {
            const char = fingerprintString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return 'fp_' + Math.abs(hash).toString(36);
    }
    
    // 获取或创建持久化用户ID
    function getOrCreatePersistentUserId() {
        // 1. 尝试从localStorage获取
        let userId = localStorage.getItem('customer_service_user_id');
        
        if (!userId) {
            // 2. 生成设备指纹
            deviceFingerprint = generateDeviceFingerprint();
            
            // 3. 尝试从服务器获取基于设备指纹的用户ID
            // 这里可以调用API检查是否已有该设备的用户记录
            
            // 4. 如果没有，创建新的用户ID
            userId = 'user_' + deviceFingerprint + '_' + Date.now();
            
            // 5. 保存到localStorage
            localStorage.setItem('customer_service_user_id', userId);
            localStorage.setItem('device_fingerprint', deviceFingerprint);
            localStorage.setItem('created_at', new Date().toISOString());
        } else {
            // 获取已保存的设备指纹
            deviceFingerprint = localStorage.getItem('device_fingerprint') || generateDeviceFingerprint();
        }
        
        return userId;
    }
    
    // 获取或创建会话ID
    function getOrCreateSessionId() {
        let sessionId = localStorage.getItem('customer_service_session_id');
        
        if (!sessionId) {
            sessionId = 'session_' + persistentUserId + '_' + Date.now();
            localStorage.setItem('customer_service_session_id', sessionId);
        }
        
        return sessionId;
    }
    
    // 初始化用户
    function initializeUser() {
        persistentUserId = getOrCreatePersistentUserId();
        sessionId = getOrCreateSessionId();
        
        document.getElementById('user-id').textContent = persistentUserId;
        document.getElementById('device-fingerprint').textContent = deviceFingerprint;
        document.getElementById('session-id').textContent = sessionId;
        
        log('✅ 用户初始化完成');
        log('👤 用户ID: ' + persistentUserId);
        log('🔍 设备指纹: ' + deviceFingerprint);
        log('💬 会话ID: ' + sessionId);
        
        // 加载历史消息
        loadHistoryMessages();
    }
    
    // 加载历史消息
    function loadHistoryMessages() {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        const messagesDiv = document.getElementById('messages');
        
        messages.forEach(msg => {
            addMessageToUI(msg.type, msg.content, msg.timestamp);
        });
        
        if (messages.length > 0) {
            log('📚 加载了 ' + messages.length + ' 条历史消息');
        }
    }
    
    // 保存消息到本地存储
    function saveMessageToLocal(type, content) {
        const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
        messages.push({
            type: type,
            content: content,
            timestamp: new Date().toISOString()
        });
        
        // 只保留最近100条消息
        if (messages.length > 100) {
            messages.splice(0, messages.length - 100);
        }
        
        localStorage.setItem('chat_messages_' + sessionId, JSON.stringify(messages));
    }
    
    // 添加消息到UI
    function addMessageToUI(type, content, timestamp) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + type;
        
        const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
        messageDiv.innerHTML = `<div>${content}</div><small>${time}</small>`;
        
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
    
    // 发送消息
    function sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // 添加到UI
        addMessageToUI('customer', message);
        saveMessageToLocal('customer', message);
        
        // 发送到服务器
        fetch('/customer-service/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                session_id: sessionId,
                message: message,
                user_id: persistentUserId,
                device_fingerprint: deviceFingerprint
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                log('✅ 消息发送成功');
                if (data.ai_reply) {
                    addMessageToUI('ai', data.ai_reply);
                    saveMessageToLocal('ai', data.ai_reply);
                }
            } else {
                log('❌ 发送失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            log('❌ 网络错误: ' + error.message);
        });
        
        input.value = '';
    }
    
    // 连接SSE
    function connectSSE() {
        if (eventSource) {
            eventSource.close();
        }
        
        const url = `/frontend-sse?session_id=${sessionId}&last_message_id=${lastMessageId}`;
        log('🔄 连接SSE: ' + url);
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            log('✅ SSE连接已建立');
            document.getElementById('connection-status').textContent = '已连接';
            document.getElementById('connection-status').className = 'status online';
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 收到SSE消息: ' + data.type);
                
                if (data.type === 'new_reply') {
                    addMessageToUI(data.sender_type, data.message);
                    saveMessageToLocal(data.sender_type, data.message);
                    lastMessageId = Math.max(lastMessageId, data.message_id);
                }
            } catch (e) {
                log('❌ 解析SSE消息失败: ' + e.message);
            }
        };
        
        eventSource.onerror = function() {
            log('❌ SSE连接错误');
            document.getElementById('connection-status').textContent = '连接错误';
            document.getElementById('connection-status').className = 'status offline';
        };
    }
    
    // 断开SSE
    function disconnectSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            log('🛑 SSE连接已断开');
            document.getElementById('connection-status').textContent = '已断开';
            document.getElementById('connection-status').className = 'status offline';
        }
    }
    
    // 重置用户
    function resetUser() {
        if (confirm('确定要重置用户吗？这将清除所有本地数据。')) {
            localStorage.removeItem('customer_service_user_id');
            localStorage.removeItem('customer_service_session_id');
            localStorage.removeItem('device_fingerprint');
            localStorage.removeItem('chat_messages_' + sessionId);
            location.reload();
        }
    }
    
    // 显示用户详细信息
    function showUserInfo() {
        const info = {
            userId: persistentUserId,
            deviceFingerprint: deviceFingerprint,
            sessionId: sessionId,
            createdAt: localStorage.getItem('created_at'),
            messageCount: JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]').length
        };
        
        alert('用户详细信息:\n' + JSON.stringify(info, null, 2));
    }
    
    // 处理回车键
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }
    
    // 日志函数
    function log(message) {
        const logDiv = document.getElementById('system-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    // AJAX轮询检查新消息
    function startPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        log('🔄 开始AJAX轮询检查新消息');
        document.getElementById('connection-status').textContent = '轮询中';
        document.getElementById('connection-status').className = 'status online';

        pollingInterval = setInterval(function() {
            // 这里可以调用API检查新消息
            // 暂时模拟收到消息
            if (Math.random() < 0.1) { // 10%概率收到消息
                const mockMessage = '模拟管理员回复 ' + new Date().toLocaleTimeString();
                addMessageToUI('admin', mockMessage);
                saveMessageToLocal('admin', mockMessage);
                log('📨 轮询收到新消息');
            }
        }, 3000); // 每3秒检查一次
    }

    // 停止轮询
    function stopPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
            log('🛑 AJAX轮询已停止');
            document.getElementById('connection-status').textContent = '已断开';
            document.getElementById('connection-status').className = 'status offline';
        }
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeUser();
        log('🚀 系统初始化完成');

        // 先尝试SSE，如果失败则使用轮询
        setTimeout(function() {
            connectSSE();

            // 5秒后如果SSE还没连接成功，自动切换到轮询
            setTimeout(function() {
                if (!eventSource || eventSource.readyState !== EventSource.OPEN) {
                    log('⚠️ SSE连接失败，切换到AJAX轮询');
                    startPolling();
                }
            }, 5000);
        }, 1000);
    });
    </script>
</body>
</html>
