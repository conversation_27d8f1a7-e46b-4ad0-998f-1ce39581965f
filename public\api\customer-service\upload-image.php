<?php
// Laravel环境初始化
require_once __DIR__ . '/../../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '只支持POST请求'
    ]);
    exit;
}

try {
    // 检查是否有文件上传
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('没有上传文件或上传失败');
    }

    $file = $_FILES['image'];
    $sessionId = $_POST['session_id'] ?? '';

    // 验证会话ID
    if (empty($sessionId)) {
        throw new Exception('缺少会话ID');
    }

    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedTypes) || !in_array($fileType, $allowedTypes)) {
        throw new Exception('不支持的文件类型，只支持 JPEG, PNG, GIF, WebP 格式');
    }

    // 验证文件大小 (最大5MB)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        throw new Exception('文件大小不能超过5MB');
    }

    // 创建上传目录 - 直接保存到public目录下
    $uploadDir = 'uploads/customer-service/images/' . date('Y/m/d');
    $fullUploadDir = __DIR__ . '/../../' . $uploadDir;

    if (!is_dir($fullUploadDir)) {
        if (!mkdir($fullUploadDir, 0755, true)) {
            throw new Exception('无法创建上传目录');
        }
    }

    // 生成唯一文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = Str::uuid() . '.' . strtolower($extension);
    $filePath = $fullUploadDir . '/' . $fileName;

    // 生成相对路径，避免域名问题
    $webPath = '/' . $uploadDir . '/' . $fileName;

    // 移动上传的文件
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('文件保存失败');
    }

    // 获取图片信息
    $imageInfo = getimagesize($filePath);
    $width = $imageInfo[0] ?? 0;
    $height = $imageInfo[1] ?? 0;

    // 记录到数据库
    $imageId = DB::table('customer_service_images')->insertGetId([
        'session_id' => $sessionId,
        'original_name' => $file['name'],
        'file_name' => $fileName,
        'file_path' => $webPath,
        'file_size' => $file['size'],
        'mime_type' => $mimeType,
        'width' => $width,
        'height' => $height,
        'uploaded_at' => now(),
        'created_at' => now(),
        'updated_at' => now()
    ]);

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '图片上传成功',
        'data' => [
            'image_id' => $imageId,
            'image_url' => $webPath,
            'original_name' => $file['name'],
            'file_size' => $file['size'],
            'width' => $width,
            'height' => $height
        ]
    ]);

} catch (Exception $e) {
    // 如果文件已经移动但数据库操作失败，删除文件
    if (isset($filePath) && file_exists($filePath)) {
        unlink($filePath);
    }
    
    error_log('客服图片上传失败: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
