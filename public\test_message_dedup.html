<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息去重测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .log { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 4px; 
            margin: 10px 0; 
            font-family: monospace; 
            font-size: 12px; 
            max-height: 300px; 
            overflow-y: auto; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
        }
        .test-btn:hover { background: #0056b3; }
        .message-count {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 消息去重测试</h1>
        <p>测试消息去重机制是否正常工作</p>
        
        <div class="message-count">
            <div>消息计数器: <span id="message-counter">0</span></div>
            <div>去重集合大小: <span id="dedup-size">0</span></div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <button class="test-btn" onclick="testDuplicateMessage()">测试重复消息</button>
            <button class="test-btn" onclick="testMessageDeduplication()">测试去重机制</button>
            <button class="test-btn" onclick="clearMessages()">清空消息</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log" class="log">
            <div class="info">📋 测试日志:</div>
        </div>
    </div>

    <!-- 客服组件 -->
    <div id="customer-service-widget" style="display: block;">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #000000 0%, #000000 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(0, 0, 0, 0.4);transition:all 0.3s ease;border:none;" onclick="toggleChat()">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:block;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <!-- 头部 -->
            <div style="background:linear-gradient(135deg, #000000 0%, #000000 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">消息去重测试 🔧</small>
                    </div>
                    <button onclick="toggleChat()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;">×</button>
                </div>
            </div>

            <!-- 消息区域 -->
            <div id="cs-messages" style="flex:1;padding:20px;overflow-y:auto;max-height:360px;background:#fafafa;">
                <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                    <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);margin-bottom:15px;">
                        <div style="font-weight:600;margin-bottom:5px;">🔧 消息去重测试</div>
                        <div>测试消息是否会重复显示</div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div style="padding:15px;border-top:1px solid #eee;background:white;">
                <div style="display:flex;gap:10px;align-items:center;">
                    <input type="text" id="cs-message-input" placeholder="请输入测试消息..." style="flex:1;padding:12px;border:1px solid #ddd;border-radius:25px;outline:none;font-size:14px;">
                    <button onclick="sendMessage()" style="background:linear-gradient(135deg, #000000 0%, #000000 100%);color:white;border:none;width:40px;height:40px;border-radius:50%;cursor:pointer;font-size:16px;">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟去重机制
        let processedMessageIds = new Set();
        let messageCounter = 0;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            logDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">📋 测试日志:</div>';
        }

        function updateCounters() {
            document.getElementById('message-counter').textContent = messageCounter;
            document.getElementById('dedup-size').textContent = processedMessageIds.size;
        }

        function addMessage(sender, message) {
            messageCounter++;
            
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '15px';
            messageDiv.style.display = 'flex';
            messageDiv.style.alignItems = 'flex-start';
            
            const isAdmin = sender === 'admin';
            const bgColor = isAdmin ? '#007bff' : '#28a745';
            const align = isAdmin ? 'flex-end' : 'flex-start';
            
            messageDiv.style.justifyContent = align;
            
            messageDiv.innerHTML = `
                <div style="max-width: 70%; padding: 12px 16px; border-radius: 18px; background: ${bgColor}; color: white; font-size: 14px; line-height: 1.4;">
                    <div style="font-weight: 600; margin-bottom: 4px;">${sender === 'admin' ? '客服' : '用户'}</div>
                    <div>${message}</div>
                    <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
            
            const messagesContainer = document.getElementById('cs-messages');
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            updateCounters();
            log(`添加消息: [${sender}] ${message}`, 'info');
        }

        function testDuplicateMessage() {
            log('🔄 测试重复消息...', 'info');
            
            const testMessage = '这是一条测试消息 - ' + Date.now();
            
            // 模拟多次收到同一条消息
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    log(`第${i+1}次尝试添加相同消息`, 'warning');
                    addMessage('admin', testMessage);
                }, i * 100);
            }
        }

        function testMessageDeduplication() {
            log('🛡️ 测试消息去重机制...', 'info');
            
            const testMessage = '去重测试消息 - ' + Date.now();
            const messageKey = `test_${testMessage}_${new Date().toISOString()}`;
            
            // 模拟带去重的消息处理
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    if (processedMessageIds.has(messageKey)) {
                        log(`第${i+1}次: 跳过重复消息`, 'success');
                        return;
                    }
                    
                    processedMessageIds.add(messageKey);
                    log(`第${i+1}次: 添加新消息`, 'info');
                    addMessage('admin', testMessage);
                    updateCounters();
                }, i * 100);
            }
        }

        function clearMessages() {
            log('🧹 清空消息...', 'info');
            
            const messagesContainer = document.getElementById('cs-messages');
            messagesContainer.innerHTML = `
                <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                    <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);margin-bottom:15px;">
                        <div style="font-weight:600;margin-bottom:5px;">🔧 消息去重测试</div>
                        <div>测试消息是否会重复显示</div>
                    </div>
                </div>
            `;
            
            messageCounter = 0;
            processedMessageIds.clear();
            updateCounters();
        }

        function toggleChat() {
            const chatWindow = document.getElementById('cs-chat-window');
            if (chatWindow.style.display === 'none') {
                chatWindow.style.display = 'block';
                log('💬 聊天窗口已打开', 'info');
            } else {
                chatWindow.style.display = 'none';
                log('💬 聊天窗口已关闭', 'info');
            }
        }

        function sendMessage() {
            const input = document.getElementById('cs-message-input');
            const message = input.value.trim();
            if (message) {
                addMessage('customer', message);
                input.value = '';
                
                // 模拟客服回复
                setTimeout(() => {
                    addMessage('admin', '收到您的消息: ' + message);
                }, 1000);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成，消息去重测试准备就绪', 'info');
            updateCounters();
        });
    </script>
</body>
</html>
