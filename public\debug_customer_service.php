<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .log { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服系统调试工具</h1>
        
        <div style="margin-bottom: 20px;">
            <button class="test-btn" onclick="testAPIs()">测试API</button>
            <button class="test-btn" onclick="testDOMElements()">测试DOM元素</button>
            <button class="test-btn" onclick="testJavaScript()">测试JavaScript</button>
            <button class="test-btn" onclick="forceShowWidget()">强制显示客服</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log" class="log">
            <div class="info">🚀 客服系统调试工具已启动</div>
        </div>
        
        <!-- 客服组件测试 -->
        <div id="customer-service-widget" style="display: none;">
            <!-- 客服按钮 -->
            <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;">
                <span id="cs-button-icon">💬</span>
            </div>

            <!-- 聊天窗口 -->
            <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
                <!-- 头部 -->
                <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                    <div style="display:flex;justify-content:space-between;align-items:center;">
                        <div>
                            <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                            <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                        </div>
                        <button onclick="toggleChatWindow()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;">×</button>
                    </div>
                    <div id="cs-status" style="position:absolute;bottom:8px;right:20px;font-size:12px;opacity:0.8;">
                        <span style="display:inline-block;width:8px;height:8px;background:#4ade80;border-radius:50%;margin-right:5px;"></span>在线
                    </div>
                </div>

                <!-- 消息区域 -->
                <div id="cs-messages" style="flex:1;padding:20px;overflow-y:auto;max-height:360px;background:#fafafa;">
                    <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                        <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);margin-bottom:15px;">
                            <div style="font-weight:600;margin-bottom:5px;">👋 欢迎咨询</div>
                            <div>我们随时为您提供帮助！</div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div style="padding:15px;border-top:1px solid #eee;background:white;">
                    <div style="display:flex;gap:10px;align-items:center;">
                        <input type="text" id="cs-message-input" placeholder="请输入您的问题..." style="flex:1;padding:12px;border:1px solid #ddd;border-radius:25px;outline:none;font-size:14px;">
                        <button id="cs-send-btn" style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border:none;width:40px;height:40px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:16px;">➤</button>
                    </div>
                    <div style="margin-top:8px;font-size:11px;color:#9ca3af;text-align:center;">按 Enter 快速发送</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">🚀 日志已清空</div>';
        }

        async function testAPIs() {
            log('🔍 开始测试API...', 'info');
            
            // 测试状态API
            try {
                const statusResponse = await fetch('/api/customer-service/status');
                const statusData = await statusResponse.json();
                log('✅ 状态API: ' + JSON.stringify(statusData), 'success');
            } catch (error) {
                log('❌ 状态API失败: ' + error.message, 'error');
            }

            // 测试设置API
            try {
                const settingsResponse = await fetch('/api/customer-service/get-settings.php');
                const settingsData = await settingsResponse.json();
                log('✅ 设置API: ' + (settingsData.success ? '成功' : '失败'), settingsData.success ? 'success' : 'error');
                if (settingsData.success) {
                    log('📊 系统启用状态: ' + settingsData.data.system_enabled, 'info');
                }
            } catch (error) {
                log('❌ 设置API失败: ' + error.message, 'error');
            }
        }

        function testDOMElements() {
            log('🔍 开始测试DOM元素...', 'info');
            
            const elements = [
                'customer-service-widget',
                'cs-chat-button', 
                'cs-chat-window',
                'cs-messages',
                'cs-message-input',
                'cs-send-btn'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    log(`✅ 元素 ${id} 存在`, 'success');
                    log(`   - 显示状态: ${getComputedStyle(element).display}`, 'info');
                } else {
                    log(`❌ 元素 ${id} 不存在`, 'error');
                }
            });
        }

        function testJavaScript() {
            log('🔍 开始测试JavaScript功能...', 'info');
            
            // 测试基本功能
            try {
                // 测试fetch
                if (typeof fetch !== 'undefined') {
                    log('✅ fetch API 可用', 'success');
                } else {
                    log('❌ fetch API 不可用', 'error');
                }

                // 测试Promise
                if (typeof Promise !== 'undefined') {
                    log('✅ Promise 可用', 'success');
                } else {
                    log('❌ Promise 不可用', 'error');
                }

                // 测试async/await
                (async function() {
                    log('✅ async/await 可用', 'success');
                })();

            } catch (error) {
                log('❌ JavaScript测试失败: ' + error.message, 'error');
            }
        }

        function forceShowWidget() {
            log('🔧 强制显示客服组件...', 'warning');
            
            const widget = document.getElementById('customer-service-widget');
            if (widget) {
                widget.style.display = 'block';
                log('✅ 客服组件已强制显示', 'success');
                
                // 绑定点击事件
                const chatButton = document.getElementById('cs-chat-button');
                if (chatButton) {
                    chatButton.onclick = function() {
                        toggleChatWindow();
                        log('💬 客服按钮被点击', 'info');
                    };
                }
            } else {
                log('❌ 找不到客服组件', 'error');
            }
        }

        function toggleChatWindow() {
            const chatWindow = document.getElementById('cs-chat-window');
            const chatButton = document.getElementById('cs-chat-button');
            
            if (chatWindow.style.display === 'none' || !chatWindow.style.display) {
                chatWindow.style.display = 'block';
                chatButton.style.display = 'none';
                log('📖 聊天窗口已打开', 'info');
            } else {
                chatWindow.style.display = 'none';
                chatButton.style.display = 'flex';
                log('📕 聊天窗口已关闭', 'info');
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成', 'info');
            setTimeout(() => {
                testAPIs();
                testDOMElements();
            }, 1000);
        });
    </script>
</body>
</html>
