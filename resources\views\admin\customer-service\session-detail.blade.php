@extends('admin.layouts.app')

@section('title', '客服会话详情')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- 返回按钮 -->
        <div class="col-12 mb-3">
            <a href="{{ route('admin.customer-service.sessions') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回会话列表
            </a>
        </div>

        <!-- 客户信息 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user"></i> 客户信息
                    </h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $session->user ? ($session->user->avatar ?? '/images/user-avatar.png') : '/images/user-avatar.png' }}" 
                             class="img-circle" width="80" height="80" alt="Avatar">
                        <h4 class="mt-2">{{ $session->display_name }}</h4>
                        @if($session->user)
                            <span class="badge badge-primary">注册用户</span>
                        @else
                            <span class="badge badge-secondary">访客</span>
                        @endif
                    </div>

                    <table class="table table-sm">
                        <tr>
                            <td><strong>会话ID:</strong></td>
                            <td><code>{{ $session->session_id }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>联系方式:</strong></td>
                            <td>{{ $session->contact_info }}</td>
                        </tr>
                        <tr>
                            <td><strong>IP地址:</strong></td>
                            <td>{{ $session->visitor_ip }}</td>
                        </tr>
                        <tr>
                            <td><strong>状态:</strong></td>
                            <td>
                                @if($session->status == 'active')
                                    <span class="badge badge-success">活跃</span>
                                @elseif($session->status == 'waiting')
                                    <span class="badge badge-warning">等待中</span>
                                @else
                                    <span class="badge badge-secondary">已关闭</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>分配客服:</strong></td>
                            <td>
                                @if($session->assignedAdmin)
                                    <span class="badge badge-success">{{ $session->assignedAdmin->name }}</span>
                                @else
                                    <span class="badge badge-warning">未分配</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>创建时间:</strong></td>
                            <td>{{ $session->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                        <tr>
                            <td><strong>最后活动:</strong></td>
                            <td>{{ $session->last_activity ? $session->last_activity->format('Y-m-d H:i:s') : '无' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- 聊天界面 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comments"></i> 聊天记录
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ $session->messages->count() }} 条消息</span>
                    </div>
                </div>

                <!-- 消息区域 -->
                <div class="card-body" style="height: 500px; overflow-y: auto;" id="messages-container">
                    @forelse($session->messages as $message)
                        <div class="message-item mb-3 {{ $message->sender_type == 'admin' ? 'admin-message' : 'customer-message' }}">
                            <div class="d-flex {{ $message->sender_type == 'admin' ? 'justify-content-end' : 'justify-content-start' }}">
                                <div class="message-content" style="max-width: 70%;">
                                    <div class="message-header mb-1">
                                        <small class="text-muted">
                                            @if($message->sender_type == 'admin')
                                                <i class="fas fa-user-tie"></i> {{ $message->senderAdmin ? $message->senderAdmin->name : '客服' }}
                                            @elseif($message->sender_type == 'ai')
                                                <i class="fas fa-robot"></i> AI助手
                                            @else
                                                <i class="fas fa-user"></i> {{ $session->display_name }}
                                            @endif
                                            <span class="ml-2">{{ $message->created_at->format('H:i:s') }}</span>
                                        </small>
                                    </div>
                                    <div class="message-bubble p-3 rounded {{ $message->sender_type == 'admin' ? 'bg-primary text-white' : ($message->sender_type == 'ai' ? 'bg-success text-white' : 'bg-light') }}">
                                        {{ $message->message }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center text-muted">
                            <i class="fas fa-comments fa-3x mb-3"></i>
                            <p>暂无聊天记录</p>
                        </div>
                    @endforelse
                </div>

                <!-- 发送消息 -->
                <div class="card-footer">
                    <form id="send-message-form" method="POST">
                        @csrf
                        <div class="input-group">
                            <input type="text" name="message" id="message-input" class="form-control" 
                                   placeholder="输入回复消息..." maxlength="1000" required>
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> 发送
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-item {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-bubble {
    border-radius: 18px !important;
    word-wrap: break-word;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.admin-message .message-bubble {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.customer-message .message-bubble {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

#messages-container {
    scroll-behavior: smooth;
}

.img-circle {
    border-radius: 50%;
    border: 3px solid #dee2e6;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 滚动到底部
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;

    // 发送消息
    const form = document.getElementById('send-message-form');
    const input = document.getElementById('message-input');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const message = input.value.trim();
        if (!message) return;

        // 禁用表单
        input.disabled = true;
        form.querySelector('button').disabled = true;

        // 发送请求
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: 'message=' + encodeURIComponent(message)
        })
        .then(response => response.text())
        .then(data => {
            // 重新加载页面显示新消息
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发送失败，请重试');
        })
        .finally(() => {
            // 恢复表单
            input.disabled = false;
            form.querySelector('button').disabled = false;
        });
    });

    // 回车发送
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            form.dispatchEvent(new Event('submit'));
        }
    });

    // 自动刷新消息（每10秒）
    setInterval(function() {
        window.location.reload();
    }, 10000);
});
</script>
@endsection
