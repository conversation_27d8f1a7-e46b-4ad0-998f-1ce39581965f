<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript语法调试器 v2</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
        .line-item {
            padding: 8px;
            margin: 3px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            border-left: 4px solid #ccc;
        }
        .line-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .line-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .line-ok {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .line-target {
            background: #e7f3ff;
            border-left-color: #007bff;
            color: #004085;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JavaScript语法调试器 v2</h1>
        <p>专门用于查找第2677行的 "missing ) after argument list" 错误</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="findLine2677()">定位第2677行</button>
            <button class="test-btn" onclick="checkSyntaxErrors()">检查语法错误</button>
            <button class="test-btn" onclick="testProblematicCode()">测试问题代码</button>
            <button class="test-btn danger" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>📋 JavaScript语法调试器 v2 已就绪</h3>
                <p>专门用于定位和修复第2677行的语法错误</p>
                <p><strong>错误信息:</strong> SyntaxError: missing ) after argument list (at 第2677行:第9列)</p>
            </div>
        </div>
    </div>

    <script>
        function updateDisplay(content, type = 'success') {
            const display = document.getElementById('result-display');
            const className = type === 'error' ? 'error-box' : 
                            type === 'warning' ? 'warning-box' : 'success-box';
            display.innerHTML = `<div class="${className}">${content}</div>`;
        }

        async function findLine2677() {
            updateDisplay('<h3>🔍 正在定位第2677行...</h3>', 'warning');
            
            try {
                const response = await fetch('/');
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                
                const pageContent = await response.text();
                const allLines = pageContent.split('\n');
                
                let result = '<h3>🎯 第2677行定位结果</h3>';
                result += '<p><strong>页面总行数:</strong> ' + allLines.length + '</p>';
                
                if (allLines.length >= 2677) {
                    const targetLine = allLines[2676]; // 数组索引从0开始
                    const startLine = Math.max(0, 2676 - 10);
                    const endLine = Math.min(allLines.length, 2676 + 10);
                    
                    result += '<h4>🔍 第2677行及其周围内容:</h4>';
                    
                    for (let i = startLine; i <= endLine; i++) {
                        const lineNum = i + 1;
                        const line = allLines[i] || '';
                        const isTarget = lineNum === 2677;
                        
                        let lineClass = 'line-ok';
                        let issues = [];
                        
                        if (isTarget) {
                            lineClass = 'line-target';
                            issues.push('这是错误报告的行！');
                        }
                        
                        // 检查常见的语法错误模式
                        if (line.includes('console.') && !line.includes(');')) {
                            lineClass = 'line-error';
                            issues.push('可能的console调用语法错误');
                        }
                        
                        if (line.includes('alert(') && !line.includes(');')) {
                            lineClass = 'line-error';
                            issues.push('可能的alert调用语法错误');
                        }
                        
                        if (line.includes('fetch(') && !line.includes(')')) {
                            lineClass = 'line-error';
                            issues.push('可能的fetch调用语法错误');
                        }
                        
                        // 检查括号匹配
                        const openParens = (line.match(/\(/g) || []).length;
                        const closeParens = (line.match(/\)/g) || []).length;
                        if (openParens > closeParens) {
                            lineClass = 'line-warning';
                            issues.push('缺少 ' + (openParens - closeParens) + ' 个右括号');
                        }
                        
                        result += '<div class="line-item ' + lineClass + '">';
                        result += '<strong>行 ' + lineNum + ':</strong> ' + (line.substring(0, 120) + (line.length > 120 ? '...' : ''));
                        if (issues.length > 0) {
                            result += '<br><span style="font-weight: bold;">问题: ' + issues.join(', ') + '</span>';
                        }
                        result += '</div>';
                    }
                    
                    // 特别分析第2677行
                    result += '<div style="background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 15px 0;">';
                    result += '<h4>🔬 第2677行详细分析</h4>';
                    result += '<p><strong>内容:</strong> ' + targetLine + '</p>';
                    result += '<p><strong>长度:</strong> ' + targetLine.length + ' 字符</p>';
                    result += '<p><strong>左括号数量:</strong> ' + (targetLine.match(/\(/g) || []).length + '</p>';
                    result += '<p><strong>右括号数量:</strong> ' + (targetLine.match(/\)/g) || []).length + '</p>';
                    result += '<p><strong>包含script标签:</strong> ' + (targetLine.includes('<script') ? '是' : '否') + '</p>';
                    result += '<p><strong>包含console:</strong> ' + (targetLine.includes('console.') ? '是' : '否') + '</p>';
                    result += '<p><strong>包含alert:</strong> ' + (targetLine.includes('alert(') ? '是' : '否') + '</p>';
                    result += '</div>';
                    
                } else {
                    result += '<div style="background: #f8d7da; padding: 15px; border-radius: 4px;">';
                    result += '<h4>❌ 第2677行不存在</h4>';
                    result += '<p>页面只有 ' + allLines.length + ' 行，但错误报告在第2677行。</p>';
                    result += '<p>这可能表示错误来自:</p>';
                    result += '<ul>';
                    result += '<li>动态生成的JavaScript代码</li>';
                    result += '<li>外部脚本文件</li>';
                    result += '<li>浏览器内部处理的代码</li>';
                    result += '</ul>';
                    result += '</div>';
                }
                
                updateDisplay(result, allLines.length >= 2677 ? 'warning' : 'error');
                
            } catch (error) {
                updateDisplay('<h3>❌ 定位过程出错</h3><p><strong>错误:</strong> ' + error.message + '</p>', 'error');
            }
        }

        function checkSyntaxErrors() {
            updateDisplay('<h3>🔍 检查常见语法错误...</h3>', 'warning');
            
            // 测试一些可能导致 "missing ) after argument list" 的代码
            const testCases = [
                'console.log("test"',           // 缺少右括号
                'alert("message"',              // 缺少右括号
                'fetch("/api/test"',            // 缺少右括号
                'document.getElementById("id"', // 缺少右括号
                'JSON.stringify(data',          // 缺少右括号
                'new Date(',                    // 缺少右括号
                'setTimeout(function() {',      // 缺少右括号
                'addEventListener("click"',     // 缺少右括号
            ];
            
            let result = '<h3>🧪 语法错误测试结果</h3>';
            result += '<p>测试常见的导致 "missing ) after argument list" 错误的代码模式:</p>';
            
            testCases.forEach((code, index) => {
                try {
                    new Function(code);
                    result += '<div class="line-item line-warning">';
                    result += '<strong>测试 ' + (index + 1) + ':</strong> ' + code + '<br>';
                    result += '<span style="color: orange;">⚠️ 意外通过 - 这个代码应该有语法错误</span>';
                    result += '</div>';
                } catch (error) {
                    const isMissingParen = error.message.includes('missing') && error.message.includes('after argument list');
                    result += '<div class="line-item ' + (isMissingParen ? 'line-error' : 'line-warning') + '">';
                    result += '<strong>测试 ' + (index + 1) + ':</strong> ' + code + '<br>';
                    result += '<span style="font-weight: bold;">' + (isMissingParen ? '❌ 确认错误' : '⚠️ 其他错误') + ': ' + error.message + '</span>';
                    result += '</div>';
                }
            });
            
            updateDisplay(result);
        }

        function testProblematicCode() {
            updateDisplay('<h3>🔍 测试可能有问题的代码模式...</h3>', 'warning');
            
            // 基于我们之前修复的代码，测试一些可能仍有问题的模式
            const problematicPatterns = [
                {
                    name: '字符串拼接的console调用',
                    code: 'console.log("测试 " + variable + " 结果");'
                },
                {
                    name: '复杂的三元运算符',
                    code: 'const result = field === "name" ? "visitor_name" : field === "email" ? "visitor_email" : field;'
                },
                {
                    name: 'Fetch调用',
                    code: 'fetch("/api/test", { method: "POST", headers: { "Content-Type": "application/json" } });'
                },
                {
                    name: 'DOM查询',
                    code: 'document.querySelector("meta[name=\\"csrf-token\\"]").getAttribute("content");'
                },
                {
                    name: 'JSON操作',
                    code: 'JSON.stringify({ session_id: sessionId, status: "online" });'
                }
            ];
            
            let result = '<h3>🧪 问题代码模式测试</h3>';
            
            problematicPatterns.forEach((pattern, index) => {
                try {
                    new Function('variable', 'field', 'sessionId', pattern.code);
                    result += '<div class="line-item line-ok">';
                    result += '<strong>' + pattern.name + ':</strong><br>';
                    result += '<code>' + pattern.code + '</code><br>';
                    result += '<span style="color: green;">✅ 语法正确</span>';
                    result += '</div>';
                } catch (error) {
                    result += '<div class="line-item line-error">';
                    result += '<strong>' + pattern.name + ':</strong><br>';
                    result += '<code>' + pattern.code + '</code><br>';
                    result += '<span style="color: red;">❌ 语法错误: ' + error.message + '</span>';
                    result += '</div>';
                }
            });
            
            updateDisplay(result);
        }

        function clearResults() {
            updateDisplay('<h3>🧹 结果已清空</h3><p>可以重新开始调试。</p>');
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay('<h3>🚀 JavaScript语法调试器 v2 已启动</h3><p>点击"定位第2677行"开始精确定位错误位置。</p>');
        });
    </script>
</body>
</html>
