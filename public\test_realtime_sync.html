<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前后台实时同步</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .test-btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .status { padding: 5px 10px; border-radius: 15px; font-size: 12px; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 测试前后台实时同步</h1>
        <p>这个页面用来测试客服系统的前后台实时消息同步功能。</p>
        
        <div class="test-panel">
            <h2>📊 系统状态</h2>
            <div class="grid">
                <div>
                    <h4>前台客服系统</h4>
                    <span id="frontend-status" class="status offline">检查中...</span>
                    <div id="frontend-log" class="log"></div>
                </div>
                <div>
                    <h4>后台管理系统</h4>
                    <span id="backend-status" class="status offline">检查中...</span>
                    <div id="backend-log" class="log"></div>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>🧪 测试功能</h2>
            <div class="grid">
                <div>
                    <h4>前台测试</h4>
                    <button class="test-btn" onclick="testFrontendSSE()">测试前台SSE连接</button>
                    <button class="test-btn" onclick="sendCustomerMessage()">发送客户消息</button>
                    <button class="test-btn" onclick="openFrontendChat()">打开前台聊天</button>
                </div>
                <div>
                    <h4>后台测试</h4>
                    <button class="test-btn" onclick="testBackendSSE()">测试后台SSE连接</button>
                    <button class="test-btn" onclick="sendAdminReply()">发送客服回复</button>
                    <button class="test-btn" onclick="openBackendAdmin()">打开后台管理</button>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>📝 测试步骤</h2>
            <ol>
                <li><strong>检查系统状态</strong> - 确保前后台SSE连接正常</li>
                <li><strong>发送客户消息</strong> - 测试前台→后台的实时推送</li>
                <li><strong>发送客服回复</strong> - 测试后台→前台的实时推送</li>
                <li><strong>验证同步效果</strong> - 确保消息双向实时同步</li>
            </ol>
        </div>
        
        <div class="test-panel">
            <h2>🔗 快速链接</h2>
            <a href="/" target="_blank" class="test-btn">前台首页</a>
            <a href="/strongadmin/customer-service/sessions" target="_blank" class="test-btn">后台会话管理</a>
            <a href="/strongadmin/customer-service/system-config" target="_blank" class="test-btn">系统配置</a>
            <a href="/test_cs.php" target="_blank" class="test-btn">发送测试消息</a>
        </div>
    </div>

    <script>
    let frontendEventSource = null;
    let backendEventSource = null;
    let testSessionId = 'test_sync_' + Date.now();
    
    // 页面加载时检查状态
    document.addEventListener('DOMContentLoaded', function() {
        checkSystemStatus();
        testConnections();
    });
    
    // 检查系统状态
    function checkSystemStatus() {
        fetch('/api/customer-service/status')
            .then(response => response.json())
            .then(data => {
                const status = document.getElementById('frontend-status');
                if (data.success && data.enabled) {
                    status.textContent = '系统在线';
                    status.className = 'status online';
                } else {
                    status.textContent = '系统离线';
                    status.className = 'status offline';
                }
            })
            .catch(error => {
                document.getElementById('frontend-status').textContent = '连接失败';
                logToFrontend('❌ 系统状态检查失败: ' + error.message);
            });
    }
    
    // 测试连接
    function testConnections() {
        testFrontendSSE();
        testBackendSSE();
    }
    
    // 测试前台SSE连接
    function testFrontendSSE() {
        logToFrontend('🔄 测试前台SSE连接...');
        
        if (frontendEventSource) {
            frontendEventSource.close();
        }
        
        const url = `/customer-service/message-stream?session_id=${testSessionId}&last_message_id=0`;
        frontendEventSource = new EventSource(url);
        
        frontendEventSource.onopen = function() {
            logToFrontend('✅ 前台SSE连接成功');
            document.getElementById('frontend-status').textContent = 'SSE已连接';
            document.getElementById('frontend-status').className = 'status online';
        };
        
        frontendEventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            logToFrontend('📨 收到前台消息: ' + JSON.stringify(data));
        };
        
        frontendEventSource.onerror = function() {
            logToFrontend('❌ 前台SSE连接失败');
            document.getElementById('frontend-status').className = 'status offline';
        };
    }
    
    // 测试后台SSE连接
    function testBackendSSE() {
        logToBackend('🔄 测试后台SSE连接...');
        
        if (backendEventSource) {
            backendEventSource.close();
        }
        
        const url = '/strongadmin/customer-service/message-stream?last_message_id=0';
        backendEventSource = new EventSource(url);
        
        backendEventSource.onopen = function() {
            logToBackend('✅ 后台SSE连接成功');
            document.getElementById('backend-status').textContent = 'SSE已连接';
            document.getElementById('backend-status').className = 'status online';
        };
        
        backendEventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            logToBackend('📨 收到后台消息: ' + JSON.stringify(data));
        };
        
        backendEventSource.onerror = function() {
            logToBackend('❌ 后台SSE连接失败');
            document.getElementById('backend-status').className = 'status offline';
        };
    }
    
    // 发送客户消息
    function sendCustomerMessage() {
        const message = '测试客户消息 ' + new Date().toLocaleTimeString();
        logToFrontend('📤 发送客户消息: ' + message);
        
        fetch('/customer-service/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                session_id: testSessionId,
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logToFrontend('✅ 客户消息发送成功');
            } else {
                logToFrontend('❌ 发送失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            logToFrontend('❌ 网络错误: ' + error.message);
        });
    }
    
    // 发送客服回复（模拟）
    function sendAdminReply() {
        const message = '测试客服回复 ' + new Date().toLocaleTimeString();
        logToBackend('📤 模拟发送客服回复: ' + message);
        
        // 这里需要实际的后台回复API
        logToBackend('ℹ️ 请在后台管理页面手动回复消息进行测试');
    }
    
    // 打开前台聊天
    function openFrontendChat() {
        window.open('/', '_blank');
    }
    
    // 打开后台管理
    function openBackendAdmin() {
        window.open('/strongadmin/customer-service/sessions', '_blank');
    }
    
    // 记录日志
    function logToFrontend(message) {
        const log = document.getElementById('frontend-log');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] ${message}\n`;
        log.scrollTop = log.scrollHeight;
    }
    
    function logToBackend(message) {
        const log = document.getElementById('backend-log');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] ${message}\n`;
        log.scrollTop = log.scrollHeight;
    }
    </script>
</body>
</html>
