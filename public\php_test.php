<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>PHP测试</title></head><body>";
echo "<h1>PHP测试页面</h1>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>服务器信息: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

// 测试输出缓冲
echo "<h2>输出缓冲测试</h2>";
echo "<p>ob_get_level(): " . ob_get_level() . "</p>";

// 测试文件权限
echo "<h2>文件权限测试</h2>";
$testFile = 'test_write.txt';
if (file_put_contents($testFile, 'test')) {
    echo "<p style='color:green;'>✅ 文件写入权限正常</p>";
    unlink($testFile);
} else {
    echo "<p style='color:red;'>❌ 文件写入权限有问题</p>";
}

// 测试错误报告
echo "<h2>错误报告设置</h2>";
echo "<p>error_reporting: " . error_reporting() . "</p>";
echo "<p>display_errors: " . ini_get('display_errors') . "</p>";

echo "<h2>测试链接</h2>";
echo "<ul>";
echo "<li><a href='/minimal_sse.php' target='_blank'>最简单SSE测试</a></li>";
echo "<li><a href='/basic_sse.php?session_id=test&last_message_id=0' target='_blank'>基础SSE测试</a></li>";
echo "</ul>";

echo "</body></html>";
?>
