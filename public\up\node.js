// server.js
const express = require('express');
const STS = require('ali-oss').STS;
const app = express();
const port = 3000;

const config = {
  accessKeyId: 'LTAI5tDmyfEgPPUZmy46fdJt',
  accessKeySecret: '******************************',
  roleArn: 'acs:ram::31987770:role/mosrxxoos',
  bucket: 'mosrxx',
  region: 'OSSBAG-cn-gh648386i00e'
};

// 生成临时凭证
app.get('/sts-token', (req, res) => {
  const sts = new STS({
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret
  });

  const policy = {
    Statement: [{
      Action: ['oss:PutObject'],
      Effect: 'Allow',
      Resource: [`acs:oss:*:*:${config.bucket}/uploads/haitao*`]
    }],
    Version: '1'
  };

  sts.assumeRole(config.roleArn, policy, 3600, 'web-client')
    .then(result => {
      res.json({
        accessKeyId: result.credentials.AccessKeyId,
        accessKeySecret: result.credentials.AccessKeySecret,
        stsToken: result.credentials.SecurityToken,
        bucket: config.bucket,
        region: config.region
      });
    })
    .catch(err => res.status(500).send(err));
});

app.listen(port, () => console.log(`Server running on port ${port}`));