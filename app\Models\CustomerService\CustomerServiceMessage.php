<?php

namespace App\Models\CustomerService;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Admin\User as AdminUser;

class CustomerServiceMessage extends Model
{
    protected $fillable = [
        'session_id',
        'sender_type',
        'sender_id',
        'message',
        'attachments',
        'is_read',
        'read_at'
    ];

    protected $casts = [
        'attachments' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime'
    ];

    // 关联会话
    public function session()
    {
        return $this->belongsTo(CustomerServiceSession::class, 'session_id');
    }

    // 关联发送者（用户）
    public function senderUser()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    // 关联发送者（管理员）
    public function senderAdmin()
    {
        return $this->belongsTo(AdminUser::class, 'sender_id');
    }

    // 获取发送者信息
    public function getSenderInfoAttribute()
    {
        switch ($this->sender_type) {
            case 'customer':
                if ($this->senderUser) {
                    return [
                        'name' => $this->senderUser->name,
                        'avatar' => $this->senderUser->avatar ?? '/images/default-avatar.png'
                    ];
                }
                return [
                    'name' => $this->session->visitor_name ?: 'Customer',
                    'avatar' => '/images/default-avatar.png'
                ];
            
            case 'admin':
                if ($this->senderAdmin) {
                    return [
                        'name' => $this->senderAdmin->name,
                        'avatar' => $this->senderAdmin->avatar ?? '/images/admin-avatar.png'
                    ];
                }
                return [
                    'name' => 'Customer Service',
                    'avatar' => '/images/admin-avatar.png'
                ];
            
            case 'ai':
                return [
                    'name' => 'AI Assistant',
                    'avatar' => '/images/ai-avatar.png'
                ];
            
            default:
                return [
                    'name' => 'Unknown',
                    'avatar' => '/images/default-avatar.png'
                ];
        }
    }

    // 标记为已读
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now()
            ]);
        }
    }

    // 格式化消息时间
    public function getFormattedTimeAttribute()
    {
        return $this->created_at->format('H:i');
    }

    // 格式化消息日期
    public function getFormattedDateAttribute()
    {
        return $this->created_at->format('Y-m-d');
    }
}
