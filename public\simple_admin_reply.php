<?php
/**
 * 简单的管理员回复工具 - 直接操作数据库
 */

if ($_POST['action'] ?? '' === 'send_reply') {
    $sessionDbId = $_POST['session_db_id'] ?? '';
    $message = $_POST['message'] ?? '';
    
    if ($sessionDbId && $message) {
        // 数据库配置
        $envFile = dirname(__DIR__) . '/.env';
        $config = [];
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $config[trim($key)] = trim($value);
                }
            }
        }
        
        $host = $config['DB_HOST'] ?? '127.0.0.1';
        $port = $config['DB_PORT'] ?? '3306';
        $database = $config['DB_DATABASE'] ?? 'mostxx_com';
        $username = $config['DB_USERNAME'] ?? 'mostxx_com';
        $password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';
        
        try {
            $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            
            // 直接插入到正确的表
            $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, sender_id, message, is_read, created_at, updated_at) VALUES (?, 'admin', 1, ?, 0, NOW(), NOW())");
            $stmt->execute([$sessionDbId, $message]);
            $messageId = $pdo->lastInsertId();
            
            echo json_encode(['success' => true, 'message_id' => $messageId]);
            exit;
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            exit;
        }
    }
    
    echo json_encode(['success' => false, 'error' => '参数不完整']);
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单管理员回复</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>📤 简单管理员回复工具</h1>
    
    <form method="POST">
        <input type="hidden" name="action" value="send_reply">
        <div>
            <label>会话数据库ID: <input type="number" name="session_db_id" required></label>
        </div>
        <div>
            <label>回复消息: <input type="text" name="message" placeholder="输入回复内容" style="width:300px;" required></label>
        </div>
        <div>
            <button type="submit" class="btn">发送回复</button>
        </div>
    </form>
    
    <p><strong>使用方法：</strong></p>
    <ol>
        <li>在快速测试页面获取会话数据库ID</li>
        <li>在这里输入ID和回复内容</li>
        <li>点击发送回复</li>
        <li>观察前台是否收到</li>
    </ol>
</body>
</html>
