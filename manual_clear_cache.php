<?php
/**
 * 手动清除缓存脚本
 * 访问: http://your-domain/manual_clear_cache.php
 */

// 设置基本路径
$basePath = __DIR__;
require_once $basePath . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once $basePath . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// 创建请求
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>🧹 手动缓存清理工具</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.success { color: green; background: #e6ffe6; padding: 10px; border-radius: 4px; margin: 10px 0; }
.error { color: red; background: #ffe6e6; padding: 10px; border-radius: 4px; margin: 10px 0; }
.info { color: blue; background: #e6f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
.step { margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
</style>";

echo "<div class='container'>";

try {
    echo "<div class='step'>";
    echo "<h2>🗂️ 步骤1: 清除Laravel缓存</h2>";
    
    // 清除应用缓存
    if (function_exists('cache')) {
        \Illuminate\Support\Facades\Cache::flush();
        echo "<div class='success'>✅ 应用缓存已清除</div>";
    }
    
    // 清除配置缓存
    $configCachePath = $basePath . '/bootstrap/cache/config.php';
    if (file_exists($configCachePath)) {
        unlink($configCachePath);
        echo "<div class='success'>✅ 配置缓存已清除</div>";
    } else {
        echo "<div class='info'>ℹ️ 配置缓存文件不存在</div>";
    }
    
    // 清除路由缓存
    $routeCachePath = $basePath . '/bootstrap/cache/routes.php';
    if (file_exists($routeCachePath)) {
        unlink($routeCachePath);
        echo "<div class='success'>✅ 路由缓存已清除</div>";
    } else {
        echo "<div class='info'>ℹ️ 路由缓存文件不存在</div>";
    }
    
    // 清除视图缓存
    $viewCachePath = $basePath . '/storage/framework/views';
    if (is_dir($viewCachePath)) {
        $files = glob($viewCachePath . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "<div class='success'>✅ 视图缓存已清除</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🏠 步骤2: 清除产品首页缓存</h2>";
    
    // 清除产品首页缓存
    \Illuminate\Support\Facades\Cache::forget('product:home');
    echo "<div class='success'>✅ 产品首页缓存已清除</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🔍 步骤3: 验证产品数据</h2>";
    
    // 检查产品数据
    use App\Models\Product\Product;
    
    $testProduct = Product::query()
        ->where('status', 1)
        ->whereNotNull('img_photos')
        ->select(['id', 'title', 'img_cover', 'img_photos'])
        ->first();
    
    if ($testProduct) {
        echo "<div class='success'>✅ 找到测试产品: {$testProduct->title}</div>";
        echo "<p><strong>产品ID:</strong> {$testProduct->id}</p>";
        echo "<p><strong>封面图:</strong> {$testProduct->img_cover}</p>";
        
        if ($testProduct->asset_img_photos) {
            echo "<p><strong>相册图片数量:</strong> " . count($testProduct->asset_img_photos) . "</p>";
            if (isset($testProduct->asset_img_photos[1])) {
                echo "<div class='success'>✅ 第二张图片存在: {$testProduct->asset_img_photos[1]['src']}</div>";
            } else {
                echo "<div class='error'>❌ 第二张图片不存在</div>";
            }
        } else {
            echo "<div class='error'>❌ 没有相册图片数据</div>";
        }
    } else {
        echo "<div class='error'>❌ 没有找到有图片的产品</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>✅ 清理完成</h2>";
    echo "<p>缓存清理已完成！现在可以：</p>";
    echo "<ul>";
    echo "<li>🔄 刷新网站首页查看效果</li>";
    echo "<li>🖱️ 测试产品图片悬停切换</li>";
    echo "<li>📱 在手机端测试响应式效果</li>";
    echo "</ul>";
    echo "<p><a href='/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>返回首页</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 错误: " . $e->getMessage() . "</div>";
    echo "<div class='error'>详细信息: " . $e->getTraceAsString() . "</div>";
}

echo "</div>";

echo "<hr>";
echo "<p><small>清理完成时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
