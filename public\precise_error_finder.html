<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确错误定位器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .char-highlight {
            background: #ff5722;
            color: white;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 精确错误定位器</h1>
        <p>定位第2677行第9列的具体字符和语法错误</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="findExactError()">定位第2677行第9列</button>
            <button class="test-btn" onclick="analyzeCharacter()">分析错误字符</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>🎯 精确错误定位器已就绪</h3>
                <p>准备定位第2677行第9列的语法错误</p>
            </div>
        </div>
    </div>

    <script>
        function updateDisplay(content, type = 'success') {
            const display = document.getElementById('result-display');
            const className = type === 'error' ? 'error-box' : 'success-box';
            display.innerHTML = '<div class="' + className + '">' + content + '</div>';
        }

        async function findExactError() {
            updateDisplay('<h3>🔍 正在定位第2677行第9列...</h3>', 'warning');
            
            try {
                const response = await fetch('/');
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                
                const pageContent = await response.text();
                const allLines = pageContent.split('\n');
                const totalLines = allLines.length;
                
                let result = '<h3>🎯 第2677行第9列精确定位</h3>';
                result += '<p><strong>页面总行数:</strong> ' + totalLines + '</p>';
                
                if (totalLines >= 2677) {
                    const targetLineIndex = 2676; // 数组索引从0开始
                    const targetLine = allLines[targetLineIndex];
                    const targetColumn = 9;
                    
                    result += '<h4>🔍 第2677行完整内容:</h4>';
                    result += '<div style="background: #ffcdd2; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; overflow-x: auto;">';
                    result += escapeHtml(targetLine);
                    result += '</div>';
                    
                    result += '<h4>🎯 第9列字符分析:</h4>';
                    result += '<div style="background: #e3f2fd; padding: 15px; border-radius: 4px;">';
                    
                    if (targetLine.length >= targetColumn) {
                        const targetChar = targetLine.charAt(targetColumn - 1); // 列号从1开始
                        const beforeChar = targetColumn > 1 ? targetLine.charAt(targetColumn - 2) : '';
                        const afterChar = targetColumn < targetLine.length ? targetLine.charAt(targetColumn) : '';
                        
                        result += '<p><strong>目标字符 (第9列):</strong> <span class="char-highlight">' + escapeHtml(targetChar) + '</span></p>';
                        result += '<p><strong>字符编码:</strong> ' + targetChar.charCodeAt(0) + '</p>';
                        result += '<p><strong>前一个字符:</strong> ' + escapeHtml(beforeChar) + ' (编码: ' + (beforeChar ? beforeChar.charCodeAt(0) : 'N/A') + ')</p>';
                        result += '<p><strong>后一个字符:</strong> ' + escapeHtml(afterChar) + ' (编码: ' + (afterChar ? afterChar.charCodeAt(0) : 'N/A') + ')</p>';
                        
                        // 显示第9列前后的字符串片段
                        const startPos = Math.max(0, targetColumn - 10);
                        const endPos = Math.min(targetLine.length, targetColumn + 10);
                        const snippet = targetLine.substring(startPos, endPos);
                        const highlightPos = targetColumn - 1 - startPos;
                        
                        result += '<p><strong>周围字符串:</strong></p>';
                        result += '<div style="background: white; padding: 10px; border-radius: 3px; font-family: monospace;">';
                        for (let i = 0; i < snippet.length; i++) {
                            if (i === highlightPos) {
                                result += '<span class="char-highlight">' + escapeHtml(snippet.charAt(i)) + '</span>';
                            } else {
                                result += escapeHtml(snippet.charAt(i));
                            }
                        }
                        result += '</div>';
                        
                        // 分析可能的语法错误
                        result += '<h4>🔬 语法错误分析:</h4>';
                        
                        // 检查是否是括号问题
                        if (targetChar === '(' || targetChar === ')') {
                            result += '<p style="color: #d32f2f;">⚠️ 错误字符是括号: "' + targetChar + '"</p>';
                        }
                        
                        // 检查前后是否有函数调用
                        const beforeSnippet = targetLine.substring(0, targetColumn - 1);
                        const afterSnippet = targetLine.substring(targetColumn - 1);
                        
                        if (beforeSnippet.includes('console.') || beforeSnippet.includes('alert(') || beforeSnippet.includes('fetch(')) {
                            result += '<p style="color: #d32f2f;">⚠️ 检测到函数调用，可能缺少右括号</p>';
                        }
                        
                        // 检查是否有未闭合的字符串
                        const openQuotes = (beforeSnippet.match(/"/g) || []).length;
                        const openSingleQuotes = (beforeSnippet.match(/'/g) || []).length;
                        const openBackticks = (beforeSnippet.match(/`/g) || []).length;
                        
                        if (openQuotes % 2 !== 0) {
                            result += '<p style="color: #d32f2f;">⚠️ 检测到未闭合的双引号字符串</p>';
                        }
                        if (openSingleQuotes % 2 !== 0) {
                            result += '<p style="color: #d32f2f;">⚠️ 检测到未闭合的单引号字符串</p>';
                        }
                        if (openBackticks % 2 !== 0) {
                            result += '<p style="color: #d32f2f;">⚠️ 检测到未闭合的模板字符串</p>';
                        }
                        
                    } else {
                        result += '<p style="color: #d32f2f;">❌ 第9列超出行长度 (行长度: ' + targetLine.length + ')</p>';
                    }
                    
                    result += '</div>';
                    
                    // 显示上下文行
                    result += '<h4>📋 上下文 (第2672-2682行):</h4>';
                    result += '<div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 11px; max-height: 300px; overflow-y: auto;">';
                    
                    for (let i = Math.max(0, 2671); i <= Math.min(totalLines - 1, 2681); i++) {
                        const lineNum = i + 1;
                        const line = allLines[i] || '';
                        const isTarget = lineNum === 2677;
                        
                        const style = isTarget ? 'background: #ffcdd2; font-weight: bold;' : 'background: white;';
                        result += '<div style="' + style + ' padding: 2px 5px; margin: 1px 0; border-radius: 2px;">';
                        result += '<strong>行 ' + lineNum + ':</strong> ';
                        
                        if (isTarget && line.length >= 9) {
                            // 高亮第9列
                            result += escapeHtml(line.substring(0, 8));
                            result += '<span class="char-highlight">' + escapeHtml(line.charAt(8)) + '</span>';
                            result += escapeHtml(line.substring(9, 120));
                        } else {
                            result += escapeHtml(line.substring(0, 120));
                        }
                        
                        if (line.length > 120) result += '...';
                        result += '</div>';
                    }
                    
                    result += '</div>';
                    
                } else {
                    result += '<div style="background: #ffebee; padding: 15px; border-radius: 4px;">';
                    result += '<h4>❌ 第2677行不存在</h4>';
                    result += '<p>页面只有 ' + totalLines + ' 行。</p>';
                    result += '</div>';
                }
                
                updateDisplay(result, totalLines >= 2677 ? 'warning' : 'error');
                
            } catch (error) {
                updateDisplay('<h3>❌ 定位失败</h3><p><strong>错误:</strong> ' + error.message + '</p>', 'error');
            }
        }

        function analyzeCharacter() {
            updateDisplay('<h3>🔬 分析错误字符...</h3>', 'warning');
            
            // 基于"missing ) after argument list"错误，分析可能的原因
            let result = '<h3>🔬 "missing ) after argument list" 错误分析</h3>';
            result += '<p>这个错误通常由以下原因导致:</p>';
            
            result += '<h4>🎯 常见原因:</h4>';
            result += '<ol>';
            result += '<li><strong>函数调用缺少右括号</strong><br>';
            result += '例如: <code>console.log("test"</code> 应该是 <code>console.log("test")</code></li>';
            result += '<li><strong>字符串中的特殊字符</strong><br>';
            result += '例如: <code>alert("It\'s working")</code> 中的单引号需要转义</li>';
            result += '<li><strong>模板字符串语法错误</strong><br>';
            result += '例如: <code>`Hello ${name`</code> 缺少闭合的大括号</li>';
            result += '<li><strong>嵌套函数调用错误</strong><br>';
            result += '例如: <code>func1(func2(param)</code> 缺少外层函数的右括号</li>';
            result += '</ol>';
            
            result += '<h4>🔧 修复策略:</h4>';
            result += '<div style="background: #e3f2fd; padding: 15px; border-radius: 4px;">';
            result += '<p><strong>1. 检查第2677行第9列附近的代码</strong></p>';
            result += '<p><strong>2. 查找未闭合的括号、引号或大括号</strong></p>';
            result += '<p><strong>3. 特别注意以下模式:</strong></p>';
            result += '<ul>';
            result += '<li><code>console.log(</code> 或 <code>console.error(</code></li>';
            result += '<li><code>alert(</code> 或 <code>confirm(</code></li>';
            result += '<li><code>fetch(</code> 或其他API调用</li>';
            result += '<li><code>document.querySelector(</code></li>';
            result += '<li>模板字符串 <code>`${</code></li>';
            result += '</ul>';
            result += '</div>';
            
            updateDisplay(result, 'warning');
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay('<h3>🎯 精确错误定位器已启动</h3><p>准备定位第2677行第9列的语法错误。</p>');
        });
    </script>
</body>
</html>
