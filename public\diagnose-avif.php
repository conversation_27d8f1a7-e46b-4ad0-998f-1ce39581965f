<?php
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 AVIF支持诊断工具</h1>";

// 检查系统状态
echo "<h2>📋 系统状态检查</h2>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
echo "<tr style='background: #f0f0f0;'><th>检查项</th><th>状态</th><th>说明</th></tr>";

// PHP版本
echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td><td>当前PHP版本</td></tr>";

// GD扩展
$gdLoaded = extension_loaded('gd');
echo "<tr><td>GD扩展</td><td>" . ($gdLoaded ? '<span style="color: green;">✅ 已安装</span>' : '<span style="color: red;">❌ 未安装</span>') . "</td><td>基础图像处理</td></tr>";

// Imagick扩展
$imagickLoaded = extension_loaded('imagick');
echo "<tr><td>Imagick扩展</td><td>" . ($imagickLoaded ? '<span style="color: green;">✅ 已安装</span>' : '<span style="color: orange;">⚠️ 未安装</span>') . "</td><td>高级图像处理</td></tr>";

// Intervention Image
$interventionLoaded = class_exists('Intervention\Image\Facades\Image');
echo "<tr><td>Intervention Image</td><td>" . ($interventionLoaded ? '<span style="color: green;">✅ 已安装</span>' : '<span style="color: red;">❌ 未安装</span>') . "</td><td>图像处理库</td></tr>";

// 文件上传
echo "<tr><td>文件上传</td><td>" . (ini_get('file_uploads') ? '<span style="color: green;">✅ 启用</span>' : '<span style="color: red;">❌ 禁用</span>') . "</td><td>PHP文件上传功能</td></tr>";

echo "</table>";

// 如果Intervention Image已安装，检查驱动
if ($interventionLoaded) {
    echo "<h2>🖼️ Intervention Image 配置</h2>";
    
    try {
        // 检查可用的驱动
        $manager = new \Intervention\Image\ImageManager(['driver' => 'gd']);
        echo "<p><strong>GD驱动:</strong> <span style='color: green;'>✅ 可用</span></p>";
    } catch (Exception $e) {
        echo "<p><strong>GD驱动:</strong> <span style='color: red;'>❌ 不可用</span> - " . $e->getMessage() . "</p>";
    }
    
    if ($imagickLoaded) {
        try {
            $manager = new \Intervention\Image\ImageManager(['driver' => 'imagick']);
            echo "<p><strong>Imagick驱动:</strong> <span style='color: green;'>✅ 可用</span></p>";
            
            // 检查Imagick支持的格式
            $imagick = new Imagick();
            $formats = $imagick->queryFormats();
            $avifSupported = in_array('AVIF', $formats);
            echo "<p><strong>Imagick AVIF支持:</strong> " . ($avifSupported ? '<span style="color: green;">✅ 支持</span>' : '<span style="color: red;">❌ 不支持</span>') . "</p>";
        } catch (Exception $e) {
            echo "<p><strong>Imagick驱动:</strong> <span style='color: red;'>❌ 不可用</span> - " . $e->getMessage() . "</p>";
        }
    }
}

// 测试AVIF文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['avif_file'])) {
    echo "<h2>🧪 AVIF文件测试结果</h2>";
    
    $file = $_FILES['avif_file'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "<h3>📋 文件信息</h3>";
        echo "<p><strong>文件名:</strong> " . htmlspecialchars($file['name']) . "</p>";
        echo "<p><strong>大小:</strong> " . number_format($file['size'] / 1024, 2) . " KB</p>";
        echo "<p><strong>MIME类型:</strong> " . htmlspecialchars($file['type']) . "</p>";
        
        // 检测实际MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detectedMimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        echo "<p><strong>检测到的MIME类型:</strong> " . htmlspecialchars($detectedMimeType) . "</p>";
        
        // 检查文件头
        $handle = fopen($file['tmp_name'], 'rb');
        if ($handle) {
            $header = fread($handle, 32);
            fclose($handle);
            
            $isAvif = (strpos($header, 'ftypavif') !== false || strpos($header, 'ftypavis') !== false);
            echo "<p><strong>AVIF文件头验证:</strong> " . ($isAvif ? '<span style="color: green;">✅ 是AVIF文件</span>' : '<span style="color: red;">❌ 不是AVIF文件</span>') . "</p>";
        }
        
        // 测试Intervention Image处理
        if ($interventionLoaded) {
            echo "<h3>🔧 Intervention Image 处理测试</h3>";
            
            try {
                $img = \Intervention\Image\Facades\Image::make($file['tmp_name']);
                echo "<p><strong>读取文件:</strong> <span style='color: green;'>✅ 成功</span></p>";
                echo "<p><strong>图像尺寸:</strong> " . $img->width() . " x " . $img->height() . "</p>";
                
                // 尝试转换为PNG
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.png';
                $img->save($tempPath, 100, 'png');
                
                if (file_exists($tempPath)) {
                    echo "<p><strong>转换为PNG:</strong> <span style='color: green;'>✅ 成功</span></p>";
                    echo "<p><strong>转换后大小:</strong> " . number_format(filesize($tempPath) / 1024, 2) . " KB</p>";
                    unlink($tempPath);
                } else {
                    echo "<p><strong>转换为PNG:</strong> <span style='color: red;'>❌ 失败</span></p>";
                }
                
            } catch (Exception $e) {
                echo "<p><strong>Intervention Image 处理:</strong> <span style='color: red;'>❌ 失败</span></p>";
                echo "<p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        // 测试实际上传接口
        echo "<h3>🔗 上传接口测试</h3>";
        
        $postData = array(
            'file' => new CURLFile($file['tmp_name'], $file['type'], $file['name'])
        );
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://www.strongshop.local/strongadmin/upload/image?thumb=1');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'X-Requested-With: XMLHttpRequest'
        ));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
        echo "<h4>API响应:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && isset($result['code'])) {
                if ($result['code'] === 0) {
                    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
                    echo "<h4>✅ 上传成功！</h4>";
                    echo "<p>AVIF文件已成功处理并转换</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
                    echo "<h4>❌ 上传失败</h4>";
                    echo "<p>错误代码: " . $result['code'] . "</p>";
                    echo "<p>错误信息: " . htmlspecialchars($result['message']) . "</p>";
                    echo "</div>";
                }
            }
        }
    }
}

echo "<form method='POST' enctype='multipart/form-data' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>📤 上传AVIF文件测试</h2>";
echo "<input type='file' name='avif_file' accept='.avif,image/avif' required style='margin: 10px 0;'><br>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>测试AVIF文件</button>";
echo "</form>";

// 建议
echo "<h2>💡 建议</h2>";
if (!$interventionLoaded) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<p>❌ Intervention Image 未正确安装，请运行:</p>";
    echo "<code>composer require intervention/image</code>";
    echo "</div>";
} elseif (!$imagickLoaded) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404;'>";
    echo "<p>⚠️ 建议安装 Imagick 扩展以获得更好的AVIF支持</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<p>✅ 系统配置良好，应该支持AVIF格式</p>";
    echo "</div>";
}
?>
