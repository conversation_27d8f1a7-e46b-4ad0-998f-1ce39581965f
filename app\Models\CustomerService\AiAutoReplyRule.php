<?php

namespace App\Models\CustomerService;

use Illuminate\Database\Eloquent\Model;

class AiAutoReplyRule extends Model
{
    protected $fillable = [
        'name',
        'keywords',
        'reply_message',
        'priority',
        'is_active',
        'usage_count'
    ];

    protected $casts = [
        'keywords' => 'array',
        'is_active' => 'boolean'
    ];

    // 检查消息是否匹配关键词
    public function matchesMessage($message)
    {
        if (!$this->is_active) {
            return false;
        }

        $message = strtolower($message);
        
        foreach ($this->keywords as $keyword) {
            if (strpos($message, strtolower($keyword)) !== false) {
                return true;
            }
        }
        
        return false;
    }

    // 增加使用次数
    public function incrementUsage()
    {
        $this->increment('usage_count');
    }

    // 获取活跃规则（按优先级排序）
    public static function getActiveRules()
    {
        return static::where('is_active', true)
                    ->orderBy('priority', 'desc')
                    ->orderBy('created_at', 'asc')
                    ->get();
    }

    // 查找匹配的规则
    public static function findMatchingRule($message)
    {
        $rules = static::getActiveRules();
        
        foreach ($rules as $rule) {
            if ($rule->matchesMessage($message)) {
                return $rule;
            }
        }
        
        return null;
    }
}
