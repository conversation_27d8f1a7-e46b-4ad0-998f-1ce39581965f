<?php
/**
 * 直接创建客服系统数据表
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>直接创建客服数据表</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .btn{padding:8px 15px;margin:5px;background:#007bff;color:white;text-decoration:none;border-radius:3px;}</style>";
echo "</head><body>";

echo "<h1>🛠️ 直接创建客服系统数据表</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    echo "<p class='info'>📊 数据库: $database</p>";
    
    // 直接定义SQL语句
    $sqls = [
        // 客服会话表
        "CREATE TABLE IF NOT EXISTS `st_customer_service_sessions` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话主键ID',
            `session_id` varchar(255) NOT NULL COMMENT '会话唯一标识符',
            `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联的用户ID（如果是注册用户）',
            `visitor_name` varchar(255) DEFAULT NULL COMMENT '访客姓名',
            `visitor_email` varchar(255) DEFAULT NULL COMMENT '访客邮箱',
            `visitor_ip` varchar(255) DEFAULT NULL COMMENT '访客IP地址',
            `user_agent` text DEFAULT NULL COMMENT '访客浏览器信息',
            `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active' COMMENT '会话状态：active=活跃，waiting=等待，closed=已关闭',
            `assigned_admin_id` bigint(20) unsigned DEFAULT NULL COMMENT '分配的客服管理员ID',
            `last_activity` timestamp NULL DEFAULT NULL COMMENT '最后活动时间',
            `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `session_id` (`session_id`),
            KEY `assigned_admin_id` (`assigned_admin_id`),
            KEY `status_last_activity` (`status`, `last_activity`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服会话表'",
        
        // 客服消息表
        "CREATE TABLE IF NOT EXISTS `st_customer_service_messages` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息主键ID',
            `session_id` bigint(20) unsigned NOT NULL COMMENT '关联的会话ID',
            `sender_type` enum('customer','admin','ai') NOT NULL COMMENT '发送者类型：customer=客户，admin=客服，ai=AI自动回复',
            `sender_id` bigint(20) unsigned DEFAULT NULL COMMENT '发送者ID（客户ID或管理员ID）',
            `message` text NOT NULL COMMENT '消息内容',
            `attachments` json DEFAULT NULL COMMENT '附件信息（JSON格式）',
            `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0=未读，1=已读',
            `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
            `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `session_id` (`session_id`),
            KEY `sender_type_created` (`sender_type`, `created_at`),
            KEY `is_read` (`is_read`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表'",
        
        // AI自动回复规则表
        "CREATE TABLE IF NOT EXISTS `st_ai_auto_reply_rules` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则主键ID',
            `name` varchar(255) NOT NULL COMMENT '规则名称',
            `keywords` json NOT NULL COMMENT '触发关键词（JSON数组格式）',
            `reply_message` text NOT NULL COMMENT 'AI回复的消息内容',
            `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
            `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
            `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数统计',
            `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `is_active_priority` (`is_active`,`priority`),
            KEY `usage_count` (`usage_count`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI自动回复规则表'"
    ];
    
    echo "<h2>📝 创建数据表：</h2>";
    
    foreach ($sqls as $index => $sql) {
        try {
            $pdo->exec($sql);
            
            if (strpos($sql, 'st_customer_service_sessions') !== false) {
                echo "<p class='success'>✅ 创建客服会话表成功</p>";
            } elseif (strpos($sql, 'st_customer_service_messages') !== false) {
                echo "<p class='success'>✅ 创建客服消息表成功</p>";
            } elseif (strpos($sql, 'st_ai_auto_reply_rules') !== false) {
                echo "<p class='success'>✅ 创建AI规则表成功</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 创建表失败: " . $e->getMessage() . "</p>";
        }
    }
    
    // 插入默认AI规则
    echo "<h2>📝 插入默认AI规则：</h2>";
    
    $rules = [
        ['问候语', '["hello", "hi", "hey", "你好", "您好"]', 'Hello! 👋 Welcome to our store! How can I help you today?', 10],
        ['物流咨询', '["shipping", "delivery", "物流", "快递", "发货"]', 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You will receive a tracking number once your order ships.', 9],
        ['退换货', '["return", "refund", "退货", "退款", "换货"]', 'We have a 30-day return policy! 🔄 If you are not satisfied, you can return items within 30 days for a full refund.', 8],
        ['价格咨询', '["price", "cost", "价格", "多少钱", "费用"]', 'Our prices are very competitive! 💰 You can see the current price on the product page. We also offer bulk discounts for large orders.', 7],
        ['产品咨询', '["product", "item", "产品", "商品", "质量"]', 'We have high-quality products with detailed descriptions! 📦 Please check the product page for specifications, or let me know what specific information you need.', 6]
    ];
    
    foreach ($rules as $rule) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO `st_ai_auto_reply_rules` (`name`, `keywords`, `reply_message`, `priority`, `is_active`, `usage_count`, `created_at`, `updated_at`) VALUES (?, ?, ?, ?, 1, 0, NOW(), NOW())");
            $stmt->execute([$rule[0], $rule[1], $rule[2], $rule[3]]);
            echo "<p class='success'>✅ 插入AI规则: {$rule[0]}</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ 插入规则失败: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>📊 验证表创建结果：</h2>";
    
    // 检查表是否创建成功
    $tables = [
        'st_customer_service_sessions' => '客服会话表',
        'st_customer_service_messages' => '客服消息表', 
        'st_ai_auto_reply_rules' => 'AI自动回复规则表'
    ];
    
    foreach ($tables as $table => $description) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                echo "<p class='success'>✅ $description ($table) 存在，记录数: $count</p>";
            } else {
                echo "<p class='error'>❌ $description ($table) 不存在</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 检查表 $table 失败: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🎉 完成！</h2>";
    echo "<p>现在可以测试客服系统了：</p>";
    echo "<div>";
    echo "<a href='test_cs.php' target='_blank' class='btn'>测试前台发送消息</a>";
    echo "<a href='check_cs_data.php' target='_blank' class='btn'>检查数据库数据</a>";
    echo "<a href='/strongadmin/customer-service/sessions' target='_blank' class='btn'>查看后台会话管理</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
