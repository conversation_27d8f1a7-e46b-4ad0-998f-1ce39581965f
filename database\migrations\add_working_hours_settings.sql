-- 添加工作时间相关设置
INSERT INTO `st_customer_service_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `title`, `description`, `sort_order`) VALUES

-- 工作时间设置
('working_hours_enabled', '1', 'boolean', 'working_hours', '启用工作时间', '是否启用工作时间控制', 1),
('working_hours_mode', 'auto', 'text', 'working_hours', '工作时间模式', 'auto=自动判断, manual=手动控制', 2),
('working_start_time', '09:00', 'text', 'working_hours', '上班时间', '每日上班时间，格式：HH:MM', 3),
('working_end_time', '18:00', 'text', 'working_hours', '下班时间', '每日下班时间，格式：HH:MM', 4),
('working_days', '["1","2","3","4","5"]', 'json', 'working_hours', '工作日', '工作日设置，1-7代表周一到周日', 5),
('working_timezone', 'Asia/Shanghai', 'text', 'working_hours', '时区设置', '工作时间所在时区', 6),
('lunch_break_enabled', '1', 'boolean', 'working_hours', '启用午休时间', '是否设置午休时间', 7),
('lunch_start_time', '12:00', 'text', 'working_hours', '午休开始时间', '午休开始时间，格式：HH:MM', 8),
('lunch_end_time', '13:30', 'text', 'working_hours', '午休结束时间', '午休结束时间，格式：HH:MM', 9),

-- 离线时间设置
('offline_auto_message', '1', 'boolean', 'working_hours', '自动离线消息', '非工作时间是否自动显示离线消息', 10),
('offline_custom_message', '我们的工作时间是周一至周五 9:00-18:00（午休12:00-13:30），现在是非工作时间，请留言或稍后再试。', 'text', 'working_hours', '自定义离线消息', '非工作时间显示的消息内容', 11),
('weekend_message', '今天是周末，我们的客服暂时休息。工作时间：周一至周五 9:00-18:00，请留言或工作日再联系我们。', 'text', 'working_hours', '周末提示消息', '周末时显示的特殊消息', 12),

-- 节假日设置
('holiday_mode_enabled', '1', 'boolean', 'working_hours', '启用节假日模式', '是否支持节假日设置', 13),
('holiday_dates', '[]', 'json', 'working_hours', '节假日日期', '节假日日期列表，格式：["2024-01-01","2024-02-10"]', 14),
('holiday_message', '今天是节假日，客服暂时休息。如有紧急事务请留言，我们会尽快回复。', 'text', 'working_hours', '节假日消息', '节假日显示的消息', 15),

-- 手动控制
('manual_online_status', '1', 'boolean', 'working_hours', '手动在线状态', '手动控制模式下的在线状态', 16),
('status_change_log', '1', 'boolean', 'working_hours', '状态变更日志', '是否记录客服状态变更日志', 17);
