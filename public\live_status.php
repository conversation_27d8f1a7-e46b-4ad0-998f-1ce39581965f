<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>实时状态监控</title>
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .online { background: #f6ffed; border-color: #b7eb8f; }
        .offline { background: #fff2f0; border-color: #ffccc7; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat { flex: 1; text-align: center; padding: 15px; border-radius: 8px; }
        .stat-online { background: #f6ffed; color: #389e0d; }
        .stat-offline { background: #fff2f0; color: #cf1322; }
        .refresh-info { background: #e6f7ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        .time-recent { color: #52c41a; font-weight: bold; }
        .time-warning { color: #faad14; font-weight: bold; }
        .time-danger { color: #ff4d4f; font-weight: bold; }
    </style>
    <script>
        let autoRefresh = true;
        function toggleRefresh() {
            autoRefresh = !autoRefresh;
            document.getElementById('refreshBtn').textContent = autoRefresh ? '停止刷新' : '开始刷新';
        }
        
        // 每1秒刷新
        setInterval(() => {
            if (autoRefresh) {
                location.reload();
            }
        }, 1000);
    </script>
</head>
<body>
    <div class="container">
        <h1>📡 实时状态监控</h1>
        
        <div class="refresh-info">
            🕐 当前时间: <?php echo date('Y-m-d H:i:s'); ?> 
            <button id="refreshBtn" onclick="toggleRefresh()" style="margin-left: 20px; padding: 5px 10px;">停止刷新</button>
            <span style="margin-left: 10px;">每1秒自动刷新</span>
        </div>
        
        <?php
        try {
            // 获取最近的会话
            $sessions = DB::select("
                SELECT 
                    id, session_id, visitor_name, visitor_ip, status, 
                    last_seen, created_at,
                    TIMESTAMPDIFF(SECOND, last_seen, NOW()) as seconds_ago
                FROM st_customer_service_sessions 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                ORDER BY last_seen DESC 
                LIMIT 10
            ");
            
            // 统计
            $total = count($sessions);
            $online = 0;
            $offline = 0;
            
            foreach ($sessions as $session) {
                if ($session->status === 'online') {
                    $online++;
                } else {
                    $offline++;
                }
            }
            
            echo "<div class='stats'>";
            echo "<div class='stat stat-online'>";
            echo "<h3>🟢 在线</h3>";
            echo "<div style='font-size: 24px; font-weight: bold;'>{$online}</div>";
            echo "</div>";
            echo "<div class='stat stat-offline'>";
            echo "<h3>⚫ 离线</h3>";
            echo "<div style='font-size: 24px; font-weight: bold;'>{$offline}</div>";
            echo "</div>";
            echo "<div class='stat' style='background: #f0f0f0;'>";
            echo "<h3>📊 总计</h3>";
            echo "<div style='font-size: 24px; font-weight: bold;'>{$total}</div>";
            echo "</div>";
            echo "</div>";
            
            if (empty($sessions)) {
                echo "<div style='text-align: center; padding: 40px; color: #999;'>最近30分钟内没有会话记录</div>";
            } else {
                echo "<h2>📋 会话状态详情</h2>";
                echo "<table>";
                echo "<tr>";
                echo "<th>ID</th>";
                echo "<th>访客</th>";
                echo "<th>IP</th>";
                echo "<th>状态</th>";
                echo "<th>最后心跳</th>";
                echo "<th>距离现在</th>";
                echo "<th>创建时间</th>";
                echo "</tr>";
                
                foreach ($sessions as $session) {
                    $statusClass = $session->status === 'online' ? 'online' : 'offline';
                    $statusIcon = $session->status === 'online' ? '🟢' : '⚫';
                    
                    // 时间颜色
                    $timeClass = '';
                    if ($session->seconds_ago !== null) {
                        if ($session->seconds_ago <= 5) {
                            $timeClass = 'time-recent';
                        } elseif ($session->seconds_ago <= 8) {
                            $timeClass = 'time-warning';
                        } else {
                            $timeClass = 'time-danger';
                        }
                    }
                    
                    echo "<tr class='{$statusClass}'>";
                    echo "<td>{$session->id}</td>";
                    echo "<td>{$session->visitor_name}</td>";
                    echo "<td>{$session->visitor_ip}</td>";
                    echo "<td>{$statusIcon} {$session->status}</td>";
                    echo "<td>{$session->last_seen}</td>";
                    
                    if ($session->seconds_ago !== null) {
                        echo "<td class='{$timeClass}'>{$session->seconds_ago}秒前</td>";
                    } else {
                        echo "<td>无记录</td>";
                    }
                    
                    echo "<td>{$session->created_at}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 20px;'>错误: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <h2>⚡ 实时性说明</h2>
        <ul>
            <li><strong>前台心跳:</strong> 每5秒发送一次</li>
            <li><strong>离线判定:</strong> 超过8秒无心跳即离线</li>
            <li><strong>后台刷新:</strong> 每2秒检查一次</li>
            <li><strong>页面刷新:</strong> 每1秒自动刷新</li>
            <li><strong>最大延迟:</strong> 理论上最多9秒检测到状态变化</li>
        </ul>
        
        <h2>🎨 颜色说明</h2>
        <ul>
            <li><span class="time-recent">🟢 绿色</span> - 最近5秒内有心跳（正常）</li>
            <li><span class="time-warning">🟡 黄色</span> - 6-8秒前有心跳（即将离线）</li>
            <li><span class="time-danger">🔴 红色</span> - 超过8秒无心跳（应该离线）</li>
        </ul>
        
        <h2>🔗 测试链接</h2>
        <ul>
            <li><a href="/" target="_blank">网站首页（测试客服）</a></li>
            <li><a href="/strongadmin/customer-service/sessions" target="_blank">后台管理</a></li>
            <li><a href="/simple_close_test.php" target="_blank">简单测试页面</a></li>
        </ul>
    </div>
</body>
</html>
