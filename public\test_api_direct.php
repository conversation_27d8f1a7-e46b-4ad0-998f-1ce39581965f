<?php
/**
 * 直接测试API端点
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

// 获取CSRF token
session_start();
$csrfToken = $_SESSION['_token'] ?? 'test-token';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>直接测试API</title>";
echo "<meta name='csrf-token' content='$csrfToken'>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 直接测试API端点</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 获取会话列表
    $sessions = $pdo->query("SELECT * FROM st_customer_service_sessions ORDER BY created_at DESC LIMIT 5")->fetchAll();
    
    if (empty($sessions)) {
        echo "<p class='info'>ℹ️ 暂无会话数据，请先发送一些测试消息</p>";
        echo "<a href='/test_cs.php' target='_blank' class='test-btn'>发送测试消息</a>";
    } else {
        echo "<h2>📋 会话列表</h2>";
        echo "<table><tr><th>会话ID</th><th>会话标识</th><th>访客名称</th><th>状态</th><th>测试API</th></tr>";
        
        foreach ($sessions as $session) {
            $visitorName = $session['visitor_name'] ?: '访客-' . strtoupper(substr(md5($session['session_id']), 0, 6));
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>$visitorName</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>";
            echo "<button class='test-btn' onclick=\"testGetMessages({$session['id']})\">获取消息</button> ";
            echo "<button class='test-btn' onclick=\"testReply({$session['id']})\">测试回复</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 显示第一个会话的消息
        $firstSession = $sessions[0];
        $messages = $pdo->query("SELECT * FROM st_customer_service_messages WHERE session_id = {$firstSession['id']} ORDER BY created_at ASC")->fetchAll();
        
        echo "<h2>📨 会话 {$firstSession['id']} 的消息记录</h2>";
        if (empty($messages)) {
            echo "<p class='info'>ℹ️ 该会话暂无消息</p>";
        } else {
            echo "<table><tr><th>消息ID</th><th>发送者</th><th>消息内容</th><th>创建时间</th></tr>";
            foreach ($messages as $msg) {
                echo "<tr>";
                echo "<td>{$msg['id']}</td>";
                echo "<td>{$msg['sender_type']}</td>";
                echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
                echo "<td>{$msg['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 数据库错误: " . $e->getMessage() . "</p>";
}

echo "<div id='test-results' style='margin-top:20px;padding:10px;background:#f8f9fa;border-radius:4px;'></div>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/debug_api_errors.php' target='_blank'>调试API错误</a></li>";
echo "<li><a href='/test_cs.php' target='_blank'>发送测试消息</a></li>";
echo "</ul>";

echo "<script>
function testGetMessages(sessionId) {
    const results = document.getElementById('test-results');
    results.innerHTML = '<p class=\"info\">🔄 测试获取会话 ' + sessionId + ' 的消息...</p>';
    
    const url = '/strongadmin/customer-service/session/' + sessionId + '/messages';
    
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        results.innerHTML += '<p class=\"info\">📊 响应状态: ' + response.status + '</p>';
        
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error('HTTP ' + response.status + ': ' + text);
            });
        }
        return response.json();
    })
    .then(data => {
        results.innerHTML += '<p class=\"success\">✅ API调用成功</p>';
        results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        results.innerHTML += '<p class=\"error\">❌ API调用失败: ' + error.message + '</p>';
    });
}

function testReply(sessionId) {
    const results = document.getElementById('test-results');
    const message = '测试API回复 ' + new Date().toLocaleTimeString();
    
    results.innerHTML = '<p class=\"info\">🔄 测试回复会话 ' + sessionId + '...</p>';
    
    const url = '/strongadmin/customer-service/session/' + sessionId + '/reply';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || 'test-token'
        },
        body: JSON.stringify({
            message: message
        })
    })
    .then(response => {
        results.innerHTML += '<p class=\"info\">📊 响应状态: ' + response.status + '</p>';
        
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error('HTTP ' + response.status + ': ' + text);
            });
        }
        return response.json();
    })
    .then(data => {
        results.innerHTML += '<p class=\"success\">✅ 回复发送成功</p>';
        results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        results.innerHTML += '<p class=\"error\">❌ 回复发送失败: ' + error.message + '</p>';
    });
}
</script>";

echo "</body></html>";
?>
