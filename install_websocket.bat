@echo off
echo 正在安装WebSocket相关包...
echo.

echo 1. 安装Laravel WebSockets...
composer require beyondcode/laravel-websockets

echo.
echo 2. 安装Pusher PHP Server...
composer require pusher/pusher-php-server

echo.
echo 3. 发布WebSocket配置文件...
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="config"

echo.
echo 4. 发布WebSocket迁移文件...
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="migrations"

echo.
echo 5. 运行数据库迁移...
php artisan migrate

echo.
echo ✅ WebSocket安装完成！
echo.
pause
