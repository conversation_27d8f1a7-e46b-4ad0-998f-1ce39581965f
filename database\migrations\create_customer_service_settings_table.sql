-- 客服系统设置表
CREATE TABLE IF NOT EXISTS `st_customer_service_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
  `setting_value` text COMMENT '设置值',
  `setting_type` enum('text','number','boolean','json','file') NOT NULL DEFAULT 'text' COMMENT '设置类型',
  `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '设置分类',
  `title` varchar(200) NOT NULL COMMENT '设置标题',
  `description` text COMMENT '设置描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `category` (`category`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服系统设置表';

-- 插入默认设置
INSERT INTO `st_customer_service_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `title`, `description`, `sort_order`) VALUES
-- 基础设置
('system_enabled', '1', 'boolean', 'basic', '启用客服系统', '是否启用客服系统功能', 1),
('welcome_message', '您好！欢迎咨询，我们将竭诚为您服务！', 'text', 'basic', '欢迎消息', '客户打开聊天窗口时显示的欢迎消息', 2),
('offline_message', '客服暂时离线，请留下您的联系方式，我们会尽快回复您！', 'text', 'basic', '离线消息', '客服离线时显示的提示消息', 3),

-- 声音设置
('sound_enabled', '1', 'boolean', 'sound', '启用提示音', '是否启用消息提示音', 1),
('sound_type', 'default', 'text', 'sound', '提示音类型', 'default=默认音效, custom=自定义音效', 2),
('sound_file', '', 'file', 'sound', '自定义音效文件', '支持MP3格式，建议文件大小不超过1MB', 3),
('sound_volume', '0.5', 'number', 'sound', '音量大小', '提示音音量，范围0-1', 4),

-- 外观设置
('chat_position', 'bottom-right', 'text', 'appearance', '聊天窗口位置', 'bottom-right, bottom-left, top-right, top-left', 1),
('chat_theme_color', '#667eea', 'text', 'appearance', '主题颜色', '聊天窗口的主题颜色', 2),
('show_avatar', '1', 'boolean', 'appearance', '显示头像', '是否在消息中显示头像', 3),

-- 离线表单设置
('offline_form_enabled', '1', 'boolean', 'offline', '启用离线表单', '客服离线时是否显示留言表单', 1),
('offline_form_fields', '["email","phone","whatsapp","message"]', 'json', 'offline', '表单字段', '离线表单包含的字段', 2),
('offline_form_required', '["message"]', 'json', 'offline', '必填字段', '离线表单的必填字段', 3),

-- 高级设置
('auto_open_enabled', '1', 'boolean', 'advanced', '自动打开聊天窗口', '收到客服消息时是否自动打开聊天窗口', 1),
('heartbeat_interval', '5', 'number', 'advanced', '心跳间隔(秒)', '客户端发送心跳的间隔时间', 2),
('offline_timeout', '8', 'number', 'advanced', '离线超时(秒)', '多少秒无心跳后判定为离线', 3),
('max_message_length', '1000', 'number', 'advanced', '消息最大长度', '单条消息的最大字符数', 4);
