<?php
/**
 * 完整的端到端测试 - 模拟整个消息流程
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>完整测试</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;} .log{background:#f8f9fa;padding:10px;border-radius:4px;margin:10px 0;font-family:monospace;font-size:12px;max-height:300px;overflow-y:auto;border:1px solid #ddd;}</style>";
echo "</head><body>";

echo "<h1>🔍 完整的端到端测试</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 处理测试操作
    if ($_POST['action'] ?? '' === 'full_test') {
        echo "<h2>🧪 执行完整测试</h2>";
        
        $testSessionId = 'test_complete_' . time();
        
        // 步骤1：创建会话
        echo "<h3>步骤1：创建会话</h3>";
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, '127.0.0.1', 'Test Browser', 'active', NOW(), NOW(), NOW())");
        $stmt->execute([$testSessionId]);
        $sessionDbId = $pdo->lastInsertId();
        echo "<p class='success'>✅ 会话创建成功，数据库ID: $sessionDbId</p>";
        
        // 步骤2：发送客户消息
        echo "<h3>步骤2：发送客户消息</h3>";
        $customerMessage = '测试客户消息 ' . date('H:i:s');
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, message, is_read, created_at, updated_at) VALUES (?, 'customer', ?, 0, NOW(), NOW())");
        $stmt->execute([$sessionDbId, $customerMessage]);
        $customerMessageId = $pdo->lastInsertId();
        echo "<p class='success'>✅ 客户消息发送成功，消息ID: $customerMessageId</p>";
        echo "<p class='info'>消息内容: $customerMessage</p>";
        
        // 步骤3：发送管理员回复
        echo "<h3>步骤3：发送管理员回复</h3>";
        $adminReply = '测试管理员回复 ' . date('H:i:s');
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, sender_id, message, is_read, created_at, updated_at) VALUES (?, 'admin', 1, ?, 0, NOW(), NOW())");
        $stmt->execute([$sessionDbId, $adminReply]);
        $adminMessageId = $pdo->lastInsertId();
        echo "<p class='success'>✅ 管理员回复发送成功，消息ID: $adminMessageId</p>";
        echo "<p class='info'>回复内容: $adminReply</p>";
        
        // 步骤4：模拟前台SSE查询
        echo "<h3>步骤4：模拟前台SSE查询</h3>";
        $stmt = $pdo->prepare("SELECT * FROM st_customer_service_messages WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') ORDER BY id ASC");
        $stmt->execute([$sessionDbId, $customerMessageId]);
        $newMessages = $stmt->fetchAll();
        
        if (empty($newMessages)) {
            echo "<p class='error'>❌ 前台SSE查询没有找到新回复消息</p>";
            echo "<p class='info'>查询条件：session_id=$sessionDbId, id>$customerMessageId, sender_type IN ('ai', 'admin')</p>";
        } else {
            echo "<p class='success'>✅ 前台SSE查询找到 " . count($newMessages) . " 条新回复消息</p>";
            echo "<table><tr><th>消息ID</th><th>发送者</th><th>消息内容</th><th>创建时间</th></tr>";
            foreach ($newMessages as $msg) {
                echo "<tr>";
                echo "<td>{$msg['id']}</td>";
                echo "<td>{$msg['sender_type']}</td>";
                echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
                echo "<td>{$msg['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 步骤5：测试SSE响应格式
        echo "<h3>步骤5：测试SSE响应格式</h3>";
        if (!empty($newMessages)) {
            foreach ($newMessages as $message) {
                $sseData = [
                    'type' => 'new_reply',
                    'message_id' => $message['id'],
                    'sender_type' => $message['sender_type'],
                    'message' => $message['message'],
                    'created_at' => $message['created_at'],
                    'timestamp' => time()
                ];
                echo "<p class='success'>✅ SSE数据格式正确</p>";
                echo "<pre>" . json_encode($sseData, JSON_PRETTY_PRINT) . "</pre>";
            }
        }
        
        // 保存测试数据供SSE使用
        file_put_contents('test_session_data.txt', json_encode([
            'session_id' => $testSessionId,
            'session_db_id' => $sessionDbId,
            'last_message_id' => $customerMessageId,
            'admin_message_id' => $adminMessageId
        ]));
        
        echo "<p class='info'>📝 测试数据已保存，可以用于SSE测试</p>";
    }
    
    // 显示当前数据库状态
    echo "<h2>📊 当前数据库状态</h2>";
    
    // 最新会话
    $sessions = $pdo->query("SELECT * FROM st_customer_service_sessions ORDER BY created_at DESC LIMIT 5")->fetchAll();
    echo "<h3>最新会话</h3>";
    if (empty($sessions)) {
        echo "<p class='info'>暂无会话</p>";
    } else {
        echo "<table><tr><th>数据库ID</th><th>会话标识</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 最新消息
    $messages = $pdo->query("SELECT m.*, s.session_id as session_key FROM st_customer_service_messages m LEFT JOIN st_customer_service_sessions s ON m.session_id = s.id ORDER BY m.created_at DESC LIMIT 10")->fetchAll();
    echo "<h3>最新消息</h3>";
    if (empty($messages)) {
        echo "<p class='info'>暂无消息</p>";
    } else {
        echo "<table><tr><th>消息ID</th><th>会话</th><th>发送者</th><th>消息内容</th><th>创建时间</th></tr>";
        foreach ($messages as $msg) {
            $senderClass = $msg['sender_type'] === 'admin' ? 'success' : ($msg['sender_type'] === 'ai' ? 'info' : '');
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['session_key']}</td>";
            echo "<td class='$senderClass'>{$msg['sender_type']}</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td>{$msg['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<h2>🧪 测试操作</h2>";
echo "<form method='POST'>";
echo "<button type='submit' name='action' value='full_test' class='test-btn'>执行完整测试</button>";
echo "</form>";

echo "<h2>🔗 测试链接</h2>";
echo "<ul>";
echo "<li><a href='/real_sse.php?session_id=test123&last_message_id=0' target='_blank'>测试真实SSE</a></li>";
echo "<li><a href='/working_chat.html' target='_blank'>工作的客服系统</a></li>";
echo "<li><a href='/' target='_blank'>前台首页</a></li>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台管理</a></li>";
echo "</ul>";

echo "</body></html>";
?>
