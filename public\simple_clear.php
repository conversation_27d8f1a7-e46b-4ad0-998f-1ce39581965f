<?php
/**
 * 简单清除缓存
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>清除缓存</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;}</style>";
echo "</head><body>";

echo "<h1>🧹 清除缓存</h1>";

try {
    $basePath = dirname(__DIR__);
    
    // 清除缓存目录
    $cacheDirectories = [
        $basePath . '/bootstrap/cache',
        $basePath . '/storage/framework/cache',
        $basePath . '/storage/framework/views',
        $basePath . '/storage/framework/sessions'
    ];
    
    foreach ($cacheDirectories as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*');
            $count = 0;
            foreach ($files as $file) {
                if (is_file($file) && basename($file) !== '.gitignore') {
                    unlink($file);
                    $count++;
                }
            }
            echo "<p class='success'>✅ 清除目录 " . basename($dir) . " ($count 个文件)</p>";
        }
    }
    
    // 清除配置缓存文件
    $configCache = $basePath . '/bootstrap/cache/config.php';
    if (file_exists($configCache)) {
        unlink($configCache);
        echo "<p class='success'>✅ 清除配置缓存</p>";
    }
    
    // 清除路由缓存文件
    $routeCache = $basePath . '/bootstrap/cache/routes.php';
    if (file_exists($routeCache)) {
        unlink($routeCache);
        echo "<p class='success'>✅ 清除路由缓存</p>";
    }
    
    echo "<h2>🎉 缓存清除完成！</h2>";
    echo "<p>现在可以测试客服系统功能了</p>";
    echo "<p><a href='/' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>返回首页</a></p>";
    echo "<p><a href='/strongadmin' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>进入后台</a></p>";

} catch (Exception $e) {
    echo "<p class='error'>❌ 清除缓存失败: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
