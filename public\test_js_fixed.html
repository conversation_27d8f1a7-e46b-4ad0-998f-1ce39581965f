<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript修复测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .success { 
            color: #28a745; 
            background: #d4edda; 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .error { 
            color: #dc3545; 
            background: #f8d7da; 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .layui-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .layui-btn:hover {
            background: #0078d4;
        }
        .layui-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .layui-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 80px;
        }
        .layui-tab-title {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        .layui-tab-title li {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .layui-tab-title li.layui-this {
            border-bottom-color: #1E9FFF;
            color: #1E9FFF;
        }
        .layui-tab-item {
            display: none;
            padding: 20px;
        }
        .layui-tab-item.layui-show {
            display: block;
        }
        .layui-progress {
            background: #f0f0f0;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }
        .layui-progress-bar {
            height: 100%;
            background: #1E9FFF;
            transition: width 0.3s ease;
        }
        .st-stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #1E9FFF;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JavaScript修复测试</h1>
        <p>测试原生JavaScript替换jQuery和Layui的功能</p>
        
        <!-- 测试1: 表单提交 -->
        <div class="test-section">
            <h3>测试1: 表单提交功能</h3>
            <form id="settings-form" class="reply-form">
                <textarea name="reply" class="layui-textarea" placeholder="测试回复内容..."></textarea>
                <button type="submit" class="layui-btn" data-message-id="123">
                    <i class="layui-icon layui-icon-ok"></i> 测试提交
                </button>
            </form>
            <div id="form-result"></div>
        </div>
        
        <!-- 测试2: 标签页切换 -->
        <div class="test-section">
            <h3>测试2: 标签页切换功能</h3>
            <ul class="layui-tab-title">
                <li class="layui-this">标签1</li>
                <li>标签2</li>
                <li>标签3</li>
            </ul>
            <div class="layui-tab-item layui-show">
                <p>这是标签1的内容</p>
            </div>
            <div class="layui-tab-item">
                <p>这是标签2的内容</p>
            </div>
            <div class="layui-tab-item">
                <p>这是标签3的内容</p>
            </div>
        </div>
        
        <!-- 测试3: 颜色选择器 -->
        <div class="test-section">
            <h3>测试3: 颜色选择器联动</h3>
            <div>
                <input type="color" value="#1E9FFF" style="margin-right: 10px;">
                <input type="text" class="color-display" value="#1E9FFF" readonly style="margin-right: 10px;">
                <span class="st-color-preview" style="display:inline-block;width:30px;height:30px;background:#1E9FFF;border:1px solid #ddd;"></span>
            </div>
        </div>
        
        <!-- 测试4: 进度条和数字动画 -->
        <div class="test-section">
            <h3>测试4: 进度条和数字动画</h3>
            <div class="layui-progress">
                <div class="layui-progress-bar" lay-percent="75%"></div>
            </div>
            <p>统计数字: <span class="st-stats-number">1234</span></p>
        </div>
        
        <!-- 测试5: 显示/隐藏功能 -->
        <div class="test-section">
            <h3>测试5: 显示/隐藏功能</h3>
            <button class="layui-btn" onclick="showReplyForm('test')">显示回复表单</button>
            <button class="layui-btn" onclick="hideReplyForm('test')">隐藏回复表单</button>
            <div id="reply-form-test" style="display:none; margin-top:10px; padding:10px; background:#f0f0f0;">
                <p>这是一个测试回复表单</p>
            </div>
        </div>
        
        <div id="test-log" style="background:#f8f9fa;padding:15px;border-radius:4px;margin-top:20px;font-family:monospace;font-size:12px;max-height:200px;overflow-y:auto;">
            <div style="color:#28a745;font-weight:bold;">📋 测试日志:</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            logDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化...', 'info');
            
            // 测试1: 表单提交
            document.addEventListener('submit', function(e) {
                if (e.target.classList.contains('reply-form')) {
                    e.preventDefault();
                    log('表单提交事件触发', 'success');
                    
                    var form = e.target;
                    var messageId = form.querySelector('[data-message-id]').getAttribute('data-message-id');
                    var replyTextarea = form.querySelector('textarea[name="reply"]');
                    var replyText = replyTextarea.value;
                    var submitBtn = form.querySelector('button[type="submit"]');
                    var originalText = submitBtn.innerHTML;
                    
                    if (!replyText.trim()) {
                        log('回复内容为空', 'error');
                        alert('请输入回复内容');
                        return;
                    }
                    
                    log('开始模拟提交，消息ID: ' + messageId, 'info');
                    
                    // 显示加载状态
                    submitBtn.innerHTML = '<i class="layui-icon layui-icon-loading"></i> 发送中...';
                    submitBtn.disabled = true;
                    
                    // 模拟异步请求
                    setTimeout(() => {
                        log('模拟请求完成', 'success');
                        document.getElementById('form-result').innerHTML = '<div class="success">✅ 表单提交测试成功！</div>';
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 2000);
                }
            });
            
            // 测试2: 标签页切换
            var tabTitles = document.querySelectorAll('.layui-tab-title li');
            var tabItems = document.querySelectorAll('.layui-tab-item');
            
            tabTitles.forEach(function(tab, index) {
                tab.addEventListener('click', function() {
                    log('切换到标签 ' + (index + 1), 'info');
                    
                    // 移除所有活动状态
                    tabTitles.forEach(function(t) { t.classList.remove('layui-this'); });
                    tabItems.forEach(function(item) { item.classList.remove('layui-show'); });
                    
                    // 添加当前活动状态
                    this.classList.add('layui-this');
                    if (tabItems[index]) {
                        tabItems[index].classList.add('layui-show');
                    }
                });
            });
            
            // 测试3: 颜色选择器联动
            var colorInputs = document.querySelectorAll('input[type="color"]');
            colorInputs.forEach(function(colorInput) {
                colorInput.addEventListener('change', function() {
                    log('颜色改变为: ' + this.value, 'info');
                    
                    var colorDisplay = this.parentNode.querySelector('.color-display');
                    var colorPreview = this.parentNode.querySelector('.st-color-preview');
                    
                    if (colorDisplay) {
                        colorDisplay.value = this.value;
                    }
                    if (colorPreview) {
                        colorPreview.style.backgroundColor = this.value;
                    }
                });
            });
            
            // 测试4: 进度条渲染
            var progressBars = document.querySelectorAll('.layui-progress');
            progressBars.forEach(function(progress) {
                var progressBar = progress.querySelector('.layui-progress-bar');
                if (progressBar) {
                    var percent = progressBar.getAttribute('lay-percent');
                    if (percent) {
                        progressBar.style.width = percent;
                        log('进度条设置为: ' + percent, 'success');
                    }
                }
            });
            
            // 测试4: 数字动画
            setTimeout(function() {
                var statsNumbers = document.querySelectorAll('.st-stats-number');
                statsNumbers.forEach(function(element) {
                    var countTo = parseInt(element.textContent);
                    var current = 0;
                    var increment = countTo / 50;
                    
                    log('开始数字动画: ' + countTo, 'info');
                    
                    var timer = setInterval(function() {
                        current += increment;
                        if (current >= countTo) {
                            element.textContent = countTo;
                            clearInterval(timer);
                            log('数字动画完成', 'success');
                        } else {
                            element.textContent = Math.floor(current);
                        }
                    }, 20);
                });
            }, 1000);
            
            log('所有功能初始化完成', 'success');
        });

        // 显示回复表单
        function showReplyForm(messageId) {
            var form = document.getElementById('reply-form-' + messageId);
            if (form) {
                form.style.display = 'block';
                log('显示回复表单: ' + messageId, 'info');
            }
        }

        // 隐藏回复表单
        function hideReplyForm(messageId) {
            var form = document.getElementById('reply-form-' + messageId);
            if (form) {
                form.style.display = 'none';
                log('隐藏回复表单: ' + messageId, 'info');
            }
        }
    </script>
</body>
</html>
