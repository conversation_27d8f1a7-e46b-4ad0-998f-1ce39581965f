<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌺 东南亚风格 UI 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .preview-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        /* 复制主要的东南亚风格样式 */
        .sea-product-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 20px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(255, 154, 158, 0.3);
            border: 2px solid #fff;
        }

        .sea-product-header.recommend-section {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
            box-shadow: 0 8px 32px rgba(255, 215, 0, 0.4);
        }

        .sea-product-header.new-section {
            background: linear-gradient(135deg, #00c9ff 0%, #92fe9d 50%, #00c9ff 100%);
            box-shadow: 0 8px 32px rgba(0, 201, 255, 0.3);
        }

        .sea-product-header.hot-section {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 50%, #ff6b35 100%);
            box-shadow: 0 8px 32px rgba(255, 65, 108, 0.4);
        }

        .sea-product-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: seaShimmer 3s ease-in-out infinite;
        }

        .sea-icon-wrapper {
            position: relative;
            display: inline-block;
            margin-bottom: 10px;
        }

        .sea-icon {
            font-size: 32px;
            display: inline-block;
            animation: seaBounce 2s ease-in-out infinite;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
        }

        .sea-sparkles {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            pointer-events: none;
        }

        .sparkle {
            position: absolute;
            font-size: 16px;
            animation: seaSparkle 2s ease-in-out infinite;
        }

        .sparkle:nth-child(1) {
            top: 0;
            right: 0;
            animation-delay: 0s;
        }

        .sparkle:nth-child(2) {
            bottom: 0;
            left: 0;
            animation-delay: 0.7s;
        }

        .sparkle:nth-child(3) {
            top: 50%;
            right: -5px;
            animation-delay: 1.4s;
        }

        .sea-product-title {
            display: block;
            text-decoration: none;
            color: #fff;
            font-size: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 0;
            transition: all 0.3s ease;
        }

        .sea-product-title:hover {
            color: #fff;
            text-decoration: none;
            transform: scale(1.02);
        }

        .sea-subtitle {
            display: block;
            font-size: 14px;
            font-weight: 400;
            margin-top: 5px;
            opacity: 0.9;
            font-style: italic;
        }

        .sea-decorative-line {
            height: 4px;
            background: linear-gradient(90deg, transparent 0%, #fff 20%, #fff 80%, transparent 100%);
            margin-top: 15px;
            border-radius: 2px;
            animation: seaLineGlow 2s ease-in-out infinite alternate;
        }

        @keyframes seaShimmer {
            0%, 100% { transform: rotate(0deg); opacity: 0.1; }
            50% { transform: rotate(180deg); opacity: 0.3; }
        }

        @keyframes seaBounce {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-5px) scale(1.1); }
        }

        @keyframes seaSparkle {
            0%, 100% { opacity: 0; transform: scale(0.5) rotate(0deg); }
            50% { opacity: 1; transform: scale(1) rotate(180deg); }
        }

        @keyframes seaLineGlow {
            0% { opacity: 0.7; transform: scaleX(1); }
            100% { opacity: 1; transform: scaleX(1.02); }
        }

        .features {
            list-style: none;
            margin: 20px 0;
        }

        .features li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
        }

        .features li:before {
            content: "🌟";
            margin-right: 10px;
            font-size: 16px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .before, .after {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .before {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
        }

        .after {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .sea-icon {
                font-size: 28px;
            }
            
            .sea-product-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌺 东南亚风格 UI 设计</h1>
            <p>热带色彩 • 丰富装饰 • 节庆氛围</p>
        </div>
        
        <div class="preview-section">
            <h2 style="margin-bottom: 20px; color: #333;">🎨 设计效果预览</h2>
            
            <!-- 推荐产品 -->
            <div class="sea-product-header recommend-section">
                <div class="sea-icon-wrapper">
                    <span class="sea-icon">👑</span>
                    <div class="sea-sparkles">
                        <span class="sparkle">✨</span>
                        <span class="sparkle">💎</span>
                        <span class="sparkle">⭐</span>
                    </div>
                </div>
                <h4><a href="#" class="sea-product-title">
                    Recommend Products
                    <span class="sea-subtitle">精选好物 • 为您推荐</span>
                </a></h4>
                <div class="sea-decorative-line"></div>
            </div>
            
            <!-- 新品 -->
            <div class="sea-product-header new-section">
                <div class="sea-icon-wrapper">
                    <span class="sea-icon">🎉</span>
                    <div class="sea-sparkles">
                        <span class="sparkle">🌟</span>
                        <span class="sparkle">💫</span>
                        <span class="sparkle">✨</span>
                    </div>
                </div>
                <h4><a href="#" class="sea-product-title">
                    New Products
                    <span class="sea-subtitle">新品上市 • 抢先体验</span>
                </a></h4>
                <div class="sea-decorative-line"></div>
            </div>
            
            <!-- 热卖 -->
            <div class="sea-product-header hot-section">
                <div class="sea-icon-wrapper">
                    <span class="sea-icon">🔥</span>
                    <div class="sea-sparkles">
                        <span class="sparkle">💥</span>
                        <span class="sparkle">⚡</span>
                        <span class="sparkle">🌟</span>
                    </div>
                </div>
                <h4><a href="#" class="sea-product-title">
                    Hot Products
                    <span class="sea-subtitle">热销爆款 • 人气之选</span>
                </a></h4>
                <div class="sea-decorative-line"></div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 style="margin-bottom: 20px; color: #333;">🌟 东南亚风格特点</h2>
            <ul class="features">
                <li>鲜艳的热带色彩搭配</li>
                <li>金色渐变寓意财富好运</li>
                <li>丰富的装饰元素和闪烁效果</li>
                <li>圆润的设计语言</li>
                <li>节庆感的视觉氛围</li>
                <li>动态的光影效果</li>
                <li>双语标题设计</li>
                <li>手机端完美适配</li>
            </ul>
        </div>
        
        <div class="preview-section">
            <h2 style="margin-bottom: 20px; color: #333;">📱 对比效果</h2>
            <div class="comparison">
                <div class="before">
                    <h3>原版设计</h3>
                    <p style="margin: 15px 0; color: #666;">
                        <span style="font-size: 14px;">▶</span> New Products
                    </p>
                    <small>简单的箭头图标 + 文字</small>
                </div>
                <div class="after">
                    <h3>东南亚风格</h3>
                    <p style="margin: 15px 0;">
                        🎉 New Products<br>
                        <small style="opacity: 0.9;">新品上市 • 抢先体验</small>
                    </p>
                    <small>渐变背景 + 装饰元素 + 双语设计</small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
