-- 客服系统数据表

-- 客服会话表：存储每个客户的会话信息
CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '会话主键ID',
    `session_id` varchar(255) NOT NULL COMMENT '会话唯一标识符',
    `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联的用户ID（如果是注册用户）',
    `visitor_name` varchar(255) DEFAULT NULL COMMENT '访客姓名',
    `visitor_email` varchar(255) DEFAULT NULL COMMENT '访客邮箱',
    `visitor_ip` varchar(255) DEFAULT NULL COMMENT '访客IP地址',
    `user_agent` text DEFAULT NULL COMMENT '访客浏览器信息',
    `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active' COMMENT '会话状态：active=活跃，waiting=等待，closed=已关闭',
    `assigned_admin_id` bigint(20) unsigned DEFAULT NULL COMMENT '分配的客服管理员ID',
    `unread_count` int(11) NOT NULL DEFAULT 0 COMMENT '未读消息数量',
    `last_activity` timestamp NULL DEFAULT NULL COMMENT '最后活动时间',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `session_id` (`session_id`),
    KEY `assigned_admin_id` (`assigned_admin_id`),
    KEY `status_last_activity` (`status`, `last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服会话表';

-- 客服消息表：存储会话中的所有消息记录
CREATE TABLE IF NOT EXISTS `customer_service_messages` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '消息主键ID',
    `session_id` bigint(20) unsigned NOT NULL COMMENT '关联的会话ID',
    `sender_type` enum('customer','admin','ai') NOT NULL COMMENT '发送者类型：customer=客户，admin=客服，ai=AI自动回复',
    `sender_id` bigint(20) unsigned DEFAULT NULL COMMENT '发送者ID（客户ID或管理员ID）',
    `message` text NOT NULL COMMENT '消息内容',
    `attachments` json DEFAULT NULL COMMENT '附件信息（JSON格式）',
    `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0=未读，1=已读',
    `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `session_id` (`session_id`),
    KEY `sender_type_created` (`sender_type`, `created_at`),
    KEY `is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';

-- AI自动回复规则表：存储AI自动回复的规则配置
CREATE TABLE IF NOT EXISTS `ai_auto_reply_rules` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则主键ID',
    `name` varchar(255) NOT NULL COMMENT '规则名称',
    `keywords` json NOT NULL COMMENT '触发关键词（JSON数组格式）',
    `reply_message` text NOT NULL COMMENT 'AI回复的消息内容',
    `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
    `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数统计',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `is_active_priority` (`is_active`,`priority`),
    KEY `usage_count` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI自动回复规则表';

-- 添加默认AI规则
INSERT IGNORE INTO `ai_auto_reply_rules` (`name`, `keywords`, `reply_message`, `priority`, `is_active`, `usage_count`, `created_at`, `updated_at`) VALUES
('问候语', '["hello", "hi", "hey", "你好", "您好"]', 'Hello! 👋 Welcome to our store! How can I help you today?', 10, 1, 0, NOW(), NOW()),
('物流咨询', '["shipping", "delivery", "物流", "快递", "发货"]', 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You will receive a tracking number once your order ships.', 9, 1, 0, NOW(), NOW()),
('退换货', '["return", "refund", "退货", "退款", "换货"]', 'We have a 30-day return policy! 🔄 If you are not satisfied, you can return items within 30 days for a full refund.', 8, 1, 0, NOW(), NOW());

-- 客服系统配置表：存储系统级别的配置
CREATE TABLE IF NOT EXISTS `st_customer_service_config` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置主键ID',
    `config_key` varchar(255) NOT NULL COMMENT '配置键名',
    `config_value` text DEFAULT NULL COMMENT '配置值',
    `config_type` enum('string','integer','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
    `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服系统配置表';

-- 插入系统配置
INSERT IGNORE INTO `st_customer_service_config` (`config_key`, `config_value`, `config_type`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
('system_enabled', '1', 'boolean', '在线客服系统总开关', 1, NOW(), NOW()),
('auto_reply_enabled', '1', 'boolean', 'AI自动回复功能开关', 1, NOW(), NOW()),
('welcome_message', '您好！欢迎咨询，我是智能客服助手，有什么可以帮助您的吗？', 'string', '欢迎消息', 1, NOW(), NOW()),
('offline_message', '客服系统暂时离线，请稍后再试或留言给我们。', 'string', '离线提示消息', 1, NOW(), NOW()),
('max_sessions_per_ip', '5', 'integer', '每个IP最大会话数限制', 1, NOW(), NOW()),
('session_timeout_minutes', '30', 'integer', '会话超时时间（分钟）', 1, NOW(), NOW());

-- 添加后台菜单
INSERT IGNORE INTO `st_strongadmin_menu` (`level`, `parent_id`, `name`, `route_url`, `permissions`, `status`, `sort`, `created_at`, `updated_at`)
SELECT 1, 0, '在线客服', '', '', 1, 90, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `st_strongadmin_menu` WHERE `name` = '在线客服' AND `level` = 1);

-- 获取父菜单ID并添加子菜单
SET @parent_id = (SELECT id FROM `st_strongadmin_menu` WHERE `name` = '在线客服' AND `level` = 1 LIMIT 1);

INSERT IGNORE INTO `st_strongadmin_menu` (`level`, `parent_id`, `name`, `route_url`, `permissions`, `status`, `sort`, `created_at`, `updated_at`) VALUES
(2, @parent_id, '会话管理', 'customer-service/sessions', '', 1, 1, NOW(), NOW()),
(2, @parent_id, 'AI规则管理', 'customer-service/ai-rules', '', 1, 2, NOW(), NOW()),
(2, @parent_id, '统计报表', 'customer-service/statistics', '', 1, 3, NOW(), NOW());
