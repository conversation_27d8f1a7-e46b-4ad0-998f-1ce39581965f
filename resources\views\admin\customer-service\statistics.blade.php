@extends('admin.layouts.app')

@section('title', '客服统计')

@section('content')
<div class="container-fluid">
    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_sessions'] }}</h3>
                    <p>总会话数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-comments"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['active_sessions'] }}</h3>
                    <p>活跃会话</p>
                </div>
                <div class="icon">
                    <i class="fas fa-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['total_messages'] }}</h3>
                    <p>总消息数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-envelope"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['ai_messages'] }}</h3>
                    <p>AI回复数</p>
                </div>
                <div class="icon">
                    <i class="fas fa-robot"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 最近会话 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clock"></i> 最近会话
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.customer-service.sessions') }}" class="btn btn-primary btn-sm">
                            查看全部
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>客户</th>
                                    <th>最新消息</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentSessions as $session)
                                <tr>
                                    <td>
                                        <strong>{{ $session->display_name }}</strong>
                                        @if($session->user)
                                            <span class="badge badge-primary badge-sm">注册</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($session->latestMessage)
                                            <div class="text-truncate" style="max-width: 200px;">
                                                {{ $session->latestMessage->message }}
                                            </div>
                                        @else
                                            <span class="text-muted">暂无消息</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $session->last_activity ? $session->last_activity->diffForHumans() : '无' }}</small>
                                    </td>
                                    <td>
                                        @if($session->status == 'active')
                                            <span class="badge badge-success">活跃</span>
                                        @elseif($session->status == 'waiting')
                                            <span class="badge badge-warning">等待</span>
                                        @else
                                            <span class="badge badge-secondary">关闭</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.customer-service.session', $session->id) }}" 
                                           class="btn btn-sm btn-primary">查看</a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted">暂无会话记录</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 热门AI规则 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-fire"></i> 热门AI规则
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.customer-service.ai-rules') }}" class="btn btn-primary btn-sm">
                            管理规则
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        @forelse($popularRules as $rule)
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $rule->name }}</h6>
                                    <small class="text-muted">
                                        @foreach(array_slice($rule->keywords, 0, 3) as $keyword)
                                            <span class="badge badge-secondary badge-sm">{{ $keyword }}</span>
                                        @endforeach
                                    </small>
                                </div>
                                <span class="badge badge-primary">{{ $rule->usage_count }}</span>
                            </div>
                        </div>
                        @empty
                        <div class="list-group-item text-center text-muted">
                            暂无AI规则
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- 消息类型统计 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie"></i> 消息类型统计
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <div class="progress-group">
                                <span class="progress-text">客户消息</span>
                                <span class="float-right"><b>{{ $stats['customer_messages'] }}</b></span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-primary" 
                                         style="width: {{ $stats['total_messages'] > 0 ? ($stats['customer_messages'] / $stats['total_messages'] * 100) : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="progress-group">
                                <span class="progress-text">AI回复</span>
                                <span class="float-right"><b>{{ $stats['ai_messages'] }}</b></span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-success" 
                                         style="width: {{ $stats['total_messages'] > 0 ? ($stats['ai_messages'] / $stats['total_messages'] * 100) : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <div class="progress-group">
                                <span class="progress-text">客服回复</span>
                                <span class="float-right"><b>{{ $stats['admin_messages'] }}</b></span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-warning" 
                                         style="width: {{ $stats['total_messages'] > 0 ? ($stats['admin_messages'] / $stats['total_messages'] * 100) : 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.small-box {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-group {
    margin-bottom: 15px;
}

.progress-text {
    font-size: 12px;
    font-weight: 600;
}

.badge-sm {
    font-size: 0.7em;
}
</style>
@endsection
