<?php
/**
 * 实时在线状态监测页面
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>实时在线监测</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:1200px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:20px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
.online{color:#28a745;font-weight:bold;}
.offline{color:#dc3545;font-weight:bold;}
.timeout{background:#fff3cd;}
.recent{background:#d4edda;}
.stats{display:flex;gap:20px;margin:20px 0;}
.stat-card{flex:1;padding:15px;border-radius:8px;text-align:center;}
.stat-online{background:#d4edda;color:#155724;}
.stat-offline{background:#f8d7da;color:#721c24;}
.stat-total{background:#d1ecf1;color:#0c5460;}
</style>";
echo "<script>
let autoRefresh = true;
function toggleAutoRefresh() {
    autoRefresh = !autoRefresh;
    document.getElementById('refreshBtn').textContent = autoRefresh ? '停止自动刷新' : '开始自动刷新';
    if (autoRefresh) {
        setTimeout(refreshPage, 1000);
    }
}

function refreshPage() {
    if (autoRefresh) {
        location.reload();
    }
}

// 每1秒刷新一次
setTimeout(refreshPage, 1000);
</script>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>📡 实时在线状态监测</h1>";

try {
    echo "<div class='info'>🕐 当前时间: " . date('Y-m-d H:i:s') . " (每1秒自动刷新)</div>";
    
    echo "<div style='margin:10px 0;'>";
    echo "<button id='refreshBtn' onclick='toggleAutoRefresh()' style='padding:8px 16px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>停止自动刷新</button>";
    echo "</div>";
    
    // 查询所有会话的详细状态
    $sessions = DB::select("
        SELECT 
            id,
            session_id,
            visitor_name,
            visitor_email,
            visitor_ip,
            status,
            last_seen,
            last_activity,
            created_at,
            TIMESTAMPDIFF(SECOND, last_seen, NOW()) as seconds_since_heartbeat,
            TIMESTAMPDIFF(SECOND, last_activity, NOW()) as seconds_since_activity
        FROM st_customer_service_sessions 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY last_seen DESC
    ");
    
    // 统计数据
    $totalSessions = count($sessions);
    $onlineSessions = array();
    $offlineSessions = array();

    foreach ($sessions as $session) {
        if ($session->status === 'online') {
            $onlineSessions[] = $session;
        } else {
            $offlineSessions[] = $session;
        }
    }
    
    echo "<div class='stats'>";
    echo "<div class='stat-card stat-total'>";
    echo "<h3>总会话数</h3>";
    echo "<div style='font-size:24px;font-weight:bold;'>{$totalSessions}</div>";
    echo "</div>";
    echo "<div class='stat-card stat-online'>";
    echo "<h3>🟢 在线</h3>";
    echo "<div style='font-size:24px;font-weight:bold;'>" . count($onlineSessions) . "</div>";
    echo "</div>";
    echo "<div class='stat-card stat-offline'>";
    echo "<h3>⚫ 离线</h3>";
    echo "<div style='font-size:24px;font-weight:bold;'>" . count($offlineSessions) . "</div>";
    echo "</div>";
    echo "</div>";
    
    if (empty($sessions)) {
        echo "<div class='warning'>⚠️ 最近1小时内无会话数据</div>";
    } else {
        echo "<h2>📋 会话详细状态 (最近1小时)</h2>";
        echo "<table>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>访客信息</th>";
        echo "<th>IP地址</th>";
        echo "<th>状态</th>";
        echo "<th>最后心跳</th>";
        echo "<th>心跳间隔</th>";
        echo "<th>最后活动</th>";
        echo "<th>活动间隔</th>";
        echo "<th>创建时间</th>";
        echo "</tr>";
        
        foreach ($sessions as $session) {
            $statusClass = $session->status === 'online' ? 'online' : 'offline';
            $statusIcon = $session->status === 'online' ? '🟢' : '⚫';
            
            // 根据心跳间隔设置行样式
            $rowClass = '';
            if ($session->seconds_since_heartbeat !== null) {
                if ($session->seconds_since_heartbeat <= 5) {
                    $rowClass = 'recent'; // 最近5秒内有心跳
                } elseif ($session->seconds_since_heartbeat > 15) {
                    $rowClass = 'timeout'; // 超过15秒无心跳
                }
            }
            
            echo "<tr class='{$rowClass}'>";
            echo "<td>{$session->id}</td>";
            
            $visitorInfo = $session->visitor_name ?: '未知访客';
            if ($session->visitor_email) {
                $visitorInfo .= "<br><small>{$session->visitor_email}</small>";
            }
            echo "<td>{$visitorInfo}</td>";
            
            echo "<td>{$session->visitor_ip}</td>";
            echo "<td class='{$statusClass}'>{$statusIcon} {$session->status}</td>";
            echo "<td>{$session->last_seen}</td>";
            
            // 心跳间隔
            if ($session->seconds_since_heartbeat !== null) {
                $heartbeatTime = $session->seconds_since_heartbeat;
                if ($heartbeatTime <= 5) {
                    echo "<td style='color:#28a745;font-weight:bold;'>{$heartbeatTime}秒前</td>";
                } elseif ($heartbeatTime <= 15) {
                    echo "<td style='color:#ffc107;'>{$heartbeatTime}秒前</td>";
                } else {
                    echo "<td style='color:#dc3545;font-weight:bold;'>{$heartbeatTime}秒前</td>";
                }
            } else {
                echo "<td style='color:#6c757d;'>无记录</td>";
            }
            
            echo "<td>{$session->last_activity}</td>";
            
            // 活动间隔
            if ($session->seconds_since_activity !== null) {
                $activityTime = $session->seconds_since_activity;
                if ($activityTime <= 60) {
                    echo "<td>{$activityTime}秒前</td>";
                } else {
                    $minutes = floor($activityTime / 60);
                    echo "<td>{$minutes}分钟前</td>";
                }
            } else {
                echo "<td>无记录</td>";
            }
            
            echo "<td>{$session->created_at}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h2>🔧 监测说明</h2>";
    echo "<ul>";
    echo "<li><strong>心跳间隔:</strong> 前台每15秒发送一次心跳</li>";
    echo "<li><strong>离线判定:</strong> 超过15秒无心跳即设为离线</li>";
    echo "<li><strong>页面刷新:</strong> 每1秒自动刷新数据</li>";
    echo "<li><strong>颜色说明:</strong></li>";
    echo "<ul>";
    echo "<li>🟢 绿色背景 - 最近5秒内有心跳</li>";
    echo "<li>🟡 黄色背景 - 超过15秒无心跳(应该离线)</li>";
    echo "<li>🟢 绿色文字 - 心跳正常(≤5秒)</li>";
    echo "<li>🟡 黄色文字 - 心跳延迟(6-15秒)</li>";
    echo "<li>🔴 红色文字 - 心跳超时(>15秒)</li>";
    echo "</ul>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/test_heartbeat.php' target='_blank'>心跳状态测试</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
