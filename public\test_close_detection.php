<?php
/**
 * 测试浏览器关闭检测
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>关闭检测测试</title>";
echo "<meta name='csrf-token' content='" . csrf_token() . "'>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
button{padding:10px 20px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;margin:5px;}
button:hover{background:#0056b3;}
button.danger{background:#dc3545;}
button.danger:hover{background:#c82333;}
#log{background:#f8f9fa;padding:15px;border-radius:4px;height:300px;overflow-y:auto;font-family:monospace;font-size:12px;border:1px solid #ddd;}
.log-entry{margin:2px 0;padding:2px 5px;border-radius:3px;}
.log-info{background:#d1ecf1;}
.log-success{background:#d4edda;}
.log-error{background:#f8d7da;}
.log-warning{background:#fff3cd;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔍 浏览器关闭检测测试</h1>";

echo "<div class='info'>📋 这个页面用来测试浏览器关闭时是否能正确发送离线状态</div>";

echo "<h2>🎮 测试控制</h2>";
echo "<div>";
echo "<button onclick='startTest()'>开始测试</button>";
echo "<button onclick='sendHeartbeat()'>发送心跳</button>";
echo "<button onclick='sendOffline()'>发送离线</button>";
echo "<button onclick='testSendBeacon()'>测试SendBeacon</button>";
echo "<button onclick='clearLog()'>清空日志</button>";
echo "<button class='danger' onclick='closeWindow()'>关闭窗口(测试)</button>";
echo "</div>";

echo "<h2>📊 实时日志</h2>";
echo "<div id='log'></div>";

echo "<h2>📋 测试说明</h2>";
echo "<ul>";
echo "<li><strong>开始测试:</strong> 初始化会话并开始发送心跳</li>";
echo "<li><strong>关闭窗口:</strong> 测试关闭检测是否工作</li>";
echo "<li><strong>检查后台:</strong> 在实时监控页面查看状态变化</li>";
echo "<li><strong>预期结果:</strong> 关闭窗口后10秒内变为离线</li>";
echo "</ul>";

echo "<script>
let sessionId = null;
let heartbeatInterval = null;

function log(message, type = 'info') {
    const logDiv = document.getElementById('log');
    const time = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.className = 'log-entry log-' + type;
    entry.textContent = '[' + time + '] ' + message;
    logDiv.appendChild(entry);
    logDiv.scrollTop = logDiv.scrollHeight;
    console.log('[' + time + '] ' + message);
}

function clearLog() {
    document.getElementById('log').innerHTML = '';
}

async function startTest() {
    log('🚀 开始测试...', 'info');
    
    try {
        // 初始化会话
        const response = await fetch('/customer-service/init', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content')
            },
            body: JSON.stringify({
                visitor_name: '测试用户',
                visitor_email: '<EMAIL>'
            })
        });
        
        const data = await response.json();
        if (data.success) {
            sessionId = data.session_id;
            log('✅ 会话初始化成功: ' + sessionId, 'success');
            
            // 开始定期发送心跳
            heartbeatInterval = setInterval(sendHeartbeat, 10000); // 每10秒
            log('💓 开始发送心跳 (每10秒)', 'info');
            
            // 立即发送一次心跳
            sendHeartbeat();
            
            // 设置关闭检测
            setupCloseDetection();
            
        } else {
            log('❌ 会话初始化失败: ' + JSON.stringify(data), 'error');
        }
    } catch (error) {
        log('💥 初始化异常: ' + error.message, 'error');
    }
}

async function sendHeartbeat() {
    if (!sessionId) {
        log('⚠️ 没有会话ID，跳过心跳', 'warning');
        return;
    }
    
    try {
        const response = await fetch('/customer-service/heartbeat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content')
            },
            body: JSON.stringify({
                session_id: sessionId,
                status: 'online'
            })
        });
        
        const data = await response.json();
        if (data.success) {
            log('💓 心跳发送成功', 'success');
        } else {
            log('❌ 心跳发送失败: ' + JSON.stringify(data), 'error');
        }
    } catch (error) {
        log('💥 心跳发送异常: ' + error.message, 'error');
    }
}

async function sendOffline() {
    if (!sessionId) {
        log('⚠️ 没有会话ID，跳过离线', 'warning');
        return;
    }
    
    log('📤 发送离线状态...', 'info');
    
    const data = JSON.stringify({
        session_id: sessionId,
        status: 'offline'
    });
    
    // 尝试sendBeacon
    if (navigator.sendBeacon) {
        try {
            const blob = new Blob([data], { type: 'application/json' });
            const success = navigator.sendBeacon('/customer-service/heartbeat', blob);
            log('📡 sendBeacon 结果: ' + success, success ? 'success' : 'error');
            if (success) return;
        } catch (e) {
            log('❌ sendBeacon 失败: ' + e.message, 'error');
        }
    }
    
    // 降级到同步XHR
    try {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/customer-service/heartbeat', false);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content'));
        xhr.send(data);
        log('📡 同步XHR 完成, 状态: ' + xhr.status, 'success');
    } catch (e) {
        log('❌ 同步XHR 失败: ' + e.message, 'error');
    }
}

function testSendBeacon() {
    if (!navigator.sendBeacon) {
        log('❌ 浏览器不支持sendBeacon', 'error');
        return;
    }
    
    const testData = JSON.stringify({test: 'beacon'});
    const blob = new Blob([testData], { type: 'application/json' });
    const success = navigator.sendBeacon('/customer-service/heartbeat', blob);
    log('🧪 sendBeacon 测试结果: ' + success, success ? 'success' : 'error');
}

function setupCloseDetection() {
    log('🔧 设置关闭检测...', 'info');
    
    window.addEventListener('beforeunload', (event) => {
        log('🚪 beforeunload 触发', 'warning');
        sendOfflineImmediate();
    });
    
    window.addEventListener('pagehide', (event) => {
        log('📱 pagehide 触发', 'warning');
        sendOfflineImmediate();
    });
    
    window.addEventListener('unload', (event) => {
        log('🗑️ unload 触发', 'warning');
        sendOfflineImmediate();
    });
    
    window.addEventListener('blur', () => {
        log('👁️ blur 触发', 'info');
    });
}

function sendOfflineImmediate() {
    if (!sessionId) return;
    
    const data = JSON.stringify({
        session_id: sessionId,
        status: 'offline'
    });
    
    // 多种方式同时尝试
    if (navigator.sendBeacon) {
        const blob = new Blob([data], { type: 'application/json' });
        navigator.sendBeacon('/customer-service/heartbeat', blob);
    }
    
    try {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/customer-service/heartbeat', false);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content'));
        xhr.send(data);
    } catch (e) {}
    
    try {
        const img = new Image();
        img.src = '/customer-service/heartbeat-img?session_id=' + sessionId + '&status=offline';
    } catch (e) {}
}

function closeWindow() {
    if (confirm('确定要关闭窗口测试关闭检测吗？')) {
        log('🚪 即将关闭窗口...', 'warning');
        setTimeout(() => {
            window.close();
        }, 1000);
    }
}

// 页面加载完成后的提示
window.addEventListener('load', () => {
    log('📄 页面加载完成，点击\"开始测试\"开始', 'info');
});
</script>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/realtime_monitor.php' target='_blank'>实时状态监控</a> - 查看状态变化</li>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
