<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

header('Content-Type: text/html; charset=utf-8');

try {
    echo "<h2>📋 备注字段存储位置确认</h2>";
    
    // 1. 检查 st_customer_service_sessions 表结构
    echo "<h3>1. st_customer_service_sessions 表结构</h3>";
    
    $columns = DB::select("DESCRIBE st_customer_service_sessions");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>说明</th></tr>";
    
    foreach ($columns as $column) {
        $description = '';
        switch ($column->Field) {
            case 'internal_notes':
                $description = '✅ <strong>备注存储字段</strong>';
                break;
            case 'id':
                $description = '主键ID';
                break;
            case 'visitor_name':
                $description = '访客名称';
                break;
            case 'visitor_ip':
                $description = '访客IP';
                break;
            case 'status':
                $description = '会话状态';
                break;
            case 'created_at':
                $description = '创建时间';
                break;
            case 'updated_at':
                $description = '更新时间';
                break;
        }
        
        $style = $column->Field === 'internal_notes' ? 'background: #d4edda;' : '';
        echo "<tr style='{$style}'>";
        echo "<td><strong>{$column->Field}</strong></td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>" . ($column->Default ?? 'NULL') . "</td>";
        echo "<td>{$description}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 检查是否有备注数据
    echo "<h3>2. 当前备注数据</h3>";
    
    $sessionsWithNotes = DB::select("
        SELECT id, visitor_name, internal_notes, created_at 
        FROM st_customer_service_sessions 
        WHERE internal_notes IS NOT NULL AND internal_notes != ''
        ORDER BY updated_at DESC
        LIMIT 10
    ");
    
    if (empty($sessionsWithNotes)) {
        echo "<p>❌ 当前没有会话包含备注数据</p>";
    } else {
        echo "<p>✅ 找到 " . count($sessionsWithNotes) . " 个会话包含备注：</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>会话ID</th><th>访客名称</th><th>备注内容</th><th>创建时间</th></tr>";
        
        foreach ($sessionsWithNotes as $session) {
            echo "<tr>";
            echo "<td>{$session->id}</td>";
            echo "<td>" . ($session->visitor_name ?: '匿名访客') . "</td>";
            echo "<td style='max-width: 300px; word-break: break-all;'>{$session->internal_notes}</td>";
            echo "<td>{$session->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. 测试备注保存
    echo "<h3>3. 测试备注保存</h3>";
    
    $testSession = DB::table('customer_service_sessions')->first();
    if ($testSession) {
        $testNotes = '测试备注 - ' . date('Y-m-d H:i:s');
        
        // 保存备注
        DB::table('customer_service_sessions')
          ->where('id', $testSession->id)
          ->update(['internal_notes' => $testNotes]);
        
        // 验证保存
        $updatedSession = DB::table('customer_service_sessions')
                           ->where('id', $testSession->id)
                           ->first();
        
        echo "<p>✅ 测试会话ID: {$testSession->id}</p>";
        echo "<p>✅ 保存的备注: {$testNotes}</p>";
        echo "<p>✅ 验证结果: " . ($updatedSession->internal_notes === $testNotes ? '保存成功' : '保存失败') . "</p>";
        echo "<p>✅ 实际存储的值: {$updatedSession->internal_notes}</p>";
    }
    
    echo "<h3>📋 总结</h3>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>备注存储位置：</strong></p>";
    echo "<ul>";
    echo "<li><strong>表名：</strong> st_customer_service_sessions</li>";
    echo "<li><strong>字段名：</strong> internal_notes</li>";
    echo "<li><strong>字段类型：</strong> TEXT (可存储长文本)</li>";
    echo "<li><strong>用途：</strong> 存储客服人员对会话的内部备注</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}
?>
