<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>修复客服设置表</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 修复客服设置表</h1>";

try {
    // 数据库连接
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'mostxx_com';
    $username = 'mostxx_com';
    $password = 'fHnrmH9w5nw1pd53';

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 检查表结构
    echo "<h3>1. 检查表结构</h3>";
    $stmt = $pdo->query("DESCRIBE customer_service_settings");
    $columns = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>默认值</th></tr>";
    
    $hasIsActive = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><code>{$column['Field']}</code></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'is_active') {
            $hasIsActive = true;
        }
    }
    echo "</table>";
    
    // 如果没有is_active字段，添加它
    if (!$hasIsActive) {
        echo "<div class='info'>ℹ️ 表中没有 is_active 字段，正在添加...</div>";
        
        $pdo->exec("ALTER TABLE customer_service_settings ADD COLUMN is_active TINYINT(1) DEFAULT 1");
        echo "<div class='success'>✅ 已添加 is_active 字段</div>";
    } else {
        echo "<div class='success'>✅ is_active 字段已存在</div>";
    }
    
    // 更新所有记录的is_active为1
    echo "<h3>2. 更新记录状态</h3>";
    $stmt = $pdo->exec("UPDATE customer_service_settings SET is_active = 1");
    echo "<div class='success'>✅ 已更新 {$stmt} 条记录的 is_active 状态</div>";
    
    // 检查关键设置
    echo "<h3>3. 检查关键设置</h3>";
    $stmt = $pdo->query("
        SELECT setting_key, setting_value, is_active 
        FROM customer_service_settings 
        WHERE setting_key IN ('system_enabled', 'auto_open_enabled', 'sound_enabled')
    ");
    $keySettings = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>设置键</th><th>值</th><th>激活状态</th><th>状态</th></tr>";
    
    foreach ($keySettings as $setting) {
        $status = ($setting['setting_value'] === '1' && $setting['is_active'] == 1) ? '✅ 正常' : '❌ 异常';
        echo "<tr>";
        echo "<td><code>{$setting['setting_key']}</code></td>";
        echo "<td><strong>{$setting['setting_value']}</strong></td>";
        echo "<td>{$setting['is_active']}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 测试API
    echo "<h3>4. 测试API响应</h3>";
    $settingsUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/customer-service/get-settings.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $settingsUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<div class='success'>✅ API响应正常</div>";
            
            $systemEnabled = $data['data']['system_enabled'] ?? false;
            if ($systemEnabled) {
                echo "<div class='success'>✅ system_enabled = true，客服系统应该显示</div>";
            } else {
                echo "<div class='error'>❌ system_enabled = false，客服系统不会显示</div>";
            }
            
            echo "<details>";
            echo "<summary>查看完整API响应</summary>";
            echo "<pre style='background:#f8f9fa;padding:10px;border-radius:4px;'>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            echo "</details>";
        } else {
            echo "<div class='error'>❌ API响应异常: " . $response . "</div>";
        }
    } else {
        echo "<div class='error'>❌ API请求失败，HTTP状态码: {$httpCode}</div>";
    }
    
    echo "<div class='success'>";
    echo "<h4>🎉 修复完成！</h4>";
    echo "<p>现在可以测试前端客服系统了：</p>";
    echo "<ul>";
    echo "<li><a href='/' target='_blank'>访问首页查看客服按钮</a></li>";
    echo "<li><a href='/debug_frontend_cs.php' target='_blank'>运行前端调试</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
