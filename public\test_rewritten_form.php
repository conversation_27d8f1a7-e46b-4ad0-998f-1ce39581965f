<?php
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔄 测试重写后的离线表单</h1>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>📊 当前状态</h2>";

$now = new DateTime();
$currentTime = $now->format('H:i');
$dayOfWeek = $now->format('N');

echo "<p><strong>当前时间:</strong> " . $now->format('Y-m-d H:i:s') . " (星期" . ['', '一', '二', '三', '四', '五', '六', '日'][$dayOfWeek] . ")</p>";

$isWorkingDay = $dayOfWeek >= 1 && $dayOfWeek <= 5;
$isWorkingHours = $currentTime >= '09:00' && $currentTime <= '18:00';

echo "<p><strong>是否工作日:</strong> " . ($isWorkingDay ? '✅ 是' : '❌ 否') . "</p>";
echo "<p><strong>是否工作时间:</strong> " . ($isWorkingHours ? '✅ 是' : '❌ 否') . "</p>";

if (!$isWorkingDay || !$isWorkingHours) {
    echo "<div style='background: #ffebee; color: #c62828; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "🔴 <strong>当前是非工作时间，应该显示离线表单</strong>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "🟢 <strong>当前是工作时间，应该显示聊天界面</strong>";
    echo "</div>";
}
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>🔄 重写改进</h2>";
echo "<ul>";
echo "<li>✅ <strong>简化HTML结构</strong>：移除复杂的嵌套和定位</li>";
echo "<li>✅ <strong>统一风格</strong>：与聊天界面保持一致的设计风格</li>";
echo "<li>✅ <strong>兼容性优化</strong>：使用标准CSS属性，避免现代特性</li>";
echo "<li>✅ <strong>清晰布局</strong>：白色表单卡片，灰色背景</li>";
echo "<li>✅ <strong>标准字段</strong>：使用传统的表单字段样式</li>";
echo "<li>✅ <strong>可靠按钮</strong>：JavaScript事件绑定，不依赖内联样式</li>";
echo "<li>✅ <strong>调试友好</strong>：添加控制台日志，便于排查问题</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>🧪 测试重点</h2>";
echo "<ol>";
echo "<li><strong>打开首页</strong>：<a href='/' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>点击打开首页</a></li>";
echo "<li><strong>点击客服按钮</strong>：右下角的客服按钮</li>";
echo "<li><strong>验证重写效果</strong>：";
echo "<ul style='margin: 10px 0;'>";
if (!$isWorkingDay || !$isWorkingHours) {
    echo "<li>✅ 应该直接显示重写后的离线表单</li>";
    echo "<li>✅ 表单应该有白色背景卡片</li>";
    echo "<li>✅ 顶部应该有📝图标和标题</li>";
    echo "<li>✅ 字段应该清晰可见，间距合理</li>";
    echo "<li>✅ 提交按钮应该在底部，有渐变效果</li>";
} else {
    echo "<li>✅ 应该显示聊天界面</li>";
    echo "<li>✅ 可以点击📝按钮测试重写后的离线表单</li>";
}
echo "</ul>";
echo "</li>";
echo "<li><strong>测试交互功能</strong>：";
echo "<ul>";
echo "<li>点击输入框，边框应该变蓝色</li>";
echo "<li>填写各个字段</li>";
echo "<li>鼠标悬停按钮，应该有动画效果</li>";
echo "<li>点击提交按钮测试</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>检查控制台</strong>：按F12查看是否有调试日志</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>✅ 预期效果</h2>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";
echo "<div>";
echo "<h4>🎨 视觉设计</h4>";
echo "<ul>";
echo "<li>灰色背景 (#f8fafc)</li>";
echo "<li>白色表单卡片</li>";
echo "<li>📝 图标和清晰标题</li>";
echo "<li>标准表单字段样式</li>";
echo "<li>渐变色提交按钮</li>";
echo "</ul>";
echo "</div>";
echo "<div>";
echo "<h4>🔧 技术特性</h4>";
echo "<ul>";
echo "<li>350px固定高度</li>";
echo "<li>自动滚动支持</li>";
echo "<li>标准CSS兼容性</li>";
echo "<li>JavaScript事件绑定</li>";
echo "<li>调试日志输出</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>🔍 调试信息</h2>";
echo "<p><strong>预期的控制台日志：</strong></p>";
echo "<div style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 13px;'>";
echo "💬 客服窗口已打开<br>";
echo "🚀 初始化客服系统模式<br>";
echo "🔍 离线表单已启用，检查当前状态<br>";
echo "✅ 当前是离线状态，直接显示离线表单<br>";
echo "✅ 隐藏聊天消息区域<br>";
echo "✅ 隐藏聊天输入区域<br>";
echo "✅ 显示离线表单<br>";
echo "✅ 离线表单字段生成完成<br>";
echo "📝 已切换到离线表单界面";
echo "</div>";
echo "</div>";

echo "<div style='background: #ffebee; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>❌ 故障排除</h2>";
echo "<p><strong>如果仍然有问题：</strong></p>";
echo "<ul>";
echo "<li><strong>强制刷新</strong>：按 <kbd style='background: #f1f3f4; padding: 2px 6px; border-radius: 3px;'>Ctrl+F5</kbd> 清除缓存</li>";
echo "<li><strong>检查控制台</strong>：查看是否有JavaScript错误</li>";
echo "<li><strong>检查元素</strong>：右键检查 <code style='background: #f1f3f4; padding: 2px 6px; border-radius: 3px;'>#cs-offline-form</code> 元素</li>";
echo "<li><strong>验证设置</strong>：确认 offline_form_enabled 和 working_hours_enabled 都为 true</li>";
echo "</ul>";
echo "<p><strong>常见问题：</strong></p>";
echo "<ul>";
echo "<li>表单不显示 → 检查设置API返回值</li>";
echo "<li>字段不生成 → 查看控制台错误信息</li>";
echo "<li>按钮无效果 → 检查事件绑定</li>";
echo "<li>样式异常 → 检查CSS冲突</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h2>🔗 相关链接</h2>";
echo "<a href='/' target='_blank' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px; display: inline-block;'>🏠 首页测试</a>";
echo "<a href='/api/customer-service/get-settings.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px; display: inline-block;'>📡 设置API</a>";
echo "<a href='/strongadmin/customer-service/settings' target='_blank' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px; display: inline-block;'>⚙️ 客服设置</a>";
echo "</div>";

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2 {
    color: #2c3e50;
}

h4 {
    color: #495057;
    margin-bottom: 10px;
}

code, kbd {
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

kbd {
    border: 1px solid #dadce0;
}

a:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

li {
    margin: 5px 0;
}
</style>
