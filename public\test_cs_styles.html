<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服样式测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .log { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 4px; 
            margin: 10px 0; 
            font-family: monospace; 
            font-size: 12px; 
            max-height: 300px; 
            overflow-y: auto; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
        }
        .test-btn:hover { background: #0056b3; }
        .color-input { margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 客服样式动态应用测试</h1>
        <p>测试前端客服系统的样式动态应用功能</p>
        
        <div style="margin-bottom: 20px;">
            <h3>测试颜色设置：</h3>
            <label>按钮颜色: <input type="color" id="button-color" value="#000000" class="color-input"></label>
            <label>主题颜色: <input type="color" id="theme-color" value="#000000" class="color-input"></label>
            <br><br>
            <button class="test-btn" onclick="testAPI()">获取当前设置</button>
            <button class="test-btn" onclick="testStyleApplication()">应用测试样式</button>
            <button class="test-btn" onclick="showCustomerService()">显示客服</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log" class="log">
            <div class="info">📋 测试日志:</div>
        </div>
    </div>

    <!-- 客服组件 -->
    <div id="customer-service-widget" style="display: none;">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;" onclick="toggleChat()">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <!-- 头部 -->
            <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                    </div>
                    <button onclick="toggleChat()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;">×</button>
                </div>
            </div>

            <!-- 消息区域 -->
            <div id="cs-messages" style="flex:1;padding:20px;overflow-y:auto;max-height:360px;background:#fafafa;">
                <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                    <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);margin-bottom:15px;">
                        <div style="font-weight:600;margin-bottom:5px;">👋 欢迎咨询</div>
                        <div>我们随时为您提供帮助！</div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div style="padding:15px;border-top:1px solid #eee;background:white;">
                <div style="display:flex;gap:10px;align-items:center;">
                    <input type="text" id="cs-message-input" placeholder="请输入您的问题..." style="flex:1;padding:12px;border:1px solid #ddd;border-radius:25px;outline:none;font-size:14px;">
                    <button onclick="sendMessage()" style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border:none;width:40px;height:40px;border-radius:50%;cursor:pointer;font-size:16px;">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let csSettings = {};

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            logDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">📋 测试日志:</div>';
        }

        async function testAPI() {
            log('🔍 获取当前设置...', 'info');
            
            try {
                const response = await fetch('/api/test-settings.php');
                const data = await response.json();
                
                if (data.success) {
                    csSettings = data.data;
                    log('✅ 设置获取成功', 'success');
                    log('按钮颜色: ' + (csSettings.chat_button_color || '未设置'), 'info');
                    log('主题颜色: ' + (csSettings.chat_theme_color || '未设置'), 'info');
                    log('窗口宽度: ' + (csSettings.chat_window_width || '未设置'), 'info');
                    log('窗口高度: ' + (csSettings.chat_window_height || '未设置'), 'info');
                    
                    // 更新颜色选择器
                    if (csSettings.chat_button_color) {
                        document.getElementById('button-color').value = csSettings.chat_button_color;
                    }
                    if (csSettings.chat_theme_color) {
                        document.getElementById('theme-color').value = csSettings.chat_theme_color;
                    }
                    
                    // 自动应用设置
                    applySettingsToUI();
                } else {
                    log('❌ 获取设置失败: ' + (data.error || '未知错误'), 'error');
                }
            } catch (error) {
                log('❌ API请求失败: ' + error.message, 'error');
            }
        }

        function testStyleApplication() {
            log('🎨 应用测试样式...', 'info');
            
            const buttonColor = document.getElementById('button-color').value;
            const themeColor = document.getElementById('theme-color').value;
            
            // 创建测试设置
            csSettings = {
                chat_button_color: buttonColor,
                chat_theme_color: themeColor,
                chat_window_width: 400,
                chat_window_height: 550,
                chat_font_size: 16,
                chat_border_radius: 20
            };
            
            log('测试按钮颜色: ' + buttonColor, 'info');
            log('测试主题颜色: ' + themeColor, 'info');
            
            applySettingsToUI();
        }

        function applySettingsToUI() {
            log('🎨 开始应用UI设置...', 'info');
            
            const chatWidget = document.getElementById('customer-service-widget');
            const chatWindow = document.getElementById('cs-chat-window');
            const chatButton = document.getElementById('cs-chat-button');

            if (!chatWidget) {
                log('⚠️ 找不到客服组件', 'warning');
                return;
            }

            // 应用按钮颜色
            if (csSettings.chat_button_color && chatButton) {
                log('🎨 应用按钮颜色: ' + csSettings.chat_button_color, 'info');
                chatButton.style.background = `linear-gradient(135deg, ${csSettings.chat_button_color} 0%, ${csSettings.chat_button_color} 100%)`;
            }

            // 应用主题颜色到聊天窗口头部
            if (csSettings.chat_theme_color && chatWindow) {
                log('🎨 应用主题颜色: ' + csSettings.chat_theme_color, 'info');
                const chatHeader = chatWindow.querySelector('div[style*="background:linear-gradient"]');
                if (chatHeader) {
                    chatHeader.style.background = `linear-gradient(135deg, ${csSettings.chat_theme_color} 0%, ${csSettings.chat_theme_color} 100%)`;
                    log('✅ 头部颜色已更新', 'success');
                }
                
                // 应用到发送按钮
                const sendButton = chatWindow.querySelector('button[onclick="sendMessage()"]');
                if (sendButton) {
                    sendButton.style.background = `linear-gradient(135deg, ${csSettings.chat_theme_color} 0%, ${csSettings.chat_theme_color} 100%)`;
                    log('✅ 发送按钮颜色已更新', 'success');
                }
            }

            // 应用窗口尺寸
            if (csSettings.chat_window_width && chatWindow) {
                log('🎨 应用窗口宽度: ' + csSettings.chat_window_width, 'info');
                chatWindow.style.width = csSettings.chat_window_width + 'px';
            }
            if (csSettings.chat_window_height && chatWindow) {
                log('🎨 应用窗口高度: ' + csSettings.chat_window_height, 'info');
                chatWindow.style.height = csSettings.chat_window_height + 'px';
            }

            // 应用字体大小
            if (csSettings.chat_font_size) {
                log('🎨 应用字体大小: ' + csSettings.chat_font_size, 'info');
                const messagesContainer = document.getElementById('cs-messages');
                if (messagesContainer) {
                    messagesContainer.style.fontSize = csSettings.chat_font_size + 'px';
                }
            }

            // 应用圆角
            if (csSettings.chat_border_radius && chatWindow) {
                log('🎨 应用圆角: ' + csSettings.chat_border_radius, 'info');
                chatWindow.style.borderRadius = csSettings.chat_border_radius + 'px';
            }
            
            log('✅ 样式应用完成', 'success');
        }

        function showCustomerService() {
            log('👁️ 显示客服组件', 'info');
            document.getElementById('customer-service-widget').style.display = 'block';
        }

        function toggleChat() {
            const chatWindow = document.getElementById('cs-chat-window');
            if (chatWindow.style.display === 'none' || !chatWindow.style.display) {
                chatWindow.style.display = 'block';
                log('💬 聊天窗口已打开', 'info');
            } else {
                chatWindow.style.display = 'none';
                log('💬 聊天窗口已关闭', 'info');
            }
        }

        function sendMessage() {
            const input = document.getElementById('cs-message-input');
            const message = input.value.trim();
            if (message) {
                log('📤 发送消息: ' + message, 'info');
                input.value = '';
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成', 'info');
            setTimeout(() => {
                testAPI();
                showCustomerService();
            }, 500);
        });
    </script>
</body>
</html>
