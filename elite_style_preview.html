<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ 酷炫高端风格预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: #0a0a0a;
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(20,20,20,0.9) 100%);
            border-radius: 10px;
            border: 1px solid #333;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 300;
            letter-spacing: 3px;
        }
        
        .preview-section {
            background: rgba(10,10,10,0.8);
            border-radius: 10px;
            padding: 40px;
            margin-bottom: 30px;
            border: 1px solid #333;
        }
        
        /* 复制主要的酷炫高端样式 */
        .elite-section-header {
            position: relative;
            margin: 40px 0 30px 0;
            padding: 0;
            background: transparent;
        }

        .elite-title-container {
            position: relative;
            display: flex;
            align-items: center;
            padding: 20px 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(20,20,20,0.95) 100%);
            border-radius: 0;
            border-left: 4px solid #00d4ff;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .elite-section-header.new-products .elite-title-container {
            border-left-color: #00ff88;
            background: linear-gradient(135deg, rgba(0,20,0,0.95) 0%, rgba(0,40,20,0.95) 100%);
        }

        .elite-section-header.hot-products .elite-title-container {
            border-left-color: #ff3366;
            background: linear-gradient(135deg, rgba(20,0,0,0.95) 0%, rgba(40,0,20,0.95) 100%);
        }

        .elite-icon-bar {
            width: 6px;
            height: 60px;
            background: linear-gradient(180deg, #00d4ff 0%, #0099cc 100%);
            margin-right: 25px;
            margin-left: 20px;
            border-radius: 3px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            animation: elitePulse 2s ease-in-out infinite;
        }

        .elite-section-header.new-products .elite-icon-bar {
            background: linear-gradient(180deg, #00ff88 0%, #00cc66 100%);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .elite-section-header.hot-products .elite-icon-bar {
            background: linear-gradient(180deg, #ff3366 0%, #cc1144 100%);
            box-shadow: 0 0 20px rgba(255, 51, 102, 0.5);
        }

        .elite-title {
            color: #ffffff;
            font-size: 24px;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-decoration: none;
            margin: 0;
            position: relative;
            transition: all 0.3s ease;
            font-family: 'Arial', sans-serif;
        }

        .elite-title:hover {
            color: #00d4ff;
            text-decoration: none;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        .elite-section-header.new-products .elite-title:hover {
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .elite-section-header.hot-products .elite-title:hover {
            color: #ff3366;
            text-shadow: 0 0 10px rgba(255, 51, 102, 0.8);
        }

        .elite-glow-line {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            width: 0%;
            background: linear-gradient(90deg, #00d4ff 0%, #0099cc 100%);
            transition: width 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        .elite-section-header.new-products .elite-glow-line {
            background: linear-gradient(90deg, #00ff88 0%, #00cc66 100%);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
        }

        .elite-section-header.hot-products .elite-glow-line {
            background: linear-gradient(90deg, #ff3366 0%, #cc1144 100%);
            box-shadow: 0 0 10px rgba(255, 51, 102, 0.8);
        }

        .elite-title-container:hover .elite-glow-line {
            width: 100%;
        }

        .elite-title-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            transition: left 0.8s ease;
        }

        .elite-title-container:hover::before {
            left: 100%;
        }

        .elite-title-container:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
        }

        @keyframes elitePulse {
            0%, 100% { 
                transform: scaleY(1);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            }
            50% { 
                transform: scaleY(1.1);
                box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
            }
        }

        .features {
            list-style: none;
            margin: 30px 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .feature-item {
            padding: 20px;
            background: rgba(0,0,0,0.5);
            border-radius: 8px;
            border: 1px solid #333;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            border-color: #00d4ff;
            box-shadow: 0 5px 20px rgba(0, 212, 255, 0.2);
        }

        .feature-item h3 {
            color: #00d4ff;
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: 300;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .before, .after {
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }

        .before {
            background: rgba(100,100,100,0.2);
            border: 2px dashed #666;
        }

        .after {
            background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(20,20,20,0.9));
            border: 1px solid #00d4ff;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .elite-title {
                font-size: 18px;
                letter-spacing: 1px;
            }
            
            .elite-icon-bar {
                width: 4px;
                height: 40px;
                margin-right: 15px;
                margin-left: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ ELITE DESIGN</h1>
            <p style="font-size: 1.2rem; opacity: 0.8;">酷炫 • 大气 • 高端</p>
        </div>
        
        <div class="preview-section">
            <h2 style="margin-bottom: 30px; color: #00d4ff; font-weight: 300; letter-spacing: 2px;">🎯 设计效果预览</h2>
            
            <!-- 推荐产品 -->
            <div class="elite-section-header">
                <div class="elite-title-container">
                    <div class="elite-icon-bar"></div>
                    <h4><a href="#" class="elite-title">
                        Recommend Products
                    </a></h4>
                    <div class="elite-glow-line"></div>
                </div>
            </div>
            
            <!-- 新品 -->
            <div class="elite-section-header new-products">
                <div class="elite-title-container">
                    <div class="elite-icon-bar"></div>
                    <h4><a href="#" class="elite-title">
                        New Products
                    </a></h4>
                    <div class="elite-glow-line"></div>
                </div>
            </div>
            
            <!-- 热卖 -->
            <div class="elite-section-header hot-products">
                <div class="elite-title-container">
                    <div class="elite-icon-bar"></div>
                    <h4><a href="#" class="elite-title">
                        Hot Products
                    </a></h4>
                    <div class="elite-glow-line"></div>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 style="margin-bottom: 30px; color: #00d4ff; font-weight: 300; letter-spacing: 2px;">⚡ 酷炫高端特点</h2>
            <div class="features">
                <div class="feature-item">
                    <h3>暗黑科技风</h3>
                    <p>深色背景配合霓虹色彩，营造未来科技感</p>
                </div>
                <div class="feature-item">
                    <h3>动态光效</h3>
                    <p>脉冲动画、光线扫描、发光边框</p>
                </div>
                <div class="feature-item">
                    <h3>极简大气</h3>
                    <p>去除多余装饰，专注核心视觉冲击</p>
                </div>
                <div class="feature-item">
                    <h3>高端质感</h3>
                    <p>精细渐变、阴影层次、材质质感</p>
                </div>
                <div class="feature-item">
                    <h3>流畅交互</h3>
                    <p>平滑过渡、悬停反馈、触摸优化</p>
                </div>
                <div class="feature-item">
                    <h3>响应式设计</h3>
                    <p>完美适配桌面端和移动端</p>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 style="margin-bottom: 30px; color: #00d4ff; font-weight: 300; letter-spacing: 2px;">📱 对比效果</h2>
            <div class="comparison">
                <div class="before">
                    <h3 style="color: #999;">原版设计</h3>
                    <p style="margin: 20px 0; color: #666; font-size: 14px;">
                        ▶ New Products
                    </p>
                    <small style="color: #888;">传统箭头 + 普通文字</small>
                </div>
                <div class="after">
                    <h3 style="color: #00d4ff;">酷炫高端风格</h3>
                    <div style="margin: 20px 0; display: flex; align-items: center; justify-content: center;">
                        <div style="width: 4px; height: 30px; background: linear-gradient(180deg, #00ff88, #00cc66); margin-right: 15px; border-radius: 2px;"></div>
                        <span style="color: #fff; font-weight: 300; letter-spacing: 1px;">NEW PRODUCTS</span>
                    </div>
                    <small style="color: #aaa;">暗黑背景 + 霓虹光效 + 动态元素</small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
