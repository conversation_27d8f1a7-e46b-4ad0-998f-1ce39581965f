<?php
/**
 * 超快响应SSE - 0.2秒检查一次
 */

// 设置执行时间和内存限制
set_time_limit(0);
ini_set('memory_limit', '128M');

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');

// 禁用所有输出缓冲
while (ob_get_level()) {
    ob_end_clean();
}

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '超快SSE连接成功！',
    'session_id' => $sessionId,
    'check_interval' => '0.2秒',
    'timestamp' => time()
]) . "\n\n";
flush();

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

$pdo = null;
$sessionDbId = null;

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 获取或创建会话
    $stmt = $pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();
    
    if (!$session) {
        // 创建新会话
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
        $stmt->execute([
            $sessionId, 
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', 
            $_SERVER['HTTP_USER_AGENT'] ?? 'SSE Client'
        ]);
        $sessionDbId = $pdo->lastInsertId();
    } else {
        $sessionDbId = $session['id'];
    }
    
    echo "data: " . json_encode([
        'type' => 'session_ready',
        'message' => '会话准备就绪',
        'session_db_id' => $sessionDbId,
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'database_error',
        'message' => '数据库错误: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
}

// 超快主循环
$startTime = time();
$maxDuration = 300; // 5分钟
$checkInterval = 0.2; // 0.2秒检查一次，超快响应！
$heartbeatCounter = 0;

while (time() - $startTime < $maxDuration) {
    $heartbeatCounter++;
    
    try {
        // 检查新消息
        if ($pdo && $sessionDbId) {
            $stmt = $pdo->prepare("
                SELECT * FROM st_customer_service_messages 
                WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') 
                ORDER BY id ASC
            ");
            $stmt->execute([$sessionDbId, $lastMessageId]);
            $newMessages = $stmt->fetchAll();
            
            if (!empty($newMessages)) {
                foreach ($newMessages as $message) {
                    echo "data: " . json_encode([
                        'type' => 'new_reply',
                        'message_id' => $message['id'],
                        'sender_type' => $message['sender_type'],
                        'message' => $message['message'],
                        'created_at' => $message['created_at'],
                        'response_time' => '超快响应',
                        'timestamp' => time()
                    ]) . "\n\n";
                    
                    $lastMessageId = max($lastMessageId, $message['id']);
                }
                flush();
            }
        }
        
        // 每50次循环（10秒）发送一次心跳包
        if ($heartbeatCounter % 50 == 0) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'message' => '💓 超快心跳',
                'connection_time' => time() - $startTime,
                'checks_per_second' => 5,
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 检查客户端连接
        if (connection_aborted()) {
            break;
        }
        
        // 使用usleep实现更精确的0.2秒间隔
        usleep(200000); // 200毫秒 = 0.2秒
        
    } catch (Exception $e) {
        echo "data: " . json_encode([
            'type' => 'error',
            'message' => '错误: ' . $e->getMessage(),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        usleep(200000);
    }
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '超快SSE连接结束',
    'connection_time' => time() - $startTime,
    'total_checks' => $heartbeatCounter,
    'timestamp' => time()
]) . "\n\n";
flush();
?>
