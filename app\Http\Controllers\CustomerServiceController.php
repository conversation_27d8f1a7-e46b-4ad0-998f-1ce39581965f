<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CustomerServiceController extends Controller
{
    // 初始化客服会话
    public function initSession(Request $request)
    {
        $sessionId = $request->session()->get('customer_service_session_id');
        
        if (!$sessionId) {
            $sessionId = CustomerServiceSession::generateSessionId();
            $request->session()->put('customer_service_session_id', $sessionId);
        }

        $session = CustomerServiceSession::where('session_id', $sessionId)->first();
        
        if (!$session) {
            $session = CustomerServiceSession::create([
                'session_id' => $sessionId,
                'user_id' => Auth::id(),
                'visitor_ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'status' => 'active',
                'last_activity' => now()
            ]);
        }

        return response()->json([
            'success' => true,
            'session_id' => $sessionId,
            'session' => $session
        ]);
    }

    // 发送消息
    public function sendMessage(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string',
            'message' => 'required|string|max:1000'
        ]);

        $session = CustomerServiceSession::where('session_id', $request->session_id)->first();
        
        if (!$session) {
            return response()->json(['success' => false, 'message' => 'Session not found'], 404);
        }

        // 创建用户消息
        $message = CustomerServiceMessage::create([
            'session_id' => $session->id,
            'sender_type' => Auth::check() ? 'customer' : 'customer',
            'sender_id' => Auth::id(),
            'message' => $request->message
        ]);

        // 更新会话活动时间
        $session->updateLastActivity();

        // 检查AI自动回复
        $this->checkAutoReply($session, $request->message);

        return response()->json([
            'success' => true,
            'message' => $message->load('session')
        ]);
    }

    // 获取消息历史
    public function getMessages(Request $request)
    {
        $sessionId = $request->get('session_id');
        
        if (!$sessionId) {
            return response()->json(['success' => false, 'message' => 'Session ID required'], 400);
        }

        $session = CustomerServiceSession::where('session_id', $sessionId)->first();
        
        if (!$session) {
            return response()->json(['success' => false, 'message' => 'Session not found'], 404);
        }

        $messages = $session->messages()
                          ->orderBy('created_at', 'asc')
                          ->get()
                          ->map(function ($message) {
                              return [
                                  'id' => $message->id,
                                  'sender_type' => $message->sender_type,
                                  'message' => $message->message,
                                  'sender_info' => $message->sender_info,
                                  'formatted_time' => $message->formatted_time,
                                  'created_at' => $message->created_at->toISOString()
                              ];
                          });

        return response()->json([
            'success' => true,
            'messages' => $messages
        ]);
    }

    // 更新访客信息
    public function updateVisitorInfo(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string',
            'name' => 'nullable|string|max:100',
            'email' => 'nullable|email|max:255'
        ]);

        $session = CustomerServiceSession::where('session_id', $request->session_id)->first();
        
        if (!$session) {
            return response()->json(['success' => false, 'message' => 'Session not found'], 404);
        }

        $session->update([
            'visitor_name' => $request->name,
            'visitor_email' => $request->email
        ]);

        return response()->json(['success' => true]);
    }

    // 检查AI自动回复
    private function checkAutoReply($session, $userMessage)
    {
        $rule = AiAutoReplyRule::findMatchingRule($userMessage);
        
        if ($rule) {
            // 延迟1-3秒回复，模拟真实对话
            $delay = rand(1, 3);
            
            dispatch(function () use ($session, $rule) {
                CustomerServiceMessage::create([
                    'session_id' => $session->id,
                    'sender_type' => 'ai',
                    'sender_id' => null,
                    'message' => $rule->reply_message
                ]);
                
                $rule->incrementUsage();
            })->delay(now()->addSeconds($delay));
        }
    }

    // 获取在线状态
    public function getOnlineStatus()
    {
        // 这里可以检查是否有客服在线
        $isOnline = true; // 简化处理，实际可以检查管理员在线状态
        
        return response()->json([
            'success' => true,
            'is_online' => $isOnline,
            'message' => $isOnline ? 'Customer service is online' : 'Customer service is offline'
        ]);
    }
}
