<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>快速测试</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .log { background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px 0; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🔧 快速测试后台回复</h1>
    
    <div>
        <button class="btn" onclick="testSSE()">测试SSE连接</button>
        <button class="btn" onclick="sendTestReply()">发送测试回复</button>
        <button class="btn" onclick="clearLog()">清除日志</button>
    </div>
    
    <div id="log" class="log"></div>
    
    <script>
    let sessionId = 'quick_test_' + Date.now();
    let eventSource = null;
    let sessionDbId = null;
    
    function log(message) {
        const logDiv = document.getElementById('log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function testSSE() {
        log('🔄 开始测试SSE连接...');
        log('会话ID: ' + sessionId);
        
        if (eventSource) {
            eventSource.close();
        }
        
        eventSource = new EventSource('/lightning_sse.php?session_id=' + sessionId + '&last_message_id=0');
        
        eventSource.onopen = function() {
            log('✅ SSE连接成功');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 收到: ' + data.type + ' - ' + (data.message || ''));
                
                if (data.session_db_id) {
                    sessionDbId = data.session_db_id;
                    log('📍 会话数据库ID: ' + sessionDbId);
                }
                
                if (data.type === 'new_reply') {
                    log('🎉 收到新回复: ' + data.message);
                }
            } catch (e) {
                log('❌ 解析失败: ' + event.data);
            }
        };
        
        eventSource.onerror = function() {
            log('❌ SSE连接错误');
        };
    }
    
    function sendTestReply() {
        if (!sessionDbId) {
            log('❌ 请先测试SSE连接获取会话ID');
            return;
        }
        
        const message = '测试回复 ' + new Date().toLocaleTimeString();
        log('📤 发送测试回复: ' + message);
        
        fetch('/strongadmin/customer-service/session/' + sessionDbId + '/reply', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: '_token=' + encodeURIComponent('test') + '&message=' + encodeURIComponent(message)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                log('✅ 回复发送成功，等待SSE接收...');
            } else {
                log('❌ 回复发送失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            log('❌ 网络错误: ' + error.message);
        });
    }
    
    function clearLog() {
        document.getElementById('log').innerHTML = '';
    }
    
    // 页面加载时自动开始测试
    window.onload = function() {
        log('🚀 快速测试页面已加载');
        log('点击"测试SSE连接"开始测试');
    };
    </script>
</body>
</html>
