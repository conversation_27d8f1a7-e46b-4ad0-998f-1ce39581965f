<?php
/**
 * 测试心跳状态页面
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>心跳状态测试</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:1000px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:20px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
.online{color:#28a745;font-weight:bold;}
.offline{color:#dc3545;font-weight:bold;}
</style>";
echo "<script>
function refreshData() {
    location.reload();
}
setInterval(refreshData, 2000); // 每2秒刷新
</script>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>💓 心跳状态实时监控</h1>";

try {
    echo "<div class='info'>🕐 当前时间: " . date('Y-m-d H:i:s') . " (每2秒自动刷新)</div>";
    
    // 查询所有会话的状态
    $sessions = DB::select("
        SELECT 
            id,
            session_id,
            visitor_name,
            visitor_ip,
            status,
            last_seen,
            last_activity,
            created_at,
            TIMESTAMPDIFF(SECOND, last_seen, NOW()) as seconds_since_heartbeat
        FROM st_customer_service_sessions 
        ORDER BY last_seen DESC
        LIMIT 20
    ");
    
    echo "<h2>📋 会话状态列表</h2>";
    
    if (empty($sessions)) {
        echo "<div class='warning'>⚠️ 暂无会话数据</div>";
    } else {
        echo "<table>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>会话ID</th>";
        echo "<th>访客名称</th>";
        echo "<th>IP地址</th>";
        echo "<th>状态</th>";
        echo "<th>最后心跳</th>";
        echo "<th>距离心跳</th>";
        echo "<th>创建时间</th>";
        echo "</tr>";
        
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session->id}</td>";
            echo "<td>" . substr($session->session_id, 0, 8) . "...</td>";
            echo "<td>{$session->visitor_name}</td>";
            echo "<td>{$session->visitor_ip}</td>";
            
            $statusClass = $session->status === 'online' ? 'online' : 'offline';
            $statusIcon = $session->status === 'online' ? '🟢' : '⚫';
            echo "<td class='{$statusClass}'>{$statusIcon} {$session->status}</td>";
            
            echo "<td>{$session->last_seen}</td>";
            
            $secondsAgo = $session->seconds_since_heartbeat;
            $timeAgo = '';
            if ($secondsAgo < 60) {
                $timeAgo = "{$secondsAgo}秒前";
            } else {
                $minutesAgo = floor($secondsAgo / 60);
                $timeAgo = "{$minutesAgo}分钟前";
            }
            
            $timeClass = '';
            if ($secondsAgo > 30) {
                $timeClass = 'error';
            } elseif ($secondsAgo > 15) {
                $timeClass = 'warning';
            } else {
                $timeClass = 'success';
            }
            
            echo "<td class='{$timeClass}'>{$timeAgo}</td>";
            echo "<td>{$session->created_at}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // 统计信息
    $stats = DB::select("
        SELECT 
            status,
            COUNT(*) as count,
            AVG(TIMESTAMPDIFF(SECOND, last_seen, NOW())) as avg_seconds_ago
        FROM st_customer_service_sessions 
        GROUP BY status
    ");
    
    echo "<h2>📊 状态统计</h2>";
    echo "<table>";
    echo "<tr><th>状态</th><th>数量</th><th>平均心跳间隔</th></tr>";
    
    foreach ($stats as $stat) {
        $statusIcon = $stat->status === 'online' ? '🟢' : '⚫';
        $avgTime = round($stat->avg_seconds_ago);
        echo "<tr>";
        echo "<td>{$statusIcon} {$stat->status}</td>";
        echo "<td>{$stat->count}</td>";
        echo "<td>{$avgTime}秒前</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔧 调试信息</h2>";
    echo "<ul>";
    echo "<li><strong>心跳间隔:</strong> 前台每15秒发送一次</li>";
    echo "<li><strong>离线判定:</strong> 超过30秒无心跳即离线</li>";
    echo "<li><strong>后台刷新:</strong> 每2秒检查一次状态</li>";
    echo "<li><strong>页面刷新:</strong> 每2秒自动刷新数据</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "<li><a href='/clean_session_status.php' target='_blank'>清理会话状态</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
