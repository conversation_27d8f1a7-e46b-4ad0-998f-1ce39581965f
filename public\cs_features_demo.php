<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统功能演示</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .demo-header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        .demo-header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .demo-content {
            padding: 40px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
        }
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), transparent);
            border-radius: 0 0 0 100px;
        }
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
            font-weight: 600;
        }
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .feature-status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: 600;
        }
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        .status-new {
            background: #cce5ff;
            color: #0066cc;
        }
        .demo-actions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-top: 40px;
        }
        .demo-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1em;
        }
        .action-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .action-btn.secondary {
            background: #6c757d;
        }
        .action-btn.secondary:hover {
            background: #5a6268;
        }
        .action-btn.success {
            background: #28a745;
        }
        .action-btn.success:hover {
            background: #218838;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 客服系统功能演示</h1>
            <p>完整的客服解决方案 - 从基础聊天到高级自定义</p>
        </div>
        
        <div class="demo-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div class="stat-label">核心功能</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">配置选项</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">可自定义</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div class="stat-label">依赖安装</div>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <span class="feature-icon">🎵</span>
                    <h3>智能提示音系统</h3>
                    <p>支持默认音效和自定义MP3文件，可调节音量，智能节流防止重复播放</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🎨</span>
                    <h3>完全自定义外观</h3>
                    <p>主题颜色、字体大小、窗口尺寸、位置、圆角等25+个外观选项完全可控</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">📧</span>
                    <h3>智能离线留言</h3>
                    <p>客服离线时自动切换留言表单，收集访客信息，支持邮件自动回复</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">⚙️</span>
                    <h3>统一设置中心</h3>
                    <p>分类清晰的配置管理，支持实时预览，导入导出配置，一键重置</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🔧</span>
                    <h3>智能状态检测</h3>
                    <p>自动检测客服在线状态，智能切换聊天/留言模式，实时同步</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">📱</span>
                    <h3>响应式设计</h3>
                    <p>完美适配桌面、平板、手机，支持多种位置布局，触摸友好</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🔔</span>
                    <h3>多重通知系统</h3>
                    <p>声音提醒、桌面通知、标题闪烁、自动打开聊天窗口</p>
                    <span class="feature-status status-completed">✅ 已完成</span>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🖼️</span>
                    <h3>图片和表情支持</h3>
                    <p>管理员可发送图片和表情，支持拖拽上传，自动压缩优化</p>
                    <span class="feature-status status-new">🔄 开发中</span>
                </div>
            </div>

            <div class="demo-actions">
                <h3>🎯 立即体验所有功能</h3>
                <div class="action-buttons">
                    <a href="/" class="action-btn">
                        🏠 前台演示
                    </a>
                    <a href="/strongadmin/customer-service/settings" class="action-btn">
                        ⚙️ 设置中心
                    </a>
                    <a href="/strongadmin/customer-service/offline-messages" class="action-btn success">
                        📧 离线留言
                    </a>
                    <a href="/strongadmin/customer-service/sessions" class="action-btn secondary">
                        💬 会话管理
                    </a>
                    <button onclick="testAllAPIs()" class="action-btn">
                        🧪 API测试
                    </button>
                    <button onclick="showTechnicalDetails()" class="action-btn secondary">
                        📋 技术详情
                    </button>
                </div>
            </div>

            <div id="test-results" style="display: none; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <h4>🧪 API测试结果</h4>
                <div id="test-output"></div>
            </div>

            <div id="technical-details" style="display: none; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <h4>📋 技术实现详情</h4>
                <ul style="line-height: 1.8; color: #666;">
                    <li><strong>前端技术：</strong> 原生JavaScript + CSS3，无框架依赖</li>
                    <li><strong>后端技术：</strong> Laravel + MySQL，RESTful API设计</li>
                    <li><strong>实时通信：</strong> 轮询 + SSE双重保障</li>
                    <li><strong>数据存储：</strong> 2个新增数据表，完整的设置和留言管理</li>
                    <li><strong>文件上传：</strong> 支持MP3音效和图片头像上传</li>
                    <li><strong>安全性：</strong> CSRF保护，输入验证，XSS防护</li>
                    <li><strong>性能优化：</strong> 节流机制，缓存策略，懒加载</li>
                    <li><strong>用户体验：</strong> 响应式设计，无缝切换，智能提醒</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function testAllAPIs() {
            const resultsDiv = document.getElementById('test-results');
            const outputDiv = document.getElementById('test-output');
            
            resultsDiv.style.display = 'block';
            outputDiv.innerHTML = '<p>🔄 正在测试所有API...</p>';

            const tests = [
                { name: '设置API', url: '/api/customer-service/get-settings.php' },
                { 
                    name: '离线留言API', 
                    url: '/api/customer-service/submit-offline-message.php',
                    method: 'POST',
                    data: {
                        session_id: 'demo_' + Date.now(),
                        visitor_name: '演示用户',
                        visitor_email: '<EMAIL>',
                        message: '这是一条演示留言'
                    }
                }
            ];

            let results = '';
            
            for (const test of tests) {
                try {
                    const options = {
                        method: test.method || 'GET',
                        headers: { 'Content-Type': 'application/json' }
                    };
                    
                    if (test.data) {
                        options.body = JSON.stringify(test.data);
                    }
                    
                    const response = await fetch(test.url, options);
                    const data = await response.json();
                    
                    results += `
                        <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            <strong>${test.name}:</strong> 
                            <span style="color: ${data.success ? 'green' : 'red'};">
                                ${data.success ? '✅ 成功' : '❌ 失败'}
                            </span>
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            <strong>${test.name}:</strong> 
                            <span style="color: red;">❌ 错误: ${error.message}</span>
                        </div>
                    `;
                }
            }
            
            outputDiv.innerHTML = results;
        }

        function showTechnicalDetails() {
            const detailsDiv = document.getElementById('technical-details');
            detailsDiv.style.display = detailsDiv.style.display === 'none' ? 'block' : 'none';
        }

        // 页面加载动画
        window.addEventListener('load', function() {
            console.log('🎉 客服系统功能演示页面加载完成');
            
            // 添加卡片动画
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
