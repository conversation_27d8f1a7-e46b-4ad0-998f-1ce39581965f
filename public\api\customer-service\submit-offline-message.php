<?php
// Laravel环境初始化
require_once __DIR__ . '/../../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit;
}

try {
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $sessionId = $input['session_id'] ?? '';
    $message = trim($input['message'] ?? '');
    
    if (!$sessionId) {
        echo json_encode(['success' => false, 'message' => '会话ID不能为空']);
        exit;
    }
    
    if (!$message) {
        echo json_encode(['success' => false, 'message' => '留言内容不能为空']);
        exit;
    }
    
    // 获取客户端IP
    function getRealClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    // 插入离线留言
    $offlineMessageId = DB::table('customer_service_offline_messages')->insertGetId([
        'session_id' => $sessionId,
        'visitor_name' => $input['visitor_name'] ?? null,
        'visitor_email' => $input['visitor_email'] ?? null,
        'visitor_phone' => $input['visitor_phone'] ?? null,
        'visitor_whatsapp' => $input['visitor_whatsapp'] ?? null,
        'visitor_ip' => getRealClientIp(),
        'message' => $message,
        'status' => 'pending',
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    
    // 获取自动回复消息
    $autoReply = DB::table('customer_service_settings')
                   ->where('setting_key', 'offline_auto_reply')
                   ->value('setting_value') ?: '感谢您的留言，我们会尽快回复您！';
    
    echo json_encode([
        'success' => true,
        'message' => '留言提交成功',
        'auto_reply' => $autoReply,
        'offline_message_id' => $offlineMessageId
    ]);
    
} catch (Exception $e) {
    error_log('提交离线留言失败: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '提交失败，请稍后重试'
    ]);
}
