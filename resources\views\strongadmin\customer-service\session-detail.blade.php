@extends('strongadmin::layouts.app')

@push('styles')
<style>
.chat-container {
    height: 600px;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    background: #fff;
}

.chat-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e6e6e6;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
}

.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #fafafa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.message.customer {
    justify-content: flex-start;
}

.message.admin, .message.ai {
    justify-content: flex-end;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin: 0 10px;
}

.message.customer .message-avatar {
    background: #e3f2fd;
    color: #1976d2;
}

.message.admin .message-avatar {
    background: #e8f5e8;
    color: #4caf50;
}

.message.ai .message-avatar {
    background: #fff3e0;
    color: #ff9800;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
}

.message.customer .message-content {
    background: #e3f2fd;
    color: #1976d2;
}

.message.admin .message-content {
    background: #4caf50;
    color: white;
}

.message.ai .message-content {
    background: #ff9800;
    color: white;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 5px;
}

.chat-input {
    padding: 15px 20px;
    border-top: 1px solid #e6e6e6;
    background: #fff;
    border-radius: 0 0 6px 6px;
}

.input-group {
    display: flex;
    gap: 10px;
}

.message-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    outline: none;
    resize: none;
    min-height: 40px;
    max-height: 100px;
}

.send-btn {
    padding: 10px 20px;
    background: #1E9FFF;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    white-space: nowrap;
}

.send-btn:hover {
    background: #1976d2;
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.session-info {
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.quick-replies {
    margin-bottom: 10px;
}

.quick-reply-btn {
    display: inline-block;
    padding: 5px 12px;
    margin: 3px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.quick-reply-btn:hover {
    background: #1E9FFF;
    color: white;
    border-color: #1E9FFF;
}
</style>
@endpush

@section('content')
<div class="st-h15"></div>

<!-- 返回按钮 -->
<div class="layui-btn-container" style="margin-bottom: 15px;">
    <a href="../sessions" class="layui-btn layui-btn-primary">
        <i class="layui-icon layui-icon-return"></i> 返回会话列表
    </a>
    <button class="layui-btn layui-btn-normal" onclick="refreshMessages()">
        <i class="layui-icon layui-icon-refresh-3"></i> 刷新消息
    </button>
</div>

<div class="layui-row layui-col-space15">
    <!-- 左侧：聊天界面 -->
    <div class="layui-col-md8">
        <div class="chat-container">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3 style="margin: 0;">
                            <i class="layui-icon layui-icon-username"></i>
                            {{ $session->visitor_name ?: '访客' }}
                            @if($session->user_id)
                                <span class="layui-badge layui-badge-rim">注册用户</span>
                            @endif
                        </h3>
                        <small style="color: #666;">会话ID: {{ $session->session_id }}</small>
                    </div>
                    <div>
                        @if($session->status == 'active')
                            <span class="layui-badge">活跃</span>
                        @elseif($session->status == 'waiting')
                            <span class="layui-badge layui-bg-orange">等待中</span>
                        @else
                            <span class="layui-badge layui-bg-gray">已关闭</span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- 消息列表 -->
            <div class="chat-messages" id="chat-messages">
                <!-- 调试信息 -->
                @if(config('app.debug'))
                <div style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px; border-radius: 4px;">
                    <strong>调试信息:</strong><br>
                    会话ID: {{ $session->id ?? 'N/A' }}<br>
                    消息数量: {{ isset($session->messages) ? count($session->messages) : '0' }}<br>
                    @if(isset($session->messages))
                        @foreach($session->messages as $index => $msg)
                            消息{{ $index + 1 }}: {{ $msg->sender_type }} - {{ substr($msg->message, 0, 30) }}...<br>
                        @endforeach
                    @endif
                </div>
                @endif
                @if(isset($session->messages) && count($session->messages) > 0)
                    @foreach($session->messages as $message)
                    <div class="message {{ $message->sender_type }}">
                        @if($message->sender_type == 'customer')
                            <div class="message-avatar">
                                <i class="layui-icon layui-icon-username"></i>
                            </div>
                            <div class="message-content">
                                {{ $message->message }}
                                <div class="message-time">{{ $message->created_at }}</div>
                            </div>
                        @else
                            <div class="message-content">
                                {{ $message->message }}
                                <div class="message-time">
                                    {{ $message->sender_type == 'ai' ? 'AI回复' : '客服' }} · {{ $message->created_at }}
                                </div>
                            </div>
                            <div class="message-avatar">
                                @if($message->sender_type == 'ai')
                                    <i class="layui-icon layui-icon-face-smile"></i>
                                @else
                                    <i class="layui-icon layui-icon-service"></i>
                                @endif
                            </div>
                        @endif
                    </div>
                    @endforeach
                @else
                <div style="text-align: center; padding: 40px; color: #999;">
                    <i class="layui-icon layui-icon-dialogue" style="font-size: 48px; display: block; margin-bottom: 10px;"></i>
                    <h5>暂无消息记录</h5>
                    <p>当客户发送消息时，对话记录将显示在这里</p>
                    <div style="margin-top: 20px;">
                        <a href="../sessions" class="layui-btn layui-btn-primary layui-btn-sm">
                            <i class="layui-icon layui-icon-return"></i> 返回会话列表
                        </a>
                    </div>
                </div>
                @endif
            </div>

            <!-- 输入框 -->
            <div class="chat-input">
                <!-- 快捷回复 -->
                <div class="quick-replies">
                    <span style="font-size: 12px; color: #666;">快捷回复：</span>
                    <span class="quick-reply-btn" onclick="insertQuickReply('您好！我是客服，有什么可以帮助您的吗？')">问候</span>
                    <span class="quick-reply-btn" onclick="insertQuickReply('请稍等，我为您查询一下...')">查询中</span>
                    <span class="quick-reply-btn" onclick="insertQuickReply('感谢您的咨询，还有其他问题吗？')">感谢</span>
                    <span class="quick-reply-btn" onclick="insertQuickReply('如有其他问题，随时联系我们！')">结束</span>
                </div>
                
                <form method="POST" id="reply-form">
                    @csrf
                    <div class="input-group">
                        <textarea class="message-input" name="message" id="message-input" placeholder="输入回复消息..." rows="1" required></textarea>
                        <button type="submit" class="send-btn">
                            <i class="layui-icon layui-icon-release"></i> 发送
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 右侧：会话信息 -->
    <div class="layui-col-md4">
        <div class="session-info">
            <h3 style="margin-top: 0;">
                <i class="layui-icon layui-icon-user"></i> 客户信息
            </h3>
            
            <div class="info-item">
                <span>访客姓名：</span>
                <span>{{ $session->visitor_name ?: '未提供' }}</span>
            </div>
            
            <div class="info-item">
                <span>邮箱地址：</span>
                <span>{{ $session->visitor_email ?: '未提供' }}</span>
            </div>
            
            <div class="info-item">
                <span>IP地址：</span>
                <span>{{ $session->visitor_ip }}</span>
            </div>
            
            <div class="info-item">
                <span>用户类型：</span>
                <span>
                    @if($session->user_id)
                        <span class="layui-badge">注册用户</span>
                    @else
                        <span class="layui-badge layui-bg-gray">访客</span>
                    @endif
                </span>
            </div>
            
            <div class="info-item">
                <span>会话状态：</span>
                <span>
                    @if($session->status == 'active')
                        <span class="layui-badge">活跃</span>
                    @elseif($session->status == 'waiting')
                        <span class="layui-badge layui-bg-orange">等待中</span>
                    @else
                        <span class="layui-badge layui-bg-gray">已关闭</span>
                    @endif
                </span>
            </div>
            
            <div class="info-item">
                <span>创建时间：</span>
                <span>{{ $session->created_at }}</span>
            </div>
            
            <div class="info-item">
                <span>最后活动：</span>
                <span>{{ $session->last_activity ?: '无' }}</span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="layui-btn-container">
            @if($session->status != 'closed')
                <button class="layui-btn layui-btn-danger layui-btn-sm" onclick="closeSession()">
                    <i class="layui-icon layui-icon-close"></i> 关闭会话
                </button>
            @endif
            <button class="layui-btn layui-btn-normal layui-btn-sm" onclick="assignToMe()">
                <i class="layui-icon layui-icon-username"></i> 分配给我
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
// 插入快捷回复
function insertQuickReply(text) {
    document.getElementById('message-input').value = text;
    document.getElementById('message-input').focus();
}

// 刷新消息
function refreshMessages() {
    location.reload();
}

// 关闭会话
function closeSession() {
    layer.confirm('确定要关闭这个会话吗？', {
        icon: 3,
        title: '提示'
    }, function(index){
        layer.msg('功能开发中...');
        layer.close(index);
    });
}

// 分配给我
function assignToMe() {
    layer.confirm('确定要将此会话分配给自己吗？', {
        icon: 3,
        title: '提示'
    }, function(index){
        layer.msg('功能开发中...');
        layer.close(index);
    });
}

// 自动滚动到底部
function scrollToBottom() {
    const messagesContainer = document.getElementById('chat-messages');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 页面加载完成后滚动到底部
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
});

// 回车发送消息
document.getElementById('message-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        document.getElementById('reply-form').submit();
    }
});

// 表单提交后滚动到底部
document.getElementById('reply-form').addEventListener('submit', function() {
    setTimeout(scrollToBottom, 100);
});
</script>
@endpush

@endsection
