<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化语法测试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 最小化语法测试</h1>
        <p>逐步测试可能导致语法错误的代码段</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="testBasicSyntax()">测试基础语法</button>
            <button class="test-btn" onclick="testTemplateStrings()">测试模板字符串</button>
            <button class="test-btn" onclick="testConsoleCalls()">测试Console调用</button>
            <button class="test-btn" onclick="testFetchCalls()">测试Fetch调用</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>📋 最小化测试已就绪</h3>
                <p>这个页面将逐步测试可能导致语法错误的代码模式</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function updateDisplay(content, isError = false) {
            const display = document.getElementById('result-display');
            const className = isError ? 'error-box' : 'success-box';
            display.innerHTML = `<div class="${className}">${content}</div>`;
        }

        function testBasicSyntax() {
            updateDisplay('<h3>🔍 测试基础语法...</h3>');
            
            const tests = [
                {
                    name: '变量声明',
                    code: () => {
                        const test = 'hello';
                        let test2 = 'world';
                        var test3 = 'test';
                    }
                },
                {
                    name: '函数定义',
                    code: () => {
                        function testFunc() {
                            return 'test';
                        }
                        const arrowFunc = () => 'test';
                    }
                },
                {
                    name: '对象字面量',
                    code: () => {
                        const obj = {
                            prop1: 'value1',
                            prop2: 'value2',
                            method() {
                                return 'test';
                            }
                        };
                    }
                },
                {
                    name: '数组操作',
                    code: () => {
                        const arr = [1, 2, 3];
                        arr.forEach(item => console.log(item));
                        arr.map(item => item * 2);
                    }
                }
            ];
            
            let results = '<h3>✅ 基础语法测试结果</h3>';
            let hasErrors = false;
            
            tests.forEach(test => {
                try {
                    test.code();
                    results += `<div style="color: green;">✅ ${test.name}: 通过</div>`;
                } catch (e) {
                    hasErrors = true;
                    results += `<div style="color: red;">❌ ${test.name}: 失败 - ${e.message}</div>`;
                }
            });
            
            updateDisplay(results, hasErrors);
        }

        function testTemplateStrings() {
            updateDisplay('<h3>🔍 测试模板字符串...</h3>');
            
            const tests = [
                {
                    name: '简单模板字符串',
                    code: () => {
                        const name = 'test';
                        const result = `Hello ${name}`;
                        return result;
                    }
                },
                {
                    name: '复杂表达式',
                    code: () => {
                        const obj = { prop: 'value' };
                        const result = `Result: ${obj && obj.prop ? obj.prop : 'default'}`;
                        return result;
                    }
                },
                {
                    name: '三元运算符',
                    code: () => {
                        const field = 'name';
                        const fieldKey = field === 'name' ? 'visitor_name' :
                                       field === 'email' ? 'visitor_email' :
                                       field === 'phone' ? 'visitor_phone' : field;
                        return fieldKey;
                    }
                },
                {
                    name: 'Alert中的模板字符串',
                    code: () => {
                        const fieldName = '姓名';
                        // 不实际调用alert，只测试语法
                        const alertMessage = `请填写${fieldName}`;
                        return alertMessage;
                    }
                }
            ];
            
            let results = '<h3>✅ 模板字符串测试结果</h3>';
            let hasErrors = false;
            
            tests.forEach(test => {
                try {
                    const result = test.code();
                    results += `<div style="color: green;">✅ ${test.name}: 通过 (结果: ${result})</div>`;
                } catch (e) {
                    hasErrors = true;
                    results += `<div style="color: red;">❌ ${test.name}: 失败 - ${e.message}</div>`;
                }
            });
            
            updateDisplay(results, hasErrors);
        }

        function testConsoleCalls() {
            updateDisplay('<h3>🔍 测试Console调用...</h3>');
            
            const tests = [
                {
                    name: 'console.log基础',
                    code: () => {
                        console.log('测试消息');
                    }
                },
                {
                    name: 'console.log模板字符串',
                    code: () => {
                        const now = new Date().toLocaleTimeString();
                        console.log(`时间: ${now}`);
                    }
                },
                {
                    name: 'console.error带参数',
                    code: () => {
                        const now = new Date().toLocaleTimeString();
                        const error = new Error('测试错误');
                        console.error(`❌ [${now}] 错误:`, error);
                    }
                },
                {
                    name: 'console.log复杂表达式',
                    code: () => {
                        const data = { messages: [1, 2, 3] };
                        console.log(`📨 收到 ${data.messages.length} 条新消息`);
                    }
                }
            ];
            
            let results = '<h3>✅ Console调用测试结果</h3>';
            let hasErrors = false;
            
            tests.forEach(test => {
                try {
                    test.code();
                    results += `<div style="color: green;">✅ ${test.name}: 通过</div>`;
                } catch (e) {
                    hasErrors = true;
                    results += `<div style="color: red;">❌ ${test.name}: 失败 - ${e.message}</div>`;
                }
            });
            
            updateDisplay(results, hasErrors);
        }

        function testFetchCalls() {
            updateDisplay('<h3>🔍 测试Fetch调用...</h3>');
            
            const tests = [
                {
                    name: 'Fetch语法结构',
                    code: () => {
                        // 只测试语法，不实际执行
                        const fetchCode = `
                            fetch('/api/test', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                },
                                body: JSON.stringify({
                                    session_id: 'test',
                                    message: 'test'
                                })
                            })
                        `;
                        // 使用Function构造函数测试语法
                        new Function('document', 'JSON', fetchCode);
                    }
                },
                {
                    name: 'Fetch with template string',
                    code: () => {
                        const sessionId = 'test123';
                        const lastMessageId = 0;
                        const apiUrl = `/api/customer-service/messages/${sessionId}?last_id=${lastMessageId}`;
                        // 只测试URL构造，不实际fetch
                        return apiUrl;
                    }
                },
                {
                    name: 'Fetch promise chain',
                    code: () => {
                        // 测试promise链语法
                        const promiseCode = `
                            fetch('/api/test')
                                .then(response => response.json())
                                .then(data => {
                                    console.log('数据:', data);
                                })
                                .catch(error => {
                                    console.error('错误:', error);
                                });
                        `;
                        new Function('fetch', 'console', promiseCode);
                    }
                }
            ];
            
            let results = '<h3>✅ Fetch调用测试结果</h3>';
            let hasErrors = false;
            
            tests.forEach(test => {
                try {
                    const result = test.code();
                    results += `<div style="color: green;">✅ ${test.name}: 通过${result ? ` (结果: ${result})` : ''}</div>`;
                } catch (e) {
                    hasErrors = true;
                    results += `<div style="color: red;">❌ ${test.name}: 失败 - ${e.message}</div>`;
                }
            });
            
            if (!hasErrors) {
                results += `
                    <div style="background: #e7f3ff; padding: 15px; border-radius: 4px; margin-top: 15px;">
                        <h4>🎉 所有测试通过！</h4>
                        <p>如果这些基础语法都正确，那么主页面的语法错误可能是由于:</p>
                        <ul>
                            <li>动态生成的代码中有问题</li>
                            <li>某个特定的字符串包含特殊字符</li>
                            <li>浏览器缓存了旧的错误代码</li>
                            <li>外部脚本冲突</li>
                        </ul>
                        <p><strong>建议</strong>: 清除浏览器缓存后重新测试主页面</p>
                    </div>
                `;
            }
            
            updateDisplay(results, hasErrors);
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay(`
                <h3>🚀 最小化测试已启动</h3>
                <p>这个页面将逐步测试可能导致语法错误的代码模式。</p>
                <p><strong>目标</strong>: 找出导致 "missing ) after argument list" 错误的具体原因</p>
                <p><strong>方法</strong>: 逐个测试不同的JavaScript语法模式</p>
            `);
        });
    </script>
</body>
</html>
