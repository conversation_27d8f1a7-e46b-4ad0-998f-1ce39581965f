<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试后台回复功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .test-btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试后台回复功能</h1>
        <p>这个页面用来测试后台回复消息的功能。</p>
        
        <div class="test-panel">
            <h2>📝 手动测试回复</h2>
            <div>
                <label>会话ID: <input type="number" id="session-id" value="8" min="1"></label>
                <label>回复消息: <input type="text" id="reply-message" value="测试管理员回复" style="width: 300px;"></label>
                <button class="test-btn" onclick="sendReply()">发送回复</button>
            </div>
            <div id="reply-results" class="log"></div>
        </div>
        
        <div class="test-panel">
            <h2>🔍 测试步骤</h2>
            <ol>
                <li><strong>获取CSRF Token</strong> - <button class="test-btn" onclick="getCSRFToken()">获取Token</button></li>
                <li><strong>测试获取消息</strong> - <button class="test-btn" onclick="testGetMessages()">获取消息</button></li>
                <li><strong>发送回复消息</strong> - 使用上面的表单</li>
                <li><strong>验证前台接收</strong> - 检查前台是否收到回复</li>
            </ol>
            <div id="test-results" class="log"></div>
        </div>
        
        <div class="test-panel">
            <h2>🔗 相关链接</h2>
            <a href="/strongadmin/customer-service/sessions" target="_blank" class="test-btn">后台会话管理</a>
            <a href="/" target="_blank" class="test-btn">前台首页</a>
            <a href="/test_cs.php" target="_blank" class="test-btn">发送客户消息</a>
            <a href="/debug_tables.php" target="_blank" class="test-btn">检查数据库</a>
        </div>
    </div>

    <script>
    let csrfToken = null;
    
    // 获取CSRF Token
    function getCSRFToken() {
        const results = document.getElementById('test-results');
        results.innerHTML = '<p class="info">🔄 获取CSRF Token...</p>';
        
        fetch('/api/customer-service/csrf-token')
            .then(response => response.json())
            .then(data => {
                csrfToken = data.token;
                results.innerHTML += '<p class="success">✅ CSRF Token获取成功: ' + csrfToken.substring(0, 20) + '...</p>';
            })
            .catch(error => {
                results.innerHTML += '<p class="error">❌ 获取CSRF Token失败: ' + error.message + '</p>';
            });
    }
    
    // 测试获取消息
    function testGetMessages() {
        const sessionId = document.getElementById('session-id').value;
        const results = document.getElementById('test-results');
        
        if (!sessionId) {
            results.innerHTML += '<p class="error">❌ 请输入会话ID</p>';
            return;
        }
        
        results.innerHTML += '<p class="info">🔄 测试获取会话 ' + sessionId + ' 的消息...</p>';
        
        fetch('/strongadmin/customer-service/session/' + sessionId + '/messages', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            results.innerHTML += '<p class="info">📊 响应状态: ' + response.status + '</p>';
            return response.json();
        })
        .then(data => {
            if (data.success) {
                results.innerHTML += '<p class="success">✅ 获取消息成功，消息数量: ' + data.messages.length + '</p>';
                results.innerHTML += '<pre>' + JSON.stringify(data.messages, null, 2) + '</pre>';
            } else {
                results.innerHTML += '<p class="error">❌ 获取消息失败: ' + data.error + '</p>';
            }
        })
        .catch(error => {
            results.innerHTML += '<p class="error">❌ 请求失败: ' + error.message + '</p>';
        });
    }
    
    // 发送回复
    function sendReply() {
        const sessionId = document.getElementById('session-id').value;
        const message = document.getElementById('reply-message').value;
        const results = document.getElementById('reply-results');
        
        if (!sessionId || !message) {
            results.innerHTML = '<p class="error">❌ 请输入会话ID和回复消息</p>';
            return;
        }
        
        if (!csrfToken) {
            results.innerHTML = '<p class="error">❌ 请先获取CSRF Token</p>';
            return;
        }
        
        results.innerHTML = '<p class="info">🔄 发送回复到会话 ' + sessionId + '...</p>';
        
        const formData = new FormData();
        formData.append('_token', csrfToken);
        formData.append('message', message);
        
        fetch('/strongadmin/customer-service/session/' + sessionId + '/reply', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: formData
        })
        .then(response => {
            results.innerHTML += '<p class="info">📊 响应状态: ' + response.status + '</p>';
            return response.json();
        })
        .then(data => {
            if (data.success) {
                results.innerHTML += '<p class="success">✅ 回复发送成功!</p>';
                results.innerHTML += '<p class="info">现在检查前台是否收到回复...</p>';
            } else {
                results.innerHTML += '<p class="error">❌ 回复发送失败: ' + data.error + '</p>';
            }
        })
        .catch(error => {
            results.innerHTML += '<p class="error">❌ 请求失败: ' + error.message + '</p>';
        });
    }
    
    // 页面加载时自动获取CSRF Token
    document.addEventListener('DOMContentLoaded', function() {
        getCSRFToken();
    });
    </script>
</body>
</html>
