body{
    /*background-color: #f2f2f2;*/
    font-family: "Times New Roman", Times, serif !important;
}

/* 全局字体设置 - 排除图标字体 */
h1, h2, h3, h4, h5, h6, p, span:not([class*="glyphicon"]):not([class*="fa"]):not([class*="icon"]),
div:not([class*="glyphicon"]):not([class*="fa"]):not([class*="icon"]),
a:not([class*="glyphicon"]):not([class*="fa"]):not([class*="icon"]),
li, td, th, input, textarea, select, button, label {
    font-family: "Times New Roman", Times, serif !important;
}

/* 确保图标字体正常显示 */
.glyphicon, [class*="glyphicon-"], .fa, [class*="fa-"], [class*="icon-"] {
    font-family: 'Glyphicons Halflings', FontAwesome, inherit !important;
}

/* Bootstrap图标字体 */
.glyphicon {
    font-family: 'Glyphicons Halflings' !important;
}

/* FontAwesome图标字体 */
.fa, [class*="fa-"] {
    font-family: FontAwesome !important;
}

/* 产品详情页布局修复 */
.product-gallery-modern {
    width: 100% !important;
    box-sizing: border-box !important;
}

.product-info-modern {
    width: 100% !important;
    box-sizing: border-box !important;
}

.main-image-container {
    width: 100% !important;
    box-sizing: border-box !important;
}

.thumbnail-list {
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 产品列表页灵活布局 - 一行3个 */
.st-main-product .product-card {
    border: none !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
}

.st-main-product .product-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .st-main-product div[style*="width: calc(33.333% - 14px)"] {
        width: calc(33.333% - 14px) !important;
        max-width: 300px !important;
    }
}

@media (max-width: 992px) {
    .st-main-product div[style*="width: calc(33.333% - 14px)"] {
        width: calc(50% - 10px) !important;
        max-width: none !important;
    }

    .st-main-product div[style*="display: flex"] {
        gap: 20px !important;
    }
}

@media (max-width: 768px) {
    .st-main-product div[style*="width: calc(33.333% - 14px)"] {
        width: 100% !important;
        max-width: none !important;
    }

    .st-main-product div[style*="display: flex"] {
        gap: 15px !important;
    }
}

.st-main-product .thumbnail {
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    border-radius: 8px !important;
    margin-bottom: 0 !important;
}

.st-main-product .thumbnail img {
    transition: transform 0.3s ease !important;
}

.st-main-product .btn-primary {
    background-color: #333 !important;
    border-color: #333 !important;
    font-family: 'Times New Roman', Times, serif !important;
}

.st-main-product .btn-primary:hover {
    background-color: #555 !important;
    border-color: #555 !important;
}

/* 分页样式优化 */
.pagination > li > a,
.pagination > li > span {
    color: #333 !important;
    border: 1px solid #ddd !important;
    font-family: 'Times New Roman', Times, serif !important;
}

.pagination > .active > a,
.pagination > .active > span {
    background-color: #333 !important;
    border-color: #333 !important;
    color: #fff !important;
}

.pagination > li > a:hover,
.pagination > li > span:hover {
    background-color: #f5f5f5 !important;
    border-color: #333 !important;
}

/* 排序选择框样式 */
.st-main-product-header-sortby select {
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    padding: 8px 12px !important;
    font-family: 'Times New Roman', Times, serif !important;
    background: #fff !important;
    color: #333 !important;
}

.st-main-product-header-sortby select:focus {
    border-color: #333 !important;
    outline: none !important;
    box-shadow: 0 0 5px rgba(51,51,51,0.3) !important;
}

/* 左侧菜单现代化样式 - 性能优化 */
.st-main-left {
    background: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05) !important;
    padding: 0 !important;
    margin-bottom: 20px !important;
}

.st-main-left-category,
.st-main-left-price,
.st-main-left-stock {
    padding: 0 !important;
}

.filter-header {
    background: #f8f9fa !important;
    padding: 15px 20px !important;
    border-radius: 8px 8px 0 0 !important;
    border-bottom: 1px solid #eee !important;
}

.filter-header h5 {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #333 !important;
    font-family: 'Times New Roman', Times, serif !important;
}

.category-group {
    border-bottom: 1px solid #f5f5f5 !important;
    margin-bottom: 5px !important;
}

.category-level-1 {
    padding: 12px 20px !important;
    transition: all 0.2s ease !important;
}

.category-level-1:hover {
    background: #f8f9fa !important;
}

.category-level-1.active {
    background: #333 !important;
}

.category-level-1 a {
    color: #333 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    font-family: 'Times New Roman', Times, serif !important;
    display: block !important;
}

.category-level-1.active a {
    color: #fff !important;
}

.category-children {
    padding-left: 15px !important;
    background: #fafafa !important;
}

.category-level-2 {
    border-left: 2px solid #eee !important;
    margin-left: 10px !important;
}

.category-level-2 > div {
    padding: 8px 15px !important;
    transition: all 0.2s ease !important;
}

.category-level-2 > div:hover {
    background: #f0f0f0 !important;
}

.category-level-2 > div.active {
    background: #333 !important;
}

.category-level-2 a {
    color: #666 !important;
    text-decoration: none !important;
    font-size: 13px !important;
    font-family: 'Times New Roman', Times, serif !important;
    display: block !important;
}

.category-level-2 .active a {
    color: #fff !important;
}

.category-level-3 > div {
    padding: 6px 15px !important;
    transition: all 0.2s ease !important;
}

.category-level-3 > div:hover {
    background: #e9e9e9 !important;
}

.category-level-3 > div.active {
    background: #333 !important;
}

.category-level-3 a {
    color: #999 !important;
    text-decoration: none !important;
    font-size: 12px !important;
    font-family: 'Times New Roman', Times, serif !important;
    display: block !important;
}

.category-level-3 .active a {
    color: #fff !important;
}

.filter-content {
    padding: 15px 20px !important;
}

.price-option,
.stock-option {
    margin-bottom: 10px !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
}

.price-option:hover,
.stock-option:hover {
    background: #f8f9fa !important;
}

.price-option.active,
.stock-option.active {
    background: #333 !important;
}

.price-option a,
.stock-option a {
    color: #666 !important;
    text-decoration: none !important;
    font-size: 13px !important;
    font-family: 'Times New Roman', Times, serif !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.price-option.active a,
.stock-option.active a {
    color: #fff !important;
}

.custom-checkbox {
    width: 16px !important;
    height: 16px !important;
    border: 2px solid #ddd !important;
    border-radius: 3px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 10px !important;
}

.custom-checkbox.checked {
    border-color: #fff !important;
}
.st .pull-right .dropdown-menu{
    left:auto;
    right:0;
}
.st .pagination a,.st .breadcrumb a{
    color:#333;
}
/*div高度*/
.st-h5{
    height: 5px;
    line-height: 5px;
}
.st-h10{
    height: 10px;
    line-height: 10px;
}
.st-h20{
    height: 20px;
    line-height: 20px;
}
.st-h50{
    height: 50px;
    line-height: 50px;
}
.st-h100{
    height: 100px;
    line-height: 50px;
}
.st-navtip .alert{
    margin-bottom: 5px;
    text-align: center;
}
.st-navtip > div:first-child > div.alert{
    margin-top: 5px;
}
/*导航区域-顶部*/
.st-navtop{
    height: 30px;
    line-height: 30px;
    background-color:#FAFAFA;
    border-bottom:1px solid #e9e9e9;
}
.st-navtop li a:hover,.st-navtop li a:hover i{
    color:#4285F4;
    background:none;
}
.st-navtop-items > li > a{
    font-size:12px;
    color:#666;
    padding-top: 0;
    padding-bottom: 0;
}
.st-navtop-items > li:first-child > a{
    padding-left: 0;
}
.st-navtop-items > li:last-child > a{
    padding-right: 0;
}
.st-navtop-items > li a font{
    display: inline-block;
    padding:0 5px;
}
.st-navtop-items > li > a > i{
    color:#999;
}
/*导航区域-品牌和搜索*/
.st-navbrand{
    height: 90px;
    background: #ffffff;
}
.st-navbrand-logo{
    max-width: 200px;
    min-width: 170px;
    padding-top: 10px;
}
.st-navbrand-slogan{
    line-height: 90px;
    font-style:italic;
}
.st-navbrand-search{
    padding-top:25px;
}
.st-navbrand-search .input-group-addon{
    background-color: #666666;
    border: 2px solid #666666;
    cursor: pointer;
}
.st-navbrand-search input{
    border: 2px solid #666666;
}
.st-navbrand-search .input-group-addon i{
    color: #ffffff;
}
.st-navbrand-cart{
    position: relative;
    cursor: pointer;
    padding-top:30px;
}
.st-navbrand-cart a{
    display: inline-block;
    color:#666;
}
.st-navbrand-cart i{
    position: relative;
    font-size: 28px;
    color:#666;
}
.st-navbrand-cart .badge{
    position: absolute;
    bottom:29px;
    left: 4px;
    background-color: #EA4335;
    color:#ffffff;
    width:24px;
    padding:3px;
}
.st-navbrand-cart i:hover,.st-navbrand-cart a:hover{
    color:#999;
    text-decoration: none;
}
/*导航区域-菜单*/
.st-navmenu{
    background-color: #F9F9F9;
    border-top:1px solid #e9e9e9;
}
.st-navmenu .nav > li > a{
    height: 44px;
    line-height: 44px;
    color:#666;
    border-radius: 0;
    padding:0;
    margin-right: 30px;
}
.st-navmenu .nav > li > a:hover{
    color:#080808;
    background:none;
    border-radius: 0;
    border-bottom: 3px solid #EA4335;
}
#products{
    position: relative;
}
.st-allcat{
    display: none;
    position: absolute;
    top:44px;
    left: 0;
    z-index: 999;
    width: 240px;
    box-shadow: none;
    border-radius: 0;
    border-right:none;
}
.st-allcat-items{
    border: none;
    box-shadow: none;
}
.st-allcat-items > li{
    padding:0;
    margin:0;
    border:none;
}
.st-allcat-items > li > a{
    color:#666;
    text-decoration: none;
    display:inline-block;
    padding: 0 8px;
    width:100%;
    height: 35px;
    line-height: 35px;
    border:1px solid #ddd;
    border-left: 1px solid #ffffff;
    border-top:1px solid #ffffff;
    border-bottom:1px solid #ffffff;
}
.st-allcat-items > li > a > i{
    padding-right:5px;
    color:#BCBCBC;
    font-size: 16px;
}
.st-allcat-items > li.active > a{
    background-color:#ffffff;
    color: orange;
    text-shadow:none;
    border-right: none;
    border-top:1px solid #ddd;
    border-bottom:1px solid #ddd;
    border-radius: 0;
}
.st-allcat-items > li.active:last-child > a{
    border-bottom:1px solid #ffffff;
}
.st-allcat-items > li.active:first-child > a{
    border-top:1px solid #ffffff;
}
.st-allcat-items > li.active > a > i{
    color: orange;
}
.st-allcat-content{
    display: none;
    position: absolute;
    top:-1px;
    left:238px;
    background-color: #ffffff;
}
.st-allcat-content-item{
    width:490px;
    min-height: 400px;
    border: 1px solid #ddd;
    padding:0 15px;
    padding-right: 0;
    overflow-x: hidden;
}
.st-allcat-content-item > dl{
    width: 50%;
    /*min-height: 230px;*/
    float: left;
    margin: 15px 0;
}
.st-allcat-content-item > dl > dt{
    border-bottom: 1px solid #ddd;
    margin-right: 15px;
    margin-bottom: 7px;
    padding:5px 0;
    padding-top:0;
}
.st-allcat-content-item > dl > dt > a{
    color:#333;
    text-decoration: none;
}
.st-allcat-content-item > dl > dd{
    line-height: 25px;
}
.st-allcat-content-item > dl > dd > i{
    font-size:18px;
    color:#999;
    vertical-align: middle;
    padding-right: 2px;
}
.st-allcat-content-item > dl > dd > a{
    color:#666;
    text-decoration: none;
}
.st-allcat-content-item > dl > dd > a::before{
    content: "•";
    font-size:18px;
    color:#999;
    padding-right: 2px;
    vertical-align: middle;
}
.st-allcat-content-item > dl a:hover,.st-allcat-content-item > dl > dd > a:hover::before{
    color:orange;
}
/*导航区域-顶部-移动端*/
.st-header{
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top:none;
    margin-bottom: 0;
}
.st-header .navbar-header{
    /*padding:0 5px;*/
}



/* 响应式品牌容器 */
.st-header .navbar-brand {
  display: flex !important; /* 覆盖默认inline-block */
  padding: 10px 0;
  width: auto;
  /* max-width: 280px; 最大宽度限制 */
   /*height: 60px; 固定容器高度 */
  transition: opacity 0.3s ease; /* 交互效果 */
}

/* 图片自适应配置 */
.brand-logo {
  width: auto;
  height: 100%; /* 继承容器高度 */
  max-height: 44px; /* 移动端优化 */
  object-fit: contain; /* 保持比例 */
  image-rendering: -webkit-optimize-contrast; /* 锐化处理 */
}

/* 桌面端优化 */
@media (min-width: 768px) {
  .st-header .navbar-brand {
    max-width: 320px;
    padding: 15px 0;
  }
  .brand-logo {
    max-height: 52px;
  }
}

/* 超宽屏适配 */
@media (min-width: 1200px) {
  .st-header .navbar-brand {
    max-width: 360px;
  }
}


.st-header-cart{
    position: relative;
    color:#666;
    font-size: 26px;
    margin-right: 15px;
    padding-top:8px;
}
.st-header-cart > i{
    padding-left: 3px;
}
.st-header-cart > span{
    position: absolute;
    top:3px;
    left:9px;
    display: inline-block;
    text-align: center;
    color:#EA4335;
    font-size: 1rem;
    width:15px;
}
.st-header .navbar-form{
    border-top:none;
    border-bottom: none;
}
.st-header .navbar-nav ul.st-subcat,.st-header .navbar-nav ul.st-subsubcat{
    padding-left: 25px;
}
.st-header .navbar-nav ul.st-cat > li{
    border-bottom: 1px solid #ddd;
}
.st-header .navbar-nav ul.st-subcat > li{
    border-top: 1px solid #ddd;
}
.st-header .navbar-nav ul.st-subcat > li > ul.st-subsubcat > li:first-child{
    border-top: 1px solid #ddd;
}
/*首页推荐产品和产品列表页*/
.st-home-product > div.container > div.row,.st-main-product > div.row{
    padding:0 12px;
}
.st-home-product > div.container > div.row > div,.st-main-product > div.row > div{
    padding:0 4px;
}
.st-home-product .thumbnail,.st-main-product .thumbnail{
    padding: 0;
    width: 350px;
    height: 450px;
    border: none;
    box-shadow: none;
    display: flex;
    flex-direction: column;
}
.st-home-product .thumbnail > a.st-thumb,.st-main-product .thumbnail > a.st-thumb{
    display: block !important;
    width: 350px !important;
    height: 350px !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
    background: #f8f8f8 !important;
}
.st-home-product .thumbnail > a.st-thumb img,.st-main-product .thumbnail > a.st-thumb img{
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    background: transparent;
}
.st-home-product .caption,.st-main-product .caption{
    padding: 10px 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100px;
    box-sizing: border-box;
}

.st-home-product .caption h5,.st-main-product .caption h5{
    margin: 0 0 5px 0;
    font-size: 14px;
    line-height: 1.3;
    height: 35px;
    overflow: hidden;
}

.st-home-product .caption p,.st-main-product .caption p{
    margin: 2px 0;
    font-size: 12px;
    line-height: 1.2;
}
.st-home-product .caption > h5 > a,.st-main-product .caption > h5 > a{
    display: inline-block;
    height: 30px;
    overflow-y: hidden;
    color: #333;
    font-size:1.4rem;
}
.st-home-product .caption > p,.st-main-product .caption > p{
    margin-bottom: 5px;
}
.st-home-product-price{
    color:#080808;
    font-weight: bolder;
    font-size:1.5rem;
}
.st-home-product-sold{
    color:#666;
    font-size: 12px;
}
.st-home-product-addcart{
    margin: 0 9px;
    margin-bottom:10px ;
}
.st-home-product-addcart > button{
    width: 100%;
}
.st-home-product-title{
    display: block;
    width: 100%;
    text-align: center;
    color:#000000;
}
.st-home-product-title > span{
    font-size: 13px;
    vertical-align: baseline;
    margin-right: 8px;
    line-height: 1;
    position: relative;
    top: -2px;
}
/*底部*/
.st-footer{
    background-color: #FAFAFA;
    padding-top: 80px;
}
.st-footer-service{
    padding-bottom: 80px;
    color:#333;
}
.st-footer-service dt{
    padding-bottom: 5px;
}
.st-footer-service a{
    display: inline-block;
    line-height: 30px;
    color:#666;
}
.st-footer-beian{
    text-align: center;
    font-size: 12px;
    color: gray;
}
.st-footer-beian a{
    color:gray;
}
.st-footer-service-icon > a{
    text-decoration: none;
    display: inline-block;
    padding-right: 10px;
}
.st-footer-service-icon > a > i{
    font-size: 20px;
}
.st-footer-service-icon > a > i:hover{
    color:#999;
}
.st-footer-service-icon > a > img{
    width: 25px;
    display: inline-block;
    vertical-align: top;
}
.st-footer-service-icon .bi-pinterest{
    /*padding-top:3px;*/
    vertical-align: bottom;
}
.st-footer-service-signup{
    padding-top:10px;
}
.st-footer-service-signup p{
    color:#666;
}
.st-footer-service-signup span{
    background: #000000;
    color: #ffffff;
    font-weight: bolder;
    border: 1px solid #000000;
}
/*导航路径/面包屑*/
.st-breadcrumb .breadcrumb{
    margin: 5px 0;
    background-color:#ffffff;
    padding-left:0;
}
/*产品列表页*/
.st-main{}
.st-main-left>div{
    background: #F9F9F9;
    padding:10px;
    margin-bottom: 10px;
}
.st-main-left a{
    color:#666;
}
.st-main-left h5,#FILTER h5{
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    font-weight: bolder;
    margin-top: 0;
}
.st-main-left-category a,.st-main-product-header-filter a{
    color:#333;
}
.st-main-left-category dl{
    margin-bottom: 5px;
}
.st-main-left-category dt a{
    color:#333;
    font-weight: lighter;
}
.st-main-left-category dd{
    margin-left: 15px;
}
.st-main-left-category > dl > dt{
    border-bottom: 1px solid #ddd;
}
.st-main-left-category > dl > dt > a{
    font-weight: bold;
}
.st-main-left-category > dl > dd::before{
    position: absolute;
    content: "•";
    left:32px;
    color:gray;
}
.st-main-left-category .active{
    background-color: #BCBCBC;
}
.st-main-product-header{
    padding-bottom:8px;
}
.st-main-product-header > span{
    display: inline-block;
}
.st-main-product-header-pager .pager{
    margin: 0;
    padding: 0;
}
.st-main-product-header-pager .pager li a{
    padding:2px 14px;
}
.st-main-product-header-pager ul.pagination{
    margin: 0 0;
    margin-bottom: 10px;
}
.st-main-product-header-sortby{
    line-height: 34px;
}
.st-main-product-header-sortby select{
    height: 37px;
}
.st-main-product-header-filter{
    border:1px solid #ddd;
    padding:2px 14px;
    border-radius: 15px;
}
/*产品详情-图片放大镜*/
.st-detail-img{
    position:relative;
    margin-bottom: 15px;
}
.st-detail-img-left {
    position:relative;
    display:inline-block;
    *display:inline;
    *zoom:1;
    width:10%;
    height: 417px;
    overflow-x:hidden;
    overflow-y:auto;
}
.st-detail-img-left::-webkit-scrollbar{
    display: none;
}
.st-detail-img-left ul {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    font-size:0;
    padding: 0;
}
.st-detail-img-left ul li {
    display:inline-block;
    *display:inline;
    *zoom:1;
    border:1px solid #BCBCBC;
    border-color:#F0F0F0;
    margin-bottom: 2px;
    border-radius: 2px;
    min-width: 35px;
    min-height: 35px;
}
.st-detail-img-left ul li:last-of-type {
    margin-bottom:0;
}
.st-detail-img-left ul .active {
    border-color:#999;
}
.st-detail-img-left ul li img{
    width:100%;
}
.st-detail-img-right {
    vertical-align:top;
    position:relative;
    display:inline-block;
    *display:inline;
    *zoom:1;
    width:460px;
    width:89%;
    border:1px solid #ddd;
}
.st-detail-img-right .pic {
    position:relative;
    width: 100%;
    /*min-height: 318px;*/
}
.st-detail-img-right .pic img{
    width: 100%;
}
.st-detail-img-right .pic .magnify {
    width:230px;
    height:230px;
    display:none;
    position:absolute;
    top:0;
    left:0;
    cursor:crosshair;
    background-color:#000000;
    opacity:.3;
    filter:alpha(opacity=30);
}
.st-detail-img-right .bigpic {
    display:none;
    position:absolute;
    top:-1px;
    right:-460px;
    width:460px;
    height:460px;
    z-index:333;
    border:1px solid #ddd;
    border-left: none;
    overflow:hidden;
}
.st-detail-img-right .bigpic > img {
    width:920px;
    height:920px;
    position:absolute;
    top:0;
    left:0;
}
.st-detail-img-right-share{
    position: absolute;
    left:0;
    bottom:-40px;
}
.st-detail-img .swiper-container {
    /*width: 100%;*/
    min-height: 260px;
}
.st-detail-img .swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;
    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}
/*产品详情-产品属性信息*/
.st-detail-attr{
    /*padding-bottom: 10px;*/
}
.st-detail-attr > h3{
    padding-top: 0;
    margin-top: 0;
    font-size:19px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    line-height: 1.4;
}
.st-detail-attr > dl{
    display: block;
    clear: both;
    margin-bottom: 0px;
}
.st-detail-attr > dl > dt{
    display: block;
    line-height: 20px;
    padding-right: 5px;
    color:#555555;
    font-weight: normal;
}
.st-detail-attr > dl > dd{
    display: block;
    text-align: center;
    line-height: 20px;
}
.st-detail-attr .st-attr > dd{
    display: inline-block;
    margin:0 10px 10px 0;
    border:1px solid #BCBCBC;
    cursor: pointer;
    min-width: 40px;
    text-align: center;
    padding:0 3px;
}
.st-detail-attr .st-picture > dd{
    width:40px;
    height: 40px;
    overflow: hidden;
    padding:0;
}
.st-detail-attr .st-attr > dd > img{
    width:100%;
}
.st-detail-attr .st-attr > dd.st-stockout,.st-detail-attr .st-attr > dd.st-invalid{
    border-style: dotted;
    opacity: .6;
    filter:alpha(opacity=60);
}
.st-detail-attr .st-price{
    margin-top: 20px;
    margin-bottom: 20px;
}
.st-detail-attr .st-price > dt{
    font-size: 2.5rem;
    color:#333;
    font-weight: 700;
}
.st-detail-attr .st-price > dt > del{
    font-size: 1.8rem;
    color:#666;
    padding-left: 10px;
    font-weight: lighter;
}
.st-detail-attr .st-attr > dd.active{
    border:2px solid #EA4335;
}
.st-detail-attr .st-attr > dd:hover{
    box-shadow: 0px 0px 5px #BCBCBC;
}
.st-detail-attr .st-general > dt,.st-detail-attr .st-general > dd{
    display: inline-block;
}
.st-detail-attr .st-itemcode > dd{
    display: block;
    text-align: left;
    color:#999;
    font-size: 12px;
}
/*产品详情操作按钮*/
.st-detail-btn button{
    font-size: 12px;
    margin-top:5px;
    margin-bottom:5px;
}
.st-detail-btn button span{
    text-align: left;
    float: left;
    display: inline-block;
    padding-right: 8px;
}
.st-detail-btn .st-share a,.st-detail-img-right-share .st-share a{
    font-size:16px;
    color:#666;
    padding:0 5px;
}
.st-detail-btn .st-share a:hover{
    color:#999;
}
.st-detail-btn-right{
    border:1px solid #ddd;
    padding:10px 15px;;
    border-radius: 5px;
}
.st-detail-btn-right .st-qty > input{
    width:50px;
}
.st-detail-btn-right .st-instock{
    color:#34A853;
}
.st-detail-btn-right .st-stockout{
    color:#EA4335;
}
.st-detail-btn-right .st-whosale td font{
    padding-right: 5px;
    width:15px;
    display: inline-block;
}
.st-detail-btn-right .addtocart{
    /*width:100%;*/
}
.st-detail-btn-right .st-btn .buynow > span{
    padding-right: 5px;
}
.st-main-detail .nav-tabs li a{
    color:#555555;
}
.st-main-detail-reviews-content > dl{
    border-bottom: 1px solid #ddd;
    padding-top: 15px;
}
.st-main-detail-reviews-content > dl > dt{
    line-height: 35px;
}
.st-main-detail-reviews-content > dl > dt i{
    color:orange;
}
.st-main-detail-reviews-content > dl > dd.st-datetime{
    color:#666;
    font-size:12px;
    line-height: 25px;
}
#Details img{
    max-width:100%;
    height: auto;
}
/*个人中心*/
.st-user .media-heading > a{
    color:#333;
}
.st-user .panel-body .page-header{
    margin-top:0;
}
.st-user-info .st-emailnotvalidated{
    color:#EA4335;font-size: 12px;
}
.st-user-info .st-emailnotvalidated a{
    text-decoration: underline;color:#4285F4;
}
.st-user-orders .st-price{
    font-size:12px;
    color:#666;
}
.st-user-address-add select{
    margin-top: 5px;
}
.st-user-address-default .st-default dl{
    padding:5px;
}
.st-user-address-default .st-default dl:first-child{
    margin-right: 100px;
}
.st-user-address-default .st-default dl > dt{
    color:#4285F4;
}
.st-user-address-list dl{
    padding:5px;
    border: 2px dotted #999;
}
.st-user-address-list dl dd small{
    color:#666;
    font-style: italic;
    font-size: 12px;
}
.st-user-feedback-list > dl{
    border-bottom: 1px solid #ddd;
    padding-top: 15px;
}
.st-user-feedback-list > dl > dt{
    line-height: 35px;
}
.st-user-feedback-list > dl > dt i{
    color:orange;
}
.st-user-feedback-list dd.st-datetime{
    color:#666;
    font-size:12px;
    line-height: 25px;
}
.st-user-feedback-list > dl > dd.st-reply > dl{
    padding-left: 30px;
    border-top:1px dotted #999;
}
.st-user-feedback-list>dl.st-reply{
    text-align: right;
}
.st-user-feedback-list-replies>dl{
    border-bottom: none;
    padding-top:0;
    margin-bottom: 10px;
}
.st-user-feedback-list-replies>dl>dd.st-text{
    display: inline-block;
    max-width:70%;
    border: 1px solid #eee;
    padding: 5px 10px;
    border-radius: 10px;
    word-break: break-word;
    text-align: left;
}
.st-user-feedback-list-replies>dl.st-reply>dd.st-text{
    float:right;
    background: #eee;
}
.st-user-wishlist .media .media-object,.st-user-orders .media .media-object{
    width:64px;
}
.st-user-wishlist .media .media-body .st-itemcode,.st-user-orders .media .media-body .st-itemcode{
    color:#999;font-size:12px;
}
.st-user-wishlist .media .media-body .st-datetime{
    color:#666;text-align: right;
}
.st-user-orders p{
    margin-bottom: 0px;
}
.st-user-orders .panel-body > div.st-date > p > label{
    width:100px;
}
.st-user-orders .panel-body > div.st-date > p.st-shipdesc{
    text-indent: 100px;
}
.st-user-orders table>thead>tr>th{
    border-bottom:none;
}
.st-user-orders table>thead>tr:first-child>th{
    border-bottom: 2px solid #ddd;
}

/*购物车*/
.st-cart .page-header{
    margin-top: 0;
}
.st-cart-checkout{
    padding:25px 15px;
}
.st-cart-checkout a.btn{
    width: 100%;
}
.st-cart-table tr:first-child td{
    border-top:none;
}
.st-cart-table a,.st-cart-table .st-price{
    color:#333;
}
.st-cart-table .st-price{
    margin-top:5px;
}
.st-cart-table .st-itemcode{
    color:#666;
}
.st-cart-table .st-price input{
    width:80px;
}
.st-cart-table .media-object{
    width:80px;
}
.st-cart-table .media-body > p{
    margin-bottom: 0;
}
.st-cart-table .st-wholesale{
    min-width:150px;
    font-size: 12px;
    color:gray;
}
.st-cart-table .st-wholesale td{
    padding:3px;
    border-top:none;
}
.st-cart-table .st-wholesale tr td:first-child{
    text-align:right;
}
.st-cart .st-fixed{
    position: fixed;top:5px;
}
/*订单结算 checkout*/
.st-checkout .form-group > label{
    font-weight: normal;
    font-size: 12px;
    color:#666;
}
.st-checkout div.row > div > div.panel{
    padding:0 15px;
}
.st-checkout div.page-header{
    margin-top: 0;
}
.st-checkout-address > div{
    padding: 0 15px;
}
.st-checkout-address select{
    margin-top: 5px;
}
.st-checkout-address .page-header{
    margin-left:-15px; 
    margin-right:-15px; 
}
.st-checkout-address .form-group > label > span{
    color:red;font-weight: bolder;padding-left: 3px;font-size: 16px;vertical-align: middle;
}
.st-checkout-cartlist > .page-header h4 font{
    font-weight: normal;
}
.st-checkout-shipingmethod div.radio label,.st-checkout-proceed div.radio label{
    line-height: 22px;
}
.st-checkout-proceed > p > label{
    display: inline-block;
    width: 49%;
    text-align: left;
    font-weight: normal;
}
.st-checkout-proceed > p > font{
    display: inline-block;
    width: 49%;
    text-align: right;
}
.st-checkout-proceed .btn{
    width:100%;
}
.st-signin .form-group label > font{
    color:#EA4335;
    font-weight: bolder;
}
#FILTER{
    display: none;
    position: absolute;
    top:0;
    right:0;
    z-index: 666;
    background-color:#fff;
    width:80%;
    height: 100%;
    overflow: auto;
    padding: 15px;
}
#FILTER .st-close{
    position: absolute;
    top:0;
    right:15px;
    cursor: pointer;
}
div.st-form-alert p{
    text-align: center;
}
div.st-form-alert:nth-child(2){
    margin-top: 10px;
}
#ST-NAVCART{}
#ST-NAVCART-PRODUCTS{
    display: none;
    width:380px;
    position: absolute;
    top:80px;
    right:0;
    background: #ffffff;
    z-index: 666;
    border:1px solid #BCBCBC;
    border-radius: 5px;
    box-shadow: 0px 0px 5px #BCBCBC;
}
#ST-NAVCART-PRODUCTS:before{
    content: "";
    position: absolute;
    top:-9px;
    right: 27px;
    z-index: 667;
    width:0;
    height:0;
    border-right:8px solid transparent;
    border-left:8px solid transparent;
    border-bottom:8px solid #BCBCBC;
}
#ST-NAVCART-PRODUCTS .page-header{
    margin:0;
    padding:8px 10px;
    box-shadow: 0 1px 3px #eee;
}
.st-navbrand-cart-product-list{
    min-height: 200px;
    max-height: 400px;
    overflow: auto;
}
.st-navbrand-cart-total .btn{
    width:100%;
}
.st-navbrand-cart-total .st-total > span{
    display: inline-block;
    width: 50%;
    text-align: right;
    padding-right: 10px;
}
.st-navbrand-cart-product-list .st-cart-table .st-wholesale{
    display: none;
}
.st-cart-checkout .st-total > span{
    display: inline-block;
    min-width: 60%;
    text-align: left;
}
.st-article > h3{
    text-align: center;
}
.st-article > p.author{
    text-align: center;
    color:gray;
}
.st-article-content{
    padding:15px;
}
.st-article-content img{
    max-width:100%;
    height: auto;
}
.st-home-aboutus .jumbotron>h1{
    font-size:27px;
}
.st-home-aboutus .jumbotron p{
    font-size:16px;
}
#Delivery table td{
    border:1px solid gray;
}
#Delivery table td p{
    margin: 0;
    padding: 0;
}
#Delivery table tr:first-child{
    background:#70ad47;
    line-height: 40px;
}
#Delivery table tr>td:first-child{
    width:230px;
}
.st-article-list a{
    color:#333;
    text-decoration: underline;
}
.st-article-list h4{
    font-size:16px;
}
.st-article-list .media-body{
    font-size: 14px;
    color:#666;
}
.st-article-origin{
    font-size:12px;
    color:#666;
}
.st-nav-user{
    color: #666;
    font-size: 26px;
    padding-right: 5px;
    padding-top: 8px;
}
/* LV风格新品展示样式 */
.lv-new-products-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    position: relative;
    overflow: hidden;
}

.lv-new-products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.lv-section-header {
    text-align: center;
    margin-bottom: 60px;
}

.lv-section-title {
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 3px;
    margin: 0;
    text-transform: uppercase;
    color: #000;
    font-family: 'Helvetica Neue', 'Arial', sans-serif;
    position: relative;
}

.lv-title-link {
    color: #000;
    text-decoration: none;
    transition: color 0.3s ease;
}

.lv-title-link:hover {
    color: #8B4513;
    text-decoration: none;
}

.lv-title-underline {
    width: 60px;
    height: 2px;
    background: #8B4513;
    margin: 20px auto 0;
    transition: width 0.3s ease;
}

.lv-section-header:hover .lv-title-underline {
    width: 100px;
}

.lv-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, 400px);
    gap: 30px;
    margin-bottom: 60px;
    justify-content: center;
}

.lv-product-item {
    position: relative;
    width: 400px;
    height: 500px;
}

.lv-product-card {
    background: #fff;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    border-radius: 0;
    overflow: hidden;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.lv-product-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.lv-product-image-container {
    position: relative;
    overflow: hidden;
    height: 400px;
    width: 100%;
    flex-shrink: 0;
}

.lv-image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.lv-product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    transition: transform 0.6s ease;
    background: #f8f8f8;
}

.lv-product-card:hover .lv-product-image {
    transform: scale(1.05);
}

.lv-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lv-product-card:hover .lv-image-overlay {
    opacity: 1;
}

.lv-view-details {
    color: #fff;
    font-size: 1rem;
    font-weight: 300;
    letter-spacing: 1px;
    text-transform: uppercase;
    padding: 12px 24px;
    border: 1px solid #fff;
    background: transparent;
    transition: all 0.3s ease;
}

.lv-view-details:hover {
    background: #fff;
    color: #000;
}

.lv-product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: #fff;
    padding: 8px 16px;
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    z-index: 2;
    border-radius: 0;
    box-shadow: 0 2px 8px rgba(139, 69, 19, 0.3);
    transition: all 0.3s ease;
}

.lv-product-info {
    padding: 15px 20px;
    text-align: center;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
}

.lv-product-title {
    margin: 0 0 18px 0;
    font-size: 1.3rem;
    font-weight: 400;
    line-height: 1.4;
}

.lv-product-title a {
    color: #000;
    text-decoration: none;
    transition: color 0.3s ease;
    display: block;
}

.lv-product-title a:hover {
    color: #8B4513;
    text-decoration: none;
}

.lv-product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 10px;
    font-family: 'Helvetica Neue', 'Arial', sans-serif;
    letter-spacing: 0.5px;
}

.lv-product-meta {
    font-size: 0.9rem;
    color: #666;
    font-weight: 300;
}

.lv-view-all {
    text-align: center;
    margin-top: 40px;
}

.lv-view-all-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: #000;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 1px;
    text-transform: uppercase;
    padding: 15px 30px;
    border: 2px solid #000;
    transition: all 0.3s ease;
}

.lv-view-all-btn:hover {
    background: #000;
    color: #fff;
    text-decoration: none;
}

.lv-arrow {
    transition: transform 0.3s ease;
}

.lv-view-all-btn:hover .lv-arrow {
    transform: translateX(5px);
}

/*响应式设计*/
@media (max-width: 1200px) {
    .lv-products-grid {
        grid-template-columns: repeat(auto-fill, 350px);
        gap: 30px;
        justify-content: center;
    }

    .lv-product-item {
        width: 350px;
        height: 450px;
    }

    .lv-product-image-container {
        height: 350px;
    }

    .lv-product-info {
        height: 100px;
    }

    .lv-section-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .lv-new-products-section {
        padding: 60px 0;
    }

    .lv-products-grid {
        grid-template-columns: repeat(auto-fill, 300px);
        gap: 20px;
        justify-content: center;
    }

    .lv-product-item {
        width: 300px;
        height: 380px;
    }

    .lv-product-image-container {
        height: 300px;
        width: 100%;
    }

    .lv-section-title {
        font-size: 1.8rem;
        letter-spacing: 1px;
    }

    .lv-product-info {
        height: 80px;
        padding: 10px 15px;
    }

    .lv-section-header {
        margin-bottom: 40px;
    }
}

@media (max-width: 480px) {
    .lv-products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .lv-product-badge {
        top: 10px;
        left: 10px;
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .lv-view-details {
        font-size: 0.9rem;
        padding: 10px 20px;
    }
}

/*原有响应式样式保持*/
@media (min-width: 1200px) {
    .st-home-product .thumbnail > a.st-thumb{
        width: 350px !important;
        height: 350px !important;
    }
}
@media (max-width: 992px) {
    .st-main-product .thumbnail > a.st-thumb{
        width: 350px !important;
        height: 350px !important;
    }
}
@media (max-width: 768px) {
    .st-home-product .thumbnail > a.st-thumb{
        width: 350px !important;
        height: 350px !important;
    }
}
@media (max-width: 767px) {
    .st-main-detail .nav-tabs li{
        min-width: 145px;
    }
    .st-home-product .thumbnail > a.st-thumb,.st-main-product .thumbnail > a.st-thumb{
        width: 280px !important;
        height: 280px !important;
    }
}
.st-nav-default-currencypay>a{
    color:#EA4335 !important;
    text-align: center;
}
#st-google-translate-element{
    height: 29px;overflow: hidden;
}
#st-google-translate-element>div>div{
    height: 30px;
    line-height: 27px;
    border:none;
    background: none;
}
#st-google-translate-element>div>div>span>a:hover{
    text-decoration: none;
}
#st-google-translate-element>div>div>span>a>span:last-child{
    font-size:12px;
}
.st-login-third a{
    font-size:20px;
    display: inline-block;
    padding-right:10px;
    color:#555555;
}
/* 新增缩略图自适应样式 */
.st-detail-img-left {
    width: 15% !important;
    height: auto !important;
    overflow: visible !important;
}

.st-detail-img-left ul {
    position: relative !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 5px !important;
    padding: 0 5px !important;
}

.st-detail-img-left ul li {
    width: 60px !important;
    height: 60px !important;
    border: 1px solid #eee !important;
    border-radius: 4px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    overflow: hidden !important;
}

.st-detail-img-left ul li img,
.st-detail-img-left ul li video {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.3s !important;
}

/* 视频播放器固定尺寸 */
#mainVideo {
    max-width: 100% !important;
    height: 400px !important; 
    object-fit: contain !important;
    background: #000 !important;
}

/* 视频图标居中 */
.video-icon {
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
    color: #fff !important;
    font-size: 24px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

/* 主图区域尺寸锁定 */
.st-detail-img-right {
    width: 85% !important;
    max-width: 600px !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .st-detail-img-left ul li {
        width: 60px !important;
        height: 60px !important;
    }
    
    #mainVideo {
        height: 300px !important;
    }
}




/* 强制所有产品图片填满容器 */
.st-thumb img,
.st-detail-img-left li img,
.swiper-slide img,
.st-detail-img-right .pic img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;  /* 自动裁剪填充 */
    object-position: center center !important;
    background: #f8f8f8 !important;
}

/* 固定图片容器尺寸 */
.st-thumb {
    /* 移除固定高度，使用上面的flex布局 */
}

/* 移动端适配 */
@media (max-width: 768px) {
    .st-home-product .thumbnail,.st-main-product .thumbnail{
        width: 300px;
        height: 380px;
    }
    .st-home-product .thumbnail > a.st-thumb,.st-main-product .thumbnail > a.st-thumb{
        width: 300px;
        height: 300px;
    }
    .st-home-product .caption,.st-main-product .caption{
        height: 80px;
        padding: 8px 12px;
    }
}

/* 强制覆盖所有样式 - 最终版本 */
.st-home-product .thumbnail > a.st-thumb,
.st-main-product .thumbnail > a.st-thumb,
a.st-thumb {
    width: 350px !important;
    height: 350px !important;
    display: block !important;
    overflow: hidden !important;
    background: #f8f8f8 !important;
}

@media (max-width: 768px) {
    .st-home-product .thumbnail > a.st-thumb,
    .st-main-product .thumbnail > a.st-thumb,
    a.st-thumb {
        width: 350px !important;
        height: 350px !important;
    }
}

@media (max-width: 480px) {
    .st-home-product .thumbnail > a.st-thumb,
    .st-main-product .thumbnail > a.st-thumb,
    a.st-thumb {
        width: 350px !important;
        height:350px !important;
    }
}

/* 大气舒服的产品展示样式 */
.st-home-product {
    padding: 40px 0 !important;
    background: #fafafa !important;
}

.st-home-product .container {
    max-width: 1200px !important;
    width: 95% !important;
}

.st-home-product .page-header {
    margin: 0 0 50px 0 !important;
    padding: 0 !important;
    border: none !important;
    text-align: center !important;
}

.st-home-product .page-header h4 {
    font-size: 28px !important;
    font-weight: 300 !important;
    margin: 0 !important;
    color: #333 !important;
}

.st-home-product .page-header h4 a {
    color: #333 !important;
    text-decoration: none !important;
}

.st-home-product .row {
    margin: 0 -15px !important;
}

.st-home-product [class*="col-"] {
    padding: 0 15px !important;
    margin-bottom: 40px !important;
    float: left !important;
}

.st-home-product .col-lg-3 {
    width: 25% !important;
}

.st-home-product .col-md-3 {
    width: 25% !important;
}

.st-home-product .col-sm-3 {
    width: 25% !important;
}

.st-home-product .col-xs-6 {
    width: 50% !important;
}

.st-home-product .row:after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}

.st-home-product .thumbnail {
    border: none !important;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08) !important;
    border-radius: 8px !important;
    padding: 0 !important;
    margin: 0 !important;
    background: #fff !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    width: 100% !important;
    display: block !important;
}

.st-home-product .thumbnail:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.st-home-product .st-thumb {
    display: block !important;
    width: 100% !important;
    height: 200px !important;
    overflow: hidden !important;
    background: #f8f8f8 !important;
}

.st-home-product .st-thumb img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    transition: transform 0.3s ease !important;
}

.st-home-product .thumbnail:hover .st-thumb img {
    transform: scale(1.05) !important;
}

.st-home-product .caption {
    padding: 20px 15px !important;
    text-align: center !important;
    background: #fff !important;
}

.st-home-product .caption h5 {
    margin: 0 0 8px 0 !important;
    font-size: 13px !important;
    line-height: 1.3 !important;
    height: 32px !important;
    overflow: hidden !important;
    font-weight: 500 !important;
}

.st-home-product .caption h5 a {
    color: #333 !important;
    text-decoration: none !important;
}

.st-home-product .caption h5 a:hover {
    color: #007bff !important;
}

.st-home-product .st-home-product-price {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #e74c3c !important;
    margin: 4px 0 !important;
}

.st-home-product .st-home-product-sold {
    font-size: 12px !important;
    color: #666 !important;
    margin: 0 !important;
}

/* 强制修复布局 */
.st-home-product .container {
    overflow: hidden !important;
}

.st-home-product .row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -15px !important;
}

.st-home-product [class*="col-"] {
    display: flex !important;
    padding: 0 15px !important;
    margin-bottom: 40px !important;
    box-sizing: border-box !important;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .st-home-product .st-thumb {
        height: 220px !important;
    }

    .st-home-product .caption {
        padding: 20px 15px !important;
    }

    .st-home-product .caption h5 {
        font-size: 15px !important;
        height: 40px !important;
    }

    .st-home-product .st-home-product-price {
        font-size: 16px !important;
    }
}

/* 最终强制样式 - 确保LV风格生效 */
.lv-products-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, 400px) !important;
    gap: 30px !important;
    justify-content: center !important;
    margin-bottom: 60px !important;
}

.lv-product-item {
    width: 350px !important;
    height: 450px !important;
    position: relative !important;
}

.lv-product-card {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    background: #fff !important;
    box-sizing: border-box !important;
}

.lv-product-image-container {
    width: 100% !important;
    height: 400px !important;
    overflow: hidden !important;
    position: relative !important;
    flex-shrink: 0 !important;
}

.lv-product-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    object-position: center !important;
    background: #f8f8f8 !important;
}

.lv-product-info {
    height: 100px !important;
    padding: 15px 20px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    text-align: center !important;
    flex: 1 !important;
}

/* 移动端强制样式 */
@media (max-width: 768px) {
    .lv-products-grid {
        grid-template-columns: repeat(auto-fill, 300px) !important;
        gap: 20px !important;
    }

    .lv-product-item {
        width: 300px !important;
        height: 380px !important;
    }

    .lv-product-image-container {
        height: 300px !important;
    }

    .lv-product-info {
        height: 80px !important;
        padding: 10px 15px !important;
    }
}

/* 详情页缩略图容器 */
.st-detail-img-left li {
    width: 80px !important;
    height: 80px !important;
    overflow: hidden !important;
    border: 1px solid #eee !important;
}