@extends('layouts.app')
@push('styles')
<meta http-equiv="pragram" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
<style>
    .carousel-indicators li{
        border: 1px solid gray;
    }
    .carousel-indicators .active{
        border: 1px solid #fff;
        background: gray;
    }

    /* PC端样式 - 大于768px */
    @media (min-width: 769px) {
        .home-product-row {
            display: flex;
            flex-wrap: wrap;
        }
        .home-product-item {
            margin-left: 6px;
            width: 350px;
            padding: 0 10px;
            margin-bottom: 30px;
            box-sizing: border-box;
            flex: 0 0 350px;
        }
        .home-product-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 0;
            margin: 0;
            background: #fff;
        }
        .home-product-thumb {
            display: block;
            width: 100%;
            height: 350px;
            overflow: hidden;
            background: #f8f8f8;
        }
        .home-product-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        .home-product-caption {
            padding: 15px;
            text-align: center;
            background: #fff;
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .home-product-title {
            margin: 0;
            font-size: 14px;
            line-height: 1.3;
            height: 40px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .home-product-title a {
            color: #333;
            text-decoration: none;
        }
        .home-product-price {
            font-size: 16px;
            font-weight: 600;
            color: #e74c3c;
            margin: 0;
        }
    }

    /* 手机端样式 - 小于等于768px */
    @media (max-width: 768px) {
        .home-product-row {
            display: block;
            margin: 0;
        }
        .home-product-item {
            width: 100%;
            margin-left: 0;
            padding: 15px 20px;
            margin-bottom: 30px;
            float: none;
            display: block;
            box-sizing: border-box;
        }
        .home-product-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 12px;
            padding: 0;
            margin: 0;
            background: #fff;
            min-height: 320px;
        }
        .home-product-thumb {
            display: block;
            width: 100%;
            height: 240px;
            overflow: hidden;
            border-radius: 12px 12px 0 0;
            background: #f8f8f8;
        }
        .home-product-thumb img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center;
            background: #f8f8f8;
        }
        .home-product-caption {
            padding: 20px;
            text-align: center;
            background: #fff;
            border-radius: 0 0 12px 12px;
            height: auto;
        }
        .home-product-title {
            margin: 0 0 12px 0;
            font-size: 16px;
            overflow: hidden;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-align: center;
            height: 44px;
            font-family: 'Times New Roman', Times, serif;
        }
        .home-product-title a {
            color: #333;
            text-decoration: none;
        }
        .home-product-price {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            margin: 0;
            text-align: center;
            font-family: 'Times New Roman', Times, serif;
        }
    }
</style>


@endpush
@section('content')
<!-- Banner 轮播图-->
<div class="st-banner">
    <div class="container">
        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
            <!--  <ol class="carousel-indicators">
                <li data-target="#carousel-example-generic" data-slide-to="0" class="active"></li>
                <li data-target="#carousel-example-generic" data-slide-to="1" class=""></li>
            </ol>-->
            <div class="carousel-inner" role="listbox">
                <div class="item active">
                    <a href="#"><img alt="{{config('strongshop.storeName')}}" src="img/banner01.jpg" data-holder-rendered="true"></a>
                </div>
              <!--   <div class="item">
                    <a href="#"><img alt="{{config('strongshop.storeName')}}" src="img/banner02.jpg" data-holder-rendered="true"></a>
                </div>-->
            </div>
        </div>
    </div>
</div>
<div class="st-h20"></div>
  <!-- <div class="st-home-aboutus">
    <div class="container">
        <div class="jumbotron">
            <h1>About {{config('strongshop.storeName')}}</h1>
            <p>
            
            </p>
            <p><a class="btn btn-primary btn-sm" href="#" role="button">Learn more</a></p>
        </div>
    </div>
</div>-->
<div class="st-home-product">
    <!--推荐产品-->
    @if($recommendRows->isNotEmpty())
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_recommend'=>1])}}" class="minimal-title">
                @lang('Recommend Products')
            </a></h4>
        </div>
        <div class="home-product-row">
            @foreach($recommendRows as $recommendRow)
            <div class="home-product-item">
                <div class="home-product-card">
                    <a href="{{route('product.show.rewrite', ['id'=>$recommendRow->id])}}" class="home-product-thumb product-hover-effect">
                        <img alt="{{$recommendRow->title}}"
                             src="{{$recommendRow->img_cover}}"
                             class="img-responsive product-main-img"
                             @if(isset($recommendRow->asset_img_photos) && count($recommendRow->asset_img_photos) > 1)
                             data-hover-src="{{$recommendRow->asset_img_photos[count($recommendRow->asset_img_photos) - 1]['src']}}"
                             @endif
                             />
                        @if(isset($recommendRow->asset_img_photos) && count($recommendRow->asset_img_photos) > 1)
                        <img alt="{{$recommendRow->title}}"
                             src="{{$recommendRow->asset_img_photos[count($recommendRow->asset_img_photos) - 1]['src']}}"
                             class="img-responsive product-hover-img"
                             style="position: absolute; top: 0; left: 0; opacity: 0; transition: opacity 0.3s ease;" />
                        @endif
                    </a>
                    <div class="home-product-caption">
                        <h5 title="{{$recommendRow->title}}" class="home-product-title"><a href="{{route('product.show.rewrite', ['id'=>$recommendRow->id])}}">{{$recommendRow->title}}</a></h5>
                        <p class="home-product-price st-home-product-price">@price($recommendRow->sale_price)</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    <!--新品-->
    @if($newRows->isNotEmpty())
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_new'=>1])}}" class="minimal-title">
                @lang('New Products')
            </a></h4>
        </div>
        <div class="home-product-row">
            @foreach($newRows as $newRow)
            <div class="home-product-item">
                <div class="home-product-card">
                    <a href="{{route('product.show.rewrite', ['id'=>$newRow->id])}}" class="home-product-thumb product-hover-effect">
                        <img alt="{{$newRow->title}}"
                             src="{{$newRow->img_cover}}"
                             class="img-responsive product-main-img"
                             @if(isset($newRow->asset_img_photos) && count($newRow->asset_img_photos) > 1)
                             data-hover-src="{{$newRow->asset_img_photos[count($newRow->asset_img_photos) - 1]['src']}}"
                             @endif
                             />
                        @if(isset($newRow->asset_img_photos) && count($newRow->asset_img_photos) > 1)
                        <img alt="{{$newRow->title}}"
                             src="{{$newRow->asset_img_photos[count($newRow->asset_img_photos) - 1]['src']}}"
                             class="img-responsive product-hover-img"
                             style="position: absolute; top: 0; left: 0; opacity: 0; transition: opacity 0.3s ease;" />
                        @endif
                    </a>
                    <div class="home-product-caption">
                        <h5 title="{{$newRow->title}}" class="home-product-title"><a href="{{route('product.show.rewrite', ['id'=>$newRow->id])}}">{{$newRow->title}}</a></h5>
                        <p class="home-product-price st-home-product-price">@price($newRow->sale_price)</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    <!--热卖-->
    @if($hotRows->isNotEmpty())
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_hot'=>1])}}" class="minimal-title">
                @lang('Hot Products')
            </a></h4>
        </div>
        <div class="home-product-row">
            @foreach($hotRows as $hotRow)
            <div class="home-product-item">
                <div class="home-product-card">
                    <a href="{{route('product.show.rewrite', ['id'=>$hotRow->id])}}" class="home-product-thumb product-hover-effect">
                        <img alt="{{$hotRow->title}}"
                             src="{{$hotRow->img_cover}}"
                             class="img-responsive product-main-img"
                             @if(isset($hotRow->asset_img_photos) && count($hotRow->asset_img_photos) > 1)
                             data-hover-src="{{$hotRow->asset_img_photos[count($hotRow->asset_img_photos) - 1]['src']}}"
                             @endif
                             />
                        @if(isset($hotRow->asset_img_photos) && count($hotRow->asset_img_photos) > 1)
                        <img alt="{{$hotRow->title}}"
                             src="{{$hotRow->asset_img_photos[count($hotRow->asset_img_photos) - 1]['src']}}"
                             class="img-responsive product-hover-img"
                             style="position: absolute; top: 0; left: 0; opacity: 0; transition: opacity 0.3s ease;" />
                        @endif
                    </a>
                    <div class="home-product-caption">
                        <h5 title="{{$hotRow->title}}" class="home-product-title"><a href="{{route('product.show.rewrite', ['id'=>$hotRow->id])}}">{{$hotRow->title}}</a></h5>
                        <p class="home-product-price st-home-product-price">@price($hotRow->sale_price)</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 高质量产品图片悬停效果
    const productHoverEffects = document.querySelectorAll('.product-hover-effect');

    productHoverEffects.forEach(function(element) {
        const mainImg = element.querySelector('.product-main-img');
        const hoverImg = element.querySelector('.product-hover-img');

        if (mainImg && hoverImg) {
            // 预加载悬停图片
            const preloadImg = new Image();
            preloadImg.src = hoverImg.src;

            element.addEventListener('mouseenter', function() {
                // 现代电商风格的图片切换
                mainImg.style.transform = 'scale(1.1)';
                mainImg.style.opacity = '0';
                hoverImg.style.opacity = '1';
                hoverImg.style.transform = 'scale(1)';
            });

            element.addEventListener('mouseleave', function() {
                // 恢复原始状态
                mainImg.style.transform = 'scale(1)';
                mainImg.style.opacity = '1';
                hoverImg.style.opacity = '0';
                hoverImg.style.transform = 'scale(1.1)';
            });
        }
    });

    // 添加产品卡片的进入动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // 观察所有产品卡片
    document.querySelectorAll('.home-product-card').forEach(function(card) {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
@endsection
