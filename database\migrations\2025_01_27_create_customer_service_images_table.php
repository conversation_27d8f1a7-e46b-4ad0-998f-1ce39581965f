<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_service_images', function (Blueprint $table) {
            $table->id();
            $table->string('session_id', 100)->index()->comment('客服会话ID');
            $table->string('original_name')->comment('原始文件名');
            $table->string('file_name')->comment('存储文件名');
            $table->string('file_path')->comment('文件路径');
            $table->integer('file_size')->comment('文件大小(字节)');
            $table->string('mime_type', 50)->comment('MIME类型');
            $table->integer('width')->default(0)->comment('图片宽度');
            $table->integer('height')->default(0)->comment('图片高度');
            $table->timestamp('uploaded_at')->comment('上传时间');
            $table->timestamps();
            
            $table->index(['session_id', 'uploaded_at']);
            $table->index('uploaded_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_service_images');
    }
};
