<div class="container-fluid" style="padding: 0; margin: 0; max-width: 1400px; margin: 0 auto;">
    <div class="row" style="margin: 0; min-height: 100vh;">
        <!-- 左侧图片区域 - 固定 -->
        <div class="col-md-5" style="padding: 40px 20px 20px 30px; background: #fff; position: sticky; top: 0; height: 100vh; overflow: hidden;">
            <div style="height: 100%; display: flex; flex-direction: column;">
                <!-- 主图显示 -->
                <div style="margin-bottom: 30px; text-align: center; background: #fafafa; padding: 20px; width: 100%; display: flex; align-items: center; justify-content: center; min-height: 420px;">
                    <?php if($row->mp4): ?>
                    <video
                        id="mainVideo"
                        muted loop controls playsinline
                        poster="<?php echo e($row->video_thumbnail ?? $row->asset_img_photos[0]['src']); ?>"
                        style="max-width: 100%; max-height: 420px; width: auto; height: auto; object-fit: contain; display: block;">
                        <source src="<?php echo e($row->mp4); ?>" type="video/mp4">
                    </video>
                    <?php else: ?>
                    <img id="mainImage" src="<?php echo e($row->asset_img_photos[0]['src']); ?>" alt="<?php echo e($row->title); ?>"
                         style="max-width: 100%; max-height: 420px; width: auto; height: auto; object-fit: contain; display: block;">
                    <?php endif; ?>
                </div>
                
                <!-- 大气缩略图 -->
                <div style="display: flex; gap: 20px; justify-content: flex-start; flex-wrap: wrap; width: 100%; padding: 0 10px;">
                    <?php if($row->mp4): ?>
                    <div class="thumb-simple video-thumb" onclick="showMainVideo()"
                         style="width: 100px; height: 100px; border: 2px solid #333; cursor: pointer; position: relative; overflow: hidden; transition: all 0.3s ease;">
                        <video muted loop playsinline
                            poster="<?php echo e($row->video_thumbnail ?? $row->asset_img_photos[0]['src']); ?>"
                            style="width: 100%; height: 100%; object-fit: cover;">
                            <source src="<?php echo e($row->mp4); ?>" type="video/mp4">
                        </video>
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 16px; font-weight: bold;">▶</div>
                    </div>
                    <?php endif; ?>
                    <?php $__currentLoopData = $row->asset_img_photos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="thumb-simple <?php if(!$row->mp4 && $loop->first): ?>active <?php endif; ?>" onclick="showMainImage('<?php echo e($photo['src']); ?>')"
                         style="width: 100px; height: 100px; border: <?php if(!$row->mp4 && $loop->first): ?>2px solid #333 <?php else: ?> 1px solid #ddd <?php endif; ?>; cursor: pointer; overflow: hidden; transition: all 0.3s ease;"
                         onmouseover="if(!this.classList.contains('active')) { this.style.borderColor='#333'; this.style.borderWidth='2px'; }"
                         onmouseout="if(!this.classList.contains('active')) { this.style.borderColor='#ddd'; this.style.borderWidth='1px'; }">
                        <img src="<?php echo e($photo['src']); ?>" alt="<?php echo e($row->title); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- 右侧信息区域 - 可滚动 -->
        <div class="col-md-7" style="padding: 60px 80px 60px 30px; background: #fff;">
            <div style="max-width: 500px;">
                <!-- 品牌 -->
                <div style="margin-bottom: 20px;">
                    <span style="font-size: 12px; color: #999; text-transform: uppercase; letter-spacing: 2px; font-weight: 400;"><?php echo e($row->brand ?? 'BRAND'); ?></span>
                </div>
                
                <!-- 产品标题 -->
                <h1 style="font-size: 36px; font-weight: 300; color: #000; margin: 0 0 30px 0; line-height: 1.2; font-family: 'Times New Roman', Times, serif;"><?php echo e($row->title); ?></h1>
                
                <!-- 价格 -->
                <div style="margin-bottom: 40px;">
                    <span style="font-size: 28px; font-weight: 400; color: #000; font-family: 'Times New Roman', Times, serif;"><?php echo e($_current_currency_name); ?> <?php echo e($row->sale_price); ?></span>
                    <?php if($row->original_price != $row->sale_price): ?>
                    <span style="font-size: 18px; color: #ccc; text-decoration: line-through; margin-left: 15px;"><?php echo e($_current_currency_name); ?> <?php echo e($row->original_price); ?></span>
                    <?php endif; ?>
                </div>
                
                <!-- 产品编号 -->
                <div style="margin-bottom: 40px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
                    <span style="font-size: 12px; color: #999;"><?php echo app('translator')->get('Item code #'); ?>: <?php echo e($row->sku); ?></span>
                </div>

                <!-- 规格选择 -->
                <?php if(!empty($price_sepcs)): ?>
                <?php $__currentLoopData = $price_sepcs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $price_sepc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div style="margin-bottom: 40px;">
                    <div style="font-size: 14px; color: #000; margin-bottom: 15px; font-weight: 400; font-family: 'Times New Roman', Times, serif;"><?php echo e($price_sepc['name']); ?></div>
                    <div style="display: flex; flex-wrap: wrap; gap: 12px;">
                        <?php $__currentLoopData = $price_sepc['spu_specs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spu_spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($price_sepc['is_show_img'] ==1): ?>
                        <!-- 图片规格 -->
                        <div class="spec-simple st-attr-option" data-spec="<?php echo e($spu_spec['pivot']['spec_value']); ?>" 
                             style="width: 50px; height: 50px; border: <?php if($price_sepc['pivot']['spec_value'] == $spu_spec['pivot']['spec_value']): ?> 2px solid #000 <?php else: ?> 1px solid #ddd <?php endif; ?>; cursor: pointer; overflow: hidden;"
                             title="<?php echo e($spu_spec['pivot']['spec_value']); ?>">
                            <img src="<?php echo e($spu_spec['product_img']); ?>" style="width: 100%; height: 100%; object-fit: cover;" />
                        </div>
                        <?php else: ?>
                        <!-- 文字规格 -->
                        <button type="button" class="spec-simple st-attr-option" data-spec="<?php echo e($spu_spec['pivot']['spec_value']); ?>" 
                                style="padding: 10px 16px; border: <?php if($price_sepc['pivot']['spec_value'] == $spu_spec['pivot']['spec_value']): ?> 2px solid #000; background: #000; color: #fff <?php else: ?> 1px solid #ddd; background: #fff; color: #000 <?php endif; ?>; font-size: 14px; cursor: pointer; font-family: 'Times New Roman', Times, serif; min-width: 50px;"
                                title="<?php echo e($spu_spec['pivot']['spec_value']); ?>">
                            <?php echo e($spu_spec['pivot']['spec_value']); ?>

                        </button>
                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>

                <!-- 数量选择 -->
                <div style="margin-bottom: 40px;">
                    <div style="font-size: 14px; color: #000; margin-bottom: 15px; font-weight: 400; font-family: 'Times New Roman', Times, serif;"><?php echo app('translator')->get('QTY'); ?></div>
                    <div style="display: flex; border: 1px solid #ddd; width: 120px;">
                        <button type="button" onclick="var qty = $('#ST-QTY'); var val = parseInt(qty.val()); if(val > 1) qty.val(val - 1);" 
                                style="width: 40px; height: 40px; border: none; background: #fff; color: #000; font-size: 16px; cursor: pointer;">−</button>
                        <input id="ST-QTY" name="qty" value="1" type="number" min="1" max="99999" 
                               style="width: 40px; height: 40px; border: none; text-align: center; font-size: 14px; font-family: 'Times New Roman', Times, serif; background: #fff;" />
                        <button type="button" onclick="var qty = $('#ST-QTY'); var val = parseInt(qty.val()); qty.val(val + 1);" 
                                style="width: 40px; height: 40px; border: none; background: #fff; color: #000; font-size: 16px; cursor: pointer;">+</button>
                    </div>
                </div>
                
                <!-- 购买按钮 -->
                <div style="display: flex; flex-direction: column; gap: 20px; margin-bottom: 60px;">
                    <button onclick="Util.addtocart(<?php echo e($row->id ?? 0); ?>, $('#ST-QTY').val())"
                            style="width: 100%; padding: 18px 0; background: #000; color: #fff; border: none; font-size: 14px; font-weight: 400; cursor: pointer; font-family: 'Times New Roman', Times, serif; text-transform: uppercase; letter-spacing: 1px;"
                            onmouseover="this.style.background='#333';"
                            onmouseout="this.style.background='#000';">
                        <?php echo app('translator')->get('ADD TO CART'); ?>
                    </button>
                    
                    <button onclick="Util.buyNow(<?php echo e($row->id ?? 0); ?>, $('#ST-QTY').val())"
                            style="width: 100%; padding: 18px 0; background: #fff; color: #000; border: 1px solid #000; font-size: 14px; font-weight: 400; cursor: pointer; font-family: 'Times New Roman', Times, serif; text-transform: uppercase; letter-spacing: 1px;"
                            onmouseover="this.style.background='#000'; this.style.color='#fff';"
                            onmouseout="this.style.background='#fff'; this.style.color='#000';">
                        <?php echo app('translator')->get('Buy Now'); ?>
                    </button>
                    
                    <button onclick="Util.addToWishList(<?php echo e($row->id ?? 0); ?>)"
                            style="width: 100%; padding: 12px 0; background: transparent; color: #999; border: none; font-size: 12px; cursor: pointer; font-family: 'Times New Roman', Times, serif; text-decoration: underline;"
                            onmouseover="this.style.color='#000';"
                            onmouseout="this.style.color='#999';">
                        <?php echo app('translator')->get('Add to wish list'); ?>
                    </button>

                <!-- 产品详情 -->
                <div style="border-top: 1px solid #eee; padding-top: 40px; margin-top: 40px;">
                    <h3 style="font-size: 18px; font-weight: 400; color: #000; margin-bottom: 20px; font-family: 'Times New Roman', Times, serif;"><?php echo app('translator')->get('Details'); ?></h3>

                    <?php if(!empty($general_sepcs)): ?>
                    <div style="margin-bottom: 30px;">
                        <?php $__currentLoopData = $general_sepcs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $general_sepc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div style="display: flex; padding: 8px 0; border-bottom: 1px solid #f5f5f5;">
                            <div style="width: 120px; font-size: 12px; color: #999; font-family: 'Times New Roman', Times, serif;"><?php echo e($general_sepc['name']); ?></div>
                            <div style="font-size: 12px; color: #000; font-family: 'Times New Roman', Times, serif;"><?php echo e($general_sepc['pivot']['spec_value']); ?></div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>

                    <?php if($row->details): ?>
                    <div style="font-size: 14px; line-height: 1.6; color: #666; font-family: 'Times New Roman', Times, serif;">
                        <?php echo $row->details; ?>

                    </div>
                    <?php endif; ?>
                </div>

                <!-- 配送信息 -->
                <div style="border-top: 1px solid #eee; padding-top: 40px; margin-top: 40px;">
                    <h3 style="font-size: 18px; font-weight: 400; color: #000; margin-bottom: 20px; font-family: 'Times New Roman', Times, serif;"><?php echo app('translator')->get('Shipping & Returns'); ?></h3>
                    <div style="font-size: 12px; line-height: 1.8; color: #999; font-family: 'Times New Roman', Times, serif;">
                        <p>• Free shipping on orders over $300</p>
                        <p>• Standard shipping: 5-8 business days</p>
                        <p>• Returns accepted within 30 days</p>
                        <p>• Original packaging required for returns</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 简约产品详情页样式 */
.thumb-simple {
    transition: all 0.3s ease !important;
}

.thumb-simple:hover {
    border-color: #000 !important;
    border-width: 2px !important;
    transform: scale(1.05) !important;
}

.thumb-simple.active {
    border-color: #000 !important;
    border-width: 2px !important;
}

.spec-simple:hover {
    border-color: #000 !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .col-md-5, .col-md-7 {
        width: 100% !important;
        position: relative !important;
        height: auto !important;
        padding: 20px !important;
    }

    .container-fluid .row {
        flex-direction: column !important;
    }

    /* 移动端缩略图调整 */
    .thumb-simple {
        width: 80px !important;
        height: 80px !important;
    }
}
</style>

<script>
// 简约产品详情页JavaScript
function showMainImage(src) {
    const mainImage = document.getElementById('mainImage');
    const mainVideo = document.getElementById('mainVideo');

    // 隐藏视频，显示图片
    if (mainVideo) {
        mainVideo.style.display = 'none';
        mainVideo.pause();
    }

    if (!mainImage) {
        // 创建主图元素
        const container = document.querySelector('[style*="background: #fafafa"]');
        const newImg = document.createElement('img');
        newImg.id = 'mainImage';
        newImg.alt = 'Product Image';
        newImg.style.cssText = 'max-width: 100%; max-height: 420px; width: auto; height: auto; object-fit: contain; display: block;';
        container.appendChild(newImg);
    }

    const imgElement = document.getElementById('mainImage');
    imgElement.src = src;
    imgElement.style.display = 'block';

    // 更新缩略图状态
    document.querySelectorAll('.thumb-simple').forEach(item => {
        item.style.borderColor = '#ddd';
        item.style.borderWidth = '1px';
    });
    event.target.closest('.thumb-simple').style.borderColor = '#333';
    event.target.closest('.thumb-simple').style.borderWidth = '2px';
}

function showMainVideo() {
    const mainImage = document.getElementById('mainImage');
    const mainVideo = document.getElementById('mainVideo');

    // 隐藏图片，显示视频
    if (mainImage) {
        mainImage.style.display = 'none';
    }

    if (mainVideo) {
        mainVideo.style.display = 'block';
        mainVideo.style.cssText = 'max-width: 100%; max-height: 420px; width: auto; height: auto; object-fit: contain; display: block;';
        mainVideo.play();
    }

    // 更新缩略图状态
    document.querySelectorAll('.thumb-simple').forEach(item => {
        item.style.borderColor = '#ddd';
        item.style.borderWidth = '1px';
    });
    const videoThumb = document.querySelector('.video-thumb');
    if (videoThumb) {
        videoThumb.style.borderColor = '#333';
        videoThumb.style.borderWidth = '2px';
    }
}

// 规格选择
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.st-attr-option').forEach(option => {
        option.addEventListener('click', function() {
            const specGroup = this.closest('[style*="margin-bottom: 40px"]');
            const siblings = specGroup.querySelectorAll('.st-attr-option');

            // 重置同组其他选项
            siblings.forEach(sibling => {
                if (sibling.tagName === 'BUTTON') {
                    sibling.style.borderColor = '#ddd';
                    sibling.style.borderWidth = '1px';
                    sibling.style.background = '#fff';
                    sibling.style.color = '#000';
                } else {
                    sibling.style.borderColor = '#ddd';
                    sibling.style.borderWidth = '1px';
                }
            });

            // 激活当前选项
            if (this.tagName === 'BUTTON') {
                this.style.borderColor = '#000';
                this.style.borderWidth = '2px';
                this.style.background = '#000';
                this.style.color = '#fff';
            } else {
                this.style.borderColor = '#000';
                this.style.borderWidth = '2px';
            }
        });
    });
});
</script>
<?php /**PATH D:\wwwroot\mostxx.com_xxx\resources\views\themes\default/layouts/includes/productShow.blade.php ENDPATH**/ ?>