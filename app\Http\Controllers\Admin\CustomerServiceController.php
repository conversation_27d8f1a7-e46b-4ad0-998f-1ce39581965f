<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CustomerService\CustomerServiceSession;
use App\Models\CustomerService\CustomerServiceMessage;
use App\Models\CustomerService\AiAutoReplyRule;

class CustomerServiceController extends Controller
{
    // 客服会话列表
    public function sessions(Request $request)
    {
        $query = CustomerServiceSession::with(['latestMessage', 'assignedAdmin'])
                                      ->orderBy('last_activity', 'desc');

        if ($request->status) {
            $query->where('status', $request->status);
        }

        $sessions = $query->paginate(20);

        return view('admin.customer-service.sessions', compact('sessions'));
    }

    // 查看具体会话
    public function showSession($id)
    {
        $session = CustomerServiceSession::with(['messages.senderUser', 'messages.senderAdmin', 'user', 'assignedAdmin'])
                                         ->findOrFail($id);

        return view('admin.customer-service.session-detail', compact('session'));
    }

    // 发送管理员消息
    public function sendAdminMessage(Request $request, $sessionId)
    {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $session = CustomerServiceSession::findOrFail($sessionId);

        $message = CustomerServiceMessage::create([
            'session_id' => $session->id,
            'sender_type' => 'admin',
            'sender_id' => auth()->id(),
            'message' => $request->message
        ]);

        // 分配客服
        if (!$session->assigned_admin_id) {
            $session->update(['assigned_admin_id' => auth()->id()]);
        }

        $session->updateLastActivity();

        return response()->json([
            'success' => true,
            'message' => $message->load('senderAdmin')
        ]);
    }

    // AI规则管理
    public function aiRules(Request $request)
    {
        $rules = AiAutoReplyRule::orderBy('priority', 'desc')
                               ->orderBy('created_at', 'desc')
                               ->paginate(20);

        return view('admin.customer-service.ai-rules', compact('rules'));
    }

    // 创建AI规则
    public function createAiRule()
    {
        return view('admin.customer-service.ai-rule-form');
    }

    // 保存AI规则
    public function storeAiRule(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:100',
            'reply_message' => 'required|string|max:1000',
            'priority' => 'required|integer|min:0|max:100'
        ]);

        AiAutoReplyRule::create([
            'name' => $request->name,
            'keywords' => array_filter($request->keywords),
            'reply_message' => $request->reply_message,
            'priority' => $request->priority,
            'is_active' => $request->has('is_active')
        ]);

        return redirect()->route('admin.customer-service.ai-rules')
                        ->with('success', 'AI rule created successfully!');
    }

    // 编辑AI规则
    public function editAiRule($id)
    {
        $rule = AiAutoReplyRule::findOrFail($id);
        return view('admin.customer-service.ai-rule-form', compact('rule'));
    }

    // 更新AI规则
    public function updateAiRule(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:100',
            'reply_message' => 'required|string|max:1000',
            'priority' => 'required|integer|min:0|max:100'
        ]);

        $rule = AiAutoReplyRule::findOrFail($id);
        $rule->update([
            'name' => $request->name,
            'keywords' => array_filter($request->keywords),
            'reply_message' => $request->reply_message,
            'priority' => $request->priority,
            'is_active' => $request->has('is_active')
        ]);

        return redirect()->route('admin.customer-service.ai-rules')
                        ->with('success', 'AI rule updated successfully!');
    }

    // 删除AI规则
    public function deleteAiRule($id)
    {
        $rule = AiAutoReplyRule::findOrFail($id);
        $rule->delete();

        return redirect()->route('admin.customer-service.ai-rules')
                        ->with('success', 'AI rule deleted successfully!');
    }

    // 切换AI规则状态
    public function toggleAiRule($id)
    {
        $rule = AiAutoReplyRule::findOrFail($id);
        $rule->update(['is_active' => !$rule->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $rule->is_active
        ]);
    }

    // 客服统计
    public function statistics()
    {
        $stats = [
            'total_sessions' => CustomerServiceSession::count(),
            'active_sessions' => CustomerServiceSession::where('status', 'active')->count(),
            'total_messages' => CustomerServiceMessage::count(),
            'ai_messages' => CustomerServiceMessage::where('sender_type', 'ai')->count(),
            'avg_response_time' => '2 minutes', // 这里可以计算实际响应时间
            'satisfaction_rate' => '95%' // 这里可以添加满意度调查
        ];

        $recentSessions = CustomerServiceSession::with(['latestMessage', 'user'])
                                               ->orderBy('last_activity', 'desc')
                                               ->limit(10)
                                               ->get();

        return view('admin.customer-service.statistics', compact('stats', 'recentSessions'));
    }
}
