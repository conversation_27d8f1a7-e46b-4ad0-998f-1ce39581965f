@extends('strongadmin::layouts.app')

@push('styles')
<style>
.config-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e6e6e6;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-online { background: #52c41a; }
.status-offline { background: #ff4d4f; }

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #52c41a;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.config-item {
    margin-bottom: 20px;
}

.config-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.config-description {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.stat-number {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}
</style>
@endpush

@section('content')
<div class="st-h15"></div>

<div class="config-card">
    <div class="config-header">
        <div>
            <h2 style="margin: 0; color: #333;">
                <i class="layui-icon layui-icon-set"></i> 客服系统配置
            </h2>
            <p style="margin: 5px 0 0 0; color: #666;">管理在线客服系统的全局设置</p>
        </div>
        
        <div class="system-status">
            <span class="status-indicator" id="status-indicator"></span>
            <span id="status-text">检查中...</span>
            <label class="toggle-switch">
                <input type="checkbox" id="system-toggle" onchange="toggleSystem()">
                <span class="slider"></span>
            </label>
        </div>
    </div>

    <!-- 系统统计 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-sessions">-</div>
            <div class="stat-label">总会话数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="active-sessions">-</div>
            <div class="stat-label">活跃会话</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="total-messages">-</div>
            <div class="stat-label">总消息数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="ai-messages">-</div>
            <div class="stat-label">AI回复数</div>
        </div>
    </div>
</div>

<div class="config-card">
    <h3><i class="layui-icon layui-icon-set-sm"></i> 系统设置</h3>
    
    <form method="POST" class="layui-form">
        @csrf
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="config-item">
                    <label class="config-label">AI自动回复</label>
                    <input type="checkbox" name="configs[auto_reply_enabled]" value="1" 
                           {{ ($configs['auto_reply_enabled'] ?? '1') == '1' ? 'checked' : '' }}
                           lay-skin="switch" lay-text="开启|关闭">
                    <div class="config-description">启用后，系统会自动回复客户消息</div>
                </div>
            </div>
            
            <div class="layui-col-md6">
                <div class="config-item">
                    <label class="config-label">会话超时时间（分钟）</label>
                    <input type="number" name="configs[session_timeout_minutes]" 
                           value="{{ $configs['session_timeout_minutes'] ?? '30' }}"
                           class="layui-input" min="5" max="120">
                    <div class="config-description">超过此时间无活动的会话将被标记为超时</div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="config-item">
                    <label class="config-label">每IP最大会话数</label>
                    <input type="number" name="configs[max_sessions_per_ip]" 
                           value="{{ $configs['max_sessions_per_ip'] ?? '5' }}"
                           class="layui-input" min="1" max="20">
                    <div class="config-description">限制单个IP地址同时进行的会话数量</div>
                </div>
            </div>
        </div>
        
        <div class="config-item">
            <label class="config-label">欢迎消息</label>
            <textarea name="configs[welcome_message]" class="layui-textarea" rows="3">{{ $configs['welcome_message'] ?? '您好！欢迎咨询，我是智能客服助手，有什么可以帮助您的吗？' }}</textarea>
            <div class="config-description">客户首次进入聊天时显示的欢迎消息</div>
        </div>
        
        <div class="config-item">
            <label class="config-label">离线提示消息</label>
            <textarea name="configs[offline_message]" class="layui-textarea" rows="3">{{ $configs['offline_message'] ?? '客服系统暂时离线，请稍后再试或留言给我们。' }}</textarea>
            <div class="config-description">系统关闭时向客户显示的提示消息</div>
        </div>
        
        <div style="margin-top: 30px;">
            <button type="submit" class="layui-btn layui-btn-normal">
                <i class="layui-icon layui-icon-ok"></i> 保存配置
            </button>
            <a href="../sessions" class="layui-btn layui-btn-primary">
                <i class="layui-icon layui-icon-return"></i> 返回会话管理
            </a>
        </div>
    </form>
</div>

@push('scripts')
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    // 初始化
    checkSystemStatus();
    loadStats();
    
    // 检查系统状态
    function checkSystemStatus() {
        fetch('/strongadmin/customer-service/system/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatusDisplay(data.enabled);
                }
            })
            .catch(error => {
                console.error('Error checking status:', error);
            });
    }
    
    // 更新状态显示
    function updateStatusDisplay(enabled) {
        const indicator = document.getElementById('status-indicator');
        const text = document.getElementById('status-text');
        const toggle = document.getElementById('system-toggle');
        
        if (enabled) {
            indicator.className = 'status-indicator status-online';
            text.textContent = '系统在线';
            toggle.checked = true;
        } else {
            indicator.className = 'status-indicator status-offline';
            text.textContent = '系统离线';
            toggle.checked = false;
        }
    }
    
    // 切换系统状态
    window.toggleSystem = function() {
        const toggle = document.getElementById('system-toggle');
        const enabled = toggle.checked;
        
        fetch('/strongadmin/customer-service/system/toggle', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ enabled: enabled })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatusDisplay(data.enabled);
                layer.msg(data.message, { icon: 1 });
            } else {
                // 恢复开关状态
                toggle.checked = !enabled;
                layer.msg(data.error || '操作失败', { icon: 2 });
            }
        })
        .catch(error => {
            // 恢复开关状态
            toggle.checked = !enabled;
            layer.msg('网络错误，请重试', { icon: 2 });
        });
    };
    
    // 加载统计数据
    function loadStats() {
        fetch('/strongadmin/customer-service/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('total-sessions').textContent = data.stats.total_sessions || 0;
                    document.getElementById('active-sessions').textContent = data.stats.active_sessions || 0;
                    document.getElementById('total-messages').textContent = data.stats.total_messages || 0;
                    document.getElementById('ai-messages').textContent = data.stats.ai_messages || 0;
                }
            })
            .catch(error => {
                console.error('Error loading stats:', error);
            });
    }
});
</script>
@endpush

@endsection
