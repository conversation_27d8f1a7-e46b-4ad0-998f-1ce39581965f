<?php
/**
 * 自动添加admin_read字段到数据库
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>自动添加admin_read字段</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
.warning{color:#856404;background:#fff3cd;padding:10px;border-radius:4px;margin:10px 0;}
pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow:auto;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 自动添加admin_read字段</h1>";

try {
    echo "<div class='info'>📋 开始检查和添加字段...</div>";
    
    // 先检查正确的表名
    $tables = DB::select("SHOW TABLES LIKE '%customer_service_sessions%'");
    $tableName = '';

    foreach ($tables as $table) {
        $tableArray = (array) $table;
        $tableNameValue = array_values($tableArray)[0];
        if (strpos($tableNameValue, 'customer_service_sessions') !== false) {
            $tableName = $tableNameValue;
            break;
        }
    }

    if (empty($tableName)) {
        echo "<div class='error'>❌ 找不到customer_service_sessions表</div>";
        echo "<div class='info'>现有表：</div>";
        $allTables = DB::select("SHOW TABLES");
        echo "<pre>";
        foreach ($allTables as $table) {
            $tableArray = (array) $table;
            echo array_values($tableArray)[0] . "\n";
        }
        echo "</pre>";
        exit;
    }

    echo "<div class='info'>📋 找到表名: {$tableName}</div>";

    // 使用正确的表名检查字段
    $adminReadExists = DB::select("SHOW COLUMNS FROM {$tableName} LIKE 'admin_read'");

    if (empty($adminReadExists)) {
        echo "<div class='warning'>⚠️ admin_read字段不存在，正在添加...</div>";

        // 添加admin_read字段
        DB::statement("
            ALTER TABLE {$tableName}
            ADD COLUMN admin_read tinyint(1) NOT NULL DEFAULT 0
            COMMENT '管理员是否已读该会话：0=未读，1=已读'
        ");

        echo "<div class='success'>✅ 成功添加admin_read字段</div>";
    } else {
        echo "<div class='info'>ℹ️ admin_read字段已存在</div>";
    }

    // 检查admin_read_at字段是否存在
    $adminReadAtExists = DB::select("SHOW COLUMNS FROM {$tableName} LIKE 'admin_read_at'");

    if (empty($adminReadAtExists)) {
        echo "<div class='warning'>⚠️ admin_read_at字段不存在，正在添加...</div>";

        // 添加admin_read_at字段
        DB::statement("
            ALTER TABLE {$tableName}
            ADD COLUMN admin_read_at timestamp NULL DEFAULT NULL
            COMMENT '管理员阅读该会话的时间'
        ");

        echo "<div class='success'>✅ 成功添加admin_read_at字段</div>";
    } else {
        echo "<div class='info'>ℹ️ admin_read_at字段已存在</div>";
    }
    
    echo "<div class='info'>🔄 初始化现有会话的已读状态...</div>";

    // 将所有现有会话设为未读
    $updateCount = DB::statement("UPDATE {$tableName} SET admin_read = 0, admin_read_at = NULL");

    echo "<div class='success'>✅ 已初始化会话的已读状态</div>";

    echo "<div class='info'>📊 查看当前表结构...</div>";

    // 显示表结构
    $columns = DB::select("DESCRIBE {$tableName}");
    echo "<pre>";
    echo "字段名\t\t类型\t\t\t空值\t键\t默认值\t\t备注\n";
    echo str_repeat("-", 80) . "\n";
    foreach ($columns as $column) {
        printf("%-15s %-20s %-8s %-8s %-15s %s\n", 
            $column->Field, 
            $column->Type, 
            $column->Null, 
            $column->Key, 
            $column->Default ?? 'NULL',
            $column->Extra
        );
    }
    echo "</pre>";
    
    echo "<div class='success'>🎉 所有操作完成！</div>";
    
    echo "<h2>📋 测试步骤</h2>";
    echo "<ol>";
    echo "<li>前台发送一条消息，后台应该显示角标</li>";
    echo "<li>点击该会话查看，角标应该消失</li>";
    echo "<li>刷新页面，角标应该保持消失状态</li>";
    echo "<li>前台再发送消息，角标应该重新出现</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
    echo "<div class='error'>错误详情: " . $e->getTraceAsString() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
