<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

// 获取设置数据
$settings = DB::table('customer_service_settings')
             ->where('is_active', 1)
             ->orderBy('category')
             ->orderBy('sort_order')
             ->get()
             ->groupBy('category');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统设置中心 - 测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f4f4f4; }
        .main-header { background: #007bff; color: white; padding: 1rem 0; margin-bottom: 2rem; }
        .card { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .nav-tabs .nav-link.active { background: #007bff; color: white; border-color: #007bff; }
        .form-group label { font-weight: 600; }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <h1><i class="fa fa-cog"></i> 客服系统设置中心</h1>
            <p class="mb-0">完整的客服系统配置管理</p>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-sliders-h"></i> 系统配置管理
                </h3>
            </div>
            
            <div class="card-body">
                <form id="settings-form">
                    <!-- 设置分类标签页 -->
                    <ul class="nav nav-tabs mb-3" id="settings-tabs" role="tablist">
                        <?php
                        $categoryNames = [
                            'basic' => '基础功能',
                            'sound' => '声音提醒', 
                            'appearance' => '外观样式',
                            'offline' => '离线留言',
                            'advanced' => '高级选项'
                        ];
                        $categoryIcons = [
                            'basic' => 'fa fa-cogs',
                            'sound' => 'fa fa-volume-up',
                            'appearance' => 'fa fa-palette',
                            'offline' => 'fa fa-envelope',
                            'advanced' => 'fa fa-sliders-h'
                        ];
                        $first = true;
                        ?>
                        
                        <?php foreach($settings as $category => $categorySettings): ?>
                            <li class="nav-item">
                                <a class="nav-link <?= $first ? 'active' : '' ?>" 
                                   id="<?= $category ?>-tab" 
                                   data-toggle="tab" 
                                   href="#<?= $category ?>" 
                                   role="tab">
                                    <i class="<?= $categoryIcons[$category] ?? 'fa fa-cog' ?>"></i>
                                    <?= $categoryNames[$category] ?? ucfirst($category) ?>
                                </a>
                            </li>
                            <?php $first = false; ?>
                        <?php endforeach; ?>
                    </ul>
                    
                    <!-- 设置内容 -->
                    <div class="tab-content" id="settings-content">
                        <?php $first = true; ?>
                        <?php foreach($settings as $category => $categorySettings): ?>
                            <div class="tab-pane fade <?= $first ? 'show active' : '' ?>" 
                                 id="<?= $category ?>" 
                                 role="tabpanel">
                                
                                <div class="row">
                                    <?php foreach($categorySettings as $setting): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="<?= $setting->setting_key ?>" class="font-weight-bold">
                                                    <?= $setting->title ?>
                                                    <?php if($setting->description): ?>
                                                        <small class="text-muted d-block"><?= $setting->description ?></small>
                                                    <?php endif; ?>
                                                </label>
                                                
                                                <?php if($setting->setting_type === 'boolean'): ?>
                                                    <div class="form-check">
                                                        <input type="checkbox" 
                                                               class="form-check-input" 
                                                               id="<?= $setting->setting_key ?>"
                                                               name="settings[<?= $setting->setting_key ?>]"
                                                               value="1"
                                                               <?= $setting->setting_value == '1' ? 'checked' : '' ?>>
                                                        <label class="form-check-label" for="<?= $setting->setting_key ?>">
                                                            <?= $setting->setting_value == '1' ? '已启用' : '已禁用' ?>
                                                        </label>
                                                        <input type="hidden" name="settings[<?= $setting->setting_key ?>]" value="0">
                                                    </div>
                                                    
                                                <?php elseif($setting->setting_type === 'number'): ?>
                                                    <input type="number" 
                                                           class="form-control" 
                                                           id="<?= $setting->setting_key ?>"
                                                           name="settings[<?= $setting->setting_key ?>]"
                                                           value="<?= $setting->setting_value ?>"
                                                           step="0.1"
                                                           min="0">
                                                           
                                                <?php elseif($setting->setting_type === 'color'): ?>
                                                    <div class="input-group">
                                                        <input type="color" 
                                                               class="form-control" 
                                                               id="<?= $setting->setting_key ?>"
                                                               name="settings[<?= $setting->setting_key ?>]"
                                                               value="<?= $setting->setting_value ?>"
                                                               style="width: 60px;">
                                                        <input type="text" 
                                                               class="form-control" 
                                                               value="<?= $setting->setting_value ?>"
                                                               readonly>
                                                    </div>
                                                    
                                                <?php elseif($setting->setting_type === 'select'): ?>
                                                    <select class="form-control" 
                                                            id="<?= $setting->setting_key ?>"
                                                            name="settings[<?= $setting->setting_key ?>]">
                                                        <?php if($setting->options): ?>
                                                            <?php foreach(explode(',', $setting->options) as $option): ?>
                                                                <?php list($value, $label) = explode(':', $option); ?>
                                                                <option value="<?= $value ?>" 
                                                                        <?= $setting->setting_value == $value ? 'selected' : '' ?>>
                                                                    <?= $label ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        <?php endif; ?>
                                                    </select>
                                                    
                                                <?php elseif($setting->setting_type === 'file'): ?>
                                                    <input type="file" 
                                                           class="form-control-file" 
                                                           id="<?= $setting->setting_key ?>"
                                                           name="settings[<?= $setting->setting_key ?>]"
                                                           accept="<?= strpos($setting->setting_key, 'sound') !== false ? '.mp3,.wav,.ogg' : '.jpg,.jpeg,.png,.gif' ?>">
                                                    <?php if($setting->setting_value): ?>
                                                        <small class="text-muted d-block mt-1">
                                                            当前文件: <?= basename($setting->setting_value) ?>
                                                            <?php if(strpos($setting->setting_key, 'sound') !== false): ?>
                                                                <button type="button" 
                                                                        class="btn btn-sm btn-link p-0 ml-2"
                                                                        onclick="playSound('/storage/<?= $setting->setting_value ?>')">
                                                                    试听
                                                                </button>
                                                            <?php endif; ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    
                                                <?php elseif($setting->setting_type === 'json'): ?>
                                                    <textarea class="form-control" 
                                                              id="<?= $setting->setting_key ?>"
                                                              name="settings[<?= $setting->setting_key ?>]"
                                                              rows="3"
                                                              placeholder='例: ["email","phone","message"]'><?= $setting->setting_value ?></textarea>
                                                              
                                                <?php else: ?>
                                                    <input type="text" 
                                                           class="form-control" 
                                                           id="<?= $setting->setting_key ?>"
                                                           name="settings[<?= $setting->setting_key ?>]"
                                                           value="<?= $setting->setting_value ?>"
                                                           placeholder="请输入<?= $setting->title ?>">
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php $first = false; ?>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- 保存按钮 -->
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> 保存设置
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fa fa-undo"></i> 重置
                        </button>
                        <a href="/strongadmin/customer-service/offline-messages" class="btn btn-info ml-2">
                            <i class="fa fa-envelope"></i> 离线留言
                        </a>
                        <a href="/strongadmin/customer-service/sessions" class="btn btn-success ml-2">
                            <i class="fa fa-comments"></i> 会话管理
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 音频播放器 -->
    <audio id="sound-player" style="display: none;"></audio>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 表单提交
            $('#settings-form').on('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                
                // 显示加载状态
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);
                
                $.ajax({
                    url: '/strongadmin/customer-service/settings',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('设置保存成功！');
                        } else {
                            alert('保存失败: ' + (response.message || '未知错误'));
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        alert('保存失败: ' + (response?.message || '网络错误'));
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
            
            // 颜色选择器联动
            $('input[type="color"]').on('change', function() {
                $(this).next('input[type="text"]').val($(this).val());
            });
            
            // 开关状态文字更新
            $('.form-check-input').on('change', function() {
                const label = $(this).siblings('.form-check-label');
                if ($(this).is(':checked')) {
                    label.text('已启用');
                } else {
                    label.text('已禁用');
                }
            });
        });

        // 播放音效
        function playSound(url) {
            const player = document.getElementById('sound-player');
            player.src = url;
            player.play().catch(e => {
                console.error('播放音效失败:', e);
                alert('播放音效失败');
            });
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置所有设置吗？')) {
                location.reload();
            }
        }
    </script>
</body>
</html>
