<?php
// 超级简单的回复工具
if ($_POST['reply']) {
    $sessionId = $_POST['session_id'];
    $message = $_POST['reply'];
    
    try {
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=mostxx_com;charset=utf8mb4', 'mostxx_com', 'fHnrmH9w5nw1pd53');
        
        // 获取会话数据库ID
        $stmt = $pdo->prepare("SELECT id FROM st_customer_service_sessions WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch();
        
        if ($session) {
            // 插入回复
            $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, message, created_at, updated_at) VALUES (?, 'admin', ?, NOW(), NOW())");
            $stmt->execute([$session['id'], $message]);
            echo "回复发送成功！";
        } else {
            echo "会话不存在";
        }
    } catch (Exception $e) {
        echo "错误: " . $e->getMessage();
    }
    exit;
}
?>
<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>简单回复</title></head>
<body>
<h1>超级简单回复工具</h1>
<form method="POST">
    会话ID: <input name="session_id" value="super_simple_test" style="width:200px;"><br><br>
    回复内容: <input name="reply" placeholder="输入回复" style="width:300px;"><br><br>
    <button type="submit">发送回复</button>
</form>
</body>
</html>
