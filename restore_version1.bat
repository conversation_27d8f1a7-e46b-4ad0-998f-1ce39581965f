@echo off
echo ========================================
echo StrongShop 版本恢复工具
echo ========================================
echo.
echo 当前版本: 现代简约风格 (版本2)
echo 备份版本: 原始版本 (版本1)
echo.
echo 警告: 此操作将覆盖当前所有修改！
echo.
set /p choice=确定要恢复到版本1吗？(y/N): 

if /i "%choice%"=="y" (
    echo.
    echo 正在恢复版本1...
    echo.
    
    echo 1. 停止可能的服务...
    taskkill /f /im php.exe 2>nul
    timeout /t 2 /nobreak >nul
    
    echo 2. 备份当前版本为版本2...
    if exist H:\wwwroot\mostxx.com_1111hp7hL_backup_v2 (
        rmdir /s /q H:\wwwroot\mostxx.com_1111hp7hL_backup_v2
    )
    xcopy H:\wwwroot\mostxx.com_1111hp7hL H:\wwwroot\mostxx.com_1111hp7hL_backup_v2 /E /I /H /Y /Q
    
    echo 3. 删除当前版本...
    cd /d H:\wwwroot\
    rmdir /s /q mostxx.com_1111hp7hL
    
    echo 4. 恢复版本1...
    xcopy mostxx.com_1111hp7hL_backup_v1 mostxx.com_1111hp7hL /E /I /H /Y /Q
    
    echo.
    echo ========================================
    echo 恢复完成！
    echo ========================================
    echo.
    echo 版本1已恢复到: H:\wwwroot\mostxx.com_1111hp7hL
    echo 版本2已备份到: H:\wwwroot\mostxx.com_1111hp7hL_backup_v2
    echo.
    echo 请重新启动Web服务器以确保更改生效。
    echo.
) else (
    echo.
    echo 操作已取消。
    echo.
)

pause
