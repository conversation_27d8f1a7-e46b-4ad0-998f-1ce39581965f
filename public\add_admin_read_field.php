<?php
/**
 * 添加admin_read字段到会话表
 */

// 数据库配置
$host = 'localhost';
$port = 3306;
$database = 'strongshop';
$username = 'root';
$password = 'root';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>添加admin_read字段</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 添加admin_read字段</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 检查是否已经有admin_read字段
    $stmt = $pdo->query("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'admin_read'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='info'>ℹ️ admin_read字段已存在，跳过添加</p>";
    } else {
        // 添加admin_read字段
        $sql = "ALTER TABLE st_customer_service_sessions ADD COLUMN admin_read tinyint(1) NOT NULL DEFAULT 0 COMMENT '管理员是否已读：0=未读，1=已读' AFTER unread_count";
        $pdo->exec($sql);
        echo "<p class='success'>✅ 成功添加admin_read字段</p>";
    }
    
    // 检查是否已经有admin_read_at字段
    $stmt = $pdo->query("SHOW COLUMNS FROM st_customer_service_sessions LIKE 'admin_read_at'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='info'>ℹ️ admin_read_at字段已存在，跳过添加</p>";
    } else {
        // 添加admin_read_at字段
        $sql = "ALTER TABLE st_customer_service_sessions ADD COLUMN admin_read_at timestamp NULL DEFAULT NULL COMMENT '管理员阅读时间' AFTER admin_read";
        $pdo->exec($sql);
        echo "<p class='success'>✅ 成功添加admin_read_at字段</p>";
    }
    
    echo "<h2>📊 初始化已读状态</h2>";
    
    // 将所有现有会话标记为未读
    $updateCount = $pdo->exec("UPDATE st_customer_service_sessions SET admin_read = 0, admin_read_at = NULL WHERE admin_read IS NULL OR admin_read = 1");
    echo "<p class='info'>📝 重置了 $updateCount 个会话为未读状态</p>";
    
    echo "<h2>✅ 完成</h2>";
    echo "<p class='success'>admin_read字段添加完成！现在角标将基于会话的admin_read状态显示。</p>";
    
    echo "<h3>🔗 相关链接</h3>";
    echo "<ul>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
    echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 操作失败: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
