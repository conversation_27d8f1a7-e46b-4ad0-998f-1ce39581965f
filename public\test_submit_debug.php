<?php
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🐛 测试提交按钮调试</h1>";

echo "<div style='background: #f8f8f8; padding: 25px; margin: 20px 0;'>";
echo "<h2>📊 当前状态</h2>";

$now = new DateTime();
$currentTime = $now->format('H:i');
$dayOfWeek = $now->format('N');

echo "<p><strong>当前时间:</strong> " . $now->format('Y-m-d H:i:s') . " (星期" . ['', '一', '二', '三', '四', '五', '六', '日'][$dayOfWeek] . ")</p>";

$isWorkingDay = $dayOfWeek >= 1 && $dayOfWeek <= 5;
$isWorkingHours = $currentTime >= '09:00' && $currentTime <= '18:00';

echo "<p><strong>是否工作日:</strong> " . ($isWorkingDay ? '✅ 是' : '❌ 否') . "</p>";
echo "<p><strong>是否工作时间:</strong> " . ($isWorkingHours ? '✅ 是' : '❌ 否') . "</p>";

if (!$isWorkingDay || !$isWorkingHours) {
    echo "<div style='background: #000; color: #fff; padding: 20px; text-align: center; font-weight: 300; letter-spacing: 1px;'>";
    echo "当前是非工作时间，应该显示可调试的离线表单";
    echo "</div>";
} else {
    echo "<div style='background: #fff; color: #000; padding: 20px; text-align: center; font-weight: 300; letter-spacing: 1px;'>";
    echo "当前是工作时间，应该显示聊天界面";
    echo "</div>";
}
echo "</div>";

echo "<div style='background: #fff; padding: 25px; margin: 20px 0;'>";
echo "<h2>🐛 调试修复</h2>";
echo "<ul>";
echo "<li>✅ <strong>按钮选择器修复</strong>：从 querySelector('button[type=\"submit\"]') 改为 getElementById('offline-submit-btn')</li>";
echo "<li>✅ <strong>事件触发调试</strong>：添加 '📝 离线表单提交事件触发' 日志</li>";
echo "<li>✅ <strong>数据收集调试</strong>：添加 '📤 提交离线留言数据' 日志</li>";
echo "<li>✅ <strong>验证过程调试</strong>：添加 '✅ 验证必填字段' 日志</li>";
echo "<li>✅ <strong>字段配置调试</strong>：添加 '📋 表单字段配置' 日志</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f8f8; padding: 25px; margin: 20px 0;'>";
echo "<h2>🧪 详细测试步骤</h2>";
echo "<ol>";
echo "<li><strong>打开首页</strong>：<a href='/' target='_blank' style='background: #000; color: #fff; padding: 12px 24px; text-decoration: none; letter-spacing: 1px;'>点击打开首页</a></li>";
echo "<li><strong>打开开发者工具</strong>：按F12，切换到Console标签页</li>";
echo "<li><strong>点击客服按钮</strong>：右下角的客服按钮</li>";
echo "<li><strong>查看初始化日志</strong>：";
echo "<ul style='margin: 10px 0;'>";
echo "<li>应该看到 '📋 表单字段配置' 日志</li>";
echo "<li>检查 fields 和 required 数组内容</li>";
echo "<li>确认设置是否正确加载</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>填写表单</strong>：";
echo "<ul>";
echo "<li>填写一些字段（不要全部填写）</li>";
echo "<li>故意留空一些必填字段</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>点击提交按钮</strong>：";
echo "<ul>";
echo "<li>应该看到 '🔄 开始提交离线表单' 日志</li>";
echo "<li>应该看到 '📝 离线表单提交事件触发' 日志</li>";
echo "<li>应该看到 '📤 提交离线留言数据' 日志</li>";
echo "<li>应该看到 '✅ 验证必填字段' 日志</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>观察验证结果</strong>：";
echo "<ul>";
echo "<li>如果有必填字段为空，应该显示警告</li>";
echo "<li>如果验证通过，应该发送网络请求</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff; padding: 25px; margin: 20px 0;'>";
echo "<h2>🔍 预期的完整日志序列</h2>";
echo "<div style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
echo "<strong>1. 表单初始化时：</strong><br>";
echo "📋 表单字段配置: {fields: [...], required: [...], settings: {...}}<br>";
echo "✅ 离线表单字段生成完成，使用两列布局<br><br>";

echo "<strong>2. 点击提交按钮时：</strong><br>";
echo "🔄 开始提交离线表单<br>";
echo "📝 离线表单提交事件触发<br>";
echo "📤 提交离线留言数据: {session_id: \"...\", visitor_name: \"...\", ...}<br>";
echo "✅ 验证必填字段: [\"email\", \"message\"]<br><br>";

echo "<strong>3. 验证结果：</strong><br>";
echo "如果验证失败：❌ 请填写必填字段: 邮箱<br>";
echo "如果验证通过：⏳ 正在提交离线留言...<br>";
echo "然后：✅ 离线留言提交成功 或 ❌ 提交失败";
echo "</div>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 25px; margin: 20px 0;'>";
echo "<h2>✅ 问题排查清单</h2>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 30px;'>";
echo "<div>";
echo "<h4>🔍 如果没有看到任何日志</h4>";
echo "<ul>";
echo "<li>检查是否有JavaScript错误</li>";
echo "<li>确认按钮onclick事件是否绑定</li>";
echo "<li>检查submitOfflineForm函数是否定义</li>";
echo "<li>尝试在控制台手动调用函数</li>";
echo "</ul>";
echo "</div>";
echo "<div>";
echo "<h4>🔍 如果只看到部分日志</h4>";
echo "<ul>";
echo "<li>检查表单ID是否正确</li>";
echo "<li>确认事件监听器是否绑定</li>";
echo "<li>查看是否有异常中断执行</li>";
echo "<li>检查必填字段验证逻辑</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 25px; margin: 20px 0;'>";
echo "<h2>🛠️ 手动调试命令</h2>";
echo "<p>在浏览器控制台中可以执行以下命令进行调试：</p>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 13px;'>";
echo "// 检查函数是否存在<br>";
echo "typeof submitOfflineForm<br><br>";

echo "// 手动调用提交函数<br>";
echo "submitOfflineForm()<br><br>";

echo "// 检查表单元素<br>";
echo "document.getElementById('offline-message-form')<br><br>";

echo "// 检查按钮元素<br>";
echo "document.getElementById('offline-submit-btn')<br><br>";

echo "// 检查设置数据<br>";
echo "console.log(csSettings)";
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h2>🔗 相关链接</h2>";
echo "<a href='/' target='_blank' style='background: #000; color: #fff; padding: 15px 30px; text-decoration: none; margin: 10px; display: inline-block; letter-spacing: 1px; font-weight: 300;'>首页测试</a>";
echo "<a href='/strongadmin/customer-service/settings' target='_blank' style='background: #fff; color: #000; padding: 15px 30px; text-decoration: none; margin: 10px; display: inline-block; letter-spacing: 1px; font-weight: 300; border: 1px solid #000;'>客服设置</a>";
echo "</div>";

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #000;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
}

h1, h2 {
    color: #000;
    font-weight: 300;
    letter-spacing: 1px;
}

h4 {
    color: #000;
    margin-bottom: 15px;
    font-weight: 400;
}

a:hover {
    opacity: 0.8;
    transition: all 0.3s ease;
}

li {
    margin: 8px 0;
}

strong {
    font-weight: 500;
}
</style>
