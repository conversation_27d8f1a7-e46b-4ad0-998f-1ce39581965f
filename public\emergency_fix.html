<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急修复工具</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急修复工具</h1>
        <p>专门用于解决第2677行的JavaScript语法错误</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="captureRealError()">捕获真实错误</button>
            <button class="test-btn" onclick="testMainPage()">测试主页面</button>
            <button class="test-btn danger" onclick="showSolution()">显示解决方案</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>🚨 紧急修复工具已就绪</h3>
                <p>这个工具将帮助我们快速定位和修复JavaScript语法错误</p>
            </div>
        </div>
    </div>

    <script>
        let errorCaptured = false;
        let capturedErrors = [];

        // 捕获所有JavaScript错误
        window.addEventListener('error', function(event) {
            capturedErrors.push({
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error ? event.error.toString() : 'Unknown error',
                stack: event.error ? event.error.stack : null
            });
            errorCaptured = true;
        });

        // 捕获Promise rejection错误
        window.addEventListener('unhandledrejection', function(event) {
            capturedErrors.push({
                message: 'Unhandled Promise Rejection: ' + event.reason,
                type: 'promise_rejection'
            });
        });

        function updateDisplay(content, type = 'success') {
            const display = document.getElementById('result-display');
            const className = type === 'error' ? 'error-box' : 
                            type === 'warning' ? 'warning-box' : 'success-box';
            display.innerHTML = '<div class="' + className + '">' + content + '</div>';
        }

        function captureRealError() {
            updateDisplay('<h3>🔍 正在捕获真实错误...</h3>', 'warning');
            
            // 清空之前的错误
            capturedErrors = [];
            errorCaptured = false;
            
            // 创建一个iframe来加载主页面，这样可以捕获其中的错误
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = '/';
            
            iframe.onload = function() {
                setTimeout(function() {
                    let result = '<h3>📊 错误捕获结果</h3>';
                    
                    if (capturedErrors.length > 0) {
                        result += '<p><strong>捕获到 ' + capturedErrors.length + ' 个错误:</strong></p>';
                        
                        capturedErrors.forEach(function(error, index) {
                            result += '<div style="border: 1px solid #f5c6cb; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 4px;">';
                            result += '<h4>错误 ' + (index + 1) + ':</h4>';
                            result += '<p><strong>消息:</strong> ' + error.message + '</p>';
                            if (error.filename) result += '<p><strong>文件:</strong> ' + error.filename + '</p>';
                            if (error.lineno) result += '<p><strong>行号:</strong> ' + error.lineno + '</p>';
                            if (error.colno) result += '<p><strong>列号:</strong> ' + error.colno + '</p>';
                            if (error.stack) {
                                result += '<p><strong>堆栈:</strong></p>';
                                result += '<pre style="background: white; padding: 5px; border-radius: 3px; font-size: 10px;">' + error.stack + '</pre>';
                            }
                            result += '</div>';
                        });
                    } else {
                        result += '<p style="color: green;">✅ 未捕获到JavaScript错误</p>';
                    }
                    
                    // 移除iframe
                    document.body.removeChild(iframe);
                    
                    updateDisplay(result, capturedErrors.length > 0 ? 'error' : 'success');
                }, 2000);
            };
            
            iframe.onerror = function() {
                result = '<h3>❌ 无法加载主页面</h3>';
                result += '<p>iframe加载失败，可能是由于CORS限制或其他问题。</p>';
                updateDisplay(result, 'error');
                document.body.removeChild(iframe);
            };
            
            document.body.appendChild(iframe);
        }

        function testMainPage() {
            updateDisplay('<h3>🔍 测试主页面...</h3>', 'warning');
            
            // 打开主页面在新窗口中
            const newWindow = window.open('/', '_blank');
            
            setTimeout(function() {
                let result = '<h3>🌐 主页面测试</h3>';
                result += '<p>主页面已在新窗口中打开。</p>';
                result += '<p><strong>请在新窗口中:</strong></p>';
                result += '<ol>';
                result += '<li>按F12打开开发者工具</li>';
                result += '<li>查看Console标签页</li>';
                result += '<li>寻找红色的错误信息</li>';
                result += '<li>特别注意 "missing ) after argument list" 错误</li>';
                result += '<li>记录错误的确切行号和内容</li>';
                result += '</ol>';
                result += '<p><strong>常见的错误位置:</strong></p>';
                result += '<ul>';
                result += '<li>第2677行 - 这是报告的错误位置</li>';
                result += '<li>客服系统相关的JavaScript代码</li>';
                result += '<li>统计代码加载相关的代码</li>';
                result += '</ul>';
                
                updateDisplay(result, 'warning');
            }, 1000);
        }

        function showSolution() {
            updateDisplay('<h3>💡 解决方案</h3>', 'warning');
            
            let result = '<h3>🛠️ JavaScript语法错误解决方案</h3>';
            result += '<p>基于我们的分析，这个 "missing ) after argument list" 错误最可能的原因和解决方案:</p>';
            
            result += '<h4>🎯 最可能的原因:</h4>';
            result += '<ol>';
            result += '<li><strong>@json()函数输出问题</strong> - 统计代码配置中可能包含特殊字符</li>';
            result += '<li><strong>模板字符串语法错误</strong> - 某些浏览器可能不完全支持</li>';
            result += '<li><strong>动态生成的JavaScript代码</strong> - PHP变量插值导致的语法错误</li>';
            result += '</ol>';
            
            result += '<h4>🔧 建议的修复步骤:</h4>';
            result += '<ol>';
            result += '<li><strong>检查统计代码配置</strong>';
            result += '<ul>';
            result += '<li>登录后台管理系统</li>';
            result += '<li>查看"统计代码"配置项</li>';
            result += '<li>检查是否包含特殊字符或格式错误的代码</li>';
            result += '<li>临时清空统计代码配置进行测试</li>';
            result += '</ul>';
            result += '</li>';
            
            result += '<li><strong>替换所有模板字符串</strong>';
            result += '<ul>';
            result += '<li>将所有 `${variable}` 替换为 "string" + variable</li>';
            result += '<li>特别检查console.log、alert、fetch等函数调用</li>';
            result += '</ul>';
            result += '</li>';
            
            result += '<li><strong>检查PHP变量输出</strong>';
            result += '<ul>';
            result += '<li>确保所有@json()调用都正确转义</li>';
            result += '<li>使用json_encode()配合转义标志</li>';
            result += '</ul>';
            result += '</li>';
            result += '</ol>';
            
            result += '<h4>🚨 紧急临时修复:</h4>';
            result += '<div style="background: #ffebee; padding: 15px; border-radius: 4px; border-left: 4px solid #f44336;">';
            result += '<p><strong>如果需要立即修复，可以尝试:</strong></p>';
            result += '<ol>';
            result += '<li>在app.blade.php文件的&lt;head&gt;部分添加:</li>';
            result += '<pre style="background: white; padding: 10px; border-radius: 3px;">&lt;script&gt;window.onerror = function() { return true; };&lt;/script&gt;</pre>';
            result += '<li>这将抑制JavaScript错误，但不会解决根本问题</li>';
            result += '<li>仅作为临时措施，需要尽快找到并修复真正的语法错误</li>';
            result += '</ol>';
            result += '</div>';
            
            result += '<h4>📞 需要进一步帮助?</h4>';
            result += '<p>如果以上方法都无法解决问题，请提供:</p>';
            result += '<ul>';
            result += '<li>浏览器控制台中的完整错误信息</li>';
            result += '<li>统计代码配置的内容</li>';
            result += '<li>错误出现的具体步骤</li>';
            result += '</ul>';
            
            updateDisplay(result, 'warning');
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay('<h3>🚨 紧急修复工具已启动</h3><p>准备帮助您快速解决JavaScript语法错误。</p>');
        });
    </script>
</body>
</html>
