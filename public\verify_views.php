<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>验证视图文件</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔍 验证客服系统视图文件</h1>";

// 检查视图文件
$viewFiles = [
    'settings' => 'resources/views/strongadmin/customer-service/settings.blade.php',
    'offline-messages' => 'resources/views/strongadmin/customer-service/offline-messages.blade.php',
    'sessions' => 'resources/views/strongadmin/customer-service/sessions.blade.php',
    'ai-rules' => 'resources/views/strongadmin/customer-service/ai-rules.blade.php',
    'statistics' => 'resources/views/strongadmin/customer-service/statistics.blade.php',
    'system-config' => 'resources/views/strongadmin/customer-service/system-config.blade.php'
];

echo "<h3>1. 视图文件检查</h3>";
echo "<table>";
echo "<tr><th>视图名称</th><th>文件路径</th><th>状态</th><th>大小</th><th>修改时间</th></tr>";

foreach ($viewFiles as $name => $path) {
    $fullPath = __DIR__ . '/../' . $path;
    $exists = file_exists($fullPath);
    $size = $exists ? filesize($fullPath) : 0;
    $mtime = $exists ? date('Y-m-d H:i:s', filemtime($fullPath)) : '-';
    
    echo "<tr>";
    echo "<td><strong>{$name}</strong></td>";
    echo "<td><code>{$path}</code></td>";
    echo "<td>" . ($exists ? '<span style="color:#28a745;">✅ 存在</span>' : '<span style="color:#dc3545;">❌ 不存在</span>') . "</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : '-') . "</td>";
    echo "<td>{$mtime}</td>";
    echo "</tr>";
}
echo "</table>";

// 检查控制器文件
echo "<h3>2. 控制器文件检查</h3>";
$controllerFiles = [
    'CustomerServiceController' => 'app/Http/Controllers/Strongadmin/CustomerServiceController.php',
    'CustomerServiceSettingsController' => 'app/Http/Controllers/Strongadmin/CustomerServiceSettingsController.php'
];

echo "<table>";
echo "<tr><th>控制器名称</th><th>文件路径</th><th>状态</th><th>大小</th></tr>";

foreach ($controllerFiles as $name => $path) {
    $fullPath = __DIR__ . '/../' . $path;
    $exists = file_exists($fullPath);
    $size = $exists ? filesize($fullPath) : 0;
    
    echo "<tr>";
    echo "<td><strong>{$name}</strong></td>";
    echo "<td><code>{$path}</code></td>";
    echo "<td>" . ($exists ? '<span style="color:#28a745;">✅ 存在</span>' : '<span style="color:#dc3545;">❌ 不存在</span>') . "</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : '-') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 检查路由文件
echo "<h3>3. 路由文件检查</h3>";
$routeFile = __DIR__ . '/../routes/admin.php';
if (file_exists($routeFile)) {
    echo "<div class='success'>✅ 路由文件存在: routes/admin.php</div>";
    
    $content = file_get_contents($routeFile);
    $customerServiceRoutes = [];
    
    // 查找客服相关路由
    if (strpos($content, 'customer-service') !== false) {
        echo "<div class='success'>✅ 找到客服相关路由</div>";
        
        // 提取路由行
        $lines = explode("\n", $content);
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, 'customer-service') !== false && strpos($line, 'Route::') !== false) {
                $customerServiceRoutes[] = ($lineNum + 1) . ": " . trim($line);
            }
        }
        
        if (!empty($customerServiceRoutes)) {
            echo "<h4>客服路由列表:</h4>";
            echo "<pre style='background:#f8f9fa;padding:10px;border-radius:4px;'>";
            foreach ($customerServiceRoutes as $route) {
                echo htmlspecialchars($route) . "\n";
            }
            echo "</pre>";
        }
    } else {
        echo "<div class='error'>❌ 未找到客服相关路由</div>";
    }
} else {
    echo "<div class='error'>❌ 路由文件不存在</div>";
}

// 检查数据库表
echo "<h3>4. 数据库表检查</h3>";
try {
    // 简单的数据库连接测试
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'mostxx_com';
    $username = 'mostxx_com';
    $password = 'fHnrmH9w5nw1pd53';

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 检查客服相关表
    $tables = [
        'customer_service_settings',
        'customer_service_sessions', 
        'customer_service_messages',
        'customer_service_offline_messages',
        'st_strongadmin_menu'
    ];
    
    echo "<table>";
    echo "<tr><th>表名</th><th>状态</th><th>记录数</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $result = $stmt->fetch();
            $count = $result['count'];
            
            echo "<tr>";
            echo "<td><code>{$table}</code></td>";
            echo "<td><span style='color:#28a745;'>✅ 存在</span></td>";
            echo "<td>{$count} 条记录</td>";
            echo "</tr>";
        } catch (Exception $e) {
            echo "<tr>";
            echo "<td><code>{$table}</code></td>";
            echo "<td><span style='color:#dc3545;'>❌ 不存在</span></td>";
            echo "<td>-</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
}

// 测试链接
echo "<h3>5. 测试链接</h3>";
$testUrls = [
    '设置中心' => '/strongadmin/customer-service/settings',
    '离线留言' => '/strongadmin/customer-service/offline-messages',
    '会话管理' => '/strongadmin/customer-service/sessions',
    'AI规则管理' => '/strongadmin/customer-service/ai-rules',
    '统计报表' => '/strongadmin/customer-service/statistics'
];

echo "<div class='info'>";
echo "<p><strong>测试链接（需要登录后台）:</strong></p>";
echo "<ul>";
foreach ($testUrls as $name => $url) {
    echo "<li><a href='{$url}' target='_blank'>{$name}</a> - <code>{$url}</code></li>";
}
echo "</ul>";
echo "</div>";

echo "<div class='success'>";
echo "<h4>🎉 检查完成！</h4>";
echo "<p>如果所有视图文件都存在，那么视图找不到的问题应该已经解决。</p>";
echo "<p>如果仍然有问题，请检查：</p>";
echo "<ul>";
echo "<li>Laravel缓存是否需要清理</li>";
echo "<li>文件权限是否正确</li>";
echo "<li>路由是否正确注册</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
