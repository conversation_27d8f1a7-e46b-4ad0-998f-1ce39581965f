<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单实时测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; text-align: center; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; border: 1px solid #ddd; }
        .messages { height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #fafafa; margin: 10px 0; }
        .message { margin: 5px 0; padding: 8px; border-radius: 8px; }
        .message.customer { background: #e3f2fd; }
        .message.admin { background: #f3e5f5; }
        .message.new { background: #fff3cd; border: 2px solid #ffc107; animation: highlight 2s ease-out; }
        @keyframes highlight { from { background: #ffeb3b; } to { background: #fff3cd; } }
        input[type="text"] { width: 60%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .info { background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 简单实时消息测试</h1>
        
        <div class="panel">
            <h2>📊 连接状态</h2>
            <div class="info">
                <div>会话ID: <span id="session-id">未生成</span></div>
                <div>会话数据库ID: <span id="session-db-id">未知</span></div>
                <div>最后消息ID: <span id="last-message-id">0</span></div>
            </div>
            <div id="sse-status" class="status offline">未连接</div>
            <div>
                <button class="btn" onclick="startTest()">开始测试</button>
                <button class="btn" onclick="stopTest()">停止测试</button>
                <button class="btn success" onclick="sendTestReply()">发送测试回复</button>
                <button class="btn danger" onclick="clearAll()">清除所有</button>
            </div>
        </div>
        
        <div class="panel">
            <h2>💬 消息窗口</h2>
            <div id="messages" class="messages"></div>
            <div>
                <input type="text" id="message-input" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                <button class="btn" onclick="sendMessage()">发送消息</button>
            </div>
        </div>
        
        <div class="panel">
            <h2>📝 实时日志</h2>
            <div id="log" class="log"></div>
        </div>
        
        <div class="panel">
            <h2>🧪 测试说明</h2>
            <ol>
                <li><strong>点击"开始测试"</strong> - 生成会话ID并连接SSE</li>
                <li><strong>等待连接成功</strong> - 状态显示"已连接"</li>
                <li><strong>点击"发送测试回复"</strong> - 模拟管理员回复</li>
                <li><strong>观察实时接收</strong> - 消息应该立即出现在消息窗口</li>
            </ol>
            <p><strong>预期结果：</strong>发送测试回复后，消息窗口应该立即显示新消息，并有高亮动画。</p>
        </div>
    </div>

    <script>
    let sessionId = null;
    let sessionDbId = null;
    let eventSource = null;
    let lastMessageId = 0;
    let messageCount = 0;
    
    // 开始测试
    function startTest() {
        // 生成会话ID
        sessionId = 'simple_test_' + Date.now();
        document.getElementById('session-id').textContent = sessionId;
        
        log('🚀 开始测试，会话ID: ' + sessionId);
        
        // 连接SSE
        connectSSE();
    }
    
    // 停止测试
    function stopTest() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            updateStatus('已断开', 'offline');
            log('🛑 测试已停止');
        }
    }
    
    // 连接SSE
    function connectSSE() {
        if (eventSource) {
            eventSource.close();
        }
        
        const url = `/basic_sse.php?session_id=${sessionId}&last_message_id=${lastMessageId}`;
        log('🔄 连接SSE: ' + url);
        updateStatus('连接中...', 'offline');
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            log('✅ SSE连接成功');
            updateStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                
                // 记录所有SSE消息
                log('📨 SSE: ' + data.type + ' - ' + (data.message || ''));
                
                // 处理不同类型的消息
                if (data.type === 'session_found' && data.session_db_id) {
                    sessionDbId = data.session_db_id;
                    document.getElementById('session-db-id').textContent = sessionDbId;
                    log('📍 获取到会话数据库ID: ' + sessionDbId);
                }
                
                if (data.type === 'new_reply') {
                    addMessage(data.sender_type, data.message, true);
                    lastMessageId = Math.max(lastMessageId, data.message_id);
                    document.getElementById('last-message-id').textContent = lastMessageId;
                    
                    // 播放提示音
                    playNotificationSound();
                    log('🎉 收到新回复: ' + data.message);
                }
                
                if (data.type === 'debug') {
                    log('🐛 ' + data.message);
                }
                
                if (data.type === 'heartbeat') {
                    log('💓 心跳包');
                }
                
            } catch (e) {
                log('❌ 解析SSE消息失败: ' + e.message);
                log('原始数据: ' + event.data);
            }
        };
        
        eventSource.onerror = function() {
            log('❌ SSE连接错误');
            updateStatus('连接错误', 'offline');
            
            // 5秒后重连
            setTimeout(() => {
                if (sessionId) {
                    log('🔄 尝试重新连接...');
                    connectSSE();
                }
            }, 5000);
        };
    }
    
    // 发送消息
    function sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        addMessage('customer', message, false);
        input.value = '';
        
        log('📤 发送客户消息: ' + message);
    }
    
    // 发送测试回复
    function sendTestReply() {
        if (!sessionDbId) {
            alert('请先开始测试并等待连接成功');
            return;
        }
        
        const testMessage = '测试管理员回复 ' + new Date().toLocaleTimeString();
        log('📤 发送测试回复: ' + testMessage);
        
        // 发送到服务器
        fetch('/admin_reply_test.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=send_reply&session_db_id=${sessionDbId}&reply_message=${encodeURIComponent(testMessage)}`
        })
        .then(response => response.text())
        .then(data => {
            if (data.includes('发送成功')) {
                log('✅ 测试回复发送成功，等待SSE接收...');
            } else {
                log('❌ 测试回复发送失败');
                console.log('服务器响应:', data);
            }
        })
        .catch(error => {
            log('❌ 网络错误: ' + error.message);
        });
    }
    
    // 添加消息
    function addMessage(sender, message, isNew) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender + (isNew ? ' new' : '');
        
        messageCount++;
        const time = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `<strong>${sender} #${messageCount}:</strong> ${message} <small>(${time})</small>`;
        
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
        
        if (isNew) {
            // 3秒后移除高亮
            setTimeout(() => {
                messageDiv.classList.remove('new');
            }, 3000);
        }
    }
    
    // 清除所有
    function clearAll() {
        document.getElementById('messages').innerHTML = '';
        document.getElementById('log').innerHTML = '';
        messageCount = 0;
        lastMessageId = 0;
        document.getElementById('last-message-id').textContent = '0';
        log('🗑️ 已清除所有内容');
    }
    
    // 更新状态
    function updateStatus(text, className) {
        const statusElement = document.getElementById('sse-status');
        statusElement.textContent = text;
        statusElement.className = 'status ' + className;
    }
    
    // 播放提示音
    function playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (e) {
            log('🔇 无法播放提示音');
        }
    }
    
    // 处理回车键
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }
    
    // 日志函数
    function log(message) {
        const logDiv = document.getElementById('log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    </script>
</body>
</html>
