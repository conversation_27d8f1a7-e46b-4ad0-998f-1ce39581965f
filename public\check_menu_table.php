<?php
/**
 * 检查菜单表结构
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>检查菜单表结构</title>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background:#f2f2f2;} .success{color:green;} .error{color:red;}</style>";
echo "</head><body>";

echo "<h1>🔍 检查菜单表结构</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 查找菜单表
    $tables = $pdo->query("SHOW TABLES LIKE '%menu%'")->fetchAll();
    
    echo "<h2>📋 菜单相关表：</h2>";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "<h3>表名: $tableName</h3>";
        
        // 显示表结构
        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>注释</th></tr>";
        
        $columns = $pdo->query("SHOW FULL COLUMNS FROM `$tableName`")->fetchAll();
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Comment']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 显示一些示例数据
        echo "<h4>示例数据（前5条）：</h4>";
        $data = $pdo->query("SELECT * FROM `$tableName` LIMIT 5")->fetchAll();
        if ($data) {
            echo "<table>";
            $headers = array_keys($data[0]);
            echo "<tr>";
            foreach ($headers as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";
            
            foreach ($data as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>表中暂无数据</p>";
        }
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
