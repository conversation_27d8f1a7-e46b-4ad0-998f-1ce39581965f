@extends('strongadmin::layouts.app')

@push('styles')
<style>
.st-stats-panel {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0,0,0,.1);
    padding: 20px;
    margin-bottom: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
}
.st-stats-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
}
.st-stats-panel.blue::before { background: #1E9FFF; }
.st-stats-panel.green::before { background: #5FB878; }
.st-stats-panel.orange::before { background: #FFB800; }
.st-stats-panel.red::before { background: #FF5722; }

.st-stats-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.8;
}
.st-stats-number {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}
.st-stats-label {
    color: #666;
    font-size: 14px;
}
.st-chart-panel {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0,0,0,.1);
    padding: 20px;
    margin-bottom: 15px;
}
.st-chart-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}
</style>
@endpush

@section('content')
<div class="st-h15"></div>

<!-- 统计卡片 -->
<div class="layui-row layui-col-space15">
    <div class="layui-col-md3">
        <div class="st-stats-panel blue">
            <div class="st-stats-icon" style="color: #1E9FFF;">
                <i class="layui-icon layui-icon-dialogue"></i>
            </div>
            <div class="st-stats-number">{{ $stats['total_sessions'] ?? 0 }}</div>
            <div class="st-stats-label">总会话数</div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="st-stats-panel green">
            <div class="st-stats-icon" style="color: #5FB878;">
                <i class="layui-icon layui-icon-circle-dot"></i>
            </div>
            <div class="st-stats-number">{{ $stats['active_sessions'] ?? 0 }}</div>
            <div class="st-stats-label">活跃会话</div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="st-stats-panel orange">
            <div class="st-stats-icon" style="color: #FFB800;">
                <i class="layui-icon layui-icon-email"></i>
            </div>
            <div class="st-stats-number">{{ $stats['total_messages'] ?? 0 }}</div>
            <div class="st-stats-label">总消息数</div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="st-stats-panel red">
            <div class="st-stats-icon" style="color: #FF5722;">
                <i class="layui-icon layui-icon-face-smile"></i>
            </div>
            <div class="st-stats-number">{{ $stats['ai_messages'] ?? 0 }}</div>
            <div class="st-stats-label">AI回复数</div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="layui-row layui-col-space15">
    <div class="layui-col-md8">
        <div class="st-chart-panel">
            <div class="st-chart-title">
                <i class="layui-icon layui-icon-time"></i> 最近会话
                <a href="sessions" class="layui-btn layui-btn-xs" style="float: right;">查看全部</a>
            </div>

            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th>客户</th>
                        <th>最新消息</th>
                        <th>时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($recentSessions ?? [] as $session)
                    <tr>
                        <td>
                            <strong>{{ $session->visitor_name ?? '访客' }}</strong>
                            @if($session->user_id)
                                <span class="layui-badge layui-badge-rim">注册</span>
                            @endif
                        </td>
                        <td>
                            <span style="color: #999;">暂无消息</span>
                        </td>
                        <td>
                            <small style="color: #999;">{{ $session->last_activity ?? '无' }}</small>
                        </td>
                        <td>
                            @if($session->status == 'active')
                                <span class="layui-badge">活跃</span>
                            @elseif($session->status == 'waiting')
                                <span class="layui-badge layui-bg-orange">等待</span>
                            @else
                                <span class="layui-badge layui-bg-gray">关闭</span>
                            @endif
                        </td>
                        <td>
                            <a href="session/{{ $session->id }}" class="layui-btn layui-btn-xs">查看</a>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 40px; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 40px; display: block; margin-bottom: 10px;"></i>
                            暂无会话记录
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <div class="layui-col-md4">
        <div class="st-chart-panel">
            <div class="st-chart-title">
                <i class="layui-icon layui-icon-fire"></i> 热门AI规则
                <a href="ai-rules" class="layui-btn layui-btn-xs" style="float: right;">管理规则</a>
            </div>

            @forelse($popularRules ?? [] as $rule)
            <div style="padding: 10px 0; border-bottom: 1px solid #f0f0f0;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                        <div style="font-weight: bold; margin-bottom: 5px;">{{ $rule->name }}</div>
                        <div>
                            @foreach(array_slice($rule->keywords ?? [], 0, 3) as $keyword)
                                <span class="layui-badge layui-badge-rim" style="margin-right: 3px;">{{ $keyword }}</span>
                            @endforeach
                        </div>
                    </div>
                    <div>
                        <span class="layui-badge">{{ $rule->usage_count ?? 0 }}</span>
                    </div>
                </div>
            </div>
            @empty
            <div style="text-align: center; padding: 40px; color: #999;">
                <i class="layui-icon layui-icon-face-smile" style="font-size: 40px; display: block; margin-bottom: 10px;"></i>
                暂无AI规则
            </div>
            @endforelse
        </div>

        <!-- 消息类型统计 -->
        <div class="st-chart-panel" style="margin-top: 15px;">
            <div class="st-chart-title">
                <i class="layui-icon layui-icon-chart"></i> 消息类型统计
            </div>

            <div style="padding: 10px 0;">
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>客户消息</span>
                        <span><b>{{ $stats['customer_messages'] ?? 0 }}</b></span>
                    </div>
                    <div class="layui-progress" lay-showpercent="true">
                        <div class="layui-progress-bar" lay-percent="{{ ($stats['total_messages'] ?? 0) > 0 ? round(($stats['customer_messages'] ?? 0) / ($stats['total_messages'] ?? 1) * 100) : 0 }}%"></div>
                    </div>
                </div>

                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>AI回复</span>
                        <span><b>{{ $stats['ai_messages'] ?? 0 }}</b></span>
                    </div>
                    <div class="layui-progress" lay-showpercent="true">
                        <div class="layui-progress-bar layui-bg-green" lay-percent="{{ ($stats['total_messages'] ?? 0) > 0 ? round(($stats['ai_messages'] ?? 0) / ($stats['total_messages'] ?? 1) * 100) : 0 }}%"></div>
                    </div>
                </div>

                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>客服回复</span>
                        <span><b>{{ $stats['admin_messages'] ?? 0 }}</b></span>
                    </div>
                    <div class="layui-progress" lay-showpercent="true">
                        <div class="layui-progress-bar layui-bg-orange" lay-percent="{{ ($stats['total_messages'] ?? 0) > 0 ? round(($stats['admin_messages'] ?? 0) / ($stats['total_messages'] ?? 1) * 100) : 0 }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 手动渲染进度条
    var progressBars = document.querySelectorAll('.layui-progress');
    progressBars.forEach(function(progress) {
        var progressBar = progress.querySelector('.layui-progress-bar');
        if (progressBar) {
            var percent = progressBar.getAttribute('lay-percent');
            if (percent) {
                progressBar.style.width = percent;

                // 如果需要显示百分比
                var showPercent = progress.parentNode.querySelector('[lay-showpercent="true"]');
                if (showPercent) {
                    progressBar.innerHTML = '<span class="layui-progress-text">' + percent + '</span>';
                }
            }
        }
    });

    // 添加数字动画效果
    setTimeout(function() {
        var statsNumbers = document.querySelectorAll('.st-stats-number');
        statsNumbers.forEach(function(element) {
            var countTo = parseInt(element.textContent);
            var current = 0;
            var increment = countTo / 50; // 50步完成动画

            var timer = setInterval(function() {
                current += increment;
                if (current >= countTo) {
                    element.textContent = countTo;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(current);
                }
            }, 20); // 每20ms更新一次
        });
    }, 300);
});
</script>
@endpush

@endsection
