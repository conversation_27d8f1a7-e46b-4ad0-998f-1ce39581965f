<?php
/**
 * 调试实时消息推送功能
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>调试实时消息推送</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 调试实时消息推送功能</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 检查配置表
    echo "<h2>📋 系统配置检查</h2>";
    $configExists = $pdo->query("SHOW TABLES LIKE 'st_customer_service_config'")->rowCount() > 0;
    if ($configExists) {
        echo "<p class='success'>✅ 配置表存在</p>";
        
        $configs = $pdo->query("SELECT * FROM st_customer_service_config")->fetchAll();
        echo "<table><tr><th>配置键</th><th>配置值</th><th>类型</th><th>描述</th></tr>";
        foreach ($configs as $config) {
            echo "<tr><td>{$config['config_key']}</td><td>{$config['config_value']}</td><td>{$config['config_type']}</td><td>{$config['description']}</td></tr>";
        }
        echo "</table>";
        
        $systemEnabled = $pdo->query("SELECT config_value FROM st_customer_service_config WHERE config_key = 'system_enabled'")->fetchColumn();
        if ($systemEnabled === '1') {
            echo "<p class='success'>✅ 客服系统已启用</p>";
        } else {
            echo "<p class='error'>❌ 客服系统已禁用</p>";
        }
    } else {
        echo "<p class='error'>❌ 配置表不存在，请先运行 update_cs_system.php</p>";
    }
    
    // 检查会话表
    echo "<h2>💬 会话数据检查</h2>";
    $sessions = $pdo->query("SELECT * FROM st_customer_service_sessions ORDER BY created_at DESC LIMIT 5")->fetchAll();
    if (empty($sessions)) {
        echo "<p class='info'>ℹ️ 暂无会话数据</p>";
    } else {
        echo "<table><tr><th>ID</th><th>会话ID</th><th>访客名称</th><th>状态</th><th>未读数量</th><th>最后活动</th></tr>";
        foreach ($sessions as $session) {
            $visitorName = $session['visitor_name'] ?: '访客-' . strtoupper(substr(md5($session['session_id']), 0, 6));
            echo "<tr><td>{$session['id']}</td><td>{$session['session_id']}</td><td>$visitorName</td><td>{$session['status']}</td><td>" . ($session['unread_count'] ?? 0) . "</td><td>{$session['last_activity']}</td></tr>";
        }
        echo "</table>";
    }
    
    // 检查消息表
    echo "<h2>📨 消息数据检查</h2>";
    $messages = $pdo->query("
        SELECT m.*, s.session_id as session_key 
        FROM st_customer_service_messages m 
        LEFT JOIN st_customer_service_sessions s ON m.session_id = s.id 
        ORDER BY m.created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    if (empty($messages)) {
        echo "<p class='info'>ℹ️ 暂无消息数据</p>";
    } else {
        echo "<table><tr><th>消息ID</th><th>会话</th><th>发送者</th><th>消息内容</th><th>已读</th><th>创建时间</th></tr>";
        foreach ($messages as $msg) {
            $shortMessage = substr($msg['message'], 0, 50) . (strlen($msg['message']) > 50 ? '...' : '');
            echo "<tr><td>{$msg['id']}</td><td>{$msg['session_key']}</td><td>{$msg['sender_type']}</td><td>" . htmlspecialchars($shortMessage) . "</td><td>" . ($msg['is_read'] ? '是' : '否') . "</td><td>{$msg['created_at']}</td></tr>";
        }
        echo "</table>";
        
        $latestMessageId = $pdo->query("SELECT MAX(id) FROM st_customer_service_messages")->fetchColumn();
        echo "<p class='info'>📊 最新消息ID: $latestMessageId</p>";
    }
    
    // 测试按钮
    echo "<h2>🧪 测试功能</h2>";
    echo "<button class='test-btn' onclick=\"testSSE()\">测试SSE连接</button>";
    echo "<button class='test-btn' onclick=\"sendTestMessage()\">发送测试消息</button>";
    echo "<button class='test-btn' onclick=\"checkNotificationPermission()\">检查通知权限</button>";
    echo "<button class='test-btn' onclick=\"testSound()\">测试提示音</button>";
    
    echo "<div id='test-results' style='margin-top:20px;padding:10px;background:#f8f9fa;border-radius:4px;'></div>";
    
    echo "<h2>🔗 相关链接</h2>";
    echo "<ul>";
    echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
    echo "<li><a href='/strongadmin/customer-service/system-config' target='_blank'>系统配置</a></li>";
    echo "<li><a href='/test_cs.php' target='_blank'>发送测试消息</a></li>";
    echo "<li><a href='/strongadmin/customer-service/message-stream' target='_blank'>SSE消息流</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<script>
function testSSE() {
    const results = document.getElementById('test-results');
    results.innerHTML = '<p>🔄 测试SSE连接...</p>';
    
    const eventSource = new EventSource('/strongadmin/customer-service/message-stream?last_message_id=0');
    
    eventSource.onopen = function() {
        results.innerHTML += '<p class=\"success\">✅ SSE连接成功</p>';
    };
    
    eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        results.innerHTML += '<p class=\"info\">📨 收到消息: ' + JSON.stringify(data) + '</p>';
    };
    
    eventSource.onerror = function() {
        results.innerHTML += '<p class=\"error\">❌ SSE连接失败</p>';
        eventSource.close();
    };
    
    setTimeout(() => {
        eventSource.close();
        results.innerHTML += '<p class=\"info\">🔚 SSE测试结束</p>';
    }, 10000);
}

function sendTestMessage() {
    const results = document.getElementById('test-results');
    results.innerHTML = '<p>🔄 发送测试消息...</p>';
    
    const sessionId = 'test_' + Date.now();
    const message = '这是一条测试消息 ' + new Date().toLocaleTimeString();
    
    fetch('/customer-service/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
            session_id: sessionId,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            results.innerHTML += '<p class=\"success\">✅ 测试消息发送成功</p>';
        } else {
            results.innerHTML += '<p class=\"error\">❌ 发送失败: ' + (data.error || '未知错误') + '</p>';
        }
    })
    .catch(error => {
        results.innerHTML += '<p class=\"error\">❌ 网络错误: ' + error.message + '</p>';
    });
}

function checkNotificationPermission() {
    const results = document.getElementById('test-results');
    
    if (!('Notification' in window)) {
        results.innerHTML = '<p class=\"error\">❌ 浏览器不支持通知</p>';
        return;
    }
    
    results.innerHTML = '<p class=\"info\">📱 通知权限状态: ' + Notification.permission + '</p>';
    
    if (Notification.permission === 'default') {
        Notification.requestPermission().then(function(permission) {
            results.innerHTML += '<p class=\"info\">📱 权限请求结果: ' + permission + '</p>';
        });
    }
}

function testSound() {
    const results = document.getElementById('test-results');
    results.innerHTML = '<p>🔊 测试提示音...</p>';
    
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
        
        results.innerHTML += '<p class=\"success\">✅ 提示音播放成功</p>';
    } catch (e) {
        results.innerHTML += '<p class=\"error\">❌ 提示音播放失败: ' + e.message + '</p>';
    }
}
</script>";

echo "</body></html>";
?>
