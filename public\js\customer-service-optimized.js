// 优化后的客服系统 - 懒加载版本
(function() {
    'use strict';
    
    // 全局变量
    let csLoaded = false;
    let csSettings = {};
    let sessionId = null;
    let isOpen = false;
    let pollingInterval = null;
    
    // 懒加载客服系统
    function loadCustomerService() {
        if (csLoaded) return;
        csLoaded = true;
        
        // 创建客服组件
        createCustomerServiceWidget();
        
        // 初始化
        initCustomerService();
    }
    
    // 创建客服组件（简化版）
    function createCustomerServiceWidget() {
        const widget = document.createElement('div');
        widget.id = 'customer-service-widget';
        widget.innerHTML = `
            <div id="cs-chat-button">💬</div>
            <div id="cs-chat-window">
                <div class="cs-header">
                    <h3>客服支持</h3>
                    <div class="cs-header-actions">
                        <button id="cs-minimize">−</button>
                        <button id="cs-close">×</button>
                    </div>
                </div>
                <div id="cs-messages"></div>
                <div class="cs-input-area">
                    <textarea id="cs-message-input" placeholder="输入消息..."></textarea>
                    <button id="cs-send-btn">发送</button>
                </div>
            </div>
        `;
        document.body.appendChild(widget);
    }
    
    // 初始化客服系统
    async function initCustomerService() {
        try {
            // 异步加载设置
            await loadSettings();
            
            // 绑定事件
            bindEvents();
            
            // 初始化会话
            await initSession();
            
        } catch (error) {
            console.error('客服系统初始化失败:', error);
        }
    }
    
    // 加载设置
    async function loadSettings() {
        try {
            const response = await fetch('/api/customer-service/settings.php');
            if (response.ok) {
                csSettings = await response.json();
            }
        } catch (error) {
            // 使用默认设置
            csSettings = { system_enabled: true };
        }
    }
    
    // 绑定事件
    function bindEvents() {
        const chatButton = document.getElementById('cs-chat-button');
        const closeBtn = document.getElementById('cs-close');
        const minimizeBtn = document.getElementById('cs-minimize');
        const sendBtn = document.getElementById('cs-send-btn');
        const messageInput = document.getElementById('cs-message-input');
        
        if (chatButton) {
            chatButton.addEventListener('click', toggleChat);
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', closeChat);
        }
        
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', minimizeChat);
        }
        
        if (sendBtn) {
            sendBtn.addEventListener('click', sendMessage);
        }
        
        if (messageInput) {
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }
    }
    
    // 初始化会话
    async function initSession() {
        try {
            const response = await fetch('/customer-service/init', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCSRFToken()
                },
                body: JSON.stringify({})
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    sessionId = data.session_id;
                }
            }
        } catch (error) {
            console.error('会话初始化失败:', error);
        }
    }
    
    // 切换聊天窗口
    function toggleChat() {
        const chatWindow = document.getElementById('cs-chat-window');
        const chatButton = document.getElementById('cs-chat-button');
        
        if (isOpen) {
            closeChat();
        } else {
            openChat();
        }
    }
    
    // 打开聊天
    function openChat() {
        const chatWindow = document.getElementById('cs-chat-window');
        const chatButton = document.getElementById('cs-chat-button');
        
        if (chatWindow && chatButton) {
            chatWindow.classList.add('open');
            chatButton.classList.add('hidden');
            isOpen = true;
            
            // 聚焦输入框
            setTimeout(() => {
                const input = document.getElementById('cs-message-input');
                if (input) input.focus();
            }, 300);
            
            // 开始轮询消息
            startPolling();
        }
    }
    
    // 关闭聊天
    function closeChat() {
        const chatWindow = document.getElementById('cs-chat-window');
        const chatButton = document.getElementById('cs-chat-button');
        
        if (chatWindow && chatButton) {
            chatWindow.classList.remove('open', 'minimized');
            chatButton.classList.remove('hidden');
            isOpen = false;
            
            // 停止轮询
            stopPolling();
        }
    }
    
    // 最小化聊天
    function minimizeChat() {
        const chatWindow = document.getElementById('cs-chat-window');
        if (chatWindow) {
            chatWindow.classList.add('minimized');
        }
    }
    
    // 发送消息
    async function sendMessage() {
        const input = document.getElementById('cs-message-input');
        if (!input || !sessionId) return;
        
        const message = input.value.trim();
        if (!message) return;
        
        // 清空输入框
        input.value = '';
        
        // 显示用户消息
        addMessage('user', message);
        
        try {
            const response = await fetch('/customer-service/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCSRFToken()
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    message: message
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                if (!data.success) {
                    addMessage('admin', '发送失败，请重试');
                }
            }
        } catch (error) {
            addMessage('admin', '网络错误，请重试');
        }
    }
    
    // 添加消息到界面
    function addMessage(type, content) {
        const messagesDiv = document.getElementById('cs-messages');
        if (!messagesDiv) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `cs-message ${type}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'cs-message-content';
        contentDiv.textContent = content;
        
        messageDiv.appendChild(contentDiv);
        messagesDiv.appendChild(messageDiv);
        
        // 滚动到底部
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
    
    // 开始轮询消息
    function startPolling() {
        if (pollingInterval) return;
        
        pollingInterval = setInterval(async () => {
            if (!sessionId) return;
            
            try {
                const response = await fetch(`/api/customer-service/messages/${sessionId}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.messages) {
                        data.messages.forEach(msg => {
                            if (msg.sender_type === 'admin') {
                                addMessage('admin', msg.message);
                            }
                        });
                    }
                }
            } catch (error) {
                // 静默处理错误
            }
        }, 3000);
    }
    
    // 停止轮询
    function stopPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
        }
    }
    
    // 获取CSRF Token
    function getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }
    
    // 检测用户交互后懒加载
    function setupLazyLoad() {
        let loaded = false;
        
        // 鼠标移动时加载
        document.addEventListener('mousemove', function() {
            if (!loaded) {
                loaded = true;
                setTimeout(loadCustomerService, 100);
            }
        }, { once: true });
        
        // 滚动时加载
        document.addEventListener('scroll', function() {
            if (!loaded) {
                loaded = true;
                setTimeout(loadCustomerService, 100);
            }
        }, { once: true });
        
        // 3秒后自动加载
        setTimeout(() => {
            if (!loaded) {
                loaded = true;
                loadCustomerService();
            }
        }, 3000);
    }
    
    // 页面加载完成后设置懒加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupLazyLoad);
    } else {
        setupLazyLoad();
    }
    
})();
