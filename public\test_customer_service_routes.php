<?php
/**
 * 测试客服系统路由
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>测试客服路由</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 测试客服系统路由</h1>";

$routes = [
    'GET /strongadmin/customer-service/sessions' => '会话列表',
    'DELETE /strongadmin/customer-service/session/1' => '删除会话',
    'POST /strongadmin/customer-service/sessions/clear-all' => '清空所有会话',
    'POST /strongadmin/customer-service/sessions/mark-all-read' => '标记所有已读',
    'POST /strongadmin/customer-service/session/1/mark-read' => '标记单个已读',
];

echo "<h2>📋 路由列表</h2>";
echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
echo "<tr><th>路由</th><th>说明</th><th>操作</th></tr>";

foreach ($routes as $route => $desc) {
    echo "<tr>";
    echo "<td><code>$route</code></td>";
    echo "<td>$desc</td>";
    echo "<td><button class='test-btn' onclick='testRoute(\"$route\")'>测试</button></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>📄 测试结果</h2>";
echo "<div id='test-results' style='background:#f5f5f5;padding:10px;border-radius:4px;min-height:200px;'>";
echo "<p>点击上面的测试按钮查看结果...</p>";
echo "</div>";

echo "<script>
function testRoute(route) {
    const results = document.getElementById('test-results');
    results.innerHTML = '<p class=\"info\">🔄 测试路由: ' + route + '</p>';
    
    const [method, url] = route.split(' ');
    const fullUrl = 'http://www.strongshop.local' + url;
    
    const options = {
        method: method,
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    if (method === 'POST' || method === 'DELETE') {
        options.headers['Content-Type'] = 'application/json';
        // 简单的CSRF token（实际应该从页面获取）
        options.headers['X-CSRF-TOKEN'] = 'test-token';
    }
    
    fetch(fullUrl, options)
        .then(response => {
            results.innerHTML += '<p class=\"info\">📊 状态码: ' + response.status + '</p>';
            return response.text();
        })
        .then(text => {
            results.innerHTML += '<p class=\"info\">📄 响应内容:</p>';
            results.innerHTML += '<pre style=\"background:white;padding:10px;border:1px solid #ddd;max-height:300px;overflow:auto;\">' + 
                                text.substring(0, 1000) + (text.length > 1000 ? '...' : '') + '</pre>';
            
            // 尝试解析JSON
            try {
                const json = JSON.parse(text);
                results.innerHTML += '<p class=\"success\">✅ 响应是有效的JSON</p>';
            } catch (e) {
                results.innerHTML += '<p class=\"error\">❌ 响应不是JSON格式</p>';
            }
        })
        .catch(error => {
            results.innerHTML += '<p class=\"error\">❌ 请求失败: ' + error.message + '</p>';
        });
}
</script>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/strongadmin' target='_blank'>后台首页</a></li>";
echo "<li><a href='/test_delete_route.php' target='_blank'>删除路由测试</a></li>";
echo "</ul>";

echo "</body></html>";
?>
