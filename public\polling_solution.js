/**
 * 轮询解决方案 - 不需要WebSocket，定时检查新消息
 */

class PollingCustomerService {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.lastMessageId = 0;
        this.isPolling = false;
        this.pollInterval = null;
        this.messageHandlers = [];
    }

    start() {
        if (this.isPolling) return;
        
        this.isPolling = true;
        console.log('🔄 启动消息轮询，会话ID:', this.sessionId);
        
        // 每2秒检查一次新消息
        this.pollInterval = setInterval(() => {
            this.checkNewMessages();
        }, 2000);
        
        // 立即检查一次
        this.checkNewMessages();
    }

    checkNewMessages() {
        fetch(`/api/customer-service/messages/${this.sessionId}?last_id=${this.lastMessageId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.messages && data.messages.length > 0) {
                    data.messages.forEach(message => {
                        if (message.sender_type === 'admin' || message.sender_type === 'ai') {
                            // 触发消息处理
                            this.messageHandlers.forEach(handler => handler(message));
                            this.lastMessageId = Math.max(this.lastMessageId, message.id);
                        }
                    });
                }
            })
            .catch(error => {
                console.error('检查新消息失败:', error);
            });
    }

    onMessage(handler) {
        this.messageHandlers.push(handler);
    }

    stop() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
        this.isPolling = false;
        console.log('⏹️ 消息轮询已停止');
    }
}

// 全局变量
window.pollingService = null;

// 初始化轮询服务
window.initPollingService = function(sessionId, onMessageCallback) {
    if (window.pollingService) {
        window.pollingService.stop();
    }
    
    window.pollingService = new PollingCustomerService(sessionId);
    
    if (onMessageCallback) {
        window.pollingService.onMessage(onMessageCallback);
    }
    
    window.pollingService.start();
    
    console.log('✅ 轮询客服系统已启动');
};
