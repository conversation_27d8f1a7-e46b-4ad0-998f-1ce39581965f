<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>修复菜单路由</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 修复菜单路由</h1>";

try {
    // 1. 检查当前菜单
    echo "<h3>1. 当前客服相关菜单</h3>";
    $menus = DB::select("
        SELECT id, name, route_url, parent_id, level
        FROM strongadmin_menu
        WHERE name LIKE '%客服%' OR name IN ('设置中心', '离线留言')
        ORDER BY parent_id, sort
    ");
    
    if (empty($menus)) {
        echo "<div class='error'>❌ 没有找到客服相关菜单</div>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>名称</th><th>路由</th><th>父级ID</th><th>级别</th></tr>";
        foreach ($menus as $menu) {
            echo "<tr>";
            echo "<td>{$menu->id}</td>";
            echo "<td>{$menu->name}</td>";
            echo "<td>{$menu->route_url}</td>";
            echo "<td>{$menu->parent_id}</td>";
            echo "<td>{$menu->level}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 2. 查找在线客服主菜单
    echo "<h3>2. 查找在线客服主菜单</h3>";
    $parentMenu = DB::table('strongadmin_menu')
                   ->where('name', '在线客服')
                   ->where('level', 1)
                   ->first();
    
    if (!$parentMenu) {
        echo "<div class='error'>❌ 找不到'在线客服'主菜单</div>";
        
        // 创建主菜单
        echo "<h4>创建在线客服主菜单</h4>";
        $parentId = DB::table('strongadmin_menu')->insertGetId([
            'level' => 1,
            'parent_id' => 0,
            'name' => '在线客服',
            'route_url' => '',
            'status' => 1,
            'sort' => 100,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "<div class='success'>✅ 创建在线客服主菜单，ID: {$parentId}</div>";
        $parentMenu = (object)['id' => $parentId];
    } else {
        echo "<div class='success'>✅ 找到在线客服主菜单，ID: {$parentMenu->id}</div>";
    }
    
    // 3. 修复或创建设置中心菜单
    echo "<h3>3. 修复设置中心菜单</h3>";
    $settingsMenu = DB::table('strongadmin_menu')
                     ->where('name', '设置中心')
                     ->where('parent_id', $parentMenu->id)
                     ->first();

    if ($settingsMenu) {
        // 更新路由
        DB::table('strongadmin_menu')
          ->where('id', $settingsMenu->id)
          ->update([
              'route_url' => 'customer-service/settings',
              'updated_at' => now()
          ]);
        echo "<div class='success'>✅ 更新设置中心菜单路由</div>";
    } else {
        // 创建菜单
        DB::table('strongadmin_menu')->insert([
            'level' => 2,
            'parent_id' => $parentMenu->id,
            'name' => '设置中心',
            'route_url' => 'customer-service/settings',
            'status' => 1,
            'sort' => 4,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "<div class='success'>✅ 创建设置中心菜单</div>";
    }
    
    // 4. 修复或创建离线留言菜单
    echo "<h3>4. 修复离线留言菜单</h3>";
    $offlineMenu = DB::table('st_strongadmin_menu')
                    ->where('name', '离线留言')
                    ->where('parent_id', $parentMenu->id)
                    ->first();

    if ($offlineMenu) {
        // 更新路由
        DB::table('st_strongadmin_menu')
          ->where('id', $offlineMenu->id)
          ->update([
              'route_url' => 'customer-service/offline-messages',
              'updated_at' => now()
          ]);
        echo "<div class='success'>✅ 更新离线留言菜单路由</div>";
    } else {
        // 创建菜单
        DB::table('st_strongadmin_menu')->insert([
            'level' => 2,
            'parent_id' => $parentMenu->id,
            'name' => '离线留言',
            'route_url' => 'customer-service/offline-messages',
            'status' => 1,
            'sort' => 5,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "<div class='success'>✅ 创建离线留言菜单</div>";
    }
    
    // 5. 显示修复后的菜单
    echo "<h3>5. 修复后的菜单结构</h3>";
    $updatedMenus = DB::select("
        SELECT id, name, route_url, parent_id, level
        FROM strongadmin_menu
        WHERE name LIKE '%客服%' OR name IN ('设置中心', '离线留言')
        ORDER BY parent_id, sort
    ");
    
    echo "<table>";
    echo "<tr><th>ID</th><th>名称</th><th>路由</th><th>父级ID</th><th>级别</th></tr>";
    foreach ($updatedMenus as $menu) {
        echo "<tr>";
        echo "<td>{$menu->id}</td>";
        echo "<td>{$menu->name}</td>";
        echo "<td>{$menu->route_url}</td>";
        echo "<td>{$menu->parent_id}</td>";
        echo "<td>{$menu->level}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 6. 测试链接
    echo "<h3>6. 测试链接</h3>";
    echo "<div class='info'>";
    echo "<p><strong>设置中心:</strong> <a href='/strongadmin/customer-service/settings' target='_blank'>点击测试</a></p>";
    echo "<p><strong>离线留言:</strong> <a href='/strongadmin/customer-service/offline-messages' target='_blank'>点击测试</a></p>";
    echo "<p><strong>会话管理:</strong> <a href='/strongadmin/customer-service/sessions' target='_blank'>点击测试</a></p>";
    echo "</div>";
    
    echo "<div class='success'>🎉 菜单路由修复完成！</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 修复失败: " . $e->getMessage() . "</div>";
    echo "<div class='error'>错误详情: " . $e->getTraceAsString() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
