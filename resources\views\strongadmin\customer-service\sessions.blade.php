@extends('strongadmin::layouts.app')

@push('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@push('styles')
<style>
.cs-container {
    height: calc(100vh - 120px);
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0,0,0,.1);
    overflow: hidden;
    display: flex;
    position: relative;
}

/* 左侧访客列表 */
.cs-sidebar {
    width: 320px;
    border-right: 1px solid #e6e6e6;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.cs-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e6e6e6;
    background: #f8f9fa;
}

.cs-visitor-list {
    flex: 1;
    overflow-y: auto;
}

.cs-visitor-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.cs-visitor-item:hover {
    background: #f8f9fa;
}

.cs-visitor-item:hover .cs-visitor-actions {
    opacity: 1 !important;
}

.cs-visitor-item.active {
    background: #e3f2fd;
    border-right: 3px solid #1E9FFF;
}

/* 头像样式已内联到HTML中 */

.cs-visitor-info {
    /* 信息区域样式已内联到HTML中 */
}

.cs-visitor-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.cs-visitor-message {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cs-visitor-time {
    font-size: 11px;
    color: #999;
    position: absolute;
    top: 15px;
    right: 15px;
}

.cs-visitor-status {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    color: white;
}

.cs-status-active { background: #5FB878; }
.cs-status-waiting { background: #FFB800; }
.cs-status-closed { background: #999; }

/* 角标样式已移至内联样式 */

/* 右侧聊天区域 */
.cs-chat-area {
    flex: 1;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    position: relative !important;
    min-width: 0 !important;
}

.cs-chat-header {
    padding: 20px;
    border-bottom: 1px solid #e6e6e6;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cs-chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #fafafa;
    min-height: 0;
}

.cs-chat-input {
    padding: 20px;
    border-top: 1px solid #e6e6e6;
    background: white;
    flex-shrink: 0;
    position: relative;
    z-index: 10;
}

.cs-message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.cs-message.customer {
    justify-content: flex-start;
}

.cs-message.admin, .cs-message.ai {
    justify-content: flex-end;
}

.cs-message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin: 0 10px;
}

.cs-message.customer .cs-message-avatar {
    background: #e3f2fd;
    color: #1976d2;
}

.cs-message.admin .cs-message-avatar {
    background: #e8f5e8;
    color: #4caf50;
}

.cs-message.ai .cs-message-avatar {
    background: #fff3e0;
    color: #ff9800;
}

.cs-message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
}

.cs-message.customer .cs-message-content {
    background: #e3f2fd;
    color: #1976d2;
}

.cs-message.admin .cs-message-content {
    background: #4caf50;
    color: white;
}

.cs-message.ai .cs-message-content {
    background: #ff9800;
    color: white;
}

.cs-message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 5px;
}

.cs-empty-chat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
}

.cs-input-group {
    display: flex;
    gap: 10px;
}

.cs-message-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    outline: none;
    resize: none;
    min-height: 20px;
    max-height: 100px;
}

.cs-send-btn {
    padding: 12px 20px;
    background: #1E9FFF;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    white-space: nowrap;
}

.cs-send-btn:hover {
    background: #1976d2;
}

.cs-quick-replies {
    margin-bottom: 10px;
}

.cs-quick-reply {
    display: inline-block;
    padding: 5px 12px;
    margin: 3px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.cs-quick-reply:hover {
    background: #1E9FFF;
    color: white;
    border-color: #1E9FFF;
}

/* 会话项hover效果 */
.cs-visitor-item:hover {
    background-color: #f8f9fa !important;
}

.cs-visitor-item.active {
    background-color: #e6f7ff !important;
    border-left: 3px solid #1890ff !important;
}

/* 连接状态样式 */
.status-connected {
    color: #52c41a;
}

.status-polling {
    color: #1890ff;
}

.status-connecting {
    color: #faad14;
}

.status-disconnected {
    color: #ff4d4f;
}

.status-connected::before {
    content: "●";
    margin-right: 4px;
}

.status-connecting::before {
    content: "●";
    margin-right: 4px;
    animation: blink 1s infinite;
}

.status-disconnected::before {
    content: "●";
    margin-right: 4px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}
</style>
@endpush

@section('content')
<div class="st-h15"></div>

<div class="cs-container">
    <!-- 左侧访客列表 -->
    <div class="cs-sidebar">
        <div class="cs-sidebar-header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <h3 style="margin: 0; font-size: 16px;">
                    <i class="layui-icon layui-icon-dialogue"></i> 客服会话
                    <span style="color: #999; font-size: 14px; font-weight: normal;">({{ count($sessions) }})</span>
                </h3>
                <div>
                    <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="refreshSessionList()" title="刷新列表">
                        <i class="layui-icon layui-icon-refresh-3"></i>
                    </button>
                    <button class="layui-btn layui-btn-xs layui-btn-warm" onclick="markAllAsRead()" title="全部标记已读">
                        <i class="layui-icon layui-icon-ok"></i>
                    </button>
                    <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="clearAllSessions()" title="清空所有会话">
                        <i class="layui-icon layui-icon-delete"></i>
                    </button>
                </div>
            </div>
            <div style="font-size: 12px;">
                <span id="connection-status" class="status-connecting">连接中...</span>
                <span style="margin-left: 10px; color: #999;">实时推送</span>
            </div>
        </div>

        <div class="cs-visitor-list">
            @forelse($sessions as $session)
            <div class="cs-visitor-item" data-session-id="{{ $session->id }}" onclick="selectSession({{ $session->id }})" style="padding: 8px 12px; border-bottom: 1px solid #f0f0f0; position: relative; cursor: pointer;">
                <div style="display: flex; align-items: flex-start; gap: 10px;">

                <!-- 头像区域 -->
                <div style="position: relative; flex-shrink: 0; width: 40px; height: 40px;">
                    <div class="cs-visitor-avatar" style="
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        background: #1890ff;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                        font-size: 14px;
                        position: relative;
                    ">
                        {{ strtoupper(substr($session->display_name, 0, 1)) }}
                    </div>
                    @if($session->unread_count > 0)
                        <div class="cs-unread-count" id="unread-{{ $session->id }}" style="
                            position: absolute;
                            top: -5px;
                            right: -5px;
                            background: #ff4d4f;
                            color: white;
                            border-radius: 10px;
                            min-width: 18px;
                            height: 18px;
                            font-size: 11px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                            border: 2px solid white;
                            z-index: 100;
                            box-sizing: border-box;
                            line-height: 1;
                            padding: 0 4px;
                            text-align: center;
                            font-family: Arial, sans-serif;
                            white-space: nowrap;
                        ">
                            {{ $session->unread_count > 99 ? '99+' : $session->unread_count }}
                        </div>
                    @endif
                </div>

                <!-- 主要信息区域 -->
                <div style="flex: 1; min-width: 0; overflow: hidden;">
                    <!-- 第一行：姓名 + 状态 + 时间 -->
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 2px;">
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <span style="font-weight: 500; color: #333; font-size: 14px;">{{ $session->display_name }}</span>
                            <span style="font-size: 10px; padding: 1px 4px; border-radius: 8px; font-weight: bold;">
                                @if($session->status == 'online')
                                    <span class="session-status-indicator" style="background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f;">🟢</span>
                                @else
                                    <span class="session-status-indicator" style="background: #f5f5f5; color: #999; border: 1px solid #d9d9d9;">⚫</span>
                                @endif
                            </span>
                        </div>
                        <span style="color: #999; font-size: 11px; flex-shrink: 0;">
                            {{ $session->latest_message_time ? \Carbon\Carbon::parse($session->latest_message_time)->format('H:i') : '' }}
                        </span>
                    </div>

                    <!-- 第二行：最新消息 -->
                    <div style="color: #666; font-size: 12px; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        {{ $session->latest_message }}
                    </div>

                    <!-- 第三行：地理位置和IP -->
                    <div style="display: flex; flex-direction: column; gap: 1px;">
                        @if($session->geo_city || $session->geo_region || $session->geo_country)
                            <div style="color: #888; font-size: 10px;">
                                📍 {{ $session->geo_country ?? '未知' }}{{ $session->geo_region ? '-'.$session->geo_region : '' }}{{ $session->geo_city ? '-'.$session->geo_city : '' }}
                            </div>
                        @endif
                        <div style="color: #999; font-size: 9px;">
                            IP: {{ $session->visitor_ip ?? '未知' }}
                            @if($session->last_seen)
                                | 最后在线: {{ \Carbon\Carbon::parse($session->last_seen)->format('H:i') }}
                            @endif
                        </div>
                    </div>
                </div>
                </div>

                <!-- 删除按钮 -->
                <div class="cs-visitor-actions" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); opacity: 0; transition: opacity 0.2s;">
                    <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteSession({{ $session->id }}, event)" title="删除会话">
                        <i class="layui-icon layui-icon-delete"></i>
                    </button>
                </div>
            </div>
            @empty
            <div style="text-align: center; padding: 40px; color: #999;">
                <i class="layui-icon layui-icon-face-cry" style="font-size: 48px; display: block; margin-bottom: 10px;"></i>
                <p>暂无客服会话</p>
            </div>
            @endforelse
        </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="cs-chat-area">
        <!-- 聊天头部 -->
        <div class="cs-chat-header" id="chat-header" style="display: none;">
            <div>
                <h4 style="margin: 0;" id="current-visitor-name">选择一个访客开始聊天</h4>
                <small id="current-visitor-info" style="color: #666;"></small>
            </div>
            <div>
                <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="refreshMessages()">
                    <i class="layui-icon layui-icon-refresh-3"></i> 刷新
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="toggleCustomerInfo()">
                    <i class="layui-icon layui-icon-user"></i> 客户信息
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="toggleQuickRepliesPanel()">
                    <i class="layui-icon layui-icon-template-1"></i> 快捷回复
                </button>
                <button class="layui-btn layui-btn-sm" onclick="toggleNotifications()" id="notification-toggle">
                    <i class="layui-icon layui-icon-notice"></i> <span id="notification-status">通知</span>
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="closeSession()">
                    <i class="layui-icon layui-icon-close"></i> 关闭会话
                </button>
            </div>
        </div>

        <!-- 主聊天区域 -->
        <div style="display: flex; flex: 1; min-height: 0;">
            <!-- 消息区域 -->
            <div class="cs-chat-messages" id="chat-messages" style="flex: 1; display: flex; flex-direction: column;">
                <div class="cs-empty-chat" style="flex: 1; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="layui-icon layui-icon-dialogue" style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;"></i>
                    <h4 style="margin: 0; color: #999;">选择左侧访客开始聊天</h4>
                    <p style="color: #ccc; margin-top: 10px;">点击左侧访客列表中的任意访客，即可开始对话</p>
                </div>
            </div>

            <!-- 客户信息面板 -->
            <div class="cs-customer-info" id="customer-info-panel" style="width: 320px; border-left: 1px solid #e6e6e6; background: #fafafa; display: none; overflow-y: auto;">
                <div style="padding: 15px; border-bottom: 1px solid #e6e6e6; background: white;">
                    <h4 style="margin: 0 0 10px 0; color: #333; display: flex; align-items: center; justify-content: space-between;">
                        <span><i class="layui-icon layui-icon-user"></i> 客户信息</span>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="toggleCustomerInfo()">
                            <i class="layui-icon layui-icon-close"></i>
                        </button>
                    </h4>
                </div>

                <div style="padding: 15px;">
                    <!-- 基本信息 -->
                    <div class="info-section">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">基本信息</h5>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">访客名称：</label>
                            <span id="customer-name" style="color: #333;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">邮箱：</label>
                            <span id="customer-email" style="color: #333;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">IP地址：</label>
                            <span id="customer-ip" style="color: #333;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">地理位置：</label>
                            <span id="customer-location" style="color: #333;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">浏览器：</label>
                            <span id="customer-browser" style="color: #333; word-break: break-all;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">设备类型：</label>
                            <span id="customer-device" style="color: #333;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">会话时间：</label>
                            <span id="session-time" style="color: #333;">-</span>
                        </div>
                        <div class="info-item" style="margin-bottom: 8px; font-size: 12px;">
                            <label style="color: #999; width: 70px; display: inline-block;">访问来源：</label>
                            <span id="customer-referrer" style="color: #333;">-</span>
                        </div>
                    </div>

                    <!-- 会话备注 -->
                    <div class="info-section" style="margin-top: 20px;">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">会话备注</h5>
                        <textarea id="session-notes" placeholder="添加内部备注..." style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 12px; box-sizing: border-box;"></textarea>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="saveSessionNotes()" style="margin-top: 8px;">
                            <i class="layui-icon layui-icon-ok"></i> 保存备注
                        </button>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="info-section" style="margin-top: 20px;">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">快捷操作</h5>
                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                            <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="markAsResolved()">
                                <i class="layui-icon layui-icon-ok-circle"></i> 已解决
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-warm" onclick="transferSession()">
                                <i class="layui-icon layui-icon-transfer"></i> 转接
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="closeSessionConfirm()">
                                <i class="layui-icon layui-icon-close"></i> 关闭
                            </button>
                        </div>
                    </div>

                    <!-- 会话统计 -->
                    <div class="info-section" style="margin-top: 20px;">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">会话统计</h5>
                        <div style="font-size: 12px;">
                            <div style="margin-bottom: 5px;">
                                <span style="color: #999;">消息数量：</span>
                                <span id="message-count" style="color: #333;">0</span>
                            </div>
                            <div style="margin-bottom: 5px;">
                                <span style="color: #999;">响应时间：</span>
                                <span id="response-time" style="color: #333;">-</span>
                            </div>
                            <div style="margin-bottom: 5px;">
                                <span style="color: #999;">会话状态：</span>
                                <span id="session-status" style="color: #333;">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷回复面板 -->
            <div class="cs-quick-replies-panel" id="quick-replies-panel" style="width: 300px; border-left: 1px solid #e6e6e6; background: #fafafa; display: none; overflow-y: auto;">
                <div style="padding: 15px; border-bottom: 1px solid #e6e6e6; background: white;">
                    <h4 style="margin: 0 0 10px 0; color: #333; display: flex; align-items: center; justify-content: space-between;">
                        <span><i class="layui-icon layui-icon-template-1"></i> 快捷回复</span>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="toggleQuickRepliesPanel()">
                            <i class="layui-icon layui-icon-close"></i>
                        </button>
                    </h4>
                </div>

                <div style="padding: 15px;">
                    <!-- 常用回复 -->
                    <div class="quick-reply-section">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">常用回复</h5>
                        <div class="quick-reply-list" id="common-replies">
                            <div class="quick-reply-item" onclick="insertQuickReply('您好！我是客服，有什么可以帮助您的吗？')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">问候语</div>
                                <div style="color: #666;">您好！我是客服，有什么可以帮助您的吗？</div>
                            </div>
                            <div class="quick-reply-item" onclick="insertQuickReply('请稍等，我为您查询一下相关信息...')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">查询中</div>
                                <div style="color: #666;">请稍等，我为您查询一下相关信息...</div>
                            </div>
                            <div class="quick-reply-item" onclick="insertQuickReply('感谢您的咨询，还有其他问题需要帮助吗？')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">感谢</div>
                                <div style="color: #666;">感谢您的咨询，还有其他问题需要帮助吗？</div>
                            </div>
                            <div class="quick-reply-item" onclick="insertQuickReply('如有其他问题，随时联系我们！祝您生活愉快！')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">结束语</div>
                                <div style="color: #666;">如有其他问题，随时联系我们！祝您生活愉快！</div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品相关 -->
                    <div class="quick-reply-section" style="margin-top: 20px;">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">产品相关</h5>
                        <div class="quick-reply-list">
                            <div class="quick-reply-item" onclick="insertQuickReply('我们的产品支持7天无理由退换，请提供您的订单号，我来为您处理。')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">退换货</div>
                                <div style="color: #666;">我们的产品支持7天无理由退换，请提供您的订单号...</div>
                            </div>
                            <div class="quick-reply-item" onclick="insertQuickReply('我们提供多种配送方式，标准配送3-5个工作日，加急配送1-2个工作日到达。')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">配送信息</div>
                                <div style="color: #666;">我们提供多种配送方式，标准配送3-5个工作日...</div>
                            </div>
                            <div class="quick-reply-item" onclick="insertQuickReply('请您提供订单号，我来为您查询订单状态和物流信息。')" style="padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s;" onmouseover="this.style.background='#f0f9ff'; this.style.borderColor='#1890ff';" onmouseout="this.style.background='white'; this.style.borderColor='#e6e6e6';">
                                <div style="font-weight: bold; color: #333; margin-bottom: 4px;">订单查询</div>
                                <div style="color: #666;">请您提供订单号，我来为您查询订单状态和物流信息。</div>
                            </div>
                        </div>
                    </div>

                    <!-- 自定义回复 -->
                    <div class="quick-reply-section" style="margin-top: 20px;">
                        <h5 style="margin: 0 0 10px 0; color: #666; font-size: 13px; font-weight: bold;">自定义回复</h5>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="addCustomReply()" style="width: 100%;">
                            <i class="layui-icon layui-icon-add-1"></i> 添加自定义回复
                        </button>
                        <div id="custom-replies" style="margin-top: 10px;">
                            <!-- 自定义回复将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="cs-chat-input" id="chat-input" style="display: none;">
            <!-- 表情包选择器 -->
            <div id="admin-emoji-picker" style="display: none; background: white; border: 1px solid #e6e6e6; border-radius: 8px; padding: 15px; margin-bottom: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); max-height: 200px; overflow-y: auto;">
                <div style="display: grid; grid-template-columns: repeat(10, 1fr); gap: 8px;">
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😀')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😀</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😃')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😃</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😄')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😄</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😁')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😁</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😊')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😊</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😍')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😍</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🥰')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🥰</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😘')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😘</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😗')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😗</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😙')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😙</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😚')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😚</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🙂')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🙂</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤗')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤗</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤔')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤔</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😐')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😐</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😑')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😑</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😶')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😶</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😏')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😏</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😒')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😒</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🙄')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🙄</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😬')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😬</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤥')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤥</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😌')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😌</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😔')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😔</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😪')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😪</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤤')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤤</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😴')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😴</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('😷')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">😷</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤒')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤒</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤕')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤕</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('👍')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">👍</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('👎')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">👎</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('👌')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">👌</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('✌️')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">✌️</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🤞')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🤞</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('👏')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">👏</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🙌')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🙌</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🙏')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🙏</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('❤️')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">❤️</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('💛')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">💛</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('💚')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">💚</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('💙')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">💙</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('💜')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">💜</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('💯')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">💯</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🔥')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🔥</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('⭐')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">⭐</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('🌟')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">🌟</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('✨')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">✨</span>
                    <span class="admin-emoji-item" onclick="insertAdminEmoji('⚡')" style="cursor: pointer; font-size: 20px; text-align: center; padding: 5px; border-radius: 4px; transition: background 0.2s;" onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">⚡</span>
                </div>
            </div>

            <!-- 快捷回复 -->
            <div class="cs-quick-replies">
                <span style="font-size: 12px; color: #666;">快捷回复：</span>
                <span class="cs-quick-reply" onclick="insertQuickReply('您好！我是客服，有什么可以帮助您的吗？')">问候</span>
                <span class="cs-quick-reply" onclick="insertQuickReply('请稍等，我为您查询一下...')">查询中</span>
                <span class="cs-quick-reply" onclick="insertQuickReply('感谢您的咨询，还有其他问题吗？')">感谢</span>
                <span class="cs-quick-reply" onclick="insertQuickReply('如有其他问题，随时联系我们！')">结束</span>
            </div>

            <!-- 工具栏 -->
            <div style="display: flex; gap: 8px; margin-bottom: 10px; align-items: center;">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="toggleAdminEmojiPicker()" title="表情包">
                    <i class="layui-icon layui-icon-face-smile"></i> 😀
                </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="selectAdminImage()" title="发送图片">
                    <i class="layui-icon layui-icon-picture"></i> 图片
                </button>
                <input type="file" id="admin-image-input" accept="image/*" style="display: none;" onchange="handleAdminImageSelect(event)">
                <div id="admin-upload-progress" style="display: none; margin-left: 10px;">
                    <span style="font-size: 12px; color: #666;">上传中...</span>
                </div>
            </div>

            <form method="POST" id="reply-form" onsubmit="return sendMessage(event)">
                @csrf
                <input type="hidden" id="current-session-id" name="session_id" value="">
                <input type="hidden" id="message-type" name="message_type" value="text">
                <input type="hidden" id="image-url" name="image_url" value="">
                <div class="cs-input-group">
                    <textarea class="cs-message-input" name="message" id="message-input" placeholder="输入回复消息..." rows="1" required></textarea>
                    <button type="submit" class="cs-send-btn">
                        <i class="layui-icon layui-icon-release"></i> 发送
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentSessionId = null;
let sessions = @json($sessionsArray ?? []);
let lastMessageId = 0;
let autoRefreshInterval = null;
let eventSource = null;
let lastGlobalMessageId = 0;

// 调试信息已移除以提升性能

// 请求通知权限
function requestNotificationPermission() {
    if ("Notification" in window) {
        if (Notification.permission === "default") {
            Notification.requestPermission().then(function (permission) {
                if (permission === "granted") {
                    // 显示欢迎通知
                    new Notification("客服系统", {
                        body: "通知已启用，您将收到新消息提醒",
                        icon: '/favicon.ico',
                        tag: 'welcome'
                    });
                }
            });
        }
    }
}

// 选择会话
function selectSession(sessionId) {
    currentSessionId = sessionId;

    // 更新访客列表选中状态
    document.querySelectorAll('.cs-visitor-item').forEach(item => {
        item.classList.remove('active');
    });
    const selectedItem = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (selectedItem) {
        selectedItem.classList.add('active');

    } else {
        console.error('Could not find item with session-id:', sessionId);
    }

    // 获取会话信息
    let session = null;

    // 确保 sessions 是数组
    if (!Array.isArray(sessions)) {
        console.error('Sessions is not an array:', typeof sessions);
        sessions = [];
    }

    // 查找会话
    session = sessions.find(s => s && s.id == sessionId);

    if (!session) {
        console.error('Session not found:', sessionId);
        console.error('Available sessions:', sessions.map(s => s ? s.id : 'null'));
        // 如果找不到会话，尝试从DOM获取基本信息
        const visitorItem = document.querySelector(`[data-session-id="${sessionId}"]`);
        if (visitorItem) {
            const visitorName = visitorItem.querySelector('.cs-visitor-name')?.textContent || '未知访客';
            session = {
                id: sessionId,
                display_name: visitorName,
                contact_info: '点击加载详情',
                status: 'active',
                session_id: sessionId
            };
        } else {
            return;
        }
    }


    // 显示聊天头部和输入区域
    document.getElementById('chat-header').style.display = 'flex';
    document.getElementById('chat-input').style.display = 'block';
    document.getElementById('current-session-id').value = sessionId;

    // 更新头部信息
    document.getElementById('current-visitor-name').textContent = session.display_name || '未知访客';
    document.getElementById('current-visitor-info').textContent =
        `${session.contact_info || '无联系信息'} | 状态: ${getStatusText(session.status)} | 会话ID: ${session.session_id || sessionId}`;

    // 获取最新的会话信息并更新客户信息面板
    fetchLatestSessionInfo(sessionId);

    // 立即标记该会话为已读（移除小红点）
    markSessionAsRead(sessionId);

    // 加载消息
    loadMessages(sessionId);

    // 立即标记消息为已读，清除红点
    markMessagesAsRead(sessionId);

    // 清除当前访客的未读标识
    const currentVisitorItem = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (currentVisitorItem) {
        const unreadBadge = currentVisitorItem.querySelector('.cs-unread-count');
        if (unreadBadge) {
            unreadBadge.remove();
        }
    }

    // 开始自动刷新当前会话
    startAutoRefresh();
}

// 获取状态文本
function getStatusText(status) {
    switch(status) {
        case 'active': return '活跃';
        case 'waiting': return '等待中';
        case 'closed': return '已关闭';
        default: return '未知';
    }
}

// 加载消息
function loadMessages(sessionId) {
    const messagesContainer = document.getElementById('chat-messages');
    messagesContainer.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...</div>';

    // 修复路径问题
    const url = window.location.origin + '/strongadmin/customer-service/session/' + sessionId + '/messages';


    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {

        return response.json();
    })
    .then(data => {

        if (data.success) {
            displayMessages(data.messages);
        } else {
            messagesContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;">加载消息失败: ' + (data.error || '未知错误') + '</div>';
        }
    })
    .catch(error => {
        console.error('Error loading messages:', error);
        messagesContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;">加载消息失败: ' + error.message + '</div>';
    });
}

// 格式化消息内容
function formatMessageContent(message) {


    // 优先检查是否是大括号包围的图片URL格式 {url}
    if (message.message && typeof message.message === 'string') {
        const bracketImageMatch = message.message.match(/^\{(.+)\}$/);
        if (bracketImageMatch) {
            const imageUrl = bracketImageMatch[1];

            return `
                <div class="message-image">
                    <img src="${imageUrl}"
                         alt="图片消息"
                         style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                         onclick="window.open('${imageUrl}', '_blank')">
                </div>
            `;
        }
    }

    if (message.message_type === 'image' && message.image_url) {
        return `
            <div class="message-image">
                <img src="${message.image_url}"
                     alt="图片消息"
                     style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                     onclick="window.open('${message.image_url}', '_blank')">
            </div>
        `;
    } else if (message.message_type === 'image' || isAdminImageUrl(message.message)) {
        // 如果消息内容本身就是图片URL
        return `
            <div class="message-image">
                <img src="${message.message}"
                     alt="图片消息"
                     style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                     onclick="window.open('${message.message}', '_blank')">
            </div>
        `;
    } else {
        // 普通文本消息，转义HTML并处理换行
        return escapeHtml(message.message).replace(/\n/g, '<br>');
    }
}

// 判断是否是图片URL
function isAdminImageUrl(url) {
    if (!url || typeof url !== 'string') return false;

    // 检查是否是HTTP/HTTPS URL且包含图片扩展名
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerUrl = url.toLowerCase();

    return (url.startsWith('http://') || url.startsWith('https://') ||
            url.startsWith('/storage/') || url.startsWith('/uploads/')) &&
           imageExtensions.some(ext => lowerUrl.includes(ext));
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示消息
function displayMessages(messages) {
    const messagesContainer = document.getElementById('chat-messages');

    if (!messages || messages.length === 0) {
        messagesContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #999;">
                <i class="layui-icon layui-icon-dialogue" style="font-size: 48px; margin-bottom: 10px; opacity: 0.3;"></i>
                <p>暂无消息记录</p>
                <p style="font-size: 12px; color: #ccc;">开始与客户对话吧！</p>
            </div>
        `;
        return;
    }

    let html = '';
    messages.forEach(message => {
        const time = new Date(message.created_at).toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        html += `
            <div class="cs-message ${message.sender_type}">
                ${message.sender_type === 'customer' ? `
                    <div class="cs-message-avatar">
                        <i class="layui-icon layui-icon-username"></i>
                    </div>
                    <div class="cs-message-content">
                        ${formatMessageContent(message)}
                        <div class="cs-message-time">${time}</div>
                    </div>
                ` : `
                    <div class="cs-message-content">
                        ${formatMessageContent(message)}
                        <div class="cs-message-time">
                            ${message.sender_type === 'ai' ? 'AI回复' : '客服'} · ${time}
                        </div>
                    </div>
                    <div class="cs-message-avatar">
                        <i class="layui-icon ${message.sender_type === 'ai' ? 'layui-icon-face-smile' : 'layui-icon-service'}"></i>
                    </div>
                `}
            </div>
        `;
    });

    messagesContainer.innerHTML = html;
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 发送图片消息
function sendImageMessage(imageUrl) {
    if (!imageUrl || !currentSessionId) {
        return;
    }

    // 确保使用相对路径
    const cleanImageUrl = imageUrl.startsWith('http') ?
        new URL(imageUrl).pathname : imageUrl;

    const formData = new FormData();
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_token"]')?.value ||
                     '{{ csrf_token() }}';

    formData.append('_token', csrfToken);
    formData.append('session_id', currentSessionId);
    formData.append('message', cleanImageUrl);
    formData.append('message_type', 'image');
    formData.append('image_url', cleanImageUrl);

    fetch('/strongadmin/customer-service/send-message', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 消息发送成功，刷新消息列表
            loadMessages(currentSessionId);
        } else {
            layer.msg('图片消息发送失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('发送图片消息失败:', error);
        layer.msg('图片消息发送失败，请重试');
    });
}

// 发送消息
function sendMessage(event) {
    event.preventDefault();

    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();

    if (!message || !currentSessionId) {
        layer.msg('请输入消息内容');
        return false;
    }

    const formData = new FormData();
    // 安全获取CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_token"]')?.value ||
                     '{{ csrf_token() }}';
    formData.append('_token', csrfToken);
    formData.append('message', message);

    const url = window.location.origin + '/strongadmin/customer-service/session/' + currentSessionId + '/reply';


    fetch(url, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageInput.value = '';
            loadMessages(currentSessionId);
            layer.msg('发送成功');
        } else {
            layer.msg('发送失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        layer.msg('发送失败，请重试');
    });

    return false;
}

// 插入快捷回复
function insertQuickReply(text) {
    document.getElementById('message-input').value = text;
    document.getElementById('message-input').focus();
}

// 刷新消息
function refreshMessages() {
    if (currentSessionId) {
        loadMessages(currentSessionId);
    }
}

// 关闭会话
function closeSession() {
    if (!currentSessionId) return;

    layer.confirm('确定要关闭这个会话吗？', {
        icon: 3,
        title: '提示'
    }, function(index){
        // TODO: 实现关闭会话功能
        layer.msg('功能开发中...');
        layer.close(index);
    });
}

// 切换通知设置
function toggleNotifications() {
    const isEnabled = localStorage.getItem('cs_notifications_enabled') !== 'false';
    const newState = !isEnabled;

    localStorage.setItem('cs_notifications_enabled', newState);
    updateNotificationButton();

    if (newState) {
        // 启用通知时请求权限
        requestNotificationPermission();
        layer.msg('✅ 通知已启用');
    } else {
        layer.msg('🔕 通知已关闭');
    }
}

// 更新通知按钮状态
function updateNotificationButton() {
    const isEnabled = localStorage.getItem('cs_notifications_enabled') !== 'false';
    const button = document.getElementById('notification-toggle');
    const status = document.getElementById('notification-status');

    if (button && status) {
        if (isEnabled) {
            button.className = 'layui-btn layui-btn-sm layui-btn-normal';
            status.textContent = '通知开';
        } else {
            button.className = 'layui-btn layui-btn-sm layui-btn-primary';
            status.textContent = '通知关';
        }
    }
}

// 检查是否启用通知
function isNotificationEnabled() {
    return localStorage.getItem('cs_notifications_enabled') !== 'false';
}

// 启动实时更新（SSE）
function startRealtimeUpdates() {
    // 暂时禁用SSE，避免连接错误

    updateConnectionStatus('polling'); // 显示为定时刷新模式
    return;

    // 关闭现有连接
    if (eventSource) {
        eventSource.close();
    }

    try {
        // 获取最新消息ID
        const url = '/strongadmin/customer-service/message-stream?last_message_id=' + lastGlobalMessageId;


        eventSource = new EventSource(url);

        eventSource.onopen = function(event) {

            updateConnectionStatus(true);
        };

        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);


                if (data.type === 'new_message') {
                    handleNewMessage(data);
                    lastGlobalMessageId = Math.max(lastGlobalMessageId, data.message_id);
                } else if (data.type === 'heartbeat') {

                }
            } catch (e) {
                console.error('解析SSE消息失败:', e);
            }
        };

        eventSource.onerror = function(event) {
            console.error('❌ SSE连接错误:', event);
            updateConnectionStatus(false);

            // 关闭连接，避免无限重连
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
        };
    } catch (error) {
        console.error('❌ 创建SSE连接失败:', error);
        updateConnectionStatus(false);
    }
}

// 处理新消息
function handleNewMessage(data) {


    // 1. 更新会话列表中的最新消息
    updateSessionInList(data);

    // 2. 如果正在查看该会话，刷新消息
    if (currentSessionId == data.session_id) {
        loadMessages(currentSessionId);
    }

    // 3. 播放通知（如果不是当前查看的会话）
    if (currentSessionId != data.session_id) {
        playNotificationSound();
        showNewMessageNotification(data);
    }

    // 4. 更新页面标题
    updatePageTitle();
}

// 更新会话列表中的会话信息
function updateSessionInList(data) {
    const visitorItem = document.querySelector(`[data-session-id="${data.session_id}"]`);
    if (visitorItem) {
        // 更新最新消息
        const messageElement = visitorItem.querySelector('.cs-visitor-message');
        if (messageElement) {
            const shortMessage = data.message.length > 30 ?
                                data.message.substring(0, 30) + '...' :
                                data.message;
            messageElement.textContent = shortMessage;
        }

        // 更新时间
        const timeElement = visitorItem.querySelector('.cs-visitor-time');
        if (timeElement) {
            const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            timeElement.textContent = time;
        }

        // 更新未读数量
        if (currentSessionId != data.session_id) {
            updateSessionUnreadCount(data.session_id, '+1');
        }

        // 移动到列表顶部
        const parentList = visitorItem.parentNode;
        parentList.insertBefore(visitorItem, parentList.firstChild);
    } else {
        // 新会话，刷新整个列表
        setTimeout(autoRefreshSessionList, 1000);
    }
}

// 显示新消息通知
function showNewMessageNotification(data) {
    if (!isNotificationEnabled()) return;

    // 桌面通知
    if (Notification.permission === "granted") {
        const notification = new Notification("新客服消息", {
            body: `${data.visitor_name}: ${data.message}`,
            icon: '/favicon.ico',
            tag: 'cs-message-' + data.session_id,
            requireInteraction: false
        });

        notification.onclick = function() {
            window.focus();
            selectSession(data.session_id);
            notification.close();
        };

        setTimeout(() => notification.close(), 5000);
    }
}

// 更新连接状态
function updateConnectionStatus(status) {
    // 可以在页面上显示连接状态指示器
    const indicator = document.getElementById('connection-status');
    if (indicator) {
        if (status === 'polling') {
            indicator.className = 'status-polling';
            indicator.textContent = '定时刷新';
        } else if (status === true || status === 'connected') {
            indicator.className = 'status-connected';
            indicator.textContent = '已连接';
        } else {
            indicator.className = 'status-disconnected';
            indicator.textContent = '连接中...';
        }
    }
}

// 更新页面标题
function updatePageTitle() {
    const unreadCount = document.querySelectorAll('.cs-unread-count').length;
    if (unreadCount > 0) {
        document.title = `(${unreadCount}) 客服管理 - 有新消息`;
    } else {
        document.title = '客服管理';
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化客服系统

    // 请求通知权限
    requestNotificationPermission();

    // 初始化通知按钮状态
    updateNotificationButton();

    // 启动SSE实时推送
    startRealtimeUpdates();

    // 绑定访客列表点击事件
    document.querySelectorAll('.cs-visitor-item').forEach(item => {
        const sessionId = item.getAttribute('data-session-id');


        item.addEventListener('click', function() {

            selectSession(sessionId);
        });
    });

    // 回车发送消息
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.getElementById('reply-form').dispatchEvent(new Event('submit'));
            }
        });
    }



    // 如果URL中有会话ID参数，自动选择该会话
    const urlParams = new URLSearchParams(window.location.search);
    const autoSelectSessionId = urlParams.get('session_id');
    if (autoSelectSessionId && sessions.find(s => s.id == autoSelectSessionId)) {
        setTimeout(() => {
            selectSession(autoSelectSessionId);
        }, 500);
    }

    // 启动定时刷新未读数量（不刷新整个页面）
    setInterval(refreshUnreadCounts, 2000); // 每2秒刷新一次未读数量和在线状态

    // 请求桌面通知权限
    requestAdminNotificationPermission();

    // 页面获得焦点时停止标题闪烁
    window.addEventListener('focus', () => {
        if (titleFlashInterval) {
            clearInterval(titleFlashInterval);
            titleFlashInterval = null;
            document.title = originalTitle;
        }
    });
});

// 后台声音提醒相关函数
function playAdminNotificationSound() {
    try {
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 创建提示音（双音调）
        const oscillator1 = audioContext.createOscillator();
        const oscillator2 = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator1.connect(gainNode);
        oscillator2.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 设置音调
        oscillator1.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator2.frequency.setValueAtTime(1000, audioContext.currentTime);

        // 设置音量（降低音量，避免过于刺耳）
        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.005, audioContext.currentTime + 0.2);

        // 播放
        oscillator1.start(audioContext.currentTime);
        oscillator2.start(audioContext.currentTime);
        oscillator1.stop(audioContext.currentTime + 0.3);
        oscillator2.stop(audioContext.currentTime + 0.3);


    } catch (error) {
        console.error('播放提示音失败:', error);

        // 降级方案：使用系统提示音
        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.play();
        } catch (e) {
            console.error('降级提示音也失败:', e);
        }
    }
}

function requestAdminNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
            }
        });
    }
}

function showAdminDesktopNotification(title, message) {
    if (!('Notification' in window)) {
        return;
    }

    if (Notification.permission === 'granted') {
        const notification = new Notification(title, {
            body: message,
            icon: '/images/customer-service-avatar.png',
            badge: '/images/customer-service-avatar.png',
            tag: 'admin-customer-message',
            requireInteraction: true, // 需要用户交互才关闭
            silent: false
        });

        notification.onclick = function() {
            window.focus();
            notification.close();
        };

        // 5秒后自动关闭
        setTimeout(() => {
            notification.close();
        }, 5000);
    }
}

let originalTitle = document.title;
let titleFlashInterval = null;

function flashBrowserTitle() {
    if (titleFlashInterval) {
        clearInterval(titleFlashInterval);
    }

    let isOriginal = true;
    titleFlashInterval = setInterval(() => {
        document.title = isOriginal ? '🔔 新消息 - ' + originalTitle : originalTitle;
        isOriginal = !isOriginal;
    }, 1000);

    // 10秒后停止闪烁
    setTimeout(() => {
        if (titleFlashInterval) {
            clearInterval(titleFlashInterval);
            titleFlashInterval = null;
            document.title = originalTitle;
        }
    }, 10000);
}

// 开始自动刷新当前会话消息
function startAutoRefresh() {
    // 清除之前的定时器
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    // 每3秒检查新消息
    autoRefreshInterval = setInterval(function() {
        if (currentSessionId) {
            checkNewMessages();
        }
    }, 3000);
}

// 检查新消息
function checkNewMessages() {
    if (!currentSessionId) return;

    const url = window.location.origin + '/strongadmin/customer-service/session/' + currentSessionId + '/messages';

    // 使用AbortController来避免异步错误
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        },
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.messages) {
            const currentMessages = document.querySelectorAll('.cs-message').length;
            if (data.messages.length > currentMessages) {
                // 有新消息，重新显示
                displayMessages(data.messages);
                // 播放提示音和通知
                playNotificationSound();
                // 如果当前正在查看这个会话，延迟标记为已读
                if (currentSessionId) {
                    setTimeout(() => {
                        markMessagesAsRead(currentSessionId);
                    }, 2000); // 延迟2秒标记为已读，让用户看到新消息
                }
            }
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        if (error.name !== 'AbortError') {
            console.error('Error checking new messages:', error);
        }
    });
}

// 自动刷新会话列表（内部使用）
function autoRefreshSessionList() {
    fetch(window.location.href, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 解析返回的HTML，更新会话列表
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newSessionList = doc.querySelector('.cs-visitor-list');
        if (newSessionList) {
            const currentList = document.querySelector('.cs-visitor-list');

            // 保存当前选中的会话ID，避免刷新后丢失选中状态
            const currentSelectedId = currentSessionId;

            currentList.innerHTML = newSessionList.innerHTML;

            // 重新绑定点击事件
            bindVisitorClickEvents();

            // 恢复选中状态，但不显示未读标识（因为正在查看）
            if (currentSelectedId) {
                const selectedItem = document.querySelector(`[data-session-id="${currentSelectedId}"]`);
                if (selectedItem) {
                    selectedItem.classList.add('active');
                    // 移除当前查看会话的未读标识
                    const unreadBadge = selectedItem.querySelector('.cs-unread-count');
                    if (unreadBadge) {
                        unreadBadge.remove();
                    }
                }
            }
        }
    })
    .catch(error => {
        console.error('Error refreshing session list:', error);
    });
}

// 重新绑定访客点击事件
function bindVisitorClickEvents() {
    document.querySelectorAll('.cs-visitor-item').forEach(item => {
        const sessionId = item.getAttribute('data-session-id');
        item.addEventListener('click', function() {
            selectSession(sessionId);
        });
    });
}

// 播放提示音和显示通知
function playNotificationSound() {
    // 检查用户是否启用了通知
    if (!isNotificationEnabled()) {

        return;
    }

    // 1. 播放声音提示
    playAudioNotification();

    // 2. 显示浏览器通知
    showBrowserNotification();

    // 3. 闪烁页面标题
    flashPageTitle();

    // 4. 震动提示（移动设备）
    if (navigator.vibrate) {
        navigator.vibrate([200, 100, 200]);
    }
}

// 播放音频提示（简化版，避免AudioContext错误）
function playAudioNotification() {
    try {
        // 只使用简单的音频文件，避免AudioContext
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.volume = 0.3;
        audio.play().catch(() => {
            // 音频播放失败
        });
    } catch (e) {
        // 音频不可用
    }
}

// 移除复杂的AudioContext代码，避免错误
// function playGeneratedSound() {
//     // 已移除，使用简化的音频提示
// }

// 显示浏览器通知
function showBrowserNotification() {
    // 检查浏览器是否支持通知
    if (!("Notification" in window)) {

        return;
    }

    // 检查通知权限
    if (Notification.permission === "granted") {
        createNotification();
    } else if (Notification.permission !== "denied") {
        // 请求通知权限
        Notification.requestPermission().then(function (permission) {
            if (permission === "granted") {
                createNotification();
            }
        });
    }
}

// 创建通知
function createNotification() {
    const visitorName = document.getElementById('current-visitor-name')?.textContent || '访客';

    const notification = new Notification("新客服消息", {
        body: `${visitorName} 发送了新消息`,
        icon: '/favicon.ico', // 使用网站图标
        badge: '/favicon.ico',
        tag: 'customer-service', // 防止重复通知
        requireInteraction: true, // 需要用户交互才消失
        actions: [
            {
                action: 'reply',
                title: '立即回复'
            },
            {
                action: 'close',
                title: '关闭'
            }
        ]
    });

    // 点击通知时聚焦到窗口
    notification.onclick = function() {
        window.focus();
        notification.close();
    };

    // 5秒后自动关闭
    setTimeout(() => {
        notification.close();
    }, 5000);
}

// 闪烁页面标题
function flashPageTitle() {
    const originalTitle = document.title;
    let isFlashing = true;
    let flashCount = 0;
    const maxFlashes = 10;

    const flashInterval = setInterval(() => {
        if (flashCount >= maxFlashes) {
            document.title = originalTitle;
            clearInterval(flashInterval);
            return;
        }

        document.title = isFlashing ? "🔔 新消息！" : originalTitle;
        isFlashing = !isFlashing;
        flashCount++;
    }, 800);

    // 当用户聚焦窗口时停止闪烁
    const stopFlashing = () => {
        document.title = originalTitle;
        clearInterval(flashInterval);
        window.removeEventListener('focus', stopFlashing);
    };

    window.addEventListener('focus', stopFlashing);
}

// 标记消息为已读
function markMessagesAsRead(sessionId) {


    fetch(window.location.origin + '/strongadmin/customer-service/session/' + sessionId + '/mark-read', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
    
            // 更新本地会话数据
            updateSessionUnreadCount(sessionId, 0);
        } else {
            console.error('Failed to mark messages as read:', data.error);
        }
    })
    .catch(error => {
        console.error('Error marking messages as read:', error);
    });
}

// 更新会话的未读数量
function updateSessionUnreadCount(sessionId, count) {
    // 更新本地sessions数据
    const sessionIndex = sessions.findIndex(s => s.id == sessionId);
    let newCount = 0;

    if (sessionIndex !== -1) {
        if (typeof count === 'string' && count.startsWith('+')) {
            // 增量更新
            const increment = parseInt(count.substring(1));
            newCount = (sessions[sessionIndex].unread_count || 0) + increment;
        } else {
            // 绝对值更新
            newCount = parseInt(count) || 0;
        }
        sessions[sessionIndex].unread_count = newCount;
    } else {
        newCount = typeof count === 'string' && count.startsWith('+') ? 1 : (parseInt(count) || 0);
    }

    // 更新DOM中的未读标识
    const visitorItem = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (visitorItem) {
        const existingBadge = visitorItem.querySelector('.cs-unread-count');

        if (newCount > 0) {
            // 显示或更新未读数量
            if (existingBadge) {
                existingBadge.textContent = newCount > 99 ? '99+' : newCount;
            } else {
                // 创建新的未读标识
                const badge = document.createElement('div');
                badge.className = 'cs-unread-count';
                badge.textContent = newCount > 99 ? '99+' : newCount;
                visitorItem.appendChild(badge);
            }
        } else {
            // 移除未读标识
            if (existingBadge) {
                existingBadge.remove();
            }
        }
    }
}

// 删除单个会话
function deleteSession(sessionId, event) {
    event.stopPropagation(); // 阻止冒泡，避免触发选择会话

    layer.confirm('确定要删除这个会话吗？删除后无法恢复！', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_token"]')?.value ||
                         '{{ csrf_token() }}';



        // 直接使用POST删除路由（更简单可靠）
        const formData = new FormData();
        formData.append('_token', csrfToken);

        fetch(`/strongadmin/customer-service/session/${sessionId}/delete`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: formData
        })
        .then(response => {

            return response.text(); // 先获取文本，然后尝试解析JSON
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    layer.msg('删除成功', {icon: 1});
                    // 从列表中移除
                    const sessionItem = document.querySelector(`[data-session-id="${sessionId}"]`);
                    if (sessionItem) {
                        sessionItem.remove();
                    }
                    // 如果删除的是当前选中的会话，清空右侧内容
                    if (currentSessionId == sessionId) {
                        document.getElementById('cs-messages').innerHTML = '<div style="text-align: center; color: #999; padding: 50px;">请选择一个会话</div>';
                        currentSessionId = null;
                    }
                } else {
                    layer.msg('删除失败：' + (data.error || '未知错误'), {icon: 2});
                }
            } catch (e) {
                console.error('JSON解析失败:', e, '原始响应:', text);
                // 如果不是JSON，但响应状态是200，可能删除成功了
                if (text.includes('删除成功') || text.includes('success')) {
                    layer.msg('删除成功', {icon: 1});
                    const sessionItem = document.querySelector(`[data-session-id="${sessionId}"]`);
                    if (sessionItem) {
                        sessionItem.remove();
                    }
                    if (currentSessionId == sessionId) {
                        document.getElementById('cs-messages').innerHTML = '<div style="text-align: center; color: #999; padding: 50px;">请选择一个会话</div>';
                        currentSessionId = null;
                    }
                } else {
                    layer.msg('删除失败，响应格式错误', {icon: 2});
                }
            }
        })
        .catch(error => {
            console.error('删除会话失败:', error);
            layer.msg('删除失败，请重试', {icon: 2});
        });

        layer.close(index);
    });
}

// 清空所有会话
function clearAllSessions() {
    layer.confirm('确定要清空所有会话吗？此操作不可恢复！', {
        icon: 3,
        title: '确认清空'
    }, function(index) {
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_token"]')?.value ||
                         '{{ csrf_token() }}';

        fetch('/strongadmin/customer-service/sessions/clear-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                layer.msg('清空成功', {icon: 1});
                // 刷新页面
                location.reload();
            } else {
                layer.msg('清空失败：' + (data.error || '未知错误'), {icon: 2});
            }
        })
        .catch(error => {
            console.error('清空会话失败:', error);
            layer.msg('清空失败，请重试', {icon: 2});
        });

        layer.close(index);
    });
}

// 标记所有为已读
function markAllAsRead() {
    // 获取CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_token"]')?.value ||
                     '{{ csrf_token() }}';

    fetch('/strongadmin/customer-service/sessions/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            layer.msg('已标记所有消息为已读', {icon: 1});
            // 移除所有小红点
            document.querySelectorAll('.cs-unread-count').forEach(badge => {
                badge.style.display = 'none';
            });
        } else {
            layer.msg('操作失败：' + (data.error || '未知错误'), {icon: 2});
        }
    })
    .catch(error => {
        console.error('标记已读失败:', error);
        layer.msg('操作失败，请重试', {icon: 2});
    });
}

// 手动刷新会话列表
function refreshSessionList() {
    layer.msg('正在刷新...', {icon: 16, time: 1000});
    setTimeout(() => {
        location.reload();
    }, 500);
}

// 页面刷新节流变量
let lastPageRefresh = 0;
const PAGE_REFRESH_COOLDOWN = 10000; // 10秒内最多刷新一次

// 提示音播放节流变量
let lastSoundPlay = 0;
const SOUND_PLAY_COOLDOWN = 3000; // 3秒内最多播放一次提示音

// 刷新未读数量和在线状态（不刷新整个页面）
function refreshUnreadCounts() {

    // 获取CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_token"]')?.value ||
                     '{{ csrf_token() }}';

    fetch('/strongadmin/customer-service/sessions/unread-counts', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.sessions) {

            // 检查是否有新的未读消息（用于声音提醒）
            let hasNewUnreadMessages = false;
            let hasNewSessions = false;

            // 获取当前页面上的所有会话ID
            const currentSessionIds = Array.from(document.querySelectorAll('[data-session-id]'))
                                           .map(el => parseInt(el.getAttribute('data-session-id')));

            // 更新每个会话的未读数量和在线状态
            data.sessions.forEach(session => {

                // 检查是否有新的未读消息（只在数量真正增加时才算新消息）
                const currentUnreadCount = parseInt(document.getElementById(`unread-${session.id}`)?.textContent || '0');
                if (session.unread_count > currentUnreadCount && session.unread_count > 0) {
                    // 记录这个会话的新消息数量
                    const newMessageCount = session.unread_count - currentUnreadCount;
                    if (newMessageCount > 0) {
                        hasNewUnreadMessages = true;
                    }
                }

                // 检查是否是新会话
                if (!currentSessionIds.includes(session.id)) {
                    hasNewSessions = true;
                }

                // 检查会话元素是否存在
                const sessionItem = document.querySelector(`[data-session-id="${session.id}"]`);
                if (!sessionItem) {
                    return;
                }
                // 更新在线状态
                const statusElement = sessionItem.querySelector('.session-status-indicator');
                if (statusElement) {
                    if (session.status === 'online') {
                        statusElement.innerHTML = '🟢';
                        statusElement.style.background = '#f6ffed';
                        statusElement.style.color = '#52c41a';
                        statusElement.style.border = '1px solid #b7eb8f';
                    } else {
                        statusElement.innerHTML = '⚫';
                        statusElement.style.background = '#f5f5f5';
                        statusElement.style.color = '#999';
                        statusElement.style.border = '1px solid #d9d9d9';
                    }
                }

                // 更新未读数量
                const unreadBadge = document.getElementById(`unread-${session.id}`);
                if (session.unread_count > 0) {
                    // 有未读消息，显示角标
                    if (unreadBadge) {
                        unreadBadge.textContent = session.unread_count > 99 ? '99+' : session.unread_count;
                        unreadBadge.style.display = 'flex';
                    } else {
                        // 如果角标不存在，创建一个
                        const avatarContainer = sessionItem.querySelector('div[style*="position: relative"]');
                        if (avatarContainer) {
                            const newBadge = document.createElement('div');
                            newBadge.className = 'cs-unread-count';
                            newBadge.id = `unread-${session.id}`;
                            newBadge.style.cssText = `
                                position: absolute;
                                top: -5px;
                                right: -5px;
                                background: #ff4d4f;
                                color: white;
                                border-radius: 10px;
                                min-width: 18px;
                                height: 18px;
                                font-size: 11px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-weight: bold;
                                border: 2px solid white;
                                z-index: 100;
                                box-sizing: border-box;
                                line-height: 1;
                                padding: 0 4px;
                                text-align: center;
                                font-family: Arial, sans-serif;
                                white-space: nowrap;
                            `;
                            newBadge.textContent = session.unread_count > 99 ? '99+' : session.unread_count;
                            avatarContainer.appendChild(newBadge);
                        }
                    }
                } else {
                    // 没有未读消息，隐藏角标
                    if (unreadBadge) {
                        unreadBadge.style.display = 'none';
                    }
                }
            });

            // 如果有新的未读消息，播放提示音（带节流）
            if (hasNewUnreadMessages) {
                const currentTime = Date.now();
                if (currentTime - lastSoundPlay > SOUND_PLAY_COOLDOWN) {
                    lastSoundPlay = currentTime;
                    playAdminNotificationSound();

                    // 显示桌面通知
                    showAdminDesktopNotification('新客户消息', '有客户发送了新消息，请及时回复');

                    // 闪烁浏览器标题
                    flashBrowserTitle();
                }
            }

            // 如果有新会话，刷新页面以显示新会话（带节流）
            if (hasNewSessions) {
                const currentTime = Date.now();
                if (currentTime - lastPageRefresh > PAGE_REFRESH_COOLDOWN) {
                    lastPageRefresh = currentTime;
                    setTimeout(() => {
                        location.reload();
                    }, 1000); // 延迟1秒刷新
                    return; // 不继续执行后续逻辑
                }
            }


        }
    })
    .catch(error => {
        console.error('刷新未读数量失败:', error);
        // 如果是网络错误，暂停定时刷新
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
            // 网络连接问题，暂停定时刷新
        }
    });
}

// 标记会话为已读 - 立即隐藏角标并同步到数据库
function markSessionAsRead(sessionId) {


    const unreadBadge = document.getElementById(`unread-${sessionId}`);

    // 如果有角标，立即隐藏
    if (unreadBadge && unreadBadge.style.display !== 'none') {
        unreadBadge.style.display = 'none';
    }

    // 获取CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_token"]')?.value ||
                     '{{ csrf_token() }}';

    // 同步到数据库
    fetch(`/strongadmin/customer-service/session/${sessionId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.text())
    .then(text => {

        try {
            const data = JSON.parse(text);
            if (data.success) {

            } else {
                console.error('标记已读失败:', data.error);
                // 如果数据库更新失败，恢复角标显示
                if (unreadBadge) {
                    unreadBadge.style.display = 'block';
                }
            }
        } catch (e) {
            console.error('解析响应失败:', e, text);
            // 如果解析失败，恢复角标显示
            if (unreadBadge) {
                unreadBadge.style.display = 'block';
            }
        }
    })
    .catch(error => {
        console.error('标记已读请求失败:', error);
        // 如果请求失败，恢复角标显示
        unreadBadge.style.display = 'block';
    });
}

// ==================== 表情包和图片功能 ====================

// 切换表情包选择器
function toggleAdminEmojiPicker() {
    const emojiPicker = document.getElementById('admin-emoji-picker');
    if (emojiPicker.style.display === 'none' || emojiPicker.style.display === '') {
        emojiPicker.style.display = 'block';
    } else {
        emojiPicker.style.display = 'none';
    }
}

// 插入表情包
function insertAdminEmoji(emoji) {
    const messageInput = document.getElementById('message-input');
    const currentValue = messageInput.value;
    const cursorPosition = messageInput.selectionStart || currentValue.length;

    const newValue = currentValue.slice(0, cursorPosition) + emoji + currentValue.slice(cursorPosition);
    messageInput.value = newValue;

    // 设置光标位置到表情后面
    const newCursorPosition = cursorPosition + emoji.length;
    messageInput.setSelectionRange(newCursorPosition, newCursorPosition);
    messageInput.focus();

    // 隐藏表情选择器
    document.getElementById('admin-emoji-picker').style.display = 'none';

    // 自动调整textarea高度
    autoResizeTextarea(messageInput);
}

// 选择图片
function selectAdminImage() {
    document.getElementById('admin-image-input').click();
}

// 处理图片选择
function handleAdminImageSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件！');
        return;
    }

    // 检查文件大小 (限制为5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('图片文件不能超过5MB！');
        return;
    }

    // 检查是否选择了会话
    if (!currentSessionId) {
        alert('请先选择一个会话！');
        return;
    }

    // 显示上传进度
    const progressDiv = document.getElementById('admin-upload-progress');
    const messageInput = document.getElementById('message-input');
    const originalPlaceholder = messageInput.placeholder;

    progressDiv.style.display = 'block';
    messageInput.placeholder = '正在上传图片...';
    messageInput.disabled = true;

    // 创建FormData上传图片
    const formData = new FormData();
    formData.append('image', file);
    formData.append('session_id', currentSessionId);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    fetch('/api/customer-service/upload-image.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 发送图片消息
            sendAdminImageMessage(data.data.image_url, file.name);
        } else {
            alert('图片上传失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('图片上传错误:', error);
        alert('图片上传失败，请稍后重试');
    })
    .finally(() => {
        progressDiv.style.display = 'none';
        messageInput.placeholder = originalPlaceholder;
        messageInput.disabled = false;
        // 清空文件输入
        event.target.value = '';
    });
}

// 发送图片消息
function sendAdminImageMessage(imageUrl, fileName) {
    // 创建表单数据
    const formData = new FormData();

    // 添加CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_token"]')?.value ||
                     '{{ csrf_token() }}';
    formData.append('_token', csrfToken);

    // 添加消息数据 - 用大括号包围图片URL标识为图片
    formData.append('message', `{${imageUrl}}`);

    // 发送图片消息

    fetch(`/strongadmin/customer-service/session/${currentSessionId}/reply`, {
        method: 'POST',
        body: formData
    })
    .then(response => {

        if (!response.ok) {
            return response.text().then(text => {
                console.error('服务器错误响应:', text);
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 发送图片消息到聊天
            const imageUrl = data.data.image_url;

            // 确保使用相对路径
            const cleanImageUrl = imageUrl.startsWith('http') ?
                new URL(imageUrl).pathname : imageUrl;

            // 发送图片消息
            sendImageMessage(cleanImageUrl);

            // 刷新消息列表
            loadMessages(currentSessionId);
        } else {
            alert('发送失败：' + (data.error || data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('发送图片消息错误:', error);
        alert('发送失败：' + error.message);
    });
}

// 自动调整textarea高度
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}

// 为message-input添加自动调整高度功能
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.addEventListener('input', function() {
            autoResizeTextarea(this);
        });

        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.getElementById('reply-form').dispatchEvent(new Event('submit'));
            }
        });
    }
});

// ==================== 客户信息和快捷回复功能 ====================

// 获取最新的会话信息
function fetchLatestSessionInfo(sessionId) {
    fetch(`/strongadmin/customer-service/session/${sessionId}/info`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.session) {

                updateCustomerInfo(data.session);
            } else {

                // 回退到本地数据
                const session = sessions.find(s => s && s.id == sessionId);
                if (session) {
                    updateCustomerInfo(session);
                }
            }
        })
        .catch(error => {
            console.error('获取会话信息错误:', error);
            // 回退到本地数据
            const session = sessions.find(s => s && s.id == sessionId);
            if (session) {
                updateCustomerInfo(session);
            }
        });
}

// 切换客户信息面板
function toggleCustomerInfo() {
    const panel = document.getElementById('customer-info-panel');
    const quickPanel = document.getElementById('quick-replies-panel');

    if (panel.style.display === 'none' || panel.style.display === '') {
        panel.style.display = 'block';
        // 关闭快捷回复面板
        quickPanel.style.display = 'none';
    } else {
        panel.style.display = 'none';
    }
}

// 切换快捷回复面板
function toggleQuickRepliesPanel() {
    const panel = document.getElementById('quick-replies-panel');
    const infoPanel = document.getElementById('customer-info-panel');

    if (panel.style.display === 'none' || panel.style.display === '') {
        panel.style.display = 'block';
        // 关闭客户信息面板
        infoPanel.style.display = 'none';
    } else {
        panel.style.display = 'none';
    }
}

// 更新客户信息显示
function updateCustomerInfo(session) {
    if (!session) return;



    // 访客名称
    document.getElementById('customer-name').textContent = session.visitor_name || session.display_name || '匿名访客';

    // 邮箱 - 只有客户提供才有
    document.getElementById('customer-email').textContent = session.visitor_email || session.email || '-';

    // IP地址 - 从会话数据获取
    document.getElementById('customer-ip').textContent = session.visitor_ip || session.ip_address || '-';

    // 组合地理位置信息
    let location = '';
    if (session.geo_country) location += session.geo_country;
    if (session.geo_region) location += (location ? ', ' : '') + session.geo_region;
    if (session.geo_city) location += (location ? ', ' : '') + session.geo_city;
    if (!location && session.visitor_ip) {
        location = '未知地区';
    }
    document.getElementById('customer-location').textContent = location || '-';

    // 浏览器和设备信息 - 优先使用数据库存储的信息
    let browserInfo = '-';
    let deviceInfo = '-';

    // 优先使用数据库中解析好的信息
    if (session.browser_name) {
        let icon = '🌐';
        if (session.browser_name === 'Chrome') icon = '🌐';
        else if (session.browser_name === 'Firefox') icon = '🦊';
        else if (session.browser_name === 'Safari') icon = '🧭';
        else if (session.browser_name === 'Edge') icon = '🌐';

        browserInfo = icon + ' ' + session.browser_name;
        if (session.browser_version) {
            const majorVersion = session.browser_version.split('.')[0];
            browserInfo += ' ' + majorVersion;
        }
        if (session.platform_name) {
            browserInfo += ' / ' + session.platform_name;
        }
    } else if (session.user_agent) {
        // 回退到解析user_agent
        browserInfo = parseBrowserInfo(session.user_agent);
    }

    if (session.device_type) {
        let icon = session.is_mobile ? '📱' : '💻';
        deviceInfo = icon + ' ' + session.device_type;
    } else if (session.user_agent) {
        // 回退到解析user_agent
        deviceInfo = parseDeviceInfo(session.user_agent);
    }

    document.getElementById('customer-browser').textContent = browserInfo;
    document.getElementById('customer-device').textContent = deviceInfo;

    // 会话时间
    if (session.created_at) {
        const sessionTime = new Date(session.created_at).toLocaleString('zh-CN');
        document.getElementById('session-time').textContent = sessionTime;
    } else {
        document.getElementById('session-time').textContent = '-';
    }

    // 加载会话备注
    document.getElementById('session-notes').value = session.internal_notes || '';

    // 更新会话状态
    if (document.getElementById('session-status')) {
        document.getElementById('session-status').textContent = getStatusText(session.status);
    }

    // 更新消息统计（包括响应时间）
    if (document.getElementById('message-count')) {
        // 获取消息数量和响应时间
        loadMessageStats(session.id);
    }
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'active': '进行中',
        'waiting': '等待中',
        'closed': '已关闭',
        'resolved': '已解决'
    };
    return statusMap[status] || status || '未知';
}

// 保存会话备注
function saveSessionNotes() {
    if (!currentSessionId) {
        alert('请先选择一个会话');
        return;
    }

    const notes = document.getElementById('session-notes').value;

    fetch(`/strongadmin/customer-service/session/${currentSessionId}/notes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => {
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            layui.use('layer', function() {
                const layer = layui.layer;
                layer.msg('备注保存成功', {icon: 1});
            });
            // 重新获取会话信息以确保显示最新数据
            fetchLatestSessionInfo(currentSessionId);
        } else {
            alert('保存失败：' + (data.message || data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('保存备注错误:', error);
        alert('保存失败：' + error.message);
    });
}

// 标记为已解决
function markAsResolved() {
    if (!currentSessionId) {
        alert('请先选择一个会话');
        return;
    }

    layui.use('layer', function() {
        const layer = layui.layer;
        layer.confirm('确定要标记此会话为已解决吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            updateSessionStatus('resolved');
            layer.close(index);
        });
    });
}

// 转接会话
function transferSession() {
    if (!currentSessionId) {
        alert('请先选择一个会话');
        return;
    }

    layui.use('layer', function() {
        const layer = layui.layer;
        layer.prompt({
            title: '转接会话',
            formType: 0,
            value: '',
            area: ['300px', '100px']
        }, function(value, index) {
            if (value.trim()) {
                // 这里可以实现转接逻辑
                layer.msg('转接功能开发中...', {icon: 0});
            }
            layer.close(index);
        });
    });
}

// 关闭会话确认
function closeSessionConfirm() {
    if (!currentSessionId) {
        alert('请先选择一个会话');
        return;
    }

    layui.use('layer', function() {
        const layer = layui.layer;
        layer.confirm('确定要关闭此会话吗？关闭后客户将无法继续发送消息。', {
            btn: ['确定', '取消']
        }, function(index) {
            updateSessionStatus('closed');
            layer.close(index);
        });
    });
}

// 更新会话状态
function updateSessionStatus(status) {
    fetch(`/strongadmin/customer-service/session/${currentSessionId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            layui.use('layer', function() {
                const layer = layui.layer;
                layer.msg('状态更新成功', {icon: 1});
            });
            // 更新显示
            document.getElementById('session-status').textContent = getStatusText(status);
            // 刷新会话列表
            location.reload();
        } else {
            alert('更新失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('更新状态错误:', error);
        alert('更新失败，请稍后重试');
    });
}

// 添加自定义回复
function addCustomReply() {
    layui.use('layer', function() {
        const layer = layui.layer;
        layer.open({
            type: 1,
            title: '添加自定义回复',
            area: ['500px', '300px'],
            content: `
                <div style="padding: 20px;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">回复标题：</label>
                        <input type="text" id="custom-reply-title" placeholder="请输入回复标题" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">回复内容：</label>
                        <textarea id="custom-reply-content" placeholder="请输入回复内容" style="width: 100%; height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                    </div>
                    <div style="text-align: right;">
                        <button class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                        <button class="layui-btn layui-btn-normal" onclick="saveCustomReply()">保存</button>
                    </div>
                </div>
            `
        });
    });
}

// 保存自定义回复
function saveCustomReply() {
    const title = document.getElementById('custom-reply-title').value.trim();
    const content = document.getElementById('custom-reply-content').value.trim();

    if (!title || !content) {
        alert('请填写完整信息');
        return;
    }

    // 这里可以保存到数据库，现在先保存到本地存储
    let customReplies = JSON.parse(localStorage.getItem('customReplies') || '[]');
    customReplies.push({ title: title, content: content, id: Date.now() });
    localStorage.setItem('customReplies', JSON.stringify(customReplies));

    // 刷新自定义回复列表
    loadCustomReplies();

    layui.use('layer', function() {
        const layer = layui.layer;
        layer.closeAll();
        layer.msg('保存成功', {icon: 1});
    });
}

// 加载自定义回复
function loadCustomReplies() {
    const customReplies = JSON.parse(localStorage.getItem('customReplies') || '[]');
    const container = document.getElementById('custom-replies');

    container.innerHTML = '';

    customReplies.forEach(reply => {
        const div = document.createElement('div');
        div.className = 'quick-reply-item';
        div.style.cssText = 'padding: 8px 12px; margin-bottom: 8px; background: white; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.2s; position: relative;';
        div.innerHTML = `
            <div style="font-weight: bold; color: #333; margin-bottom: 4px; display: flex; justify-content: space-between; align-items: center;">
                <span>${reply.title}</span>
                <button onclick="deleteCustomReply(${reply.id})" style="background: none; border: none; color: #999; cursor: pointer; padding: 0; font-size: 14px;" title="删除">×</button>
            </div>
            <div style="color: #666;">${reply.content}</div>
        `;
        div.onclick = function(e) {
            if (e.target.tagName !== 'BUTTON') {
                insertQuickReply(reply.content);
            }
        };
        div.onmouseover = function() {
            this.style.background = '#f0f9ff';
            this.style.borderColor = '#1890ff';
        };
        div.onmouseout = function() {
            this.style.background = 'white';
            this.style.borderColor = '#e6e6e6';
        };
        container.appendChild(div);
    });
}

// 删除自定义回复
function deleteCustomReply(id) {
    let customReplies = JSON.parse(localStorage.getItem('customReplies') || '[]');
    customReplies = customReplies.filter(reply => reply.id !== id);
    localStorage.setItem('customReplies', JSON.stringify(customReplies));
    loadCustomReplies();

    layui.use('layer', function() {
        const layer = layui.layer;
        layer.msg('删除成功', {icon: 1});
    });
}

// 浏览器信息解析函数
function parseBrowserInfo(userAgent) {
    if (!userAgent) return '未知浏览器';

    // 检测浏览器
    let browser = '其他浏览器';
    let version = '';

    // Chrome
    if (userAgent.includes('Chrome') && !userAgent.includes('Edge')) {
        browser = 'Chrome';
        const match = userAgent.match(/Chrome\/([0-9\.]+)/);
        if (match) version = match[1];
    }
    // Firefox
    else if (userAgent.includes('Firefox')) {
        browser = 'Firefox';
        const match = userAgent.match(/Firefox\/([0-9\.]+)/);
        if (match) version = match[1];
    }
    // Safari (但不是Chrome)
    else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        browser = 'Safari';
        const match = userAgent.match(/Version\/([0-9\.]+)/);
        if (match) version = match[1];
    }
    // Edge
    else if (userAgent.includes('Edge')) {
        browser = 'Edge';
        const match = userAgent.match(/Edge\/([0-9\.]+)/);
        if (match) version = match[1];
    }
    // Opera
    else if (userAgent.includes('Opera') || userAgent.includes('OPR')) {
        browser = 'Opera';
        const match = userAgent.match(/(?:Opera|OPR)\/([0-9\.]+)/);
        if (match) version = match[1];
    }
    // Internet Explorer
    else if (userAgent.includes('MSIE') || userAgent.includes('Trident')) {
        browser = 'Internet Explorer';
        const match = userAgent.match(/(?:MSIE |rv:)([0-9\.]+)/);
        if (match) version = match[1];
    }

    // 检测操作系统
    let os = '';
    if (userAgent.includes('Windows NT 10.0')) os = 'Windows 10';
    else if (userAgent.includes('Windows NT 6.3')) os = 'Windows 8.1';
    else if (userAgent.includes('Windows NT 6.1')) os = 'Windows 7';
    else if (userAgent.includes('Mac OS X')) os = 'macOS';
    else if (userAgent.includes('Android')) os = 'Android';
    else if (userAgent.includes('iPhone OS')) os = 'iOS';
    else if (userAgent.includes('Linux')) os = 'Linux';

    // 检测设备类型
    let device = '';
    if (userAgent.includes('Mobile') || userAgent.includes('iPhone')) {
        device = '📱 ';
    } else if (userAgent.includes('iPad') || userAgent.includes('Tablet')) {
        device = '📱 ';
    } else {
        device = '💻 ';
    }

    // 组合信息
    let result = device + browser;
    if (version) {
        // 只显示主版本号
        const majorVersion = version.split('.')[0];
        result += ' ' + majorVersion;
    }
    if (os) {
        result += ' / ' + os;
    }

    return result;
}

// 设备信息解析函数
function parseDeviceInfo(userAgent) {
    if (!userAgent) return '未知设备';

    let device = '';
    let icon = '';

    // 检测设备类型
    if (userAgent.includes('iPhone')) {
        device = 'iPhone';
        icon = '📱';
    } else if (userAgent.includes('iPad')) {
        device = 'iPad';
        icon = '📱';
    } else if (userAgent.includes('Android') && userAgent.includes('Mobile')) {
        device = 'Android 手机';
        icon = '📱';
    } else if (userAgent.includes('Android')) {
        device = 'Android 平板';
        icon = '📱';
    } else if (userAgent.includes('Windows Phone')) {
        device = 'Windows Phone';
        icon = '📱';
    } else if (userAgent.includes('Mobile')) {
        device = '移动设备';
        icon = '📱';
    } else if (userAgent.includes('Tablet')) {
        device = '平板电脑';
        icon = '📱';
    } else {
        device = '桌面电脑';
        icon = '💻';
    }

    // 检测屏幕分辨率信息（如果有）
    let screenInfo = '';
    if (screen && screen.width && screen.height) {
        screenInfo = ` (${screen.width}×${screen.height})`;
    }

    return icon + ' ' + device + screenInfo;
}

// 获取访问来源信息
function getReferrerInfo() {
    if (document.referrer) {
        try {
            const url = new URL(document.referrer);
            return url.hostname;
        } catch (e) {
            return '外部链接';
        }
    }
    return '直接访问';
}

// 加载消息统计
function loadMessageStats(sessionId) {
    if (!sessionId) return;

    fetch(`/strongadmin/customer-service/session/${sessionId}/stats`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;

                // 更新消息数量
                document.getElementById('message-count').textContent = stats.total_messages || 0;

                // 更新响应时间
                if (document.getElementById('response-time')) {
                    document.getElementById('response-time').textContent = stats.average_response_time || '-';
                }

                // 更新其他统计信息
                updateMessageBreakdown(stats);
            } else {
                document.getElementById('message-count').textContent = '获取失败';
                if (document.getElementById('response-time')) {
                    document.getElementById('response-time').textContent = '获取失败';
                }
            }
        })
        .catch(error => {
            document.getElementById('message-count').textContent = '获取失败';
            if (document.getElementById('response-time')) {
                document.getElementById('response-time').textContent = '获取失败';
            }
        });
}

// 计算响应时间
function calculateResponseTime(session) {
    if (!session.created_at) return '-';

    const sessionStart = new Date(session.created_at);
    const now = new Date();
    const diffMs = now - sessionStart;

    // 转换为合适的时间单位
    if (diffMs < 60000) { // 小于1分钟
        return Math.floor(diffMs / 1000) + '秒';
    } else if (diffMs < 3600000) { // 小于1小时
        return Math.floor(diffMs / 60000) + '分钟';
    } else if (diffMs < 86400000) { // 小于1天
        return Math.floor(diffMs / 3600000) + '小时';
    } else {
        return Math.floor(diffMs / 86400000) + '天';
    }
}

// 更新消息分类统计
function updateMessageBreakdown(stats) {
    // 如果有详细的消息分类，可以在这里显示
    const breakdown = [];
    if (stats.customer_messages > 0) {
        breakdown.push(`客户: ${stats.customer_messages}`);
    }
    if (stats.admin_messages > 0) {
        breakdown.push(`客服: ${stats.admin_messages}`);
    }
    if (stats.ai_messages > 0) {
        breakdown.push(`AI: ${stats.ai_messages}`);
    }

    if (breakdown.length > 0) {
        const detailText = breakdown.join(', ');
        document.getElementById('message-count').title = detailText;
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadCustomReplies();

    // 设置访问来源信息
    if (document.getElementById('customer-referrer')) {
        document.getElementById('customer-referrer').textContent = getReferrerInfo();
    }
});

</script>
@endpush

@endsection
