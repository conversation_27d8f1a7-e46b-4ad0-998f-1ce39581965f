<?php
/**
 * 调试消息流程 - 端到端测试
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>调试消息流程</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;} .log{background:#f8f9fa;padding:10px;border-radius:4px;margin:10px 0;font-family:monospace;font-size:12px;max-height:300px;overflow-y:auto;border:1px solid #ddd;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;}</style>";
echo "</head><body>";

echo "<h1>🔍 调试消息流程</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 处理测试操作
    if ($_POST['action'] ?? '' === 'send_customer_message') {
        $sessionId = 'test_session_' . time();
        $message = '测试客户消息 ' . date('H:i:s');
        
        echo "<h2>📤 步骤1: 发送客户消息</h2>";
        
        // 创建会话
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, '127.0.0.1', 'Test Browser', 'active', NOW(), NOW(), NOW())");
        $stmt->execute([$sessionId]);
        $sessionDbId = $pdo->lastInsertId();
        
        echo "<p class='success'>✅ 会话创建成功，ID: $sessionDbId</p>";
        
        // 插入客户消息
        $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, message, is_read, created_at, updated_at) VALUES (?, 'customer', ?, 0, NOW(), NOW())");
        $stmt->execute([$sessionDbId, $message]);
        $messageId = $pdo->lastInsertId();
        
        echo "<p class='success'>✅ 客户消息发送成功，消息ID: $messageId</p>";
        echo "<p class='info'>会话ID: $sessionDbId</p>";
        echo "<p class='info'>消息内容: $message</p>";
        
        // 存储到session中供后续步骤使用
        session_start();
        $_SESSION['test_session_id'] = $sessionDbId;
        $_SESSION['test_message_id'] = $messageId;
    }
    
    if ($_POST['action'] ?? '' === 'send_admin_reply') {
        session_start();
        $sessionDbId = $_SESSION['test_session_id'] ?? null;
        
        if (!$sessionDbId) {
            echo "<p class='error'>❌ 请先执行步骤1</p>";
        } else {
            $replyMessage = '测试管理员回复 ' . date('H:i:s');
            
            echo "<h2>💬 步骤2: 发送管理员回复</h2>";
            
            // 插入管理员回复
            $stmt = $pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, sender_id, message, is_read, created_at, updated_at) VALUES (?, 'admin', 1, ?, 0, NOW(), NOW())");
            $stmt->execute([$sessionDbId, $replyMessage]);
            $replyId = $pdo->lastInsertId();
            
            echo "<p class='success'>✅ 管理员回复发送成功，消息ID: $replyId</p>";
            echo "<p class='info'>回复内容: $replyMessage</p>";
            
            $_SESSION['test_reply_id'] = $replyId;
        }
    }
    
    if ($_POST['action'] ?? '' === 'check_sse') {
        session_start();
        $sessionDbId = $_SESSION['test_session_id'] ?? null;
        $lastMessageId = $_SESSION['test_message_id'] ?? 0;
        
        if (!$sessionDbId) {
            echo "<p class='error'>❌ 请先执行前面的步骤</p>";
        } else {
            echo "<h2>🔍 步骤3: 检查SSE应该返回的数据</h2>";
            
            // 模拟前台SSE查询
            $stmt = $pdo->prepare("SELECT * FROM st_customer_service_messages WHERE session_id = ? AND id > ? AND sender_type IN ('ai', 'admin') ORDER BY id ASC");
            $stmt->execute([$sessionDbId, $lastMessageId]);
            $newMessages = $stmt->fetchAll();
            
            if (empty($newMessages)) {
                echo "<p class='error'>❌ 没有找到新的回复消息</p>";
            } else {
                echo "<p class='success'>✅ 找到 " . count($newMessages) . " 条新回复消息</p>";
                echo "<table><tr><th>消息ID</th><th>发送者</th><th>消息内容</th><th>创建时间</th></tr>";
                foreach ($newMessages as $msg) {
                    echo "<tr>";
                    echo "<td>{$msg['id']}</td>";
                    echo "<td>{$msg['sender_type']}</td>";
                    echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
                    echo "<td>{$msg['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
    
    // 显示当前数据库状态
    echo "<h2>📊 当前数据库状态</h2>";
    
    // 最新会话
    $sessions = $pdo->query("SELECT * FROM st_customer_service_sessions ORDER BY created_at DESC LIMIT 5")->fetchAll();
    echo "<h3>最新会话</h3>";
    if (empty($sessions)) {
        echo "<p class='info'>暂无会话</p>";
    } else {
        echo "<table><tr><th>会话ID</th><th>会话标识</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 最新消息
    $messages = $pdo->query("SELECT m.*, s.session_id as session_key FROM st_customer_service_messages m LEFT JOIN st_customer_service_sessions s ON m.session_id = s.id ORDER BY m.created_at DESC LIMIT 10")->fetchAll();
    echo "<h3>最新消息</h3>";
    if (empty($messages)) {
        echo "<p class='info'>暂无消息</p>";
    } else {
        echo "<table><tr><th>消息ID</th><th>会话</th><th>发送者</th><th>消息内容</th><th>创建时间</th></tr>";
        foreach ($messages as $msg) {
            $senderClass = $msg['sender_type'] === 'admin' ? 'success' : ($msg['sender_type'] === 'ai' ? 'info' : '');
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['session_key']}</td>";
            echo "<td class='$senderClass'>{$msg['sender_type']}</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td>{$msg['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<h2>🧪 端到端测试</h2>";
echo "<p>按顺序执行以下步骤来测试完整的消息流程：</p>";
echo "<form method='POST'>";
echo "<button type='submit' name='action' value='send_customer_message' class='test-btn'>步骤1: 发送客户消息</button>";
echo "<button type='submit' name='action' value='send_admin_reply' class='test-btn'>步骤2: 发送管理员回复</button>";
echo "<button type='submit' name='action' value='check_sse' class='test-btn'>步骤3: 检查SSE数据</button>";
echo "</form>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/test_persistent_user.html' target='_blank'>持久化用户系统</a></li>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台管理</a></li>";
echo "<li><a href='/simple-sse' target='_blank'>简单SSE测试</a></li>";
echo "</ul>";

echo "</body></html>";
?>
