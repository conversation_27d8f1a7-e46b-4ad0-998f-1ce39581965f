<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript语法检查器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-log {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-log {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .code-sample {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JavaScript语法检查器</h1>
        <p>检查可能导致语法错误的JavaScript代码模式</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="testConsoleStatements()">测试Console语句</button>
            <button class="test-btn" onclick="testTemplateStrings()">测试模板字符串</button>
            <button class="test-btn" onclick="testTernaryOperators()">测试三元运算符</button>
            <button class="test-btn" onclick="testAllPatterns()">测试所有模式</button>
        </div>
        
        <div id="result-container">
            <div class="success-log">
                <h3>✅ 准备开始语法检查</h3>
                <p>点击上面的按钮开始测试各种JavaScript语法模式</p>
            </div>
        </div>
    </div>

    <script>
        function updateResult(content, isError = false) {
            const container = document.getElementById('result-container');
            const className = isError ? 'error-log' : 'success-log';
            container.innerHTML = `<div class="${className}">${content}</div>`;
        }

        function testConsoleStatements() {
            updateResult('<h3>🔍 测试Console语句...</h3>');
            
            const testCases = [
                // 正确的语法
                'console.log(`测试消息: ${variable}`);',
                'console.error(`错误: ${error.message}`);',
                'console.log(`状态: ${status}, 数据: ${data}`);',
                
                // 可能有问题的语法
                'console.error(`错误: ${error}`, error);',
                'console.log(`消息: ${msg}`, additionalData);',
            ];
            
            let results = '<h3>✅ Console语句测试结果</h3>';
            let hasError = false;
            
            testCases.forEach((code, index) => {
                try {
                    // 使用Function构造函数测试语法
                    new Function('variable', 'error', 'status', 'data', 'msg', 'additionalData', 'console', code);
                    results += `<div style="color: green;">✅ 测试 ${index + 1}: 语法正确</div>`;
                    results += `<div class="code-sample">${code}</div>`;
                } catch (e) {
                    hasError = true;
                    results += `<div style="color: red;">❌ 测试 ${index + 1}: 语法错误</div>`;
                    results += `<div class="code-sample">${code}</div>`;
                    results += `<div style="color: red; margin-left: 20px;">错误: ${e.message}</div>`;
                }
            });
            
            updateResult(results, hasError);
        }

        function testTemplateStrings() {
            updateResult('<h3>🔍 测试模板字符串...</h3>');
            
            const testCases = [
                // 正确的语法
                '`简单模板字符串`',
                '`带变量的模板: ${variable}`',
                '`多个变量: ${var1} 和 ${var2}`',
                '`嵌套表达式: ${obj.prop || "默认值"}`',
                
                // 可能有问题的语法
                '`未闭合的模板字符串: ${variable}',
                '`错误的变量引用: ${undefined.property}`',
            ];
            
            let results = '<h3>✅ 模板字符串测试结果</h3>';
            let hasError = false;
            
            testCases.forEach((code, index) => {
                try {
                    new Function('variable', 'var1', 'var2', 'obj', `return ${code};`);
                    results += `<div style="color: green;">✅ 测试 ${index + 1}: 语法正确</div>`;
                    results += `<div class="code-sample">${code}</div>`;
                } catch (e) {
                    hasError = true;
                    results += `<div style="color: red;">❌ 测试 ${index + 1}: 语法错误</div>`;
                    results += `<div class="code-sample">${code}</div>`;
                    results += `<div style="color: red; margin-left: 20px;">错误: ${e.message}</div>`;
                }
            });
            
            updateResult(results, hasError);
        }

        function testTernaryOperators() {
            updateResult('<h3>🔍 测试三元运算符...</h3>');
            
            const testCases = [
                // 正确的语法
                'condition ? "true" : "false"',
                'field === "name" ? "visitor_name" : "other"',
                'field === "name" ? "姓名" : field === "email" ? "邮箱" : "其他"',
                
                // 修复后的语法
                `const fieldKey = field === 'name' ? 'visitor_name' :
                               field === 'email' ? 'visitor_email' :
                               field === 'phone' ? 'visitor_phone' : field;`,
                
                // 可能有问题的语法（已修复）
                `const fieldName = field === 'name' ? '姓名' :
                                field === 'email' ? '邮箱' :
                                field === 'phone' ? '电话' : '其他';`,
            ];
            
            let results = '<h3>✅ 三元运算符测试结果</h3>';
            let hasError = false;
            
            testCases.forEach((code, index) => {
                try {
                    new Function('condition', 'field', code);
                    results += `<div style="color: green;">✅ 测试 ${index + 1}: 语法正确</div>`;
                    results += `<div class="code-sample">${code}</div>`;
                } catch (e) {
                    hasError = true;
                    results += `<div style="color: red;">❌ 测试 ${index + 1}: 语法错误</div>`;
                    results += `<div class="code-sample">${code}</div>`;
                    results += `<div style="color: red; margin-left: 20px;">错误: ${e.message}</div>`;
                }
            });
            
            updateResult(results, hasError);
        }

        function testAllPatterns() {
            updateResult('<h3>🔍 执行完整语法检查...</h3>');
            
            setTimeout(() => {
                let allResults = '<h3>🔧 完整语法检查结果</h3>';
                let totalErrors = 0;
                
                // 测试各种可能有问题的模式
                const problematicPatterns = [
                    // 1. 模板字符串中的console语句
                    'console.error(`错误: ${error}`, error);',
                    'console.log(`状态: ${status}`, data);',
                    
                    // 2. 复杂的三元运算符
                    `const result = condition1 ? value1 :
                                   condition2 ? value2 :
                                   condition3 ? value3 : defaultValue;`,
                    
                    // 3. 模板字符串中的复杂表达式
                    '`结果: ${obj && obj.prop ? obj.prop.value : "默认"}`',
                    
                    // 4. 嵌套的函数调用
                    'fetch(url).then(response => response.json()).then(data => console.log(data));',
                ];
                
                problematicPatterns.forEach((pattern, index) => {
                    try {
                        new Function('error', 'status', 'data', 'condition1', 'condition2', 'condition3', 'value1', 'value2', 'value3', 'defaultValue', 'obj', 'url', 'console', 'fetch', pattern);
                        allResults += `<div style="color: green; margin: 10px 0;">✅ 模式 ${index + 1}: 语法正确</div>`;
                    } catch (e) {
                        totalErrors++;
                        allResults += `<div style="color: red; margin: 10px 0;">❌ 模式 ${index + 1}: 语法错误 - ${e.message}</div>`;
                    }
                    allResults += `<div class="code-sample">${pattern}</div>`;
                });
                
                if (totalErrors === 0) {
                    allResults += '<div style="color: green; font-weight: bold; margin-top: 20px;">🎉 所有测试模式语法正确！</div>';
                    allResults += '<div>如果主页面仍有语法错误，可能是由于:</div>';
                    allResults += '<ul><li>动态生成的代码</li><li>外部脚本冲突</li><li>浏览器兼容性问题</li></ul>';
                } else {
                    allResults += `<div style="color: red; font-weight: bold; margin-top: 20px;">⚠️ 发现 ${totalErrors} 个语法错误</div>`;
                }
                
                updateResult(allResults, totalErrors > 0);
            }, 500);
        }

        // 页面加载完成后自动运行基本检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateResult(`
                    <h3>📋 语法检查器已就绪</h3>
                    <p>这个工具可以帮助检测JavaScript语法错误。</p>
                    <p><strong>当前状态</strong>: 已修复三元运算符嵌套问题</p>
                    <p><strong>建议</strong>: 点击"测试所有模式"进行完整检查</p>
                `);
            }, 1000);
        });
    </script>
</body>
</html>
