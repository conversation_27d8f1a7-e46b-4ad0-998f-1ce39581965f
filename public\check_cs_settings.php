<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>检查客服设置</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
.btn{background:#007bff;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;margin:5px;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔍 检查客服系统设置</h1>";

try {
    // 数据库连接
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'mostxx_com';
    $username = 'mostxx_com';
    $password = 'fHnrmH9w5nw1pd53';

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 检查客服设置表
    echo "<h3>1. 客服设置表状态</h3>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_settings");
        $result = $stmt->fetch();
        $count = $result['count'];
        
        if ($count > 0) {
            echo "<div class='success'>✅ 客服设置表存在，共有 {$count} 条记录</div>";
            
            // 显示所有设置
            echo "<h4>当前设置:</h4>";
            $stmt = $pdo->query("SELECT * FROM customer_service_settings ORDER BY category, setting_key");
            $settings = $stmt->fetchAll();
            
            echo "<table>";
            echo "<tr><th>分类</th><th>设置键</th><th>标题</th><th>值</th><th>类型</th><th>操作</th></tr>";
            
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];
                if (strlen($value) > 50) {
                    $value = substr($value, 0, 50) . '...';
                }
                
                echo "<tr>";
                echo "<td>{$setting['category']}</td>";
                echo "<td><code>{$setting['setting_key']}</code></td>";
                echo "<td>{$setting['title']}</td>";
                echo "<td><strong>{$value}</strong></td>";
                echo "<td>{$setting['setting_type']}</td>";
                echo "<td>";
                if ($setting['setting_key'] === 'system_enabled') {
                    if ($setting['setting_value'] === '1') {
                        echo "<span style='color:#28a745;'>✅ 已启用</span>";
                        echo " <button class='btn' onclick='toggleSystem(0)'>禁用</button>";
                    } else {
                        echo "<span style='color:#dc3545;'>❌ 已禁用</span>";
                        echo " <button class='btn' onclick='toggleSystem(1)'>启用</button>";
                    }
                }
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "<div class='error'>❌ 客服设置表为空，需要初始化设置</div>";
            echo "<button class='btn' onclick='initializeSettings()'>初始化设置</button>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 客服设置表不存在: " . $e->getMessage() . "</div>";
        echo "<button class='btn' onclick='createTable()'>创建设置表</button>";
    }
    
    // 检查API响应
    echo "<h3>2. API响应测试</h3>";
    
    // 测试设置API
    $settingsUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/customer-service/get-settings.php';
    echo "<p><strong>设置API:</strong> <code>{$settingsUrl}</code></p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $settingsUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<div class='success'>✅ 设置API响应正常</div>";
            echo "<pre style='background:#f8f9fa;padding:10px;border-radius:4px;'>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        } else {
            echo "<div class='error'>❌ 设置API响应异常: " . $response . "</div>";
        }
    } else {
        echo "<div class='error'>❌ 设置API请求失败，HTTP状态码: {$httpCode}</div>";
    }
    
    // 测试状态API
    $statusUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/customer-service/status';
    echo "<p><strong>状态API:</strong> <code>{$statusUrl}</code></p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $statusUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<div class='success'>✅ 状态API响应正常</div>";
            echo "<pre style='background:#f8f9fa;padding:10px;border-radius:4px;'>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        } else {
            echo "<div class='error'>❌ 状态API响应异常: " . $response . "</div>";
        }
    } else {
        echo "<div class='error'>❌ 状态API请求失败，HTTP状态码: {$httpCode}</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
}

echo "<h3>3. 解决方案</h3>";
echo "<div class='info'>";
echo "<p><strong>如果客服系统不显示，请检查：</strong></p>";
echo "<ol>";
echo "<li>确保 system_enabled 设置为 1</li>";
echo "<li>确保两个API都能正常响应</li>";
echo "<li>检查浏览器控制台是否有JavaScript错误</li>";
echo "<li>清理浏览器缓存</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "function toggleSystem(enabled) {";
echo "  if (confirm('确定要' + (enabled ? '启用' : '禁用') + '客服系统吗？')) {";
echo "    fetch('/api/customer-service/toggle-system', {";
echo "      method: 'POST',";
echo "      headers: { 'Content-Type': 'application/json' },";
echo "      body: JSON.stringify({ enabled: enabled })";
echo "    }).then(() => location.reload());";
echo "  }";
echo "}";
echo "</script>";

echo "</body></html>";
?>
