<?php
/**
 * 测试角标状态的调试页面
 */

// 数据库配置
$host = 'localhost';
$port = 3306;
$database = 'strongshop';
$username = 'root';
$password = 'root';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>角标状态测试</title>";
echo "<style>
body{font-family:Arial;margin:20px;} 
.success{color:green;} 
.error{color:red;} 
.info{color:blue;} 
.warning{color:orange;}
.test-btn{padding:8px 16px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}
.badge{background:#ff4757;color:white;border-radius:50%;padding:2px 6px;font-size:12px;margin-left:5px;}
table{border-collapse:collapse;width:100%;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f5f5f5;}
</style>";
echo "</head><body>";

echo "<h1>🔴 角标状态测试</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    
    // 获取会话数据
    $sessions = $pdo->query("
        SELECT 
            id, 
            session_id, 
            visitor_name, 
            status, 
            unread_count,
            created_at
        FROM st_customer_service_sessions 
        ORDER BY created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    echo "<h2>📋 会话列表（数据库中的unread_count）</h2>";
    if (empty($sessions)) {
        echo "<p class='warning'>⚠️ 没有找到会话数据</p>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>会话ID</th><th>访客名称</th><th>状态</th><th>数据库未读数</th><th>实际未读数</th><th>操作</th></tr>";
        
        foreach ($sessions as $session) {
            $visitorName = $session['visitor_name'] ?: '访客-' . strtoupper(substr(md5($session['session_id']), 0, 6));
            
            // 计算实际未读消息数量
            $actualUnread = $pdo->query("
                SELECT COUNT(*) 
                FROM st_customer_service_messages 
                WHERE session_id = {$session['id']} 
                AND sender_type = 'customer' 
                AND is_read = 0
            ")->fetchColumn();
            
            $dbUnread = $session['unread_count'] ?? 0;
            $mismatch = ($dbUnread != $actualUnread) ? 'style="background:#ffebee;"' : '';
            
            echo "<tr $mismatch>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>$visitorName</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>$dbUnread" . ($dbUnread > 0 ? "<span class='badge'>$dbUnread</span>" : "") . "</td>";
            echo "<td>$actualUnread" . ($actualUnread > 0 ? "<span class='badge'>$actualUnread</span>" : "") . "</td>";
            echo "<td>";
            if ($dbUnread != $actualUnread) {
                echo "<button class='test-btn' onclick='fixUnreadCount({$session['id']}, $actualUnread)'>修复</button>";
            }
            echo "<button class='test-btn' onclick='markAsRead({$session['id']})'>标记已读</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (array_filter($sessions, function($s) use ($pdo) {
            $actual = $pdo->query("SELECT COUNT(*) FROM st_customer_service_messages WHERE session_id = {$s['id']} AND sender_type = 'customer' AND is_read = 0")->fetchColumn();
            return ($s['unread_count'] ?? 0) != $actual;
        })) {
            echo "<p class='warning'>⚠️ 发现数据不一致的会话（红色背景），建议点击修复按钮</p>";
        }
    }
    
    echo "<h2>🔧 批量操作</h2>";
    echo "<button class='test-btn' onclick='fixAllUnreadCounts()'>修复所有未读数量</button>";
    echo "<button class='test-btn' onclick='markAllAsRead()'>标记所有为已读</button>";
    echo "<button class='test-btn' onclick='refreshPage()'>刷新页面</button>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<script>
function fixUnreadCount(sessionId, correctCount) {
    fetch('/strongadmin/customer-service/session/' + sessionId + '/fix-unread', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': 'test-token'
        },
        body: JSON.stringify({count: correctCount})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('修复成功');
            location.reload();
        } else {
            alert('修复失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败: ' + error.message);
    });
}

function markAsRead(sessionId) {
    fetch('/strongadmin/customer-service/session/' + sessionId + '/mark-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': 'test-token'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('标记成功');
            location.reload();
        } else {
            alert('标记失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败: ' + error.message);
    });
}

function fixAllUnreadCounts() {
    if (confirm('确定要修复所有会话的未读数量吗？')) {
        fetch('/strongadmin/customer-service/sessions/fix-all-unread', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': 'test-token'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('批量修复成功');
                location.reload();
            } else {
                alert('批量修复失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            alert('请求失败: ' + error.message);
        });
    }
}

function markAllAsRead() {
    if (confirm('确定要标记所有消息为已读吗？')) {
        fetch('/strongadmin/customer-service/sessions/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': 'test-token'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('标记成功');
                location.reload();
            } else {
                alert('标记失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            alert('请求失败: ' + error.message);
        });
    }
}

function refreshPage() {
    location.reload();
}
</script>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "</ul>";

echo "<h2>📝 测试步骤</h2>";
echo "<ol>";
echo "<li>在前台发送一条消息，观察角标是否出现</li>";
echo "<li>在后台点击该会话，观察角标是否消失</li>";
echo "<li>刷新后台页面，观察角标是否保持消失状态</li>";
echo "<li>如果角标重新出现，说明还有问题需要修复</li>";
echo "</ol>";

echo "</body></html>";
?>
