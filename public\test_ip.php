<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Http\Request;
use App\Http\Controllers\SimpleCustomerServiceController;

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>IP获取测试</title>
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .info-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .success { background: #f6ffed; border-color: #b7eb8f; }
        .warning { background: #fffbe6; border-color: #ffe58f; }
        .error { background: #fff2f0; border-color: #ffccc7; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        .ip-value { font-family: monospace; font-weight: bold; color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 IP获取测试</h1>
        
        <?php
        try {
            // 创建请求对象
            $request = Request::createFromGlobals();
            
            // 创建控制器实例
            $controller = new SimpleCustomerServiceController();
            
            // 使用反射调用私有方法
            $reflection = new ReflectionClass($controller);
            $getRealClientIpMethod = $reflection->getMethod('getRealClientIp');
            $getRealClientIpMethod->setAccessible(true);
            
            $getExternalIpMethod = $reflection->getMethod('getExternalIp');
            $getExternalIpMethod->setAccessible(true);
            
            $getGeoLocationMethod = $reflection->getMethod('getGeoLocation');
            $getGeoLocationMethod->setAccessible(true);
            
            // 获取各种IP
            $laravelIp = $request->ip();
            $realIp = $getRealClientIpMethod->invoke($controller, $request);
            $externalIp = $getExternalIpMethod->invoke($controller);
            
            echo "<div class='info-card success'>";
            echo "<h3>📊 IP获取结果</h3>";
            echo "<table>";
            echo "<tr><th>方法</th><th>IP地址</th><th>说明</th></tr>";
            echo "<tr><td>Laravel默认</td><td class='ip-value'>{$laravelIp}</td><td>request()->ip()</td></tr>";
            echo "<tr><td>智能获取</td><td class='ip-value'>{$realIp}</td><td>getRealClientIp()</td></tr>";
            echo "<tr><td>外网IP</td><td class='ip-value'>" . ($externalIp ?: '获取失败') . "</td><td>getExternalIp()</td></tr>";
            echo "</table>";
            echo "</div>";
            
            // 显示所有HTTP头
            echo "<div class='info-card'>";
            echo "<h3>🔍 HTTP头信息</h3>";
            echo "<table>";
            echo "<tr><th>头名称</th><th>值</th></tr>";
            
            $headers = [
                'HTTP_CF_CONNECTING_IP' => 'Cloudflare真实IP',
                'HTTP_X_FORWARDED_FOR' => '代理转发IP',
                'HTTP_X_FORWARDED' => '代理转发',
                'HTTP_X_CLUSTER_CLIENT_IP' => '集群客户端IP',
                'HTTP_FORWARDED_FOR' => '转发IP',
                'HTTP_FORWARDED' => '转发',
                'HTTP_CLIENT_IP' => '客户端IP',
                'REMOTE_ADDR' => '远程地址',
                'HTTP_X_REAL_IP' => '真实IP',
                'SERVER_ADDR' => '服务器地址'
            ];
            
            foreach ($headers as $header => $desc) {
                $value = $request->server($header) ?: '未设置';
                echo "<tr><td>{$header}<br><small style='color:#999;'>{$desc}</small></td><td class='ip-value'>{$value}</td></tr>";
            }
            echo "</table>";
            echo "</div>";
            
            // 获取地理位置信息
            echo "<div class='info-card'>";
            echo "<h3>🌍 地理位置信息</h3>";
            
            $geoInfo = $getGeoLocationMethod->invoke($controller, $realIp);
            echo "<table>";
            echo "<tr><th>信息</th><th>值</th></tr>";
            echo "<tr><td>使用的IP</td><td class='ip-value'>{$realIp}</td></tr>";
            echo "<tr><td>国家</td><td>{$geoInfo['country']}</td></tr>";
            echo "<tr><td>省/州</td><td>{$geoInfo['region']}</td></tr>";
            echo "<tr><td>城市</td><td>{$geoInfo['city']}</td></tr>";
            echo "</table>";
            echo "</div>";
            
            // 测试外网IP获取
            echo "<div class='info-card'>";
            echo "<h3>🌐 外网IP测试</h3>";
            echo "<p>正在测试多个IP查询服务...</p>";
            
            $services = [
                'https://api.ipify.org' => 'ipify.org',
                'https://icanhazip.com' => 'icanhazip.com',
                'https://ipecho.net/plain' => 'ipecho.net',
                'https://myexternalip.com/raw' => 'myexternalip.com'
            ];
            
            echo "<table>";
            echo "<tr><th>服务</th><th>返回IP</th><th>状态</th></tr>";
            
            foreach ($services as $url => $name) {
                try {
                    $context = stream_context_create([
                        'http' => [
                            'timeout' => 5,
                            'user_agent' => 'Mozilla/5.0 (compatible; CustomerService/1.0)'
                        ]
                    ]);
                    
                    $ip = @file_get_contents($url, false, $context);
                    if ($ip && filter_var(trim($ip), FILTER_VALIDATE_IP)) {
                        echo "<tr><td>{$name}</td><td class='ip-value'>" . trim($ip) . "</td><td style='color:green;'>✅ 成功</td></tr>";
                    } else {
                        echo "<tr><td>{$name}</td><td>-</td><td style='color:red;'>❌ 失败</td></tr>";
                    }
                } catch (Exception $e) {
                    echo "<tr><td>{$name}</td><td>-</td><td style='color:red;'>❌ 异常</td></tr>";
                }
            }
            echo "</table>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='info-card error'>";
            echo "<h3>❌ 错误</h3>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
        ?>
        
        <div class="info-card">
            <h3>💡 说明</h3>
            <ul>
                <li><strong>Laravel默认:</strong> 使用request()->ip()方法</li>
                <li><strong>智能获取:</strong> 检查多个HTTP头，优先使用真实外网IP</li>
                <li><strong>外网IP:</strong> 当检测到本地IP时，查询外网IP服务</li>
                <li><strong>地理位置:</strong> 使用ip-api.com免费服务获取位置信息</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>🔗 相关链接</h3>
            <ul>
                <li><a href="/" target="_blank">网站首页（测试客服）</a></li>
                <li><a href="/live_status.php" target="_blank">实时状态监控</a></li>
                <li><a href="/strongadmin/customer-service/sessions" target="_blank">后台管理</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
