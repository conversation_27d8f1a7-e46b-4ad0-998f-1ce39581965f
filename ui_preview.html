<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrongShop UI 设计方案预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .version-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .version-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .version-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .version-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .version-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .status-current {
            background: #e8f5e8;
            color: #27ae60;
        }
        
        .status-backup {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-available {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .version-preview {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .preview-title {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .preview-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .features {
            list-style: none;
            margin: 15px 0;
        }
        
        .features li {
            padding: 5px 0;
            color: #666;
        }
        
        .features li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .action-buttons {
            margin-top: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .instructions {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .instructions h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .version-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .version-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 StrongShop UI 设计方案</h1>
            <p>现代化手机端用户界面升级预览</p>
        </div>
        
        <div class="version-grid">
            <!-- 版本1：原始版本 -->
            <div class="version-card">
                <h2 class="version-title">版本1：原始版本</h2>
                <span class="version-status status-backup">已备份</span>
                
                <div class="version-preview">
                    <div class="preview-title">
                        <div class="preview-icon">→</div>
                        <strong>New Products</strong>
                    </div>
                    <p>使用传统的 Bootstrap 图标和简单的 H4 标题</p>
                </div>
                
                <ul class="features">
                    <li>Bootstrap 3 Glyphicon 图标</li>
                    <li>简单的文字链接</li>
                    <li>基础的响应式设计</li>
                    <li>传统电商风格</li>
                </ul>
                
                <div class="action-buttons">
                    <a href="#" class="btn btn-secondary" onclick="alert('双击 restore_version1.bat 文件')">恢复此版本</a>
                </div>
            </div>
            
            <!-- 版本2：简洁现代风格 -->
            <div class="version-card">
                <h2 class="version-title">版本2：简洁现代风格</h2>
                <span class="version-status status-current">当前版本</span>

                <div class="version-preview">
                    <div class="preview-title" style="display: flex; align-items: center; padding: 12px 0; border-left: 3px solid #007bff; padding-left: 15px;">
                        <div class="preview-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe); width: 32px; height: 32px; line-height: 32px; margin-right: 12px; font-size: 18px;">🆕</div>
                        <strong style="font-size: 18px; color: #333;">New Products</strong>
                    </div>
                    <p>简洁的emoji图标，渐变背景，左侧蓝色边框，悬停动画</p>
                </div>

                <ul class="features">
                    <li>简洁的emoji图标</li>
                    <li>渐变圆形背景</li>
                    <li>左侧彩色边框</li>
                    <li>微妙的悬停动画</li>
                    <li>更好的手机端体验</li>
                    <li>保持原有结构</li>
                </ul>

                <div class="action-buttons">
                    <a href="#" class="btn btn-success" onclick="alert('当前正在使用此版本')">当前版本</a>
                </div>
            </div>
            
            <!-- 版本3：奢华品牌风格 -->
            <div class="version-card">
                <h2 class="version-title">版本3：奢华品牌风格</h2>
                <span class="version-status status-available">可选择</span>
                
                <div class="version-preview">
                    <div class="preview-title">
                        <div class="preview-icon" style="background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);">👑</div>
                        <strong style="color: #8b4513;">NEW PRODUCTS</strong>
                        <br><small style="color: #cd853f;">Exclusively curated collection</small>
                    </div>
                    <p>金色主题，奢华字体，高端品牌感</p>
                </div>
                
                <ul class="features">
                    <li>金色渐变配色</li>
                    <li>奢华字体设计</li>
                    <li>高端品牌质感</li>
                    <li>精致的装饰元素</li>
                    <li>适合奢侈品电商</li>
                </ul>
                
                <div class="action-buttons">
                    <a href="#" class="btn btn-primary" onclick="alert('如需此版本，请联系开发者')">申请此版本</a>
                </div>
            </div>
            
            <!-- 版本4：极简主义风格 -->
            <div class="version-card">
                <h2 class="version-title">版本4：极简主义风格</h2>
                <span class="version-status status-available">可选择</span>
                
                <div class="version-preview">
                    <div class="preview-title">
                        <div class="preview-icon" style="background: #000;">•</div>
                        <strong style="color: #000; font-weight: 300;">new products</strong>
                    </div>
                    <p>纯净设计，大量留白，极简美学</p>
                </div>
                
                <ul class="features">
                    <li>纯黑白配色</li>
                    <li>大量留白设计</li>
                    <li>极简图标</li>
                    <li>现代排版</li>
                    <li>适合科技产品</li>
                </ul>
                
                <div class="action-buttons">
                    <a href="#" class="btn btn-primary" onclick="alert('如需此版本，请联系开发者')">申请此版本</a>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>当前状态</strong>：您的网站已升级为版本2（现代简约风格）</li>
                <li><strong>版本1备份</strong>：原始版本已安全备份到 <code>H:\wwwroot\mostxx.com_1111hp7hL_backup_v1</code></li>
                <li><strong>恢复版本1</strong>：双击 <code>restore_version1.bat</code> 文件即可恢复</li>
                <li><strong>切换版本</strong>：使用提供的批处理文件进行版本切换</li>
                <li><strong>自定义需求</strong>：如需其他设计风格，请联系开发者定制</li>
            </ol>
            
            <h3 style="margin-top: 25px;">🔧 技术特性</h3>
            <ul style="margin-left: 20px;">
                <li>响应式设计，完美适配手机端</li>
                <li>现代 CSS3 动画效果</li>
                <li>SVG 图标，高清显示</li>
                <li>渐变色彩，视觉层次丰富</li>
                <li>触摸友好的交互体验</li>
                <li>兼容原有功能，无需修改后端</li>
            </ul>
        </div>
    </div>
</body>
</html>
