/**
 * 前台WebSocket客户端
 */

class CustomerServiceWebSocket {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.isConnected = false;
    }

    connect() {
        try {
            // 连接到Laravel WebSockets服务器
            const wsUrl = `ws://127.0.0.1:6001/app/your-app-key?protocol=7&client=js&version=4.3.1&flash=false`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('✅ WebSocket连接成功');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                // 订阅客服频道
                this.subscribe();
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (e) {
                    console.error('解析WebSocket消息失败:', e);
                }
            };

            this.websocket.onclose = () => {
                console.log('❌ WebSocket连接关闭');
                this.isConnected = false;
                this.reconnect();
            };

            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket连接错误:', error);
                this.isConnected = false;
            };

        } catch (e) {
            console.error('WebSocket初始化失败:', e);
        }
    }

    subscribe() {
        if (!this.isConnected || !this.websocket) return;

        // 订阅客服频道
        const subscribeMessage = {
            event: 'pusher:subscribe',
            data: {
                channel: `customer-service.${this.sessionId}`
            }
        };

        this.websocket.send(JSON.stringify(subscribeMessage));
        console.log('📡 已订阅客服频道:', `customer-service.${this.sessionId}`);
    }

    handleMessage(data) {
        console.log('📨 收到WebSocket消息:', data);

        // 处理订阅确认
        if (data.event === 'pusher:subscription_succeeded') {
            console.log('✅ 频道订阅成功');
            return;
        }

        // 处理新消息
        if (data.event === 'new.message') {
            const messageData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
            
            if (messageData.sender_type === 'admin' || messageData.sender_type === 'ai') {
                // 调用前台的消息处理函数
                if (typeof window.handleNewReply === 'function') {
                    window.handleNewReply(messageData);
                }
            }
        }
    }

    sendMessage(message) {
        if (!this.isConnected || !this.websocket) {
            console.error('WebSocket未连接');
            return false;
        }

        const messageData = {
            event: 'client-message',
            data: {
                session_id: this.sessionId,
                message: message,
                sender_type: 'customer'
            }
        };

        this.websocket.send(JSON.stringify(messageData));
        return true;
    }

    reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ WebSocket重连次数超限');
            return;
        }

        this.reconnectAttempts++;
        console.log(`🔄 尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        setTimeout(() => {
            this.connect();
        }, 3000);
    }

    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        this.isConnected = false;
    }
}

// 全局WebSocket实例
window.CustomerServiceWS = null;

// 初始化WebSocket连接
window.initCustomerServiceWebSocket = function(sessionId) {
    if (window.CustomerServiceWS) {
        window.CustomerServiceWS.disconnect();
    }
    
    window.CustomerServiceWS = new CustomerServiceWebSocket(sessionId);
    window.CustomerServiceWS.connect();
};

// 处理新回复的全局函数
window.handleNewReply = function(messageData) {
    console.log('🎉 收到新回复:', messageData);
    
    // 调用前台的addMessage函数
    if (typeof addMessage === 'function') {
        addMessage(messageData.sender_type, messageData.message);
    }
    
    // 播放提示音
    if (typeof playNotificationSound === 'function') {
        playNotificationSound();
    }
    
    // 显示新消息提示
    if (typeof showNewMessageIndicator === 'function' && !window.isOpen) {
        showNewMessageIndicator();
    }
};
