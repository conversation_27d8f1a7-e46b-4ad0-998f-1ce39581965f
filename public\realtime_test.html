<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时消息测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; }
        .messages { height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #fafafa; }
        .message { margin: 5px 0; padding: 8px; border-radius: 8px; }
        .message.customer { background: #e3f2fd; text-align: right; }
        .message.admin { background: #f3e5f5; text-align: left; }
        .message.ai { background: #e8f5e8; text-align: left; }
        input[type="text"] { width: 70%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 实时消息测试</h1>
        
        <div class="grid">
            <!-- 左侧：前台客服 -->
            <div class="panel">
                <h2>💬 前台客服窗口</h2>
                <div>
                    <div>会话ID: <span id="session-id"></span></div>
                    <div>SSE状态: <span id="sse-status" class="status offline">未连接</span></div>
                </div>
                
                <div id="frontend-messages" class="messages"></div>
                
                <div style="margin-top: 10px;">
                    <input type="text" id="customer-message" placeholder="输入客户消息..." onkeypress="handleCustomerKeyPress(event)">
                    <button class="btn" onclick="sendCustomerMessage()">发送</button>
                </div>
                
                <div>
                    <button class="btn" onclick="connectSSE()">连接SSE</button>
                    <button class="btn" onclick="disconnectSSE()">断开SSE</button>
                    <button class="btn success" onclick="testConnection()">测试连接</button>
                </div>
                
                <div id="sse-log" class="log"></div>
            </div>
            
            <!-- 右侧：后台管理 -->
            <div class="panel">
                <h2>👨‍💼 后台管理回复</h2>
                <div>
                    <div>会话数据库ID: <span id="session-db-id">未知</span></div>
                    <div>最后消息ID: <span id="last-message-id">0</span></div>
                </div>
                
                <div id="admin-messages" class="messages"></div>
                
                <div style="margin-top: 10px;">
                    <input type="text" id="admin-reply" placeholder="输入管理员回复..." onkeypress="handleAdminKeyPress(event)">
                    <button class="btn" onclick="sendAdminReply()">发送回复</button>
                </div>
                
                <div>
                    <button class="btn" onclick="loadMessages()">刷新消息</button>
                    <button class="btn success" onclick="sendQuickReply()">快速回复</button>
                    <button class="btn danger" onclick="clearMessages()">清除消息</button>
                </div>
                
                <div id="admin-log" class="log"></div>
            </div>
        </div>
        
        <div class="panel" style="margin-top: 20px;">
            <h2>📊 测试说明</h2>
            <ol>
                <li><strong>连接SSE</strong> - 点击左侧"连接SSE"建立实时连接</li>
                <li><strong>发送客户消息</strong> - 在左侧输入框发送消息</li>
                <li><strong>发送管理员回复</strong> - 在右侧输入框回复消息</li>
                <li><strong>观察实时接收</strong> - 左侧应该立即收到右侧的回复</li>
            </ol>
            <p><strong>预期结果：</strong>右侧发送回复后，左侧应该在1-2秒内收到消息并显示。</p>
        </div>
    </div>

    <script>
    let sessionId = null;
    let sessionDbId = null;
    let eventSource = null;
    let lastMessageId = 0;
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeSession();
    });
    
    // 初始化会话
    function initializeSession() {
        sessionId = 'realtime_test_' + Date.now();
        document.getElementById('session-id').textContent = sessionId;
        logSSE('🚀 会话初始化: ' + sessionId);
    }
    
    // 连接SSE
    function connectSSE() {
        if (eventSource) {
            eventSource.close();
        }
        
        const url = `/test_sse.php?session_id=${sessionId}&last_message_id=${lastMessageId}`;
        logSSE('🔄 连接SSE: ' + url);
        updateSSEStatus('连接中...', 'offline');
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            logSSE('✅ SSE连接成功');
            updateSSEStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                logSSE('📨 收到: ' + data.type + ' - ' + data.message);
                
                if (data.type === 'session_found' && data.session_db_id) {
                    sessionDbId = data.session_db_id;
                    document.getElementById('session-db-id').textContent = sessionDbId;
                    logAdmin('📍 会话数据库ID: ' + sessionDbId);
                }
                
                if (data.type === 'new_reply') {
                    addFrontendMessage(data.sender_type, data.message);
                    lastMessageId = Math.max(lastMessageId, data.message_id);
                    document.getElementById('last-message-id').textContent = lastMessageId;
                    
                    // 播放提示音
                    playNotificationSound();
                    logSSE('🎉 收到新回复！');
                }
                
                if (data.type === 'debug') {
                    logSSE('🐛 调试: ' + data.message);
                }
                
            } catch (e) {
                logSSE('❌ 解析消息失败: ' + e.message);
            }
        };
        
        eventSource.onerror = function() {
            logSSE('❌ SSE连接错误');
            updateSSEStatus('连接错误', 'offline');
        };
    }
    
    // 断开SSE
    function disconnectSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            logSSE('🛑 SSE连接已断开');
            updateSSEStatus('已断开', 'offline');
        }
    }
    
    // 发送客户消息
    function sendCustomerMessage() {
        const input = document.getElementById('customer-message');
        const message = input.value.trim();
        
        if (!message) return;
        
        addFrontendMessage('customer', message);
        input.value = '';
        
        logSSE('📤 客户消息已发送: ' + message);
    }
    
    // 发送管理员回复
    function sendAdminReply() {
        const input = document.getElementById('admin-reply');
        const message = input.value.trim();
        
        if (!message || !sessionDbId) {
            alert('请先连接SSE获取会话ID，然后输入回复消息');
            return;
        }
        
        logAdmin('📤 发送管理员回复...');
        
        // 发送到服务器
        fetch('/admin_reply_test.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=send_reply&session_db_id=${sessionDbId}&reply_message=${encodeURIComponent(message)}`
        })
        .then(response => response.text())
        .then(data => {
            if (data.includes('发送成功')) {
                logAdmin('✅ 管理员回复发送成功');
                addAdminMessage('admin', message);
                input.value = '';
            } else {
                logAdmin('❌ 发送失败');
            }
        })
        .catch(error => {
            logAdmin('❌ 网络错误: ' + error.message);
        });
    }
    
    // 快速回复
    function sendQuickReply() {
        const quickReplies = [
            '您好，我是客服，有什么可以帮助您的吗？',
            '感谢您的咨询，我正在为您查询相关信息...',
            '好的，我明白了您的问题，让我为您处理一下。'
        ];

        const randomReply = quickReplies[Math.floor(Math.random() * quickReplies.length)];
        document.getElementById('admin-reply').value = randomReply + ' [' + new Date().toLocaleTimeString() + ']';
    }

    // 加载消息
    function loadMessages() {
        logAdmin('🔄 刷新消息列表...');

        if (!sessionDbId) {
            logAdmin('❌ 请先连接SSE获取会话ID');
            return;
        }

        // 这里可以添加从服务器加载消息的逻辑
        logAdmin('✅ 消息列表已刷新');
    }

    // 清除消息
    function clearMessages() {
        if (confirm('确定要清除所有消息吗？')) {
            document.getElementById('frontend-messages').innerHTML = '';
            document.getElementById('admin-messages').innerHTML = '';
            document.getElementById('sse-log').innerHTML = '';
            document.getElementById('admin-log').innerHTML = '';
            logSSE('🗑️ 消息已清除');
            logAdmin('🗑️ 消息已清除');
        }
    }
    
    // 添加前台消息
    function addFrontendMessage(sender, message) {
        const messagesDiv = document.getElementById('frontend-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender;
        messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${new Date().toLocaleTimeString()})</small>`;
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
    
    // 添加后台消息
    function addAdminMessage(sender, message) {
        const messagesDiv = document.getElementById('admin-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + sender;
        messageDiv.innerHTML = `<strong>${sender}:</strong> ${message} <small>(${new Date().toLocaleTimeString()})</small>`;
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
    
    // 更新SSE状态
    function updateSSEStatus(text, className) {
        const statusElement = document.getElementById('sse-status');
        statusElement.textContent = text;
        statusElement.className = 'status ' + className;
    }
    
    // 播放提示音
    function playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            console.log('无法播放提示音');
        }
    }
    
    // 测试连接
    function testConnection() {
        if (eventSource && eventSource.readyState === EventSource.OPEN) {
            logSSE('✅ SSE连接正常');
        } else {
            logSSE('❌ SSE连接异常，尝试重连...');
            connectSSE();
        }
    }
    
    // 处理回车键
    function handleCustomerKeyPress(event) {
        if (event.key === 'Enter') {
            sendCustomerMessage();
        }
    }
    
    function handleAdminKeyPress(event) {
        if (event.key === 'Enter') {
            sendAdminReply();
        }
    }
    
    // 日志函数
    function logSSE(message) {
        const logDiv = document.getElementById('sse-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function logAdmin(message) {
        const logDiv = document.getElementById('admin-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    </script>
</body>
</html>
