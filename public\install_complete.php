<?php
/**
 * 完整客服系统安装
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>完整客服系统安装</title>";
echo "<style>body{font-family:Arial;margin:20px;background:#f5f5f5;} .card{background:white;margin:10px;padding:15px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🚀 完整客服系统安装</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='card'>";
    echo "<h2>1. 数据库连接</h2>";
    echo "<p class='success'>✅ 数据库连接成功</p>";
    echo "</div>";

    // 创建数据表
    echo "<div class='card'>";
    echo "<h2>2. 创建数据表</h2>";
    
    $tables = [
        'customer_service_sessions' => "
            CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                `session_id` varchar(255) NOT NULL,
                `user_id` bigint(20) unsigned DEFAULT NULL,
                `visitor_name` varchar(255) DEFAULT NULL,
                `visitor_email` varchar(255) DEFAULT NULL,
                `visitor_ip` varchar(255) DEFAULT NULL,
                `user_agent` text DEFAULT NULL,
                `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active',
                `assigned_admin_id` bigint(20) unsigned DEFAULT NULL,
                `last_activity` timestamp NULL DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                UNIQUE KEY `session_id` (`session_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ",
        'customer_service_messages' => "
            CREATE TABLE IF NOT EXISTS `customer_service_messages` (
                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                `session_id` bigint(20) unsigned NOT NULL,
                `sender_type` enum('customer','admin','ai') NOT NULL,
                `sender_id` bigint(20) unsigned DEFAULT NULL,
                `message` text NOT NULL,
                `attachments` json DEFAULT NULL,
                `is_read` tinyint(1) NOT NULL DEFAULT 0,
                `read_at` timestamp NULL DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `session_id` (`session_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ",
        'ai_auto_reply_rules' => "
            CREATE TABLE IF NOT EXISTS `ai_auto_reply_rules` (
                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `keywords` json NOT NULL,
                `reply_message` text NOT NULL,
                `priority` int(11) NOT NULL DEFAULT 0,
                `is_active` tinyint(1) NOT NULL DEFAULT 1,
                `usage_count` int(11) NOT NULL DEFAULT 0,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        "
    ];

    foreach ($tables as $tableName => $sql) {
        $pdo->exec($sql);
        echo "<p class='success'>✅ 表 $tableName 创建成功</p>";
    }
    echo "</div>";

    // 添加AI规则
    echo "<div class='card'>";
    echo "<h2>3. 添加AI规则</h2>";
    
    $rules = [
        ['问候语', '["hello", "hi", "hey", "你好", "您好"]', 'Hello! 👋 Welcome to our store! How can I help you today?', 10],
        ['物流咨询', '["shipping", "delivery", "物流", "快递", "发货"]', 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days.', 9],
        ['退换货', '["return", "refund", "退货", "退款", "换货"]', 'We have a 30-day return policy! 🔄 If you are not satisfied, you can return items within 30 days.', 8]
    ];

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_auto_reply_rules WHERE name = ?");
    $insertStmt = $pdo->prepare("INSERT INTO ai_auto_reply_rules (name, keywords, reply_message, priority, is_active, usage_count, created_at, updated_at) VALUES (?, ?, ?, ?, 1, 0, NOW(), NOW())");

    foreach ($rules as $rule) {
        $stmt->execute([$rule[0]]);
        if ($stmt->fetchColumn() == 0) {
            $insertStmt->execute($rule);
            echo "<p class='success'>✅ 添加规则: {$rule[0]}</p>";
        } else {
            echo "<p class='info'>ℹ️ 规则已存在: {$rule[0]}</p>";
        }
    }
    echo "</div>";

    // 添加后台菜单
    echo "<div class='card'>";
    echo "<h2>4. 添加后台菜单</h2>";
    
    // 检查菜单表结构
    $stmt = $pdo->query("DESCRIBE st_strongadmin_menu");
    $columns = $stmt->fetchAll();
    $columnNames = array_column($columns, 'Field');
    
    echo "<p class='info'>菜单表字段: " . implode(', ', $columnNames) . "</p>";
    
    // 检查是否已存在
    $stmt = $pdo->prepare("SELECT id FROM st_strongadmin_menu WHERE name = ? AND level = 1");
    $stmt->execute(['在线客服']);
    $existingMenu = $stmt->fetch();
    
    if (!$existingMenu) {
        // 构建插入语句
        $fields = ['level', 'parent_id', 'name', 'route_url', 'status', 'sort', 'created_at', 'updated_at'];
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $stmt = $pdo->prepare("INSERT INTO st_strongadmin_menu (" . implode(',', $fields) . ") VALUES ($placeholders)");
        $stmt->execute([1, 0, '在线客服', '', 1, 90, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
        $parentId = $pdo->lastInsertId();
        
        echo "<p class='success'>✅ 主菜单【在线客服】添加成功 (ID: $parentId)</p>";

        // 添加子菜单
        $subMenus = [
            ['会话管理', 'strongadmin/customer-service/sessions', 1],
            ['AI规则管理', 'strongadmin/customer-service/ai-rules', 2],
            ['统计报表', 'strongadmin/customer-service/statistics', 3]
        ];

        foreach ($subMenus as $menu) {
            $stmt->execute([2, $parentId, $menu[0], $menu[1], 1, $menu[2], date('Y-m-d H:i:s'), date('Y-m-d H:i:s')]);
            echo "<p class='success'>✅ 子菜单【{$menu[0]}】添加成功</p>";
        }
    } else {
        echo "<p class='info'>ℹ️ 后台菜单已存在</p>";
    }
    echo "</div>";

    // 创建必要文件
    echo "<div class='card'>";
    echo "<h2>5. 创建必要文件</h2>";
    
    // 创建图片目录
    if (!is_dir('images')) {
        mkdir('images', 0755, true);
        echo "<p class='success'>✅ 创建图片目录</p>";
    }
    
    // 创建默认头像
    $avatarContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    file_put_contents('images/customer-service-avatar.png', $avatarContent);
    echo "<p class='success'>✅ 创建默认头像</p>";
    
    echo "</div>";

    echo "<div class='card success'>";
    echo "<h2>🎉 安装完成！</h2>";
    echo "<p><strong>现在可以：</strong></p>";
    echo "<ul>";
    echo "<li>🌐 <a href='/'>访问首页测试客服按钮</a></li>";
    echo "<li>🔧 <a href='/strongadmin'>登录后台查看客服菜单</a></li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='card error'>";
    echo "<h2>❌ 安装失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
