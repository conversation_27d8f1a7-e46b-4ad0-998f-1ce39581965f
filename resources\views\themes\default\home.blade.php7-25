@extends('layouts.app')
@push('styles')
<meta http-equiv="pragram" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
<style>
    .carousel-indicators li{
        border: 1px solid gray;
    }
    .carousel-indicators .active{
        border: 1px solid #fff;
        background: gray;
    }
</style>
@endpush
@section('content')
<!-- Banner 轮播图-->
<div class="st-banner">
    <div class="container">
        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
            <!--  <ol class="carousel-indicators">
                <li data-target="#carousel-example-generic" data-slide-to="0" class="active"></li>
                <li data-target="#carousel-example-generic" data-slide-to="1" class=""></li>
            </ol>-->
            <div class="carousel-inner" role="listbox">
                <div class="item active">
                    <a href="#"><img alt="{{config('strongshop.storeName')}}" src="img/banner01.jpg" data-holder-rendered="true"></a>
                </div>
              <!--   <div class="item">
                    <a href="#"><img alt="{{config('strongshop.storeName')}}" src="img/banner02.jpg" data-holder-rendered="true"></a>
                </div>-->
            </div>
        </div>
    </div>
</div>
<div class="st-h20"></div>
  <!-- <div class="st-home-aboutus">
    <div class="container">
        <div class="jumbotron">
            <h1>About {{config('strongshop.storeName')}}</h1>
            <p>
            
            </p>
            <p><a class="btn btn-primary btn-sm" href="#" role="button">Learn more</a></p>
        </div>
    </div>
</div>-->
<div class="st-home-product">
    <!--推荐产品-->
    @if($recommendRows->isNotEmpty())
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_recommend'=>1])}}" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> @lang('Recommend Products')</a></h4>
        </div>
        <div class="row" style="display: flex; flex-wrap: wrap;">
            @foreach($recommendRows as $recommendRow)
            <div class="col-xs-6 col-sm-3 col-md-3 col-lg-3" style="margin-left: 6px;width: 350px; padding: 0 10px; margin-bottom: 30px; box-sizing: border-box; flex: 0 0 350px;">
                <div class="thumbnail" style="border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; padding: 0; margin: 0; background: #fff;">
                    <a href="{{route('product.show.rewrite', ['id'=>$recommendRow->id])}}" class="st-thumb" style="display: block; width: 100%; height: 350px; overflow: hidden; background: #f8f8f8;">
                        <img alt="{{$recommendRow->title}}" src="{{$recommendRow->img_cover}}" class="img-responsive" style="width: 100%; height: 100%; object-fit: cover; object-position: center;" />
                    </a>
                    <div class="caption" style="padding: 15px; text-align: center; background: #fff; height: 100px; display: flex; flex-direction: column; justify-content: space-between;">
                        <h5 title="{{$recommendRow->title}}" style="margin: 0; font-size: 14px; line-height: 1.3; height: 40px; overflow: hidden; display: flex; align-items: center; justify-content: center;"><a href="{{route('product.show.rewrite', ['id'=>$recommendRow->id])}}" style="color: #333; text-decoration: none;">{{$recommendRow->title}}</a></h5>
                        <div style="margin-top: auto;">
                            <p class="st-home-product-price" style="font-size: 16px; font-weight: 600; color: #e74c3c; margin: 0;">@price($recommendRow->sale_price)</p>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    <!--新品-->
    @if($newRows->isNotEmpty())
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_new'=>1])}}" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> @lang('New Products')</a></h4>
        </div>
        <div class="row" style="display: flex; flex-wrap: wrap;">
            @foreach($newRows as $newRow)
            <div class="col-xs-6 col-sm-3 col-md-3 col-lg-3" style="margin-left: 6px;width: 350px; padding: 0 10px; margin-bottom: 30px; box-sizing: border-box; flex: 0 0 350px;">
                <div class="thumbnail" style="border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; padding: 0; margin: 0; background: #fff;">
                    <a href="{{route('product.show.rewrite', ['id'=>$newRow->id])}}" class="st-thumb" style="display: block; width: 100%; height: 350px; overflow: hidden; background: #f8f8f8;">
                        <img alt="{{$newRow->title}}" src="{{$newRow->img_cover}}" class="img-responsive" style="width: 100%; height: 100%; object-fit: cover; object-position: center;" />
                    </a>
                    <div class="caption" style="padding: 15px; text-align: center; background: #fff; height: 100px; display: flex; flex-direction: column; justify-content: space-between;">
                        <h5 title="{{$newRow->title}}" style="margin: 0; font-size: 14px; line-height: 1.3; height: 40px; overflow: hidden; display: flex; align-items: center; justify-content: center;"><a href="{{route('product.show.rewrite', ['id'=>$newRow->id])}}" style="color: #333; text-decoration: none;">{{$newRow->title}}</a></h5>
                        <div style="margin-top: auto;">
                            <p class="st-home-product-price" style="font-size: 16px; font-weight: 600; color: #e74c3c; margin: 0;">@price($newRow->sale_price)</p>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    <!--热卖-->
    @if($hotRows->isNotEmpty())
    <div class="container" style="max-width: 1400px; margin: 0 auto;">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_hot'=>1])}}" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> @lang('Hot Products')</a></h4>
        </div>
        <div class="row" style="display: flex; flex-wrap: wrap;">
            @foreach($hotRows as $hotRow)
            <div class="col-xs-6 col-sm-3 col-md-3 col-lg-3" style="margin-left: 6px;width: 350px; padding: 0 10px; margin-bottom: 30px; box-sizing: border-box; flex: 0 0 350px;">
                <div class="thumbnail" style="border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; padding: 0; margin: 0; background: #fff;">
                    <a href="{{route('product.show.rewrite', ['id'=>$hotRow->id])}}" class="st-thumb" style="display: block; width: 100%; height: 350px; overflow: hidden; background: #f8f8f8;">
                        <img alt="{{$hotRow->title}}" src="{{$hotRow->img_cover}}" class="img-responsive" style="width: 100%; height: 100%; object-fit: cover; object-position: center;" />
                    </a>
                    <div class="caption" style="padding: 15px; text-align: center; background: #fff; height: 100px; display: flex; flex-direction: column; justify-content: space-between;">
                        <h5 title="{{$hotRow->title}}" style="margin: 0; font-size: 14px; line-height: 1.3; height: 40px; overflow: hidden; display: flex; align-items: center; justify-content: center;"><a href="{{route('product.show.rewrite', ['id'=>$hotRow->id])}}" style="color: #333; text-decoration: none;">{{$hotRow->title}}</a></h5>
                        <div style="margin-top: auto;">
                            <p class="st-home-product-price" style="font-size: 16px; font-weight: 600; color: #e74c3c; margin: 0;">@price($hotRow->sale_price)</p>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection
