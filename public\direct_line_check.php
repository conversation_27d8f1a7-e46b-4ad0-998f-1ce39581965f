<?php
// 直接获取页面内容并分析第2677行
header('Content-Type: text/html; charset=utf-8');

try {
    // 获取主页面内容
    $url = 'http://www.strongshop.local/';
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $pageContent = file_get_contents($url, false, $context);
    
    if ($pageContent === false) {
        throw new Exception('无法获取页面内容');
    }
    
    $lines = explode("\n", $pageContent);
    $totalLines = count($lines);
    
    echo "<h1>🎯 第2677行直接检查结果</h1>";
    echo "<p><strong>页面总行数:</strong> $totalLines</p>";
    
    if ($totalLines >= 2677) {
        $targetLine = $lines[2676]; // 数组索引从0开始
        
        echo "<h2>🔍 第2677行内容:</h2>";
        echo "<div style='background: #ffcdd2; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; overflow-x: auto;'>";
        echo htmlspecialchars($targetLine);
        echo "</div>";
        
        echo "<h2>📋 上下文 (第2667-2687行):</h2>";
        echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px;'>";
        
        for ($i = max(0, 2666); $i <= min($totalLines - 1, 2686); $i++) {
            $lineNum = $i + 1;
            $line = $lines[$i] ?? '';
            $isTarget = $lineNum === 2677;
            
            $style = $isTarget ? 'background: #ffcdd2; font-weight: bold;' : 'background: white;';
            echo "<div style='$style padding: 2px 5px; margin: 1px 0; border-radius: 2px;'>";
            echo "<strong>行 $lineNum:</strong> " . htmlspecialchars(substr($line, 0, 150));
            if (strlen($line) > 150) echo '...';
            echo "</div>";
        }
        
        echo "</div>";
        
        // 分析第2677行
        echo "<h2>🔬 第2677行详细分析:</h2>";
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 4px;'>";
        echo "<p><strong>长度:</strong> " . strlen($targetLine) . " 字符</p>";
        echo "<p><strong>左括号数量:</strong> " . substr_count($targetLine, '(') . "</p>";
        echo "<p><strong>右括号数量:</strong> " . substr_count($targetLine, ')') . "</p>";
        echo "<p><strong>包含script标签:</strong> " . (strpos($targetLine, '<script') !== false || strpos($targetLine, '</script>') !== false ? '是' : '否') . "</p>";
        
        // 检查常见的语法错误模式
        $errorPatterns = [
            '/console\.(log|error|warn)\([^)]*$/' => '未闭合的console调用',
            '/alert\([^)]*$/' => '未闭合的alert调用',
            '/fetch\([^)]*$/' => '未闭合的fetch调用',
            '/document\.(querySelector|getElementById)\([^)]*$/' => '未闭合的DOM查询',
            '/JSON\.(stringify|parse)\([^)]*$/' => '未闭合的JSON调用',
            '/`[^`]*\$\{[^}]*$/' => '未闭合的模板字符串',
            '/@json\([^)]*$/' => '未闭合的@json调用'
        ];
        
        $foundPatterns = [];
        foreach ($errorPatterns as $pattern => $name) {
            if (preg_match($pattern, $targetLine)) {
                $foundPatterns[] = $name;
            }
        }
        
        if (!empty($foundPatterns)) {
            echo "<p><strong style='color: #d32f2f;'>检测到的问题模式:</strong></p>";
            echo "<ul>";
            foreach ($foundPatterns as $pattern) {
                echo "<li style='color: #d32f2f;'>$pattern</li>";
            }
            echo "</ul>";
        } else {
            echo "<p><strong style='color: #388e3c;'>未检测到明显的语法错误模式</strong></p>";
        }
        
        // 检查字符编码问题
        if (!mb_check_encoding($targetLine, 'UTF-8')) {
            echo "<p><strong style='color: #d32f2f;'>警告: 检测到字符编码问题</strong></p>";
        }
        
        // 检查特殊字符
        if (preg_match('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', $targetLine)) {
            echo "<p><strong style='color: #d32f2f;'>警告: 检测到控制字符</strong></p>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 4px;'>";
        echo "<h2>❌ 第2677行不存在</h2>";
        echo "<p>页面只有 $totalLines 行，但错误报告在第2677行。</p>";
        echo "<p>这表明错误可能来自:</p>";
        echo "<ul>";
        echo "<li>动态生成的JavaScript代码</li>";
        echo "<li>外部脚本文件</li>";
        echo "<li>浏览器内部处理过程中的行号计算</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h1>❌ 错误</h1>";
    echo "<p>错误信息: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
