<?php
/**
 * 简单的产品图片测试
 * 访问: http://your-domain/simple_test.php
 */

// 基本设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

$basePath = __DIR__;

try {
    // 加载Laravel
    require_once $basePath . '/vendor/autoload.php';
    $app = require_once $basePath . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    $request = Illuminate\Http\Request::capture();
    $response = $kernel->handle($request);

    // 获取产品数据
    use App\Models\Product\Product;
    use Illuminate\Support\Facades\Cache;

    echo "<!DOCTYPE html>";
    echo "<html><head><meta charset='UTF-8'><title>产品图片测试</title>";
    echo "<style>body{font-family:Arial;margin:20px;background:#f5f5f5;} .card{background:white;margin:10px;padding:15px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
    echo "</head><body>";

    echo "<h1>🔍 产品图片数据测试</h1>";

    // 清除缓存
    Cache::forget('product:home');
    echo "<div class='card success'>✅ 首页缓存已清除</div>";

    // 测试推荐产品
    echo "<div class='card'>";
    echo "<h2>📋 推荐产品测试</h2>";
    
    $recommendRows = Product::query()
        ->where('status', 1)
        ->where('is_recommend', 1)
        ->where('hidden', 2)
        ->select(['id', 'title', 'img_cover', 'img_photos'])
        ->limit(3)
        ->get();

    echo "<p>找到 " . $recommendRows->count() . " 个推荐产品</p>";

    foreach ($recommendRows as $product) {
        echo "<div style='border:1px solid #ddd;margin:10px 0;padding:10px;'>";
        echo "<h3>{$product->title} (ID: {$product->id})</h3>";
        echo "<p><strong>封面图:</strong> {$product->img_cover}</p>";
        
        if ($product->img_photos) {
            echo "<p><strong>原始图片数据:</strong> " . substr($product->img_photos, 0, 100) . "...</p>";
            
            if ($product->asset_img_photos) {
                echo "<p class='success'><strong>解析后图片数量:</strong> " . count($product->asset_img_photos) . "</p>";
                
                if (isset($product->asset_img_photos[1])) {
                    echo "<p class='success'>✅ 第二张图片: {$product->asset_img_photos[1]['src']}</p>";
                } else {
                    echo "<p class='error'>❌ 没有第二张图片</p>";
                }
            } else {
                echo "<p class='error'>❌ 图片数据解析失败</p>";
            }
        } else {
            echo "<p class='error'>❌ 没有图片数据</p>";
        }
        echo "</div>";
    }
    echo "</div>";

    // 测试新品
    echo "<div class='card'>";
    echo "<h2>🆕 新品测试</h2>";
    
    $newRows = Product::query()
        ->where('status', 1)
        ->where('is_new', 1)
        ->where('hidden', 2)
        ->select(['id', 'title', 'img_cover', 'img_photos'])
        ->limit(3)
        ->get();

    echo "<p>找到 " . $newRows->count() . " 个新品</p>";

    foreach ($newRows as $product) {
        echo "<div style='border:1px solid #ddd;margin:10px 0;padding:10px;'>";
        echo "<h3>{$product->title}</h3>";
        
        if ($product->asset_img_photos && isset($product->asset_img_photos[1])) {
            echo "<p class='success'>✅ 有第二张图片</p>";
        } else {
            echo "<p class='error'>❌ 没有第二张图片</p>";
        }
        echo "</div>";
    }
    echo "</div>";

    // 检查数据库中有图片的产品
    echo "<div class='card'>";
    echo "<h2>🗄️ 数据库图片数据检查</h2>";
    
    $allProducts = Product::query()
        ->where('status', 1)
        ->whereNotNull('img_photos')
        ->where('img_photos', '!=', '')
        ->select(['id', 'title', 'img_photos'])
        ->limit(5)
        ->get();

    echo "<p>数据库中有图片数据的产品: " . $allProducts->count() . " 个</p>";

    foreach ($allProducts as $product) {
        echo "<div style='border:1px solid #ddd;margin:10px 0;padding:10px;'>";
        echo "<h4>{$product->title}</h4>";
        echo "<p><strong>图片数据长度:</strong> " . strlen($product->img_photos) . " 字符</p>";
        echo "<p><strong>数据预览:</strong> " . htmlspecialchars(substr($product->img_photos, 0, 200)) . "...</p>";
        echo "</div>";
    }
    echo "</div>";

    echo "<div class='card success'>";
    echo "<h2>✅ 测试完成</h2>";
    echo "<p>现在可以访问首页查看效果：<a href='/'>返回首页</a></p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='card error'>";
    echo "<h2>❌ 错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . " 行: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<p><small>测试时间: " . date('Y-m-d H:i:s') . "</small></p>";
echo "</body></html>";
?>
