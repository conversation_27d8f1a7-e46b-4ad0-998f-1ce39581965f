<?php
/**
 * 检查菜单表结构
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>菜单表结构检查</title>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;}</style>";
echo "</head><body>";

echo "<h1>🔍 菜单表结构检查</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p style='color:green;'>✅ 数据库连接成功</p>";
    
    // 检查菜单表结构
    echo "<h2>📋 st_strongadmin_menu 表结构</h2>";
    
    $stmt = $pdo->query("DESCRIBE st_strongadmin_menu");
    $columns = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 显示现有菜单数据示例
    echo "<h2>📋 现有菜单数据示例</h2>";
    
    $stmt = $pdo->query("SELECT * FROM st_strongadmin_menu LIMIT 5");
    $menus = $stmt->fetchAll();
    
    if ($menus) {
        echo "<table>";
        $firstRow = true;
        foreach ($menus as $menu) {
            if ($firstRow) {
                echo "<tr>";
                foreach (array_keys($menu) as $key) {
                    echo "<th>$key</th>";
                }
                echo "</tr>";
                $firstRow = false;
            }
            
            echo "<tr>";
            foreach ($menu as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>菜单表为空</p>";
    }
    
    // 生成正确的INSERT语句
    echo "<h2>🔧 正确的INSERT语句</h2>";
    
    $columnNames = array_column($columns, 'Field');
    $requiredFields = array_intersect($columnNames, ['level', 'parent_id', 'name', 'route_url', 'status', 'sort', 'created_at', 'updated_at']);
    
    echo "<p>可用字段: " . implode(', ', $columnNames) . "</p>";
    echo "<p>建议使用字段: " . implode(', ', $requiredFields) . "</p>";
    
    echo "<pre>";
    echo "INSERT INTO st_strongadmin_menu (";
    echo implode(', ', $requiredFields);
    echo ") VALUES (";
    echo "1, 0, '在线客服', '', 1, 90, NOW(), NOW()";
    echo ");";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color:red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
