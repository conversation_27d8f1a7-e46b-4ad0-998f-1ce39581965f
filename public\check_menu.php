<?php
/**
 * 检查菜单路径
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>检查菜单</title>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background:#f2f2f2;}</style>";
echo "</head><body>";

echo "<h1>🔍 检查后台菜单</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 查找客服菜单
    $stmt = $pdo->query("SELECT * FROM st_strongadmin_menu WHERE name LIKE '%客服%' ORDER BY level, sort");
    $menus = $stmt->fetchAll();
    
    echo "<h2>📋 客服相关菜单</h2>";
    echo "<table>";
    echo "<tr><th>ID</th><th>级别</th><th>父ID</th><th>名称</th><th>路由URL</th><th>状态</th><th>排序</th></tr>";
    
    foreach ($menus as $menu) {
        echo "<tr>";
        echo "<td>{$menu['id']}</td>";
        echo "<td>{$menu['level']}</td>";
        echo "<td>{$menu['parent_id']}</td>";
        echo "<td><strong>{$menu['name']}</strong></td>";
        echo "<td><code>{$menu['route_url']}</code></td>";
        echo "<td>{$menu['status']}</td>";
        echo "<td>{$menu['sort']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔧 修复菜单路径</h2>";
    echo "<p>点击下面按钮修复菜单路径：</p>";
    
    if (isset($_POST['fix_menu'])) {
        // 修复菜单路径
        $updates = [
            ['name' => '会话管理', 'route_url' => 'customer-service/sessions'],
            ['name' => 'AI规则管理', 'route_url' => 'customer-service/ai-rules'],
            ['name' => '统计报表', 'route_url' => 'customer-service/statistics']
        ];
        
        foreach ($updates as $update) {
            $stmt = $pdo->prepare("UPDATE st_strongadmin_menu SET route_url = ? WHERE name = ?");
            $stmt->execute([$update['route_url'], $update['name']]);
            echo "<p style='color:green;'>✅ 更新菜单【{$update['name']}】路径为：{$update['route_url']}</p>";
        }
        
        echo "<p style='color:blue;'>🎉 菜单路径修复完成！请刷新后台页面测试。</p>";
    } else {
        echo "<form method='post'>";
        echo "<button type='submit' name='fix_menu' style='background:#007bff;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;'>修复菜单路径</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<p style='color:red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
