/**
 * 超级简单的WebSocket客户端 - 不需要额外依赖
 */

class SimpleCustomerServiceWS {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.websocket = null;
        this.isConnected = false;
        this.messageHandlers = [];
    }

    connect() {
        try {
            // 使用现有的Pusher配置连接
            const wsUrl = `wss://ws-mt1.pusher.com/app/858067385143ca395b12?protocol=7&client=js&version=4.3.1&flash=false`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('✅ 简单WebSocket连接成功');
                this.isConnected = true;
                this.subscribe();
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (e) {
                    console.error('解析消息失败:', e);
                }
            };

            this.websocket.onclose = () => {
                console.log('❌ WebSocket连接关闭');
                this.isConnected = false;
                // 3秒后重连
                setTimeout(() => this.connect(), 3000);
            };

            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket错误:', error);
            };

        } catch (e) {
            console.error('WebSocket初始化失败:', e);
        }
    }

    subscribe() {
        if (!this.isConnected) return;

        // 订阅客服频道
        const subscribeMessage = {
            event: 'pusher:subscribe',
            data: {
                channel: `customer-service.${this.sessionId}`
            }
        };

        this.websocket.send(JSON.stringify(subscribeMessage));
        console.log('📡 订阅频道:', `customer-service.${this.sessionId}`);
    }

    handleMessage(data) {
        console.log('📨 收到消息:', data);

        if (data.event === 'pusher:subscription_succeeded') {
            console.log('✅ 频道订阅成功');
            return;
        }

        if (data.event === 'new.message') {
            const messageData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
            
            if (messageData.sender_type === 'admin') {
                // 触发消息处理
                this.messageHandlers.forEach(handler => handler(messageData));
            }
        }
    }

    onMessage(handler) {
        this.messageHandlers.push(handler);
    }

    disconnect() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 全局变量
window.simpleWS = null;

// 初始化函数
window.initSimpleWebSocket = function(sessionId, onMessageCallback) {
    if (window.simpleWS) {
        window.simpleWS.disconnect();
    }
    
    window.simpleWS = new SimpleCustomerServiceWS(sessionId);
    
    if (onMessageCallback) {
        window.simpleWS.onMessage(onMessageCallback);
    }
    
    window.simpleWS.connect();
    
    console.log('🚀 简单WebSocket客服系统已启动');
};
