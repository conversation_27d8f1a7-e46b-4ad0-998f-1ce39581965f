<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>修复重复路由前缀</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 修复重复路由前缀</h1>";

try {
    // 1. 查找有问题的菜单项
    echo "<h3>1. 查找有问题的菜单项</h3>";
    $problemMenus = DB::select("
        SELECT id, name, route_url 
        FROM st_strongadmin_menu 
        WHERE route_url LIKE 'strongadmin/%'
        AND name IN ('会话管理', 'AI规则管理', '统计报表')
    ");
    
    if (empty($problemMenus)) {
        echo "<div class='info'>✅ 没有发现重复前缀的菜单项</div>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>名称</th><th>当前路由</th><th>应该修正为</th></tr>";
        foreach ($problemMenus as $menu) {
            $correctRoute = str_replace('strongadmin/', '', $menu->route_url);
            echo "<tr>";
            echo "<td>{$menu->id}</td>";
            echo "<td>{$menu->name}</td>";
            echo "<td><code>{$menu->route_url}</code></td>";
            echo "<td><code>{$correctRoute}</code></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 2. 执行修复
        echo "<h3>2. 执行修复</h3>";
        foreach ($problemMenus as $menu) {
            $correctRoute = str_replace('strongadmin/', '', $menu->route_url);
            
            DB::table('st_strongadmin_menu')
              ->where('id', $menu->id)
              ->update([
                  'route_url' => $correctRoute,
                  'updated_at' => now()
              ]);
            
            echo "<div class='success'>✅ 修复菜单【{$menu->name}】: {$menu->route_url} → {$correctRoute}</div>";
        }
    }
    
    // 3. 验证修复结果
    echo "<h3>3. 验证修复结果</h3>";
    $allMenus = DB::select("
        SELECT id, name, route_url, parent_id, level 
        FROM st_strongadmin_menu 
        WHERE name LIKE '%客服%' OR name IN ('设置中心', '离线留言', '会话管理', 'AI规则管理', '统计报表')
        ORDER BY parent_id, sort
    ");
    
    echo "<table>";
    echo "<tr><th>ID</th><th>名称</th><th>路由URL</th><th>完整访问路径</th><th>级别</th></tr>";
    foreach ($allMenus as $menu) {
        $fullPath = $menu->route_url ? '/strongadmin/' . $menu->route_url : '/strongadmin/';
        echo "<tr>";
        echo "<td>{$menu->id}</td>";
        echo "<td><strong>{$menu->name}</strong></td>";
        echo "<td><code>{$menu->route_url}</code></td>";
        echo "<td><a href='{$fullPath}' target='_blank'>{$fullPath}</a></td>";
        echo "<td>{$menu->level}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. 测试链接
    echo "<h3>4. 测试所有客服功能链接</h3>";
    $testLinks = [
        'customer-service/settings' => '设置中心',
        'customer-service/offline-messages' => '离线留言', 
        'customer-service/sessions' => '会话管理',
        'customer-service/ai-rules' => 'AI规则管理',
        'customer-service/statistics' => '统计报表'
    ];
    
    echo "<div class='info'>";
    echo "<p><strong>请测试以下链接是否正常工作:</strong></p>";
    echo "<ul>";
    foreach ($testLinks as $route => $name) {
        $fullUrl = '/strongadmin/' . $route;
        echo "<li><a href='{$fullUrl}' target='_blank'>{$name}</a> - <code>{$fullUrl}</code></li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='success'>🎉 路由修复完成！现在所有菜单链接应该都能正常工作了。</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 修复失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
