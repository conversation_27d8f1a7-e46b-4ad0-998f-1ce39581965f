<?php
/**
 * 简化客服系统安装
 */

echo "🚀 安装客服系统...\n";

// 启动Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';

try {
    // 1. 执行SQL文件
    echo "1. 创建数据表和菜单...\n";
    $sql = file_get_contents('create_customer_service_tables.sql');
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            DB::statement($statement);
        }
    }
    echo "✅ 数据表和菜单创建完成\n";

    // 2. 清除缓存
    echo "2. 清除缓存...\n";
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    echo "✅ 缓存清除完成\n";

    // 3. 创建图片目录
    echo "3. 创建图片目录...\n";
    if (!is_dir('public/images')) {
        mkdir('public/images', 0755, true);
    }
    echo "✅ 图片目录创建完成\n";

    echo "\n🎉 安装完成！\n";
    echo "现在可以：\n";
    echo "1. 访问网站查看右下角客服按钮\n";
    echo "2. 登录后台查看【在线客服】菜单\n";

} catch (Exception $e) {
    echo "❌ 安装失败: " . $e->getMessage() . "\n";
}
?>
