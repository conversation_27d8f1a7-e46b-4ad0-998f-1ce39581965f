<?php
/**
 * 测试路由是否工作
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>测试路由</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .test-btn{padding:10px 20px;margin:5px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;}</style>";
echo "</head><body>";

echo "<h1>🔍 测试客服系统路由</h1>";

$routes = [
    '/customer-service/status' => 'GET - 获取在线状态',
    '/customer-service/message-stream?session_id=test&last_message_id=0' => 'GET - SSE消息流',
    '/api/customer-service/status' => 'GET - 系统状态',
    '/api/customer-service/csrf-token' => 'GET - CSRF Token'
];

echo "<h2>📋 测试路由列表</h2>";
echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
echo "<tr><th>路由</th><th>描述</th><th>状态</th><th>操作</th></tr>";

foreach ($routes as $route => $description) {
    echo "<tr>";
    echo "<td>$route</td>";
    echo "<td>$description</td>";
    echo "<td id='status-" . md5($route) . "'>未测试</td>";
    echo "<td><button class='test-btn' onclick=\"testRoute('$route', '" . md5($route) . "')\">测试</button></td>";
    echo "</tr>";
}

echo "</table>";

echo "<div id='test-results' style='margin-top:20px;padding:10px;background:#f8f9fa;border-radius:4px;'></div>";

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/test_frontend_sse.html' target='_blank'>前台SSE测试</a></li>";
echo "<li><a href='/test_backend_reply.html' target='_blank'>后台回复测试</a></li>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台管理</a></li>";
echo "</ul>";

echo "<script>
function testRoute(route, hashId) {
    const statusCell = document.getElementById('status-' + hashId);
    const results = document.getElementById('test-results');
    
    statusCell.innerHTML = '<span style=\"color:orange;\">测试中...</span>';
    results.innerHTML += '<p class=\"info\">🔄 测试路由: ' + route + '</p>';
    
    fetch(route, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        results.innerHTML += '<p class=\"info\">📊 响应状态: ' + response.status + '</p>';
        
        if (response.ok) {
            statusCell.innerHTML = '<span class=\"success\">✅ ' + response.status + '</span>';
            return response.text();
        } else {
            statusCell.innerHTML = '<span class=\"error\">❌ ' + response.status + '</span>';
            throw new Error('HTTP ' + response.status);
        }
    })
    .then(data => {
        results.innerHTML += '<p class=\"success\">✅ 路由工作正常</p>';
        if (data.length < 500) {
            results.innerHTML += '<pre>' + data + '</pre>';
        } else {
            results.innerHTML += '<p class=\"info\">响应内容较长，已省略...</p>';
        }
    })
    .catch(error => {
        results.innerHTML += '<p class=\"error\">❌ 路由测试失败: ' + error.message + '</p>';
    });
}

// 页面加载时自动测试所有路由
document.addEventListener('DOMContentLoaded', function() {
    const routes = [
        '/customer-service/status',
        '/api/customer-service/status',
        '/api/customer-service/csrf-token'
    ];
    
    routes.forEach(function(route, index) {
        setTimeout(function() {
            testRoute(route, '" . md5('/customer-service/status') . "'.replace('/customer-service/status', route));
        }, index * 1000);
    });
});
</script>";

echo "</body></html>";
?>
