<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单SSE测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .test-btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; }
        .status { padding: 5px 10px; border-radius: 15px; font-size: 12px; margin: 5px; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 简单SSE测试</h1>
        
        <div class="test-panel">
            <h2>📊 SSE连接测试</h2>
            <div>
                <span>连接状态: </span>
                <span id="connection-status" class="status offline">未连接</span>
            </div>
            <div style="margin: 10px 0;">
                <button class="test-btn" onclick="testSimpleSSE()">测试简单SSE</button>
                <button class="test-btn" onclick="testFrontendSSE()">测试前台SSE</button>
                <button class="test-btn" onclick="stopSSE()">停止连接</button>
            </div>
            <div id="sse-log" class="log"></div>
        </div>
        
        <div class="test-panel">
            <h2>🧪 测试URL</h2>
            <ul>
                <li><a href="/simple-sse" target="_blank">/simple-sse</a> - 最简单的SSE</li>
                <li><a href="/test-sse" target="_blank">/test-sse</a> - 标准SSE测试</li>
                <li><a href="/frontend-sse?session_id=test&last_message_id=0" target="_blank">/frontend-sse</a> - 前台SSE</li>
            </ul>
        </div>
        
        <div class="test-panel">
            <h2>🔗 相关链接</h2>
            <a href="/test_persistent_user.html" target="_blank" class="test-btn">持久化用户系统</a>
            <a href="/debug_message_flow.php" target="_blank" class="test-btn">调试消息流程</a>
            <a href="/" target="_blank" class="test-btn">前台首页</a>
        </div>
    </div>

    <script>
    let eventSource = null;
    
    // 测试简单SSE
    function testSimpleSSE() {
        log('🔄 测试简单SSE连接...');
        
        if (eventSource) {
            eventSource.close();
        }
        
        eventSource = new EventSource('/simple-sse');
        
        eventSource.onopen = function() {
            log('✅ 简单SSE连接成功');
            updateStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 收到简单SSE消息: ' + JSON.stringify(data));
            } catch (e) {
                log('📨 收到简单SSE消息: ' + event.data);
            }
        };
        
        eventSource.onerror = function(event) {
            log('❌ 简单SSE连接错误');
            updateStatus('连接错误', 'offline');
        };
    }
    
    // 测试前台SSE
    function testFrontendSSE() {
        log('🔄 测试前台SSE连接...');
        
        if (eventSource) {
            eventSource.close();
        }
        
        const sessionId = 'test_session_' + Date.now();
        const url = `/frontend-sse?session_id=${sessionId}&last_message_id=0`;
        log('📡 连接URL: ' + url);
        
        eventSource = new EventSource(url);
        
        eventSource.onopen = function() {
            log('✅ 前台SSE连接成功');
            updateStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 收到前台SSE消息: ' + JSON.stringify(data, null, 2));
                
                if (data.type === 'connected') {
                    log('🎉 前台SSE连接确认！');
                } else if (data.type === 'session_created') {
                    log('🆕 新会话已创建，ID: ' + data.session_db_id);
                } else if (data.type === 'heartbeat') {
                    log('💓 心跳包');
                } else if (data.type === 'error') {
                    log('❌ SSE错误: ' + data.message);
                }
            } catch (e) {
                log('❌ 解析SSE消息失败: ' + e.message);
                log('📨 原始消息: ' + event.data);
            }
        };
        
        eventSource.onerror = function(event) {
            log('❌ 前台SSE连接错误');
            updateStatus('连接错误', 'offline');
            
            // 显示更多错误信息
            fetch(`/frontend-sse?session_id=test&last_message_id=0`)
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            log('❌ HTTP错误 ' + response.status + ': ' + text);
                        });
                    }
                })
                .catch(error => {
                    log('❌ 网络错误: ' + error.message);
                });
        };
    }
    
    // 停止SSE连接
    function stopSSE() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
            log('🛑 SSE连接已停止');
            updateStatus('已断开', 'offline');
        }
    }
    
    // 更新连接状态
    function updateStatus(text, className) {
        const statusElement = document.getElementById('connection-status');
        statusElement.textContent = text;
        statusElement.className = 'status ' + className;
    }
    
    // 记录日志
    function log(message) {
        const logDiv = document.getElementById('sse-log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    // 页面加载时自动测试
    document.addEventListener('DOMContentLoaded', function() {
        log('📋 页面加载完成');
        log('💡 点击按钮开始测试SSE连接');
    });
    </script>
</body>
</html>
