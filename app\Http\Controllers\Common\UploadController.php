<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class UploadController extends Controller
{

    public function image(Request $request)
    {
        $uploadLimitMimes = config('strongshop.productImage.uploadLimitMimes');
        $uploadLimitSize = config('strongshop.productImage.uploadLimitSize');
        $uploadLimitWidth = config('strongshop.productImage.uploadLimitWidth');
        $uploadLimitHeight = config('strongshop.productImage.uploadLimitHeight');

        // 扩展支持的图片格式
        $supportedMimes = 'jpg,jpeg,png,gif,webp,bmp,tiff,svg,ico,heic,heif,avif';

        $rules = [
            'file' => ['required', 'file', "max:{$uploadLimitSize}"],
            'thumb' => ['boolean'],
        ];

        //宽度限制
        if ($uploadLimitWidth > 0)
        {
            $rules = array_merge_recursive($rules, ['file' => "dimensions:width={$uploadLimitWidth}"]);
        }
        //高度限制
        if ($uploadLimitHeight > 0)
        {
            $rules = array_merge_recursive($rules, ['file' => "dimensions:height={$uploadLimitHeight}"]);
        }

        $validator = Validator::make($request->all(), $rules, ['file.dimensions' => ":attribute 尺寸不正确，限制为：{$uploadLimitWidth}x{$uploadLimitHeight}"], ['file' => '上传图片']);
        if ($validator->fails())
        {
            return ['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        $file = $request->file('file');

        // 验证是否为图片文件
        $validationResult = $this->isValidImageFile($file);
        if ($validationResult !== true) {
            return ['code' => 3002, 'message' => $validationResult, 'data' => []];
        }

        // 统一转换为PNG格式并优化
        try {
            $processedImage = $this->convertToPngAndOptimize($file);
            if (!$processedImage) {
                return ['code' => 3003, 'message' => '图片处理失败，请检查文件格式是否正确', 'data' => []];
            }
        } catch (\Exception $e) {
            \Log::error('图片处理异常: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType()
            ]);
            return ['code' => 3004, 'message' => '图片处理错误: ' . $e->getMessage(), 'data' => []];
        }

        // 使用处理后的PNG图片数据
        $contents = $processedImage['data'];
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $path = date('Ym') . '/' . $originalName . '_' . uniqid() . '.png'; // 统一使用PNG扩展名
        Storage::disk('public')->put($path, $contents); //存储到本地

        /*
         * 生成缩略图
         */
        $path_thumb = ''; //缩略图生成路径名称
        if ($request->thumb)
        {
            $thumb_suffix_name = config('strongshop.productImage.thumb.suffix_name'); //缩略图生成文件后缀名称
            $thumb_width = config('strongshop.productImage.thumb.width'); //缩略图生成 宽度
            $thumb_height = config('strongshop.productImage.thumb.height'); //缩略图生成 高度

            $path_arr = pathinfo($path); //路径信息
            $dirname = $path_arr['dirname']; //路径
            $extension = $path_arr['extension']; //扩展名
            $filename = $path_arr['filename']; //文件名称
            $path_thumb = $dirname . '/' . $filename . $thumb_suffix_name . '.' . $extension; //缩略图生成路径名称

            $img = Image::make(Storage::disk('public')->get($path));
            $_width = $img->width();
            $_height = $img->height();

            $width = $_width >= $thumb_width ? $thumb_width : $_width;
            $rate = $_width / $thumb_width; //计算缩略图比例
            $height = $thumb_height > 0 ? $thumb_height : $_height / $rate;

            //重置尺寸并存储缩略图至本地
            $img->resize($width, $height);
            $path_thumb_contents = (string) $img->save(config('filesystems.disks.public.root') . DIRECTORY_SEPARATOR . $path_thumb);

            //缩略图信息
            $url_thumb = Storage::disk('public')->url($path_thumb); //前端可访问图片url
            $url_arr_thumb = parse_url($url_thumb);
            $path_thumb = $url_arr_thumb['path'];
        }

        //图片信息
        $url = Storage::disk('public')->url($path); //前端可访问图片url
        $url_arr = parse_url($url);
        $path = $url_arr['path'];

        //存储到七牛云
        if (config('filesystems.disks.qiniu.driver') === 'qiniu')
        {
            Storage::disk('qiniu')->put($path, $contents);
            if ($path_thumb)
            {
                Storage::disk('qiniu')->put($path_thumb, $path_thumb_contents);
            }
        }
        //存储到阿里云
        if (config('filesystems.disks.oss.driver') === 'oss')
        {
            Storage::disk('oss')->put($path, $contents);
            if ($path_thumb)
            {
                Storage::disk('oss')->put($path_thumb, $path_thumb_contents);
            }
        }

        return ['code' => 0, 'message' => 'success',
            'data' => [
                'src' => $path,
                'src_thumb' => $path_thumb ? $path_thumb : '',
                'title' => $file->getClientOriginalName(),
            ]
        ];
    }

    /**
     * wangEditor 富文本编辑器
     * https://www.wangeditor.com/
     * @param Request $request
     * @return type
     */
    public function imageWangEditor(Request $request)
    {
        $rules = [
            'file' => ['required', 'file', 'max:' . config('strongshop.productImage.uploadLimitSize')],
        ];
        $validator = Validator::make($request->all(), $rules, [], ['file' => '上传图片']);
        if ($validator->fails())
        {
            return ['errno' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()];
        }

        $file = $request->file('file');

        // 验证是否为图片文件
        if (!$this->isValidImageFile($file)) {
            return ['errno' => 3002, 'message' => '不支持的图片格式', 'data' => []];
        }

        // 统一转换为PNG格式并优化
        try {
            $processedImage = $this->convertToPngAndOptimize($file);
            if (!$processedImage) {
                return ['errno' => 3003, 'message' => '图片处理失败，请检查文件格式是否正确', 'data' => []];
            }
        } catch (\Exception $e) {
            \Log::error('图片处理异常: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType()
            ]);
            return ['errno' => 3004, 'message' => '图片处理错误: ' . $e->getMessage(), 'data' => []];
        }

        // 使用处理后的PNG图片数据
        $contents = $processedImage['data'];
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $path = date('Ym') . '/' . $originalName . '_' . uniqid() . '.png';

        //存储到本地
        Storage::disk('public')->put($path, $contents);
        
        //前端可访问图片url
        $url = Storage::disk('public')->url($path);
        $url_arr = parse_url($url);
        $path = $url_arr['path'];
        
        //存储到七牛云
        if (config('filesystems.disks.qiniu.driver') === 'qiniu')
        {
            Storage::disk('qiniu')->put($path, $contents);
        }
        //存储到阿里云
        if (config('filesystems.disks.oss.driver') === 'oss')
        {
            Storage::disk('oss')->put($path, $contents);
        }
        
        $data[] = [
            'url' => asset($path),
            'alt' => $file->getClientOriginalName(),
            'href' => '',
        ];
        return ['errno' => 0, 'message' => 'success', 'data' => $data];
    }

    /**
     * 验证是否为有效的图片文件
     */
    private function isValidImageFile($file)
    {
        // 支持的图片格式
        $supportedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        $extension = strtolower($file->getClientOriginalExtension());

        // 检查扩展名
        if (!in_array($extension, $supportedExtensions)) {
            if ($extension === 'avif') {
                return "AVIF格式暂不支持，请先转换为PNG格式再上传。在线转换工具：https://convertio.co/avif-png/";
            }
            return "不支持的文件格式: {$extension}。支持的格式: " . implode(', ', $supportedExtensions);
        }

        // 检查MIME类型
        $mimeType = $file->getMimeType();
        $supportedMimes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'image/bmp', 'image/tiff', 'image/svg+xml', 'image/x-icon',
            'image/heic', 'image/heif', 'image/avif', 'image/avif-sequence',
            'image/x-avif', 'application/octet-stream' // AVIF有时被识别为这些类型
        ];

        // 对于现代格式，检查是否有处理工具
        $modernFormats = ['avif', 'heic', 'heif', 'svg'];
        if (in_array($extension, $modernFormats)) {
            if (!class_exists('Intervention\Image\Facades\Image')) {
                return "处理 {$extension} 格式需要安装 Intervention Image 包。请运行: composer require intervention/image";
            }
        }

        // 对于AVIF格式，进行特殊检查
        if ($extension === 'avif') {
            // 检查文件头是否包含AVIF签名
            $fileContent = file_get_contents($file->getRealPath(), false, null, 0, 32);
            if (strpos($fileContent, 'ftypavif') !== false || strpos($fileContent, 'ftypavis') !== false) {
                return true; // 通过文件头验证的AVIF文件
            }

            if (!in_array($mimeType, $supportedMimes)) {
                return "AVIF文件格式验证失败。检测到的MIME类型: {$mimeType}";
            }
        } else {
            if (!in_array($mimeType, $supportedMimes)) {
                return "不支持的MIME类型: {$mimeType}";
            }
        }

        return true;
    }

    /**
     * 将图片转换为PNG格式并优化
     */
    private function convertToPngAndOptimize($file)
    {
        try {
            $extension = strtolower($file->getClientOriginalExtension());
            $filePath = $file->getRealPath();

            // 根据不同格式创建图像资源
            $image = $this->createImageFromFile($filePath, $extension);

            if (!$image) {
                return false;
            }

            // 获取原始尺寸
            $originalWidth = imagesx($image);
            $originalHeight = imagesy($image);

            // 计算优化后的尺寸（保持宽高比）
            $optimizedDimensions = $this->calculateOptimizedDimensions($originalWidth, $originalHeight);

            // 创建优化后的图像
            $optimizedImage = imagecreatetruecolor($optimizedDimensions['width'], $optimizedDimensions['height']);

            // 保持PNG透明度
            imagealphablending($optimizedImage, false);
            imagesavealpha($optimizedImage, true);
            $transparent = imagecolorallocatealpha($optimizedImage, 255, 255, 255, 127);
            imagefill($optimizedImage, 0, 0, $transparent);

            // 使用高质量重采样
            imagecopyresampled(
                $optimizedImage, $image,
                0, 0, 0, 0,
                $optimizedDimensions['width'], $optimizedDimensions['height'],
                $originalWidth, $originalHeight
            );

            // 输出为PNG格式
            ob_start();
            imagepng($optimizedImage, null, $this->calculatePngCompressionLevel($file->getSize()));
            $pngData = ob_get_contents();
            ob_end_clean();

            // 清理内存
            imagedestroy($image);
            imagedestroy($optimizedImage);

            return [
                'data' => $pngData,
                'width' => $optimizedDimensions['width'],
                'height' => $optimizedDimensions['height'],
                'original_size' => $file->getSize(),
                'compressed_size' => strlen($pngData)
            ];

        } catch (\Exception $e) {
            \Log::error('图片转换失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 根据文件格式创建图像资源
     */
    private function createImageFromFile($filePath, $extension)
    {
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                return imagecreatefromjpeg($filePath);
            case 'png':
                return imagecreatefrompng($filePath);
            case 'gif':
                return imagecreatefromgif($filePath);
            case 'webp':
                return function_exists('imagecreatefromwebp') ? imagecreatefromwebp($filePath) : false;
            case 'bmp':
                return function_exists('imagecreatefrombmp') ? imagecreatefrombmp($filePath) : false;
            case 'avif':
                // AVIF格式处理
                return $this->handleAvifImage($filePath);
            case 'heic':
            case 'heif':
                // HEIC/HEIF格式处理
                return $this->handleHeicImage($filePath);
            case 'svg':
                // SVG需要特殊处理
                return $this->handleSvgImage($filePath);
            default:
                // 对于其他格式，尝试使用Intervention Image或其他方法
                return $this->handleOtherFormats($filePath, $extension);
        }
    }

    /**
     * 计算优化后的尺寸
     */
    private function calculateOptimizedDimensions($originalWidth, $originalHeight)
    {
        // 设置最大尺寸限制
        $maxWidth = 2048;
        $maxHeight = 2048;

        // 如果图片尺寸在合理范围内，保持原尺寸
        if ($originalWidth <= $maxWidth && $originalHeight <= $maxHeight) {
            return ['width' => $originalWidth, 'height' => $originalHeight];
        }

        // 计算缩放比例
        $widthRatio = $maxWidth / $originalWidth;
        $heightRatio = $maxHeight / $originalHeight;
        $ratio = min($widthRatio, $heightRatio);

        return [
            'width' => (int)($originalWidth * $ratio),
            'height' => (int)($originalHeight * $ratio)
        ];
    }

    /**
     * 根据文件大小计算PNG压缩级别
     */
    private function calculatePngCompressionLevel($fileSize)
    {
        // PNG压缩级别：0-9，0为无压缩，9为最大压缩
        if ($fileSize > 5 * 1024 * 1024) { // 大于5MB
            return 9; // 最大压缩
        } elseif ($fileSize > 2 * 1024 * 1024) { // 大于2MB
            return 7; // 高压缩
        } elseif ($fileSize > 1024 * 1024) { // 大于1MB
            return 5; // 中等压缩
        } else {
            return 3; // 轻度压缩，保持质量
        }
    }

    /**
     * 处理AVIF格式图片
     */
    private function handleAvifImage($filePath)
    {
        // 首先尝试使用Intervention Image
        if (class_exists('Intervention\Image\Facades\Image')) {
            try {
                $img = Image::make($filePath);
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.png';
                $img->save($tempPath, 100, 'png');
                $image = imagecreatefrompng($tempPath);
                unlink($tempPath);
                return $image;
            } catch (\Exception $e) {
                \Log::warning('Intervention Image处理AVIF失败: ' . $e->getMessage());
            }
        }

        // 如果Intervention Image不可用，尝试使用ImageMagick命令行
        if ($this->isCommandAvailable('magick')) {
            try {
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.png';
                $command = "magick " . escapeshellarg($filePath) . " " . escapeshellarg($tempPath);
                exec($command, $output, $returnCode);

                if ($returnCode === 0 && file_exists($tempPath)) {
                    $image = imagecreatefrompng($tempPath);
                    unlink($tempPath);
                    return $image;
                }
            } catch (\Exception $e) {
                \Log::warning('ImageMagick处理AVIF失败: ' . $e->getMessage());
            }
        }

        return false;
    }

    /**
     * 处理HEIC/HEIF格式图片
     */
    private function handleHeicImage($filePath)
    {
        // 尝试使用Intervention Image
        if (class_exists('Intervention\Image\Facades\Image')) {
            try {
                $img = Image::make($filePath);
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.png';
                $img->save($tempPath, 100, 'png');
                $image = imagecreatefrompng($tempPath);
                unlink($tempPath);
                return $image;
            } catch (\Exception $e) {
                \Log::warning('Intervention Image处理HEIC失败: ' . $e->getMessage());
            }
        }

        return false;
    }

    /**
     * 处理SVG格式图片
     */
    private function handleSvgImage($filePath)
    {
        // 尝试使用Intervention Image
        if (class_exists('Intervention\Image\Facades\Image')) {
            try {
                $img = Image::make($filePath);
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.png';
                $img->save($tempPath, 100, 'png');
                $image = imagecreatefrompng($tempPath);
                unlink($tempPath);
                return $image;
            } catch (\Exception $e) {
                \Log::warning('Intervention Image处理SVG失败: ' . $e->getMessage());
            }
        }

        return false;
    }

    /**
     * 处理其他格式图片
     */
    private function handleOtherFormats($filePath, $extension)
    {
        // 尝试使用Intervention Image
        if (class_exists('Intervention\Image\Facades\Image')) {
            try {
                $img = Image::make($filePath);
                $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.png';
                $img->save($tempPath, 100, 'png');
                $image = imagecreatefrompng($tempPath);
                unlink($tempPath);
                return $image;
            } catch (\Exception $e) {
                \Log::warning("Intervention Image处理{$extension}格式失败: " . $e->getMessage());
            }
        }

        return false;
    }

    /**
     * 检查命令是否可用
     */
    private function isCommandAvailable($command)
    {
        $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ['pipe', 'r'], // stdin
                1 => ['pipe', 'w'], // stdout
                2 => ['pipe', 'w'], // stderr
            ],
            $pipes
        );

        if ($process !== false) {
            $stdout = stream_get_contents($pipes[1]);
            $stderr = stream_get_contents($pipes[2]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            $returnCode = proc_close($process);
            return $returnCode === 0;
        }

        return false;
    }

    /**
     * 在没有Intervention Image的情况下处理AVIF文件
     */
    private function handleAvifWithoutIntervention($file)
    {
        try {
            // 记录日志
            \Log::info('AVIF文件临时处理: ' . $file->getClientOriginalName());

            // 创建一个简单的占位图像（400x300像素的PNG）
            $image = imagecreate(400, 300);
            $bgColor = imagecolorallocate($image, 240, 240, 240);
            $textColor = imagecolorallocate($image, 100, 100, 100);

            // 添加文本说明
            $text = "AVIF Format";
            $text2 = "Install Intervention Image";
            $text3 = "for full support";

            imagestring($image, 5, 150, 120, $text, $textColor);
            imagestring($image, 3, 120, 150, $text2, $textColor);
            imagestring($image, 3, 140, 170, $text3, $textColor);

            // 返回GD图像资源，与其他处理方法保持一致
            return $image;

        } catch (\Exception $e) {
            \Log::error('AVIF临时处理失败: ' . $e->getMessage());
            return false;
        }
    }

}
