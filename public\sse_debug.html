<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.danger { background: #dc3545; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; text-align: center; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SSE调试工具</h1>
        
        <div class="panel">
            <h2>📊 连接状态</h2>
            <div id="connection-status" class="status offline">未连接</div>
            <div>
                <button class="btn" onclick="testMinimalSSE()">测试最简单SSE</button>
                <button class="btn" onclick="testWorkingSSE()">测试工作SSE</button>
                <button class="btn" onclick="testBasicSSE()">测试基础SSE</button>
                <button class="btn danger" onclick="stopAllConnections()">停止所有连接</button>
            </div>
        </div>
        
        <div class="panel">
            <h2>📝 详细日志</h2>
            <div id="log" class="log"></div>
            <button class="btn" onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="panel">
            <h2>🧪 测试说明</h2>
            <ol>
                <li><strong>测试最简单SSE</strong> - 只发送几条固定消息，立即结束</li>
                <li><strong>测试基础SSE</strong> - 循环发送消息，持续1分钟</li>
                <li><strong>测试稳定SSE</strong> - 完整的SSE实现，包含数据库</li>
            </ol>
            <p><strong>预期结果：</strong>至少最简单的SSE应该能工作。如果都不工作，说明服务器配置有问题。</p>
        </div>
        
        <div class="panel">
            <h2>🔗 直接链接测试</h2>
            <ul>
                <li><a href="/php_test.php" target="_blank">PHP基础测试</a></li>
                <li><a href="/minimal_sse.php" target="_blank">最简单SSE（直接访问）</a></li>
                <li><a href="/basic_sse.php?session_id=test&last_message_id=0" target="_blank">基础SSE（直接访问）</a></li>
            </ul>
        </div>
    </div>

    <script>
    let eventSources = [];
    
    // 测试最简单SSE
    function testMinimalSSE() {
        log('🔄 测试最简单SSE...');
        updateStatus('连接中...', 'connecting');
        
        const eventSource = new EventSource('/minimal_sse.php');
        eventSources.push(eventSource);
        
        const timeout = setTimeout(() => {
            log('⏰ 连接超时（10秒）');
            eventSource.close();
            updateStatus('连接超时', 'offline');
        }, 10000);
        
        eventSource.onopen = function() {
            clearTimeout(timeout);
            log('✅ 最简单SSE连接成功');
            updateStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            log('📨 收到消息: ' + event.data);
        };
        
        eventSource.onerror = function(event) {
            clearTimeout(timeout);
            log('❌ 最简单SSE连接错误');
            log('错误详情: ' + JSON.stringify({
                readyState: eventSource.readyState,
                url: eventSource.url
            }));
            updateStatus('连接错误', 'offline');
        };
    }
    
    // 测试工作SSE
    function testWorkingSSE() {
        log('🔄 测试工作SSE...');
        updateStatus('连接中...', 'connecting');

        const sessionId = 'debug_test_' + Date.now();
        const eventSource = new EventSource('/working_sse.php?session_id=' + sessionId + '&last_message_id=0');
        eventSources.push(eventSource);

        const timeout = setTimeout(() => {
            log('⏰ 连接超时（15秒）');
            eventSource.close();
            updateStatus('连接超时', 'offline');
        }, 15000);

        eventSource.onopen = function() {
            clearTimeout(timeout);
            log('✅ 工作SSE连接成功');
            updateStatus('已连接', 'online');

            // 连接成功后，提示可以发送测试消息
            setTimeout(() => {
                log('💡 提示：可以打开 /send_message.php?session_id=' + sessionId + ' 发送测试消息');
            }, 2000);
        };

        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 ' + data.type + ': ' + data.message);

                if (data.type === 'new_reply') {
                    log('🎉 收到新回复！发送者: ' + data.sender_type);
                }
            } catch (e) {
                log('📨 原始消息: ' + event.data);
            }
        };

        eventSource.onerror = function(event) {
            clearTimeout(timeout);
            log('❌ 工作SSE连接错误');
            updateStatus('连接错误', 'offline');
        };
    }

    // 测试基础SSE
    function testBasicSSE() {
        log('🔄 测试基础SSE...');
        updateStatus('连接中...', 'connecting');

        const eventSource = new EventSource('/basic_sse.php?session_id=debug_test&last_message_id=0');
        eventSources.push(eventSource);

        const timeout = setTimeout(() => {
            log('⏰ 连接超时（15秒）');
            eventSource.close();
            updateStatus('连接超时', 'offline');
        }, 15000);

        eventSource.onopen = function() {
            clearTimeout(timeout);
            log('✅ 基础SSE连接成功');
            updateStatus('已连接', 'online');
        };

        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 ' + data.type + ': ' + data.message);
            } catch (e) {
                log('📨 原始消息: ' + event.data);
            }
        };

        eventSource.onerror = function(event) {
            clearTimeout(timeout);
            log('❌ 基础SSE连接错误');
            updateStatus('连接错误', 'offline');
        };
    }
    
    // 测试稳定SSE
    function testStableSSE() {
        log('🔄 测试稳定SSE...');
        updateStatus('连接中...', 'connecting');
        
        const eventSource = new EventSource('/stable_sse.php?session_id=debug_test&last_message_id=0');
        eventSources.push(eventSource);
        
        const timeout = setTimeout(() => {
            log('⏰ 连接超时（20秒）');
            eventSource.close();
            updateStatus('连接超时', 'offline');
        }, 20000);
        
        eventSource.onopen = function() {
            clearTimeout(timeout);
            log('✅ 稳定SSE连接成功');
            updateStatus('已连接', 'online');
        };
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                log('📨 ' + data.type + ': ' + data.message);
            } catch (e) {
                log('📨 原始消息: ' + event.data);
            }
        };
        
        eventSource.onerror = function(event) {
            clearTimeout(timeout);
            log('❌ 稳定SSE连接错误');
            updateStatus('连接错误', 'offline');
        };
    }
    
    // 停止所有连接
    function stopAllConnections() {
        eventSources.forEach(source => {
            if (source.readyState !== EventSource.CLOSED) {
                source.close();
            }
        });
        eventSources = [];
        log('🛑 所有连接已停止');
        updateStatus('已断开', 'offline');
    }
    
    // 更新状态
    function updateStatus(text, className) {
        const statusElement = document.getElementById('connection-status');
        statusElement.textContent = text;
        statusElement.className = 'status ' + className;
    }
    
    // 清除日志
    function clearLog() {
        document.getElementById('log').innerHTML = '';
    }
    
    // 日志函数
    function log(message) {
        const logDiv = document.getElementById('log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${time}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    // 页面加载时记录浏览器信息
    document.addEventListener('DOMContentLoaded', function() {
        log('🚀 SSE调试工具已加载');
        log('浏览器: ' + navigator.userAgent);
        log('支持SSE: ' + (typeof EventSource !== 'undefined' ? '是' : '否'));
    });
    </script>
</body>
</html>
