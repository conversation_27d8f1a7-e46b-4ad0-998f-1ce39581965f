<?php
/**
 * 修复菜单路径
 */

// 数据库配置
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>修复菜单路径</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 修复菜单路径</h1>";

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='info'>正在修复客服菜单路径...</p>";
    
    // 修复菜单路径 - 路由已有 strongadmin 前缀，菜单路径不需要
    $updates = [
        ['name' => '会话管理', 'old_url' => 'strongadmin/customer-service/sessions', 'new_url' => 'customer-service/sessions'],
        ['name' => 'AI规则管理', 'old_url' => 'strongadmin/customer-service/ai-rules', 'new_url' => 'customer-service/ai-rules'],
        ['name' => '统计报表', 'old_url' => 'strongadmin/customer-service/statistics', 'new_url' => 'customer-service/statistics']
    ];
    
    foreach ($updates as $update) {
        // 先查看当前路径
        $stmt = $pdo->prepare("SELECT route_url FROM st_strongadmin_menu WHERE name = ?");
        $stmt->execute([$update['name']]);
        $current = $stmt->fetch();
        
        if ($current) {
            echo "<p class='info'>菜单【{$update['name']}】当前路径: <code>{$current['route_url']}</code></p>";
            
            // 更新路径
            $stmt = $pdo->prepare("UPDATE st_strongadmin_menu SET route_url = ? WHERE name = ?");
            $stmt->execute([$update['new_url'], $update['name']]);
            
            echo "<p class='success'>✅ 更新菜单【{$update['name']}】路径为: <code>{$update['new_url']}</code></p>";
        } else {
            echo "<p class='error'>❌ 未找到菜单【{$update['name']}】</p>";
        }
    }
    
    echo "<h2>🎉 菜单路径修复完成！</h2>";
    echo "<p>现在菜单链接应该是：</p>";
    echo "<ul>";
    echo "<li><code>/strongadmin/customer-service/sessions</code> - 会话管理</li>";
    echo "<li><code>/strongadmin/customer-service/ai-rules</code> - AI规则管理</li>";
    echo "<li><code>/strongadmin/customer-service/statistics</code> - 统计报表</li>";
    echo "</ul>";
    
    echo "<p><a href='/strongadmin' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>返回后台测试</a></p>";
    
    // 验证修复结果
    echo "<h2>📋 验证修复结果</h2>";
    $stmt = $pdo->query("SELECT name, route_url FROM st_strongadmin_menu WHERE name IN ('会话管理', 'AI规则管理', '统计报表')");
    $menus = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
    echo "<tr><th style='padding:8px;background:#f2f2f2;'>菜单名称</th><th style='padding:8px;background:#f2f2f2;'>路由URL</th><th style='padding:8px;background:#f2f2f2;'>状态</th></tr>";
    
    foreach ($menus as $menu) {
        $status = strpos($menu['route_url'], 'strongadmin/') === 0 ? '✅ 正确' : '❌ 错误';
        echo "<tr>";
        echo "<td style='padding:8px;'>{$menu['name']}</td>";
        echo "<td style='padding:8px;'><code>{$menu['route_url']}</code></td>";
        echo "<td style='padding:8px;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 修复失败: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
