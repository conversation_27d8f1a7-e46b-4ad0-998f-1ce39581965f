<?php
/**
 * 工作的SSE实现 - 保持长连接
 */

// 设置执行时间和内存限制
set_time_limit(0);
ini_set('memory_limit', '128M');

// 设置SSE头部
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');

// 禁用所有输出缓冲
while (ob_get_level()) {
    ob_end_clean();
}

// 获取参数
$sessionId = $_GET['session_id'] ?? 'default';
$lastMessageId = intval($_GET['last_message_id'] ?? 0);

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => '工作SSE连接成功！',
    'session_id' => $sessionId,
    'timestamp' => time()
]) . "\n\n";
flush();

// 模拟数据存储（替代数据库）
$messagesFile = 'sse_messages_' . $sessionId . '.json';
$messages = [];
if (file_exists($messagesFile)) {
    $messages = json_decode(file_get_contents($messagesFile), true) ?: [];
}

echo "data: " . json_encode([
    'type' => 'session_ready',
    'message' => '会话准备就绪',
    'existing_messages' => count($messages),
    'timestamp' => time()
]) . "\n\n";
flush();

// 主循环
$startTime = time();
$maxDuration = 300; // 5分钟
$checkInterval = 1; // 1秒检查一次
$heartbeatCounter = 0;

echo "data: " . json_encode([
    'type' => 'loop_start',
    'message' => '开始实时消息检查',
    'max_duration' => $maxDuration,
    'timestamp' => time()
]) . "\n\n";
flush();

while (time() - $startTime < $maxDuration) {
    $heartbeatCounter++;
    
    try {
        // 重新读取消息文件（模拟检查新消息）
        if (file_exists($messagesFile)) {
            $currentMessages = json_decode(file_get_contents($messagesFile), true) ?: [];
            
            // 检查是否有新消息
            $newMessages = array_slice($currentMessages, count($messages));
            if (!empty($newMessages)) {
                foreach ($newMessages as $message) {
                    if ($message['id'] > $lastMessageId && $message['sender_type'] !== 'customer') {
                        echo "data: " . json_encode([
                            'type' => 'new_reply',
                            'message_id' => $message['id'],
                            'sender_type' => $message['sender_type'],
                            'message' => $message['message'],
                            'created_at' => $message['created_at'],
                            'timestamp' => time()
                        ]) . "\n\n";
                        flush();
                        
                        $lastMessageId = $message['id'];
                    }
                }
                $messages = $currentMessages;
            }
        }
        
        // 每10秒发送心跳包
        if ($heartbeatCounter % 10 == 0) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'message' => '💓 心跳包 #' . ($heartbeatCounter / 10),
                'connection_time' => time() - $startTime,
                'last_message_id' => $lastMessageId,
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 每30秒发送状态信息
        if ($heartbeatCounter % 30 == 0) {
            echo "data: " . json_encode([
                'type' => 'status',
                'message' => '📊 连接状态良好',
                'session_id' => $sessionId,
                'total_messages' => count($messages),
                'connection_time' => time() - $startTime,
                'memory_usage' => memory_get_usage(true),
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 检查客户端连接
        if (connection_aborted()) {
            echo "data: " . json_encode([
                'type' => 'client_disconnected',
                'message' => '客户端已断开连接',
                'timestamp' => time()
            ]) . "\n\n";
            flush();
            break;
        }
        
        sleep($checkInterval);
        
    } catch (Exception $e) {
        echo "data: " . json_encode([
            'type' => 'error',
            'message' => '循环错误: ' . $e->getMessage(),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        sleep($checkInterval);
    }
}

// 连接结束
echo "data: " . json_encode([
    'type' => 'disconnected',
    'message' => '工作SSE连接正常结束',
    'connection_time' => time() - $startTime,
    'total_heartbeats' => intval($heartbeatCounter / 10),
    'timestamp' => time()
]) . "\n\n";
flush();
?>
