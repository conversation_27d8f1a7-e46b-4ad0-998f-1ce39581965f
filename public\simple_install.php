<?php
/**
 * 简单客服系统安装
 * 访问: http://your-domain/simple_install.php
 */

// 数据库配置（从.env文件读取）
$envFile = dirname(__DIR__) . '/.env';
$config = [];

if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
}

$host = $config['DB_HOST'] ?? '127.0.0.1';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? 'mostxx_com';
$username = $config['DB_USERNAME'] ?? 'mostxx_com';
$password = $config['DB_PASSWORD'] ?? 'fHnrmH9w5nw1pd53';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>客服系统安装</title>";
echo "<style>body{font-family:Arial;margin:20px;background:#f5f5f5;} .card{background:white;margin:10px;padding:15px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🚀 客服系统安装程序</h1>";

try {
    echo "<div class='card'>";
    echo "<h2>1. 连接数据库</h2>";
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ 数据库连接成功</p>";
    echo "<p class='info'>数据库: $database @ $host:$port</p>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2>2. 创建数据表</h2>";
    
    // 创建客服会话表
    $sql = "
        CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `session_id` varchar(255) NOT NULL,
            `user_id` bigint(20) unsigned DEFAULT NULL,
            `visitor_name` varchar(255) DEFAULT NULL,
            `visitor_email` varchar(255) DEFAULT NULL,
            `visitor_ip` varchar(255) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `status` enum('active','waiting','closed') NOT NULL DEFAULT 'active',
            `assigned_admin_id` bigint(20) unsigned DEFAULT NULL,
            `last_activity` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `session_id` (`session_id`),
            KEY `assigned_admin_id` (`assigned_admin_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    $pdo->exec($sql);
    echo "<p class='success'>✅ 客服会话表创建成功</p>";

    // 创建客服消息表
    $sql = "
        CREATE TABLE IF NOT EXISTS `customer_service_messages` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `session_id` bigint(20) unsigned NOT NULL,
            `sender_type` enum('customer','admin','ai') NOT NULL,
            `sender_id` bigint(20) unsigned DEFAULT NULL,
            `message` text NOT NULL,
            `attachments` json DEFAULT NULL,
            `is_read` tinyint(1) NOT NULL DEFAULT 0,
            `read_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `session_id` (`session_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    $pdo->exec($sql);
    echo "<p class='success'>✅ 客服消息表创建成功</p>";

    // 创建AI规则表
    $sql = "
        CREATE TABLE IF NOT EXISTS `ai_auto_reply_rules` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `keywords` json NOT NULL,
            `reply_message` text NOT NULL,
            `priority` int(11) NOT NULL DEFAULT 0,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `usage_count` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `is_active_priority` (`is_active`,`priority`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    $pdo->exec($sql);
    echo "<p class='success'>✅ AI规则表创建成功</p>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2>3. 添加默认AI规则</h2>";
    
    $rules = [
        [
            'name' => '问候语',
            'keywords' => '["hello", "hi", "hey", "你好", "您好"]',
            'reply_message' => 'Hello! 👋 Welcome to our store! How can I help you today?',
            'priority' => 10
        ],
        [
            'name' => '物流咨询',
            'keywords' => '["shipping", "delivery", "物流", "快递", "发货"]',
            'reply_message' => 'We offer fast shipping worldwide! 🚚 Standard delivery takes 7-15 business days. You will receive a tracking number once your order ships.',
            'priority' => 9
        ],
        [
            'name' => '退换货',
            'keywords' => '["return", "refund", "退货", "退款", "换货"]',
            'reply_message' => 'We have a 30-day return policy! 🔄 If you are not satisfied, you can return items within 30 days for a full refund.',
            'priority' => 8
        ]
    ];

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_auto_reply_rules WHERE name = ?");
    $insertStmt = $pdo->prepare("
        INSERT INTO ai_auto_reply_rules (name, keywords, reply_message, priority, is_active, usage_count, created_at, updated_at) 
        VALUES (?, ?, ?, ?, 1, 0, NOW(), NOW())
    ");

    foreach ($rules as $rule) {
        $stmt->execute([$rule['name']]);
        if ($stmt->fetchColumn() == 0) {
            $insertStmt->execute([$rule['name'], $rule['keywords'], $rule['reply_message'], $rule['priority']]);
            echo "<p class='success'>✅ 添加规则: {$rule['name']}</p>";
        } else {
            echo "<p class='info'>ℹ️ 规则已存在: {$rule['name']}</p>";
        }
    }
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2>4. 添加后台菜单</h2>";
    
    // 检查菜单是否存在
    $stmt = $pdo->prepare("SELECT id FROM st_strongadmin_menu WHERE name = ? AND level = 1");
    $stmt->execute(['在线客服']);
    $existingMenu = $stmt->fetch();
    
    if (!$existingMenu) {
        // 添加主菜单
        $stmt = $pdo->prepare("
            INSERT INTO st_strongadmin_menu (level, parent_id, name, route_url, status, sort, created_at, updated_at)
            VALUES (1, 0, ?, '', 1, 90, NOW(), NOW())
        ");
        $stmt->execute(['在线客服']);
        $parentId = $pdo->lastInsertId();

        echo "<p class='success'>✅ 主菜单【在线客服】添加成功</p>";

        // 添加子菜单
        $subMenus = [
            ['name' => '会话管理', 'route_url' => 'customer-service/sessions', 'sort' => 1],
            ['name' => 'AI规则管理', 'route_url' => 'customer-service/ai-rules', 'sort' => 2],
            ['name' => '统计报表', 'route_url' => 'customer-service/statistics', 'sort' => 3]
        ];

        $stmt = $pdo->prepare("
            INSERT INTO st_strongadmin_menu (level, parent_id, name, route_url, status, sort, created_at, updated_at)
            VALUES (2, ?, ?, ?, 1, ?, NOW(), NOW())
        ");

        foreach ($subMenus as $menu) {
            $stmt->execute([$parentId, $menu['name'], $menu['route_url'], $menu['sort']]);
            echo "<p class='success'>✅ 子菜单【{$menu['name']}】添加成功</p>";
        }
    } else {
        echo "<p class='info'>ℹ️ 后台菜单已存在</p>";
    }
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2>5. 创建图片目录</h2>";
    if (!is_dir('images')) {
        mkdir('images', 0755, true);
        echo "<p class='success'>✅ 图片目录创建成功</p>";
    } else {
        echo "<p class='info'>ℹ️ 图片目录已存在</p>";
    }
    echo "</div>";

    echo "<div class='card success'>";
    echo "<h2>🎉 安装完成！</h2>";
    echo "<p><strong>现在您可以：</strong></p>";
    echo "<ul>";
    echo "<li>🌐 访问网站首页，查看右下角的客服按钮</li>";
    echo "<li>🔧 登录后台，查看【在线客服】菜单</li>";
    echo "<li>💬 测试客服对话功能</li>";
    echo "</ul>";
    echo "<p><a href='/' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>返回首页</a></p>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2>📋 安装检查</h2>";
    
    // 检查表
    $tables = ['customer_service_sessions', 'customer_service_messages', 'ai_auto_reply_rules'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        echo $exists ? "<p class='success'>✅ 表 $table 存在</p>" : "<p class='error'>❌ 表 $table 不存在</p>";
    }

    // 检查菜单
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM st_strongadmin_menu WHERE name = ?");
    $stmt->execute(['在线客服']);
    $menuExists = $stmt->fetchColumn() > 0;
    echo $menuExists ? "<p class='success'>✅ 后台菜单存在</p>" : "<p class='error'>❌ 后台菜单不存在</p>";

    // 检查AI规则
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM ai_auto_reply_rules");
    $stmt->execute();
    $rulesCount = $stmt->fetchColumn();
    echo "<p class='success'>✅ AI规则数量: $rulesCount</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='card error'>";
    echo "<h2>❌ 安装失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误代码: " . $e->getCode() . "</p>";
    echo "</div>";
}

echo "<p><small>安装时间: " . date('Y-m-d H:i:s') . "</small></p>";
echo "</body></html>";
?>
