<?php
// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数据库修复</title>
    <style>
        body { font-family: Arial; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据库修复</h1>
        
        <?php
        try {
            echo "<div class='info'>📋 开始检查数据库结构...</div>";
            
            // 检查表是否存在
            $tables = DB::select("SHOW TABLES LIKE 'st_customer_service_sessions'");
            if (empty($tables)) {
                echo "<div class='error'>❌ 表不存在，请先创建基础表</div>";
                exit;
            }
            
            echo "<div class='success'>✅ 表存在</div>";
            
            // 获取当前字段
            $columns = DB::select("SHOW COLUMNS FROM st_customer_service_sessions");
            $columnNames = array();
            foreach ($columns as $column) {
                $columnNames[] = $column->Field;
            }
            
            echo "<div class='info'>📊 当前字段: " . implode(', ', $columnNames) . "</div>";
            
            // 需要的字段
            $needFields = array(
                'geo_country' => "VARCHAR(100) NULL",
                'geo_region' => "VARCHAR(100) NULL", 
                'geo_city' => "VARCHAR(100) NULL",
                'last_seen' => "TIMESTAMP NULL"
            );
            
            // 添加缺失字段
            foreach ($needFields as $field => $type) {
                if (!in_array($field, $columnNames)) {
                    try {
                        DB::statement("ALTER TABLE st_customer_service_sessions ADD COLUMN {$field} {$type}");
                        echo "<div class='success'>✅ 添加字段: {$field}</div>";
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ 添加字段失败: {$field} - " . $e->getMessage() . "</div>";
                    }
                } else {
                    echo "<div class='info'>✓ 字段已存在: {$field}</div>";
                }
            }
            
            // 更新status字段
            try {
                DB::statement("ALTER TABLE st_customer_service_sessions MODIFY COLUMN status enum('online','offline') NOT NULL DEFAULT 'offline'");
                echo "<div class='success'>✅ 更新status字段</div>";
            } catch (Exception $e) {
                echo "<div class='warning'>⚠️ status字段更新: " . $e->getMessage() . "</div>";
            }
            
            // 测试插入
            $testId = 'test_' . time();
            try {
                DB::table('customer_service_sessions')->insert(array(
                    'session_id' => $testId,
                    'visitor_name' => '测试',
                    'visitor_ip' => '127.0.0.1',
                    'status' => 'online',
                    'last_seen' => now(),
                    'created_at' => now(),
                    'updated_at' => now()
                ));
                
                echo "<div class='success'>✅ 测试插入成功</div>";
                
                // 删除测试数据
                DB::table('customer_service_sessions')->where('session_id', $testId)->delete();
                echo "<div class='info'>🗑️ 测试数据已清理</div>";
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ 测试插入失败: " . $e->getMessage() . "</div>";
            }
            
            echo "<div class='success'>🎉 数据库修复完成！</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ 修复失败: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <h2>🔗 测试链接</h2>
        <ul>
            <li><a href="/simple_close_test.php" target="_blank">简单关闭测试</a></li>
            <li><a href="/strongadmin/customer-service/sessions" target="_blank">后台管理</a></li>
        </ul>
    </div>
</body>
</html>
