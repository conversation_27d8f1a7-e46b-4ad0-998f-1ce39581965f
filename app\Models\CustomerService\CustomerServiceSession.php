<?php

namespace App\Models\CustomerService;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Admin\User as AdminUser;

class CustomerServiceSession extends Model
{
    protected $fillable = [
        'session_id',
        'user_id',
        'visitor_name',
        'visitor_email',
        'visitor_ip',
        'user_agent',
        'status',
        'assigned_admin_id',
        'last_activity'
    ];

    protected $dates = [
        'last_activity',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'last_activity' => 'datetime'
    ];

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 关联分配的客服
    public function assignedAdmin()
    {
        return $this->belongsTo(AdminUser::class, 'assigned_admin_id');
    }

    // 关联消息
    public function messages()
    {
        return $this->hasMany(CustomerServiceMessage::class, 'session_id');
    }

    // 获取最新消息
    public function latestMessage()
    {
        return $this->hasOne(CustomerServiceMessage::class, 'session_id')->latest();
    }

    // 获取未读消息数量
    public function unreadMessagesCount()
    {
        return $this->messages()->where('is_read', false)->where('sender_type', '!=', 'admin')->count();
    }

    // 生成会话ID
    public static function generateSessionId()
    {
        return 'cs_' . time() . '_' . uniqid();
    }

    // 更新最后活动时间
    public function updateLastActivity()
    {
        $this->update(['last_activity' => now()]);
    }

    // 获取显示名称
    public function getDisplayNameAttribute()
    {
        if ($this->user) {
            return $this->user->name;
        }
        return $this->visitor_name ?: 'Anonymous';
    }

    // 获取联系方式
    public function getContactInfoAttribute()
    {
        if ($this->user) {
            return $this->user->email;
        }
        return $this->visitor_email ?: 'No email';
    }
}
