@echo off
echo ========================================
echo    客服系统WebSocket一键启动
echo ========================================
echo.

echo 1. 检查Laravel配置...
php artisan config:cache

echo.
echo 2. 清除缓存...
php artisan cache:clear

echo.
echo 3. 启动队列处理器（后台运行）...
start /B php artisan queue:work --daemon

echo.
echo 4. 启动WebSocket服务器...
echo WebSocket服务器将在端口6001启动
echo 前台页面: http://www.strongshop.local
echo 后台管理: http://www.strongshop.local/strongadmin/customer-service/sessions
echo.
echo 按Ctrl+C停止服务器
echo.

php artisan websockets:serve --port=6001
