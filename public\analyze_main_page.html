<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页面JavaScript分析</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
        }
        .test-btn:hover { 
            background: #0056b3; 
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 主页面JavaScript分析工具</h1>
        <p>分析主页面的JavaScript代码，找出语法错误的具体位置</p>
        
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="fetchMainPage()">获取主页面</button>
            <button class="test-btn" onclick="analyzeScripts()">分析脚本</button>
            <button class="test-btn" onclick="validateSyntax()">验证语法</button>
            <button class="test-btn danger" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="result-display">
            <div class="success-box">
                <h3>📋 主页面JavaScript分析工具已就绪</h3>
                <p>点击"获取主页面"开始分析主页面的JavaScript代码</p>
            </div>
        </div>
    </div>

    <script>
        let mainPageContent = '';
        let scriptBlocks = [];
        let errorLog = [];

        function updateDisplay(content, type = 'success') {
            const display = document.getElementById('result-display');
            const className = type === 'error' ? 'error-box' : 
                            type === 'warning' ? 'warning-box' : 'success-box';
            display.innerHTML = `<div class="${className}">${content}</div>`;
        }

        async function fetchMainPage() {
            updateDisplay('<h3>🔍 正在获取主页面内容...</h3>', 'warning');
            
            try {
                const response = await fetch('/');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                mainPageContent = await response.text();
                
                updateDisplay(`
                    <h3>✅ 主页面内容获取成功</h3>
                    <p><strong>页面大小:</strong> ${mainPageContent.length} 字符</p>
                    <p><strong>下一步:</strong> 点击"分析脚本"来提取和分析JavaScript代码</p>
                `);
                
            } catch (error) {
                updateDisplay(`
                    <h3>❌ 获取主页面失败</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                `, 'error');
            }
        }

        function analyzeScripts() {
            if (!mainPageContent) {
                updateDisplay('<h3>⚠️ 请先获取主页面内容</h3>', 'warning');
                return;
            }
            
            updateDisplay('<h3>🔍 正在分析JavaScript脚本...</h3>', 'warning');
            
            // 提取所有script标签
            const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/gi;
            const scripts = [];
            let match;
            
            while ((match = scriptRegex.exec(mainPageContent)) !== null) {
                const scriptContent = match[1].trim();
                if (scriptContent) {
                    scripts.push({
                        index: scripts.length + 1,
                        content: scriptContent,
                        length: scriptContent.length,
                        lines: scriptContent.split('\n').length
                    });
                }
            }
            
            scriptBlocks = scripts;
            
            let analysisResult = `<h3>📊 JavaScript脚本分析结果</h3>`;
            analysisResult += `<p><strong>找到脚本块数量:</strong> ${scripts.length}</p>`;
            
            scripts.forEach((script, index) => {
                analysisResult += `
                    <div style="border: 1px solid #dee2e6; margin: 10px 0; padding: 10px; border-radius: 4px;">
                        <h4>脚本块 ${script.index}</h4>
                        <p><strong>长度:</strong> ${script.length} 字符</p>
                        <p><strong>行数:</strong> ${script.lines} 行</p>
                        <div class="code-block">${script.content.substring(0, 500)}${script.content.length > 500 ? '...' : ''}</div>
                    </div>
                `;
            });
            
            analysisResult += `<p><strong>下一步:</strong> 点击"验证语法"来检查每个脚本块的语法</p>`;
            
            updateDisplay(analysisResult);
        }

        function validateSyntax() {
            if (scriptBlocks.length === 0) {
                updateDisplay('<h3>⚠️ 请先分析脚本</h3>', 'warning');
                return;
            }
            
            updateDisplay('<h3>🔍 正在验证JavaScript语法...</h3>', 'warning');
            
            let validationResult = `<h3>🔧 JavaScript语法验证结果</h3>`;
            let totalErrors = 0;
            
            scriptBlocks.forEach((script, index) => {
                try {
                    // 使用Function构造函数测试语法
                    new Function(script.content);
                    validationResult += `
                        <div style="border: 1px solid #c3e6cb; background: #d4edda; margin: 10px 0; padding: 10px; border-radius: 4px;">
                            <h4 style="color: #155724;">✅ 脚本块 ${script.index}: 语法正确</h4>
                            <p style="color: #155724;">长度: ${script.length} 字符，行数: ${script.lines}</p>
                        </div>
                    `;
                } catch (error) {
                    totalErrors++;
                    validationResult += `
                        <div style="border: 1px solid #f5c6cb; background: #f8d7da; margin: 10px 0; padding: 10px; border-radius: 4px;">
                            <h4 style="color: #721c24;">❌ 脚本块 ${script.index}: 语法错误</h4>
                            <p style="color: #721c24;"><strong>错误:</strong> ${error.message}</p>
                            <p style="color: #721c24;">长度: ${script.length} 字符，行数: ${script.lines}</p>
                            <div class="code-block" style="background: #fff; border: 1px solid #f5c6cb;">
                                ${script.content.substring(0, 1000)}${script.content.length > 1000 ? '...' : ''}
                            </div>
                        </div>
                    `;
                    
                    // 尝试找出具体的错误位置
                    const lines = script.content.split('\n');
                    validationResult += `<div style="margin-left: 20px; color: #721c24;">`;
                    validationResult += `<h5>🔍 尝试定位错误:</h5>`;
                    
                    // 检查常见的语法错误模式
                    lines.forEach((line, lineIndex) => {
                        if (line.includes('missing ) after argument list') || 
                            line.includes('console.error(`') ||
                            line.includes('console.log(`') ||
                            line.includes('alert(`')) {
                            validationResult += `<p>可能的问题行 ${lineIndex + 1}: ${line.trim()}</p>`;
                        }
                    });
                    
                    validationResult += `</div>`;
                }
            });
            
            if (totalErrors === 0) {
                validationResult += `
                    <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <h4 style="color: #155724;">🎉 所有脚本块语法正确！</h4>
                        <p style="color: #155724;">
                            主页面的所有JavaScript脚本块都通过了语法验证。
                            如果浏览器仍报告语法错误，可能是由于:
                        </p>
                        <ul style="color: #155724;">
                            <li>动态生成的代码</li>
                            <li>外部脚本文件</li>
                            <li>浏览器兼容性问题</li>
                            <li>缓存问题</li>
                        </ul>
                    </div>
                `;
            } else {
                validationResult += `
                    <div style="background: #f8d7da; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <h4 style="color: #721c24;">⚠️ 发现 ${totalErrors} 个语法错误</h4>
                        <p style="color: #721c24;">
                            请检查上述错误的脚本块，修复语法问题。
                        </p>
                    </div>
                `;
            }
            
            updateDisplay(validationResult, totalErrors > 0 ? 'error' : 'success');
        }

        function clearResults() {
            mainPageContent = '';
            scriptBlocks = [];
            errorLog = [];
            
            updateDisplay(`
                <h3>🧹 结果已清空</h3>
                <p>所有分析结果已清空，可以重新开始分析。</p>
            `);
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay(`
                <h3>🚀 主页面JavaScript分析工具已启动</h3>
                <p>这个工具将获取主页面内容，提取所有JavaScript代码，并逐个验证语法。</p>
                <p><strong>目标:</strong> 找出导致 "missing ) after argument list" 错误的具体脚本块</p>
                <p><strong>方法:</strong> 逐个验证每个script标签中的JavaScript代码</p>
            `);
        });
    </script>
</body>
</html>
