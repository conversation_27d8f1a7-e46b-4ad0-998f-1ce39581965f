<?php
echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>API调试</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:1000px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}
.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{border:1px solid #ddd;padding:8px;text-align:left;}
th{background:#f8f9fa;}
pre{background:#f8f9fa;padding:10px;border-radius:4px;overflow-x:auto;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 API调试</h1>";

try {
    // 数据库连接
    $host = '127.0.0.1';
    $port = '3306';
    $database = 'mostxx_com';
    $username = 'mostxx_com';
    $password = 'fHnrmH9w5nw1pd53';

    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 1. 检查所有设置记录
    echo "<h3>1. 所有设置记录</h3>";
    $stmt = $pdo->query("SELECT * FROM customer_service_settings ORDER BY category, setting_key");
    $allSettings = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>ID</th><th>分类</th><th>设置键</th><th>值</th><th>类型</th><th>激活</th></tr>";
    
    foreach ($allSettings as $setting) {
        $value = strlen($setting['setting_value']) > 30 ? substr($setting['setting_value'], 0, 30) . '...' : $setting['setting_value'];
        echo "<tr>";
        echo "<td>{$setting['id']}</td>";
        echo "<td>{$setting['category']}</td>";
        echo "<td><code>{$setting['setting_key']}</code></td>";
        echo "<td><strong>{$value}</strong></td>";
        echo "<td>{$setting['setting_type']}</td>";
        echo "<td>" . ($setting['is_active'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 模拟API查询
    echo "<h3>2. 模拟API查询</h3>";
    $stmt = $pdo->prepare("
        SELECT * FROM customer_service_settings 
        WHERE is_active = 1 
        AND category IN ('basic', 'appearance', 'sound', 'offline')
        ORDER BY category, setting_key
    ");
    $stmt->execute();
    $apiSettings = $stmt->fetchAll();
    
    echo "<div class='info'>查询条件: is_active = 1 AND category IN ('basic', 'appearance', 'sound', 'offline')</div>";
    echo "<div class='info'>查询结果: " . count($apiSettings) . " 条记录</div>";
    
    if (count($apiSettings) > 0) {
        echo "<table>";
        echo "<tr><th>设置键</th><th>原始值</th><th>类型</th><th>转换后值</th></tr>";
        
        $processedSettings = [];
        foreach ($apiSettings as $setting) {
            // 模拟API的类型转换
            $processedValue = null;
            switch ($setting['setting_type']) {
                case 'boolean':
                    $processedValue = $setting['setting_value'] === '1' ? 'true' : 'false';
                    break;
                case 'number':
                    $processedValue = (float) $setting['setting_value'];
                    break;
                case 'json':
                    $decoded = json_decode($setting['setting_value'], true);
                    $processedValue = $decoded ? json_encode($decoded) : '[]';
                    break;
                case 'color':
                    $processedValue = $setting['setting_value'] ?: '#667eea';
                    break;
                default:
                    $processedValue = $setting['setting_value'];
            }
            
            $processedSettings[$setting['setting_key']] = $processedValue;
            
            echo "<tr>";
            echo "<td><code>{$setting['setting_key']}</code></td>";
            echo "<td><strong>{$setting['setting_value']}</strong></td>";
            echo "<td>{$setting['setting_type']}</td>";
            echo "<td><strong>{$processedValue}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 3. 检查关键设置
        echo "<h3>3. 关键设置检查</h3>";
        $keySettings = ['system_enabled', 'auto_open_enabled', 'sound_enabled'];
        
        foreach ($keySettings as $key) {
            if (isset($processedSettings[$key])) {
                $value = $processedSettings[$key];
                $status = ($value === 'true' || $value === true) ? '✅ 启用' : '❌ 禁用';
                echo "<div class='info'>{$key}: {$value} {$status}</div>";
            } else {
                echo "<div class='error'>{$key}: 未找到设置</div>";
            }
        }
        
    } else {
        echo "<div class='error'>❌ 查询结果为空！这就是问题所在。</div>";
        
        // 检查可能的原因
        echo "<h4>可能的原因:</h4>";
        
        // 检查is_active字段
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_settings WHERE is_active = 1");
        $activeCount = $stmt->fetch()['count'];
        echo "<div class='info'>is_active = 1 的记录数: {$activeCount}</div>";
        
        // 检查category字段
        $stmt = $pdo->query("SELECT DISTINCT category FROM customer_service_settings");
        $categories = $stmt->fetchAll();
        echo "<div class='info'>现有分类: " . implode(', ', array_column($categories, 'category')) . "</div>";
        
        // 检查具体的system_enabled记录
        $stmt = $pdo->query("SELECT * FROM customer_service_settings WHERE setting_key = 'system_enabled'");
        $systemSetting = $stmt->fetch();
        if ($systemSetting) {
            echo "<div class='info'>system_enabled记录详情:</div>";
            echo "<pre>" . json_encode($systemSetting, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        } else {
            echo "<div class='error'>❌ 找不到 system_enabled 记录</div>";
        }
    }
    
    // 4. 直接测试API
    echo "<h3>4. 直接测试API</h3>";
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/customer-service/get-settings.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<div class='info'>API URL: <code>{$apiUrl}</code></div>";
    echo "<div class='info'>HTTP状态码: {$httpCode}</div>";
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data) {
            echo "<div class='success'>✅ API响应成功</div>";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        } else {
            echo "<div class='error'>❌ JSON解析失败</div>";
            echo "<pre>{$response}</pre>";
        }
    } else {
        echo "<div class='error'>❌ API请求失败</div>";
        echo "<pre>{$response}</pre>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 操作失败: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body></html>";
?>
