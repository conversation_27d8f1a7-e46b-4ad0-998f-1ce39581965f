<?php
/**
 * WebSocket服务器 - 客服系统实时通信
 */

require_once 'vendor/autoload.php';

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Ratchet\App;
use Ratchet\RFC6455\Messaging\MessageInterface;

class CustomerServiceWebSocket implements MessageComponentInterface {
    protected $clients;
    protected $sessions;
    protected $pdo;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->sessions = [];
        
        // 数据库连接
        try {
            $this->pdo = new PDO('mysql:host=127.0.0.1;dbname=mostxx_com;charset=utf8mb4', 'mostxx_com', 'fHnrmH9w5nw1pd53', [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            echo "数据库连接成功\n";
        } catch (Exception $e) {
            echo "数据库连接失败: " . $e->getMessage() . "\n";
        }
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "新连接: {$conn->resourceId}\n";
        
        // 发送连接成功消息
        $conn->send(json_encode([
            'type' => 'connected',
            'message' => 'WebSocket连接成功',
            'connection_id' => $conn->resourceId,
            'timestamp' => time()
        ]));
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        try {
            $data = json_decode($msg, true);
            echo "收到消息: " . $msg . "\n";
            
            switch ($data['type']) {
                case 'join_session':
                    $this->handleJoinSession($from, $data);
                    break;
                    
                case 'send_message':
                    $this->handleSendMessage($from, $data);
                    break;
                    
                case 'admin_reply':
                    $this->handleAdminReply($from, $data);
                    break;
                    
                case 'ping':
                    $from->send(json_encode(['type' => 'pong', 'timestamp' => time()]));
                    break;
            }
        } catch (Exception $e) {
            echo "处理消息错误: " . $e->getMessage() . "\n";
            $from->send(json_encode([
                'type' => 'error',
                'message' => '消息处理失败: ' . $e->getMessage()
            ]));
        }
    }

    private function handleJoinSession($conn, $data) {
        $sessionId = $data['session_id'] ?? '';
        if (!$sessionId) return;
        
        // 获取或创建会话
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM st_customer_service_sessions WHERE session_id = ?");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch();
            
            if (!$session) {
                // 创建新会话
                $stmt = $this->pdo->prepare("INSERT INTO st_customer_service_sessions (session_id, visitor_ip, user_agent, status, last_activity, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW(), NOW())");
                $stmt->execute([$sessionId, '127.0.0.1', 'WebSocket Client']);
                $sessionDbId = $this->pdo->lastInsertId();
            } else {
                $sessionDbId = $session['id'];
            }
            
            // 保存连接和会话的关联
            $this->sessions[$conn->resourceId] = [
                'session_id' => $sessionId,
                'session_db_id' => $sessionDbId,
                'type' => $data['client_type'] ?? 'customer'
            ];
            
            $conn->send(json_encode([
                'type' => 'session_joined',
                'session_id' => $sessionId,
                'session_db_id' => $sessionDbId,
                'message' => '已加入会话'
            ]));
            
            // 发送历史消息
            $this->sendHistoryMessages($conn, $sessionDbId);
            
        } catch (Exception $e) {
            echo "加入会话错误: " . $e->getMessage() . "\n";
        }
    }

    private function handleSendMessage($conn, $data) {
        $sessionInfo = $this->sessions[$conn->resourceId] ?? null;
        if (!$sessionInfo) return;
        
        $message = $data['message'] ?? '';
        if (!$message) return;
        
        try {
            // 保存消息到数据库
            $stmt = $this->pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, message, is_read, created_at, updated_at) VALUES (?, 'customer', ?, 0, NOW(), NOW())");
            $stmt->execute([$sessionInfo['session_db_id'], $message]);
            $messageId = $this->pdo->lastInsertId();
            
            // 广播消息给所有相关连接
            $this->broadcastToSession($sessionInfo['session_id'], [
                'type' => 'new_message',
                'message_id' => $messageId,
                'sender_type' => 'customer',
                'message' => $message,
                'session_id' => $sessionInfo['session_id'],
                'timestamp' => time()
            ]);
            
        } catch (Exception $e) {
            echo "发送消息错误: " . $e->getMessage() . "\n";
        }
    }

    private function handleAdminReply($conn, $data) {
        $sessionId = $data['session_id'] ?? '';
        $message = $data['message'] ?? '';
        
        if (!$sessionId || !$message) return;
        
        try {
            // 获取会话数据库ID
            $stmt = $this->pdo->prepare("SELECT id FROM st_customer_service_sessions WHERE session_id = ?");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch();
            
            if (!$session) return;
            
            // 保存管理员回复
            $stmt = $this->pdo->prepare("INSERT INTO st_customer_service_messages (session_id, sender_type, sender_id, message, is_read, created_at, updated_at) VALUES (?, 'admin', 1, ?, 0, NOW(), NOW())");
            $stmt->execute([$session['id'], $message]);
            $messageId = $this->pdo->lastInsertId();
            
            // 立即广播给前台客户
            $this->broadcastToSession($sessionId, [
                'type' => 'new_reply',
                'message_id' => $messageId,
                'sender_type' => 'admin',
                'message' => $message,
                'session_id' => $sessionId,
                'timestamp' => time()
            ]);
            
            echo "管理员回复已发送: $message\n";
            
        } catch (Exception $e) {
            echo "管理员回复错误: " . $e->getMessage() . "\n";
        }
    }

    private function sendHistoryMessages($conn, $sessionDbId) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM st_customer_service_messages WHERE session_id = ? ORDER BY created_at ASC LIMIT 50");
            $stmt->execute([$sessionDbId]);
            $messages = $stmt->fetchAll();
            
            foreach ($messages as $msg) {
                $conn->send(json_encode([
                    'type' => 'history_message',
                    'message_id' => $msg['id'],
                    'sender_type' => $msg['sender_type'],
                    'message' => $msg['message'],
                    'created_at' => $msg['created_at']
                ]));
            }
        } catch (Exception $e) {
            echo "发送历史消息错误: " . $e->getMessage() . "\n";
        }
    }

    private function broadcastToSession($sessionId, $data) {
        foreach ($this->sessions as $connId => $sessionInfo) {
            if ($sessionInfo['session_id'] === $sessionId) {
                foreach ($this->clients as $client) {
                    if ($client->resourceId == $connId) {
                        $client->send(json_encode($data));
                    }
                }
            }
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        unset($this->sessions[$conn->resourceId]);
        echo "连接关闭: {$conn->resourceId}\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "错误: {$e->getMessage()}\n";
        $conn->close();
    }
}

// 启动WebSocket服务器
$app = new App('localhost', 8080);
$app->route('/customer-service', new CustomerServiceWebSocket, ['*']);
$app->run();
?>
