<?php
/**
 * 测试角标显示效果
 */

// Laravel环境初始化
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>角标显示测试</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#f5f5f5;} 
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.test-item{display:flex;align-items:center;margin:20px 0;padding:15px;border:1px solid #ddd;border-radius:8px;}
.avatar{position:relative;width:40px;height:40px;border-radius:50%;background:#1890ff;color:white;display:flex;align-items:center;justify-content:center;font-weight:bold;font-size:14px;margin-right:15px;}
.badge{position:absolute;top:-5px;right:-5px;background:#ff4d4f;color:white;border-radius:10px;min-width:18px;height:18px;font-size:11px;display:flex;align-items:center;justify-content:center;font-weight:bold;border:2px solid white;z-index:100;box-sizing:border-box;line-height:1;padding:0 4px;text-align:center;font-family:Arial,sans-serif;white-space:nowrap;}
.info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🏷️ 角标显示效果测试</h1>";

echo "<div class='info'>📋 测试不同数字的角标显示效果</div>";

// 测试不同数字的角标
$testNumbers = [1, 5, 9, 10, 25, 50, 99, 100, 999, '99+'];

foreach ($testNumbers as $number) {
    echo "<div class='test-item'>";
    echo "<div class='avatar'>";
    echo "A";
    echo "<div class='badge'>{$number}</div>";
    echo "</div>";
    echo "<span>角标数字: <strong>{$number}</strong></span>";
    echo "</div>";
}

echo "<h2>📊 实际数据测试</h2>";

try {
    // 模拟创建一些测试数据
    $testCounts = [1, 5, 15, 25, 50, 99, 150];
    
    foreach ($testCounts as $count) {
        $displayCount = $count > 99 ? '99+' : $count;
        
        echo "<div class='test-item'>";
        echo "<div class='avatar'>";
        echo "T";
        echo "<div class='badge'>{$displayCount}</div>";
        echo "</div>";
        echo "<span>实际未读: <strong>{$count}</strong> → 显示: <strong>{$displayCount}</strong></span>";
        echo "</div>";
    }
    
    echo "<h2>🔧 样式说明</h2>";
    echo "<ul>";
    echo "<li><strong>border-radius: 10px</strong> - 椭圆形，适应文本长度</li>";
    echo "<li><strong>min-width: 18px</strong> - 最小宽度保证单数字居中</li>";
    echo "<li><strong>padding: 0 4px</strong> - 左右内边距适应长文本</li>";
    echo "<li><strong>white-space: nowrap</strong> - 防止文本换行</li>";
    echo "<li><strong>height: 18px</strong> - 固定高度保持一致</li>";
    echo "</ul>";
    
    echo "<h2>🎨 对比效果</h2>";
    echo "<div style='display:flex;gap:20px;margin:20px 0;'>";
    
    // 圆形角标（旧版）
    echo "<div style='text-align:center;'>";
    echo "<div style='position:relative;width:40px;height:40px;border-radius:50%;background:#1890ff;color:white;display:flex;align-items:center;justify-content:center;font-weight:bold;margin:0 auto 10px;'>";
    echo "A";
    echo "<div style='position:absolute;top:-5px;right:-5px;background:#ff4d4f;color:white;border-radius:50%;width:18px;height:18px;font-size:10px;display:flex;align-items:center;justify-content:center;font-weight:bold;border:2px solid white;'>99+</div>";
    echo "</div>";
    echo "<small>圆形角标<br>(99+显示不全)</small>";
    echo "</div>";
    
    // 椭圆形角标（新版）
    echo "<div style='text-align:center;'>";
    echo "<div style='position:relative;width:40px;height:40px;border-radius:50%;background:#1890ff;color:white;display:flex;align-items:center;justify-content:center;font-weight:bold;margin:0 auto 10px;'>";
    echo "A";
    echo "<div class='badge'>99+</div>";
    echo "</div>";
    echo "<small>椭圆形角标<br>(99+完整显示)</small>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;'>❌ 测试失败: " . $e->getMessage() . "</div>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='/strongadmin/customer-service/sessions' target='_blank'>后台会话管理</a></li>";
echo "<li><a href='/' target='_blank'>前台首页（测试客服）</a></li>";
echo "<li><a href='/test_heartbeat.php' target='_blank'>心跳状态测试</a></li>";
echo "</ul>";

echo "</div>";
echo "</body></html>";
?>
