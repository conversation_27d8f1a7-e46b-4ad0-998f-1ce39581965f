<?php $__env->startPush('styles'); ?>
<style>
.st-message-item {
    border-left: 4px solid #1E9FFF;
    margin-bottom: 15px;
}
.st-message-item.unread {
    border-left-color: #FF5722;
    background-color: #fff5f5;
}
.st-message-item.replied {
    border-left-color: #5FB878;
    background-color: #f8fff8;
}
.st-message-meta {
    color: #999;
    font-size: 12px;
    margin-bottom: 10px;
}
.st-message-content {
    line-height: 1.6;
    margin-bottom: 10px;
}
.st-admin-reply {
    background: #f2f2f2;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}
.st-reply-form {
    background: #fafafa;
    padding: 15px;
    border-radius: 4px;
    margin-top: 10px;
    display: none;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 回复表单提交
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('reply-form')) {
            e.preventDefault();

            var form = e.target;
            var messageId = form.querySelector('[data-message-id]').getAttribute('data-message-id');
            var replyTextarea = form.querySelector('textarea[name="reply"]');
            var replyText = replyTextarea.value;
            var submitBtn = form.querySelector('button[type="submit"]');
            var originalText = submitBtn.innerHTML;

            if (!replyText.trim()) {
                alert('请输入回复内容');
                return;
            }

            // 显示加载状态
            submitBtn.innerHTML = '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 发送中...';
            submitBtn.disabled = true;

            // 获取CSRF token
            var csrfToken = document.querySelector('meta[name="csrf-token"]');
            var token = csrfToken ? csrfToken.getAttribute('content') : '';

            // 创建FormData
            var formData = new FormData();
            formData.append('reply', replyText);
            formData.append('_token', token);

            // 发送请求
            fetch('/strongadmin/customer-service/offline-messages/' + messageId + '/reply', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if(data.success) {
                    alert('回复发送成功');
                    location.reload();
                } else {
                    alert(data.message || '回复失败');
                }
            })
            .catch(error => {
                alert('网络错误，请重试');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }
    });
});

// 显示回复表单
function showReplyForm(messageId) {
    var form = document.getElementById('reply-form-' + messageId);
    if (form) {
        form.style.display = 'block';
    }
}

// 隐藏回复表单
function hideReplyForm(messageId) {
    var form = document.getElementById('reply-form-' + messageId);
    if (form) {
        form.style.display = 'none';
    }
}

// 刷新页面
function refreshMessages() {
    location.reload();
}

// 删除留言
function deleteMessage(messageId) {
    layer.confirm('确定要删除这条留言吗？删除后无法恢复！', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        // 发送删除请求
        fetch('/strongadmin/customer-service/offline-messages/' + messageId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                layer.msg('删除成功', {icon: 1});
                // 刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                layer.msg('删除失败: ' + (data.message || '未知错误'), {icon: 2});
            }
        })
        .catch(error => {
            console.error('删除请求失败:', error);
            layer.msg('删除失败，请重试', {icon: 2});
        });

        layer.close(index);
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="st-h15"></div>

<!-- 搜索表单 -->
<form class="layui-form st-form-search" lay-filter="ST-FORM-SEARCH">
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">访客姓名</label>
            <div class="layui-input-inline">
                <input type="text" name="visitor_name" placeholder="请输入访客姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">联系邮箱</label>
            <div class="layui-input-inline">
                <input type="text" name="visitor_email" placeholder="请输入邮箱" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <select name="status">
                    <option value="">全部状态</option>
                    <option value="pending">待回复</option>
                    <option value="replied">已回复</option>
                    <option value="closed">已关闭</option>
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <a class="layui-btn layui-btn-sm st-search-button">
                <i class="layui-icon layui-icon-search"></i> 搜索
            </a>
        </div>
    </div>
</form>

<?php if($messages->isEmpty()): ?>
    <div class="layui-card">
        <div class="layui-card-body" style="text-align: center; padding: 50px;">
            <i class="layui-icon layui-icon-email" style="font-size: 60px; color: #ccc;"></i>
            <h3 style="color: #999; margin: 20px 0;">暂无离线留言</h3>
            <p style="color: #999;">当客户在非工作时间访问时，会显示离线留言表单</p>
        </div>
    </div>
<?php else: ?>
    <?php $__currentLoopData = $messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="layui-card st-message-item <?php echo e($message->status === 'pending' ? 'unread' : ($message->status === 'replied' ? 'replied' : '')); ?>">
            <div class="layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-md9">
                        <h4 style="margin-bottom: 10px;">
                            <i class="layui-icon layui-icon-username"></i> <?php echo e($message->visitor_name ?: '匿名用户'); ?>

                            <?php if($message->status === 'pending'): ?>
                                <span class="layui-badge">待回复</span>
                            <?php elseif($message->status === 'replied'): ?>
                                <span class="layui-badge layui-bg-green">已回复</span>
                            <?php else: ?>
                                <span class="layui-badge layui-bg-gray">已关闭</span>
                            <?php endif; ?>
                        </h4>
                        
                        <div class="st-message-meta">
                            <?php if($message->visitor_email): ?>
                                <i class="layui-icon layui-icon-email"></i> <?php echo e($message->visitor_email); ?>

                            <?php endif; ?>
                            <?php if($message->visitor_phone): ?>
                                | <i class="layui-icon layui-icon-cellphone"></i> <?php echo e($message->visitor_phone); ?>

                            <?php endif; ?>
                            <?php if($message->visitor_whatsapp): ?>
                                | <i class="layui-icon layui-icon-dialogue"></i> <?php echo e($message->visitor_whatsapp); ?>

                            <?php endif; ?>
                            | <i class="layui-icon layui-icon-time"></i> <?php echo e($message->created_at); ?>

                        </div>
                        
                        <div class="st-message-content">
                            <strong>留言内容:</strong>
                            <p style="margin-top: 5px; line-height: 1.6;"><?php echo e($message->message); ?></p>
                        </div>
                        
                        <?php if($message->admin_reply): ?>
                            <div class="st-admin-reply">
                                <strong><i class="layui-icon layui-icon-reply-fill"></i> 管理员回复:</strong>
                                <p style="margin: 5px 0; line-height: 1.6;"><?php echo e($message->admin_reply); ?></p>
                                <small>回复时间: <?php echo e($message->replied_at); ?></small>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="layui-col-md3" style="text-align: right;">
                        <?php if($message->status === 'pending'): ?>
                            <button class="layui-btn layui-btn-sm" onclick="showReplyForm(<?php echo e($message->id); ?>)">
                                <i class="layui-icon layui-icon-reply-fill"></i> 回复
                            </button>
                        <?php endif; ?>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="layer.msg('查看详情功能')">
                            <i class="layui-icon layui-icon-about"></i> 详情
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="deleteMessage(<?php echo e($message->id); ?>)">
                            <i class="layui-icon layui-icon-delete"></i> 删除
                        </button>
                    </div>
                </div>
                
                <!-- 回复表单 -->
                <?php if($message->status === 'pending'): ?>
                    <div id="reply-form-<?php echo e($message->id); ?>" class="st-reply-form">
                        <h4><i class="layui-icon layui-icon-reply-fill"></i> 回复留言</h4>
                        <form class="reply-form">
                            <div class="layui-form-item">
                                <textarea name="reply"
                                          placeholder="请输入回复内容..."
                                          class="layui-textarea"
                                          required></textarea>
                            </div>
                            <div class="layui-form-item">
                                <button type="submit"
                                        class="layui-btn layui-btn-sm"
                                        data-message-id="<?php echo e($message->id); ?>">
                                    <i class="layui-icon layui-icon-ok"></i> 发送回复
                                </button>
                                <button type="button"
                                        class="layui-btn layui-btn-sm layui-btn-primary"
                                        onclick="hideReplyForm(<?php echo e($message->id); ?>)">
                                    取消
                                </button>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    
    <!-- 分页 -->
    <?php if($messages->hasPages()): ?>
        <div style="text-align: center; margin-top: 20px;">
            <?php echo e($messages->links()); ?>

        </div>
    <?php endif; ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('strongadmin::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\mostxx.com_xxx\resources\views/strongadmin/customer-service/offline-messages.blade.php ENDPATH**/ ?>