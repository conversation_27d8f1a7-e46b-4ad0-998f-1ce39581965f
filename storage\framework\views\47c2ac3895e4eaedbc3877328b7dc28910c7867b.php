<?php $__env->startPush('styles'); ?>
<style>
.st-rule-card {
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s;
}
.st-rule-card:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.st-rule-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}
.st-rule-keywords {
    margin-bottom: 10px;
}
.st-keyword-tag {
    display: inline-block;
    background: #f2f2f2;
    color: #666;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 3px;
}
.st-rule-message {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 10px;
    max-height: 60px;
    overflow: hidden;
}
.st-rule-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="st-h15"></div>

<!-- 搜索表单 -->
<form class="layui-form st-form-search" action="" lay-filter="ST-FORM-SEARCH">
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">规则名称</label>
            <div class="layui-input-inline">
                <input type="text" name="search" autocomplete="off" placeholder="搜索规则名称或关键词" class="layui-input" value="<?php echo e(request('search')); ?>">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline">
                <select name="status">
                    <option value="">全部状态</option>
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <a class="layui-btn layui-btn-xs st-search-button">开始搜索</a>
        </div>
    </div>
</form>

<!-- 工具栏 -->
<div class="layui-btn-container st-tool-bar">
    <a class="layui-btn layui-btn-sm" href="ai-rule/create">
        <i class="layui-icon layui-icon-add-1"></i>添加规则
    </a>
    <a class="layui-btn layui-btn-sm layui-btn-normal" onclick="location.reload()">
        <i class="layui-icon layui-icon-refresh-3"></i>刷新
    </a>
</div>

<!-- 规则列表 -->
<div class="layui-row layui-col-space15">
    <?php $__empty_1 = true; $__currentLoopData = $rules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <div class="layui-col-md6">
        <div class="st-rule-card">
            <div class="st-rule-title">
                <?php echo e($rule->name); ?>

                <?php if($rule->is_active): ?>
                    <span class="layui-badge">启用</span>
                <?php else: ?>
                    <span class="layui-badge layui-bg-gray">禁用</span>
                <?php endif; ?>
            </div>

            <div class="st-rule-keywords">
                <?php $__currentLoopData = $rule->keywords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $keyword): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="st-keyword-tag"><?php echo e($keyword); ?></span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="st-rule-message">
                <?php echo e($rule->reply_message); ?>

            </div>

            <div class="st-rule-meta">
                <div>
                    <span>优先级: <?php echo e($rule->priority); ?></span>
                    <span style="margin-left: 15px;">使用次数: <?php echo e($rule->usage_count); ?></span>
                </div>
                <div>
                    <a href="ai-rule/<?php echo e($rule->id); ?>/edit" class="layui-btn layui-btn-xs">
                        <i class="layui-icon layui-icon-edit"></i>编辑
                    </a>
                    <a href="javascript:;" onclick="deleteRule(<?php echo e($rule->id); ?>)" class="layui-btn layui-btn-xs layui-btn-danger">
                        <i class="layui-icon layui-icon-delete"></i>删除
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <div class="layui-col-md12">
        <div style="text-align: center; padding: 60px 20px; color: #999;">
            <i class="layui-icon layui-icon-face-cry" style="font-size: 60px; display: block; margin-bottom: 20px;"></i>
            <p style="font-size: 16px; margin-bottom: 20px;">暂无AI规则</p>
            <a href="ai-rule/create" class="layui-btn">
                <i class="layui-icon layui-icon-add-1"></i>添加第一个规则
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function deleteRule(id) {
    layer.confirm('确定要删除这个规则吗？', {
        icon: 3,
        title: '提示'
    }, function(index){
        // 创建表单并提交
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = 'ai-rule/' + id + '/delete';

        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();

        layer.close(index);
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('strongadmin::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\mostxx.com_xxx\resources\views/strongadmin/customer-service/ai-rules.blade.php ENDPATH**/ ?>