/* 优化后的客服系统样式 - 压缩版本 */
#customer-service-widget{position:fixed;bottom:20px;right:20px;z-index:9999;font-family:Arial,sans-serif}
#cs-chat-button{width:60px;height:60px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:#fff;font-size:28px;box-shadow:0 4px 20px rgba(102,126,234,.4);transition:all .3s ease;border:none}
#cs-chat-button:hover{transform:translateY(-2px);box-shadow:0 6px 25px rgba(102,126,234,.6)}
#cs-chat-button.hidden{opacity:0;pointer-events:none;transform:scale(.8)}
#cs-chat-window{position:fixed;bottom:20px;right:20px;width:350px;height:500px;background:#fff;border-radius:12px;box-shadow:0 8px 30px rgba(0,0,0,.15);transform:translateY(100%) scale(.8);opacity:0;visibility:hidden;transition:all .3s cubic-bezier(.4,0,.2,1);z-index:10000;display:flex;flex-direction:column;overflow:hidden}
#cs-chat-window.open{transform:translateY(0) scale(1);opacity:1;visibility:visible}
#cs-chat-window.minimized{height:60px;transform:translateY(calc(100% - 60px)) scale(1)}
.cs-header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;padding:15px 20px;display:flex;justify-content:space-between;align-items:center;border-radius:12px 12px 0 0}
.cs-header h3{margin:0;font-size:16px;font-weight:600}
.cs-header-actions{display:flex;gap:8px}
.cs-header-actions button{background:rgba(255,255,255,.2);border:none;color:#fff;width:28px;height:28px;border-radius:6px;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:background .2s}
.cs-header-actions button:hover{background:rgba(255,255,255,.3)}
#cs-messages{flex:1;padding:15px;overflow-y:auto;background:#f8f9fa;min-height:0}
.cs-message{margin-bottom:12px;display:flex;align-items:flex-start;gap:8px}
.cs-message.user{flex-direction:row-reverse}
.cs-message-content{max-width:80%;padding:10px 14px;border-radius:18px;font-size:14px;line-height:1.4;word-wrap:break-word}
.cs-message.user .cs-message-content{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;border-bottom-right-radius:6px}
.cs-message.admin .cs-message-content{background:#fff;color:#333;border:1px solid #e1e5e9;border-bottom-left-radius:6px}
.cs-message-time{font-size:11px;color:#999;margin-top:4px;text-align:center}
.cs-input-area{padding:15px;background:#fff;border-top:1px solid #e1e5e9;display:flex;gap:10px;align-items:flex-end}
#cs-message-input{flex:1;border:1px solid #e1e5e9;border-radius:20px;padding:10px 15px;font-size:14px;resize:none;outline:none;max-height:80px;min-height:40px}
#cs-message-input:focus{border-color:#667eea}
#cs-send-btn{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;border:none;width:40px;height:40px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:transform .2s}
#cs-send-btn:hover{transform:scale(1.05)}
#cs-send-btn:disabled{opacity:.5;cursor:not-allowed;transform:none}
.cs-typing-indicator{display:none;padding:10px 15px;color:#999;font-style:italic;font-size:13px}
.cs-typing-indicator.show{display:block}
.cs-offline-form{padding:20px;text-align:center}
.cs-offline-form h4{margin:0 0 15px;color:#333;font-size:16px}
.cs-offline-form input,.cs-offline-form textarea{width:100%;padding:10px;border:1px solid #e1e5e9;border-radius:6px;margin-bottom:10px;font-size:14px;box-sizing:border-box}
.cs-offline-form button{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;border:none;padding:12px 24px;border-radius:6px;cursor:pointer;font-size:14px;width:100%}
.cs-status-indicator{width:8px;height:8px;border-radius:50%;background:#52c41a;display:inline-block;margin-right:6px;animation:pulse 2s infinite}
@keyframes pulse{0%{opacity:1}50%{opacity:.5}100%{opacity:1}}
@media (max-width:480px){#cs-chat-window{width:calc(100vw - 20px);height:calc(100vh - 40px);bottom:10px;right:10px;left:10px;border-radius:8px}#cs-chat-button{bottom:15px;right:15px}}
.cs-message-image{max-width:200px;border-radius:8px;margin:5px 0;cursor:pointer}
.cs-message-image:hover{opacity:.9}
.cs-emoji-btn{background:none;border:none;font-size:18px;cursor:pointer;padding:5px;border-radius:4px;transition:background .2s}
.cs-emoji-btn:hover{background:rgba(0,0,0,.05)}
.cs-quick-replies{display:flex;flex-wrap:wrap;gap:6px;margin:10px 0}
.cs-quick-reply{background:#f0f0f0;border:1px solid #ddd;border-radius:15px;padding:6px 12px;font-size:12px;cursor:pointer;transition:all .2s}
.cs-quick-reply:hover{background:#e0e0e0;border-color:#ccc}
