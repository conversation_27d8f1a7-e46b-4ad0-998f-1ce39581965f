<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端客服调试</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .log { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-btn:hover { background: #0056b3; }
        .status-box { padding: 15px; border-radius: 8px; margin: 10px 0; }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端客服系统调试</h1>
        
        <div style="margin-bottom: 20px;">
            <button class="test-btn" onclick="testAPIs()">测试API</button>
            <button class="test-btn" onclick="testInitialization()">测试初始化</button>
            <button class="test-btn" onclick="testDOMElements()">检查DOM元素</button>
            <button class="test-btn" onclick="forceShowWidget()">强制显示客服</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status-display">
            <div class="status-warning">🔍 准备就绪，点击按钮开始调试</div>
        </div>
        
        <div id="log" class="log">
            <div class="info">📋 调试日志:</div>
        </div>
    </div>

    <!-- 客服组件 -->
    <div id="customer-service-widget" style="display: none;">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                    </div>
                    <button onclick="toggleChat()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;">×</button>
                </div>
            </div>
            <div id="cs-messages" style="flex:1;padding:20px;overflow-y:auto;max-height:360px;background:#fafafa;">
                <div style="text-align:center;color:#666;font-size:14px;margin:20px 0;">
                    <div style="background:white;padding:15px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);">
                        <div style="font-weight:600;margin-bottom:5px;">👋 欢迎咨询</div>
                        <div>我们随时为您提供帮助！</div>
                    </div>
                </div>
            </div>
            <div style="padding:15px;border-top:1px solid #eee;background:white;">
                <div style="display:flex;gap:10px;align-items:center;">
                    <input type="text" id="cs-message-input" placeholder="请输入您的问题..." style="flex:1;padding:12px;border:1px solid #ddd;border-radius:25px;outline:none;font-size:14px;">
                    <button onclick="sendMessage()" style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border:none;width:40px;height:40px;border-radius:50%;cursor:pointer;">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let csSettings = {};
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            logDiv.innerHTML += `<div style="color: ${colors[type] || colors.info}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">📋 调试日志:</div>';
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status-display');
            const className = 'status-' + type;
            statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
        }

        async function testAPIs() {
            log('🔍 开始测试API...', 'info');
            
            try {
                // 测试状态API
                log('测试状态API: /api/customer-service/status', 'info');
                const statusResponse = await fetch('/api/customer-service/status');
                const statusData = await statusResponse.json();
                log('状态API响应: ' + JSON.stringify(statusData), statusData.success ? 'success' : 'error');
                
                // 测试设置API
                log('测试设置API: /api/customer-service/get-settings.php', 'info');
                const settingsResponse = await fetch('/api/customer-service/get-settings.php');
                const settingsData = await settingsResponse.json();
                log('设置API响应: ' + JSON.stringify(settingsData), settingsData.success ? 'success' : 'error');
                
                if (settingsData.success) {
                    csSettings = settingsData.data;
                    log('系统启用状态: ' + csSettings.system_enabled, 'info');
                    
                    if (statusData.success && statusData.enabled && csSettings.system_enabled) {
                        updateStatus('✅ API测试通过，客服系统应该显示', 'success');
                    } else {
                        updateStatus('⚠️ API正常但客服系统被禁用', 'warning');
                    }
                } else {
                    updateStatus('❌ 设置API失败', 'error');
                }
                
            } catch (error) {
                log('❌ API测试失败: ' + error.message, 'error');
                updateStatus('❌ API连接失败: ' + error.message, 'error');
            }
        }

        async function testInitialization() {
            log('🚀 开始测试完整初始化流程...', 'info');
            
            // 步骤1: 加载设置
            log('步骤1: 加载客服设置...', 'info');
            await loadCustomerServiceSettings();
            
            // 步骤2: 检查系统启用状态
            log('步骤2: 检查系统启用状态...', 'info');
            if (!csSettings || !csSettings.system_enabled) {
                log('❌ 客服系统已禁用或设置加载失败', 'error');
                updateStatus('❌ 客服系统初始化失败 - 系统被禁用', 'error');
                return;
            }
            
            log('✅ 客服系统已启用，继续初始化', 'success');
            
            // 步骤3: 检查服务状态
            log('步骤3: 检查服务状态...', 'info');
            checkCustomerServiceStatus();
        }

        async function loadCustomerServiceSettings() {
            try {
                log('📡 正在加载客服设置...', 'info');
                const response = await fetch('/api/customer-service/get-settings.php');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();

                if (data.success) {
                    csSettings = data.data;
                    log('✅ 客服设置加载成功', 'success');
                    log('设置详情: ' + JSON.stringify(csSettings, null, 2), 'info');
                } else {
                    log('❌ 加载客服设置失败: ' + data.message, 'error');
                    csSettings = getDefaultSettings();
                }
            } catch (error) {
                log('💥 加载客服设置异常: ' + error.message, 'error');
                csSettings = getDefaultSettings();
            }
        }

        function checkCustomerServiceStatus() {
            log('🔍 检查客服系统状态...', 'info');
            
            fetch('/api/customer-service/status')
                .then(response => response.json())
                .then(data => {
                    log('客服系统状态响应: ' + JSON.stringify(data), 'info');
                    
                    if (data.success && data.enabled) {
                        log('✅ 服务状态检查通过，显示客服组件', 'success');
                        document.getElementById('customer-service-widget').style.display = 'block';
                        updateStatus('✅ 客服系统正常运行，右下角应该可以看到客服按钮', 'success');
                    } else {
                        log('❌ 服务状态检查失败，隐藏客服组件', 'warning');
                        document.getElementById('customer-service-widget').style.display = 'none';
                        updateStatus('⚠️ 客服系统服务被禁用', 'warning');
                    }
                })
                .catch(error => {
                    log('❌ 检查客服系统状态失败: ' + error.message, 'error');
                    // 出错时默认显示
                    document.getElementById('customer-service-widget').style.display = 'block';
                    updateStatus('⚠️ 状态检查失败，但强制显示客服', 'warning');
                });
        }

        function getDefaultSettings() {
            return {
                system_enabled: true,
                welcome_message: '您好！欢迎咨询，我们将竭诚为您服务！'
            };
        }

        function testDOMElements() {
            log('🔍 检查DOM元素...', 'info');
            
            const elements = [
                'customer-service-widget',
                'cs-chat-button', 
                'cs-chat-window',
                'cs-messages',
                'cs-message-input'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    const display = getComputedStyle(element).display;
                    log(`✅ 元素 ${id} 存在，显示状态: ${display}`, 'success');
                } else {
                    log(`❌ 元素 ${id} 不存在`, 'error');
                }
            });
        }

        function forceShowWidget() {
            log('🔧 强制显示客服组件...', 'warning');
            
            const widget = document.getElementById('customer-service-widget');
            if (widget) {
                widget.style.display = 'block';
                log('✅ 客服组件已强制显示', 'success');
                updateStatus('🔧 客服组件已强制显示', 'warning');
            } else {
                log('❌ 找不到客服组件', 'error');
                updateStatus('❌ 找不到客服组件DOM元素', 'error');
            }
        }

        function toggleChat() {
            const chatWindow = document.getElementById('cs-chat-window');
            if (chatWindow.style.display === 'none' || !chatWindow.style.display) {
                chatWindow.style.display = 'block';
                log('💬 聊天窗口已打开', 'info');
            } else {
                chatWindow.style.display = 'none';
                log('💬 聊天窗口已关闭', 'info');
            }
        }

        function sendMessage() {
            const input = document.getElementById('cs-message-input');
            const message = input.value.trim();
            if (message) {
                log('📤 发送消息: ' + message, 'info');
                input.value = '';
            }
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成，开始自动测试', 'info');
            setTimeout(() => {
                testAPIs();
                testDOMElements();
            }, 500);
        });
    </script>
</body>
</html>
